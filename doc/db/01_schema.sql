CREATE TABLE `cs_channel_system`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `biz_code`          varchar(32)    DEFAULT NULL COMMENT '渠道编号',
    `channel_name`      varchar(32)    DEFAULT NULL COMMENT '渠道名称',
    `channel_logo`      varchar(255)   DEFAULT NULL COMMENT '渠道图标',
    `transfer_out_flag` char(1)        DEFAULT NULL COMMENT '转出开关标志(0=不可转出 1=可转出)',
    `transfer_price`    decimal(10, 2) DEFAULT '0.00' COMMENT '转出手续费',
    `transfer_size`     int(11) DEFAULT '0' COMMENT '单次转出最大数量',
    `status`            varchar(4)     DEFAULT NULL COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `is_transfer`       varchar(4)     DEFAULT NULL COMMENT '是否可流转 0:不可,1:可以',
    `access_url`        varchar(255)   DEFAULT NULL COMMENT '访问地址',
    `md5_key`           varchar(255)   DEFAULT NULL COMMENT '加密key',
    `create_datetime`   datetime       DEFAULT NULL COMMENT '创建时间',
    `updater`           bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32)    DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`   datetime       DEFAULT NULL COMMENT '更新时间',
    `company_id`        bigint(20) DEFAULT NULL COMMENT '关联机构',
    `contract_id`       bigint(20) DEFAULT NULL COMMENT '合约序号',
    `collection_type`   varchar(4)     DEFAULT NULL COMMENT '藏品类型',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渠道系统';

CREATE TABLE `cs_channel_temp_code`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `channel_code`    varchar(20) DEFAULT NULL COMMENT '渠道编号',
    `channel_user_id` varchar(64) DEFAULT NULL COMMENT '关联渠道用户',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `temp_code`       varchar(32) DEFAULT NULL COMMENT '临时编号',
    `status`          varchar(8)  DEFAULT NULL COMMENT '状态 dict={"0":"待使用","1":"已使用","2":"已过期"}',
    `create_datetime` datetime    DEFAULT NULL COMMENT '创建时间',
    `expire_time`     bigint(20) DEFAULT NULL COMMENT '过期时间戳',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7254 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='临时授权编号';

CREATE TABLE `cs_channel_user`
(
    `id`              bigint(32) NOT NULL COMMENT '序号',
    `channel_code`    varchar(20) DEFAULT NULL COMMENT '渠道编号',
    `channel_user_id` varchar(64) DEFAULT NULL COMMENT '关联渠道用户',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `create_datetime` datetime    DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_01` (`user_id`,`channel_code`),
    UNIQUE KEY `index_02` (`channel_code`,`channel_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渠道用户序号';

CREATE TABLE `cs_third_trade_transfer_order`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `plat_type`          varchar(32)                   NOT NULL COMMENT '三方平台类型，dict={"0":"xmeta"}',
    `tran_no`            varchar(32) DEFAULT NULL COMMENT '三方订单号',
    `source_user_id`     bigint(20) NOT NULL COMMENT '发起方用户id',
    `source_wallet_hash` varchar(32)                   NOT NULL COMMENT '发起用户钱包地址',
    `source_mobile`      varchar(32)                   NOT NULL COMMENT '发起用户手机号(不验证)',
    `target_user_id`     bigint(20) NOT NULL COMMENT '接收方用户id',
    `target_wallet_hash` varchar(32)                   NOT NULL COMMENT '接收用户钱包地址',
    `target_mobile`      varchar(32)                   NOT NULL COMMENT '接收用户手机号(不验证)',
    `status`             varchar(4) CHARACTER SET utf8 NOT NULL COMMENT '状态(0=待回调确认 1=交易成功 2=交易失败)',
    `create_datetime`    datetime    DEFAULT NULL COMMENT '创建时间',
    `callback_datetime`  datetime    DEFAULT NULL COMMENT '回调时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `tran_no_UNIQUE` (`tran_no`)
) ENGINE=InnoDB AUTO_INCREMENT=10521 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='三方系统交易划转订单';

CREATE TABLE `cs_third_trade_transfer_order_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `order_id`             bigint(20) DEFAULT NULL COMMENT '订单序号',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `price`                decimal(18, 2) DEFAULT '0.00' COMMENT '成交价格',
    `reduce_cost`          decimal(18, 2) DEFAULT '0.00' COMMENT '用户降费',
    `reback_plat`          decimal(18, 2) DEFAULT '0.00' COMMENT '平台返佣',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=53915 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='三方系统交易划转订单明细';

CREATE TABLE `cs_transfer_order`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `biz_id`             bigint(20) DEFAULT NULL COMMENT '关联id',
    `channel_code`       varchar(32)                    NOT NULL COMMENT '渠道编号',
    `channel_user_id`    bigint(20) DEFAULT NULL COMMENT '竞拍用户',
    `user_id`            bigint(20) NOT NULL COMMENT '用户序号',
    `mobile`             varchar(32)                             DEFAULT NULL COMMENT '转入人手机号',
    `type`               varchar(4)                     NOT NULL COMMENT '类型 dict={"0":"转出","1":"转入"}',
    `price`              decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '单价',
    `pay_amount`         decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '支付金额',
    `pay_type`           varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`     varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '支付订单号',
    `pay_status`         varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`       datetime                                DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount` decimal(18, 2)                          DEFAULT '0.00' COMMENT '余额支付金额',
    `pay_cash_amount`    decimal(18, 2)                          DEFAULT '0.00' COMMENT '现金支付金额',
    `commission_amount`  decimal(18, 2)                          DEFAULT '0.00' COMMENT '平台佣金收入',
    `create_datetime`    datetime                       NOT NULL COMMENT '创建时间',
    `update_datetime`    datetime                                DEFAULT NULL COMMENT '回调时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16156 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渠道系统藏品流转订单';

CREATE TABLE `cs_transfer_order_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `order_id`             bigint(20) DEFAULT NULL COMMENT '订单序号',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `price`                decimal(18, 2) DEFAULT '0.00' COMMENT '上架价格',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=63957 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渠道系统藏品流转订单详情';

CREATE TABLE `es_collection_yao_config`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_id`   bigint(20) NOT NULL COMMENT '作品序号',
    `type`            varchar(4)                     NOT NULL COMMENT '类型 dict={"0":"转化"}',
    `status`          varchar(4)                     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `yin_yao`         decimal(18, 2)                          DEFAULT '0.00' COMMENT '获得阴爻数量',
    `yang_yao`        decimal(18, 2)                          DEFAULT '0.00' COMMENT '获得阳爻数量',
    `creater`         bigint(20) NOT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名',
    `create_datetime` datetime                       NOT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`collection_id`,`type`),
    KEY               `index01` (`collection_id`,`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品关联爻数';

CREATE TABLE `es_meta_millet_transfer_detail`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `transfer_record_id` bigint(20) NOT NULL COMMENT '转赠订单序号',
    `currency`           varchar(32) CHARACTER SET utf8mb4 NOT NULL COMMENT '币种',
    `millet_type`        varchar(4)                        NOT NULL COMMENT '元粟类型',
    `quantity`           decimal(18, 2)                    NOT NULL DEFAULT '0.00' COMMENT '转赠数量',
    `create_datetime`    datetime                          NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=740 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='元粟转赠明细';

CREATE TABLE `es_meta_millet_transfer_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `from_user_id`    bigint(20) NOT NULL COMMENT '来源用户序号',
    `to_user_id`      bigint(20) NOT NULL COMMENT '新用户序号',
    `quantity`        decimal(18, 2) DEFAULT '0.00' COMMENT '数量',
    `keyword`         varchar(64)    DEFAULT NULL COMMENT '转赠查询关键字',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=652633562356654081 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='元粟转赠记录';

CREATE TABLE `es_yao_change_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `account_type`      varchar(4)   NOT NULL COMMENT '账户类型',
    `biz_category`      varchar(32)  NOT NULL COMMENT '业务大类',
    `biz_category_note` varchar(32)  NOT NULL COMMENT '业务大类',
    `biz_type`          varchar(255) NOT NULL COMMENT '业务小类',
    `biz_note`          varchar(255) NOT NULL COMMENT '业务小类说明',
    `ref_no`            varchar(255) NOT NULL COMMENT '系统内部参考订单号',
    `ref_user_id`       bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
    `yin_yao`           decimal(18, 2)                                                DEFAULT '0.00' COMMENT '阴爻数',
    `yang_yao`          decimal(18, 2)                                                DEFAULT '0.00' COMMENT '阳爻数',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_datetime`   datetime     NOT NULL COMMENT '创建时间',
    `series_no`         bigint(20) DEFAULT NULL COMMENT '关联单号',
    PRIMARY KEY (`id`),
    KEY                 `index3` (`biz_type`),
    KEY                 `index4` (`user_id`),
    KEY                 `index7` (`ref_no`)
) ENGINE=InnoDB AUTO_INCREMENT=17132 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='爻账户变动明细';

CREATE TABLE `es_yao_exchange_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户',
    `exchange_id`          bigint(20) NOT NULL COMMENT '兑换序号',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `token_id`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代币编号',
    `yin_yao`              decimal(18, 2)                                         DEFAULT NULL COMMENT '阴爻数',
    `yang_yao`             decimal(18, 2)                                         DEFAULT NULL COMMENT '阳爻数',
    `create_datetime`      datetime                                               DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=270 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='爻转化订单明细';

CREATE TABLE `es_yao_exchange_diamond_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户',
    `yin_yao`         decimal(18, 2) DEFAULT NULL COMMENT '阴爻数',
    `yang_yao`        decimal(18, 2) DEFAULT NULL COMMENT '阳爻数',
    `diamond_amount`  decimal(18, 2) NOT NULL COMMENT '转化的钻石数',
    `exchange_rate`   decimal(18, 2) DEFAULT NULL COMMENT '转化比例',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=900 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='钻石兑爻记录';

CREATE TABLE `es_yao_exchange_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `yin_yao`         decimal(18, 2) DEFAULT NULL COMMENT '阴爻数',
    `yang_yao`        decimal(18, 2) DEFAULT NULL COMMENT '阳爻数',
    `quantity`        int(11) DEFAULT NULL COMMENT '单次兑换藏品数',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=651608540821331969 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='爻转化订单';

CREATE TABLE `es_yao_login_rule`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `day`             int(11) NOT NULL DEFAULT '0' COMMENT '天数',
    `yin_yao`         decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '固定阴爻数量',
    `yang_yao`        decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '固定阳爻数量',
    `extra_yin_yao`   decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '额外阴爻数量',
    `extra_yang_yao`  decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '额外阳爻数量',
    `status`          varchar(4)                     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `creater`         bigint(32) NOT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名称',
    `create_datetime` datetime                       NOT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='连续登录送爻规则';

CREATE TABLE `es_yao_millet_config`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4)  NOT NULL COMMENT '类型 dict={"0":"乾","1":"巽","2":"坎","3":"艮","4":"坤","5":"震","6":"离","7":"兑"}',
    `currency`        varchar(32) NOT NULL COMMENT '币种',
    `name`            varchar(32) NOT NULL COMMENT '名称',
    `yao_pic`         varchar(255)                   DEFAULT NULL COMMENT '所需爻图',
    `millet_pic`      varchar(255)                   DEFAULT NULL COMMENT '元粟图',
    `day_max`         decimal(18, 2)                 DEFAULT '0.00' COMMENT '每日兑换上限',
    `yin_yao`         decimal(18, 2)                 DEFAULT '0.00' COMMENT '需要的阴爻数',
    `yang_yao`        decimal(18, 2)                 DEFAULT '0.00' COMMENT '需要的阳爻数',
    `status`          varchar(4)  NOT NULL           DEFAULT '1' COMMENT '状态 dict={"0":"废弃","1":"启用"}',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index1` (`type`),
    UNIQUE KEY `index` (`currency`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元粟配置';

CREATE TABLE `es_yao_millet_config_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `currency`        varchar(10)    DEFAULT NULL COMMENT '币种',
    `total_quantity`  decimal(18, 0) DEFAULT NULL COMMENT '总数',
    `remain_quantity` decimal(18, 0) DEFAULT NULL COMMENT '剩余数量',
    `date_number`     int(11) DEFAULT NULL COMMENT '日期编号',
    `date_time`       date           DEFAULT NULL COMMENT '日期',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`currency`,`date_number`)
) ENGINE=InnoDB AUTO_INCREMENT=905 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元粟每日配置记录';

CREATE TABLE `es_yao_millet_order`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户序号',
    `yin_yao`         decimal(18, 2)          DEFAULT '0.00' COMMENT '消耗阴爻数',
    `yang_yao`        decimal(18, 2)          DEFAULT '0.00' COMMENT '消耗阳爻数',
    `quantity`        decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '订单总数',
    `status`          varchar(4)     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"待合成","1":"已合成"}',
    `create_datetime` datetime                DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime                DEFAULT NULL COMMENT '合成时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4236 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元粟合成订单';

CREATE TABLE `es_yao_millet_order_detail`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `order_id`        bigint(20) DEFAULT NULL COMMENT '订单序号',
    `currency`        varchar(32) NOT NULL COMMENT '币种',
    `millet_type`     varchar(4)     DEFAULT NULL COMMENT '元粟',
    `yin_yao`         decimal(18, 2) DEFAULT '0.00' COMMENT '消耗阴爻数',
    `yang_yao`        decimal(18, 2) DEFAULT '0.00' COMMENT '消耗阳爻数',
    `quantity`        decimal(18, 2) DEFAULT NULL COMMENT '合成数',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7045 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元粟合成订单明细';

CREATE TABLE `es_yao_task_config`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4) NOT NULL COMMENT '类型 dict={"0":"登录","1":"购买藏品"}',
    `status`          varchar(4) NOT NULL            DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `online_duration` int(11) DEFAULT '0' COMMENT '在线时长(分钟)',
    `yin_yao`         decimal(18, 2)                 DEFAULT '0.00' COMMENT '获得阴爻数量',
    `yang_yao`        decimal(18, 2)                 DEFAULT '0.00' COMMENT '获得阳爻数量',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='爻获取任务配置';

CREATE TABLE `lxa_invitation_activity`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_type`         varchar(4)                                              DEFAULT NULL COMMENT '发行方类型 0:平台,1:发行方',
    `company_id`           bigint(20) DEFAULT NULL COMMENT '发行方序号',
    `name`                 varchar(64)                                             DEFAULT NULL COMMENT '拉新活动名称',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '赠送藏品序号',
    `period_id`            bigint(20) DEFAULT NULL COMMENT '关联期数序号',
    `pic`                  varchar(255)                                            DEFAULT NULL COMMENT '图片',
    `size`                 int(11) DEFAULT NULL COMMENT '单次邀请人数',
    `max_number`           int(11) DEFAULT NULL COMMENT '奖励领取上限',
    `total_quantity`       int(10) NOT NULL DEFAULT '1' COMMENT '总数量',
    `remain_quantity`      int(10) NOT NULL DEFAULT '1' COMMENT '剩余数量',
    `type`                 varchar(4)                                              DEFAULT '0' COMMENT '类型 dict={"0":"实名","1":"购买"}',
    `send_type`            varchar(4)                                              DEFAULT NULL COMMENT '邀请人赠送类型 dict={"0":"单藏品","1":"盲盒","2":"期数盲盒购买次数"}',
    `status`               varchar(4)                                              DEFAULT NULL COMMENT '状态 dict={"0":"待上架","1":"上架","2":"已下架","3":"已售罄"}',
    `sold_status`          varchar(4)                                              DEFAULT NULL COMMENT '售卖状态 0:正常，1:已售罄',
    `lock_time`            int(11) DEFAULT '0' COMMENT '锁仓时间(小时)',
    `transform_limit_time` int(11) DEFAULT '0' COMMENT '转赠限制(小时)',
    `content`              varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '作品故事',
    `right_type`           varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '权益类型0=文字 1=图片 2=暂无权益',
    `right_content`        longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin COMMENT '权益内容',
    `creater`              bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`         varchar(32) CHARACTER SET utf8                          DEFAULT NULL COMMENT '创建人名',
    `create_datetime`      datetime                                                DEFAULT NULL COMMENT '创建时间',
    `updater`              bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`         varchar(32) CHARACTER SET utf8                          DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`      datetime                                                DEFAULT NULL COMMENT '更新时间',
    `channel_id`           bigint(20) DEFAULT NULL COMMENT '分发渠道',
    `play_rule`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '规则',
    PRIMARY KEY (`id`),
    KEY                    `index1` (`send_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动';

CREATE TABLE `lxa_invitation_activity_blind_box_collection`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `activity_id`     bigint(20) NOT NULL COMMENT '活动id',
    `collection_id`   bigint(20) NOT NULL COMMENT '作品编号',
    `collection_name` varchar(64)    NOT NULL COMMENT '作品名称',
    `collection_pic`  varchar(255)   NOT NULL COMMENT '作品图片',
    `price`           decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '价格',
    `total_quantity`  int(11) DEFAULT NULL COMMENT '总数量',
    `remain_quantity` int(11) DEFAULT NULL COMMENT '剩余数量',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`),
    KEY               `index1` (`activity_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动赠送盲盒';

CREATE TABLE `lxa_invitation_activity_collection_send_record`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`            bigint(20) DEFAULT NULL COMMENT '活动序号',
    `activity_send_type`     varchar(4) DEFAULT NULL COMMENT '活动类型',
    `user_id`                bigint(20) DEFAULT NULL COMMENT '用户序号',
    `period_id`              bigint(20) DEFAULT NULL COMMENT '期数序号',
    `collection_id`          bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_detail_id`   bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `ref_user`               bigint(20) DEFAULT NULL COMMENT '触发用户',
    `get_collection_size`    int(11) DEFAULT NULL COMMENT '本次获得藏品数',
    `befor_collection_size`  int(11) DEFAULT NULL COMMENT '获得前藏品数',
    `after_collection_size`  int(11) DEFAULT NULL COMMENT '获得后藏品数',
    `invitation_finish_size` int(11) DEFAULT '0' COMMENT '当时已实名邀请人数',
    `create_datetime`        datetime NOT NULL COMMENT '创建时间',
    `create_time`            bigint(20) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动藏品分配记录';

CREATE TABLE `lxa_invitation_activity_registered_collection`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `series_id`       bigint(20) DEFAULT NULL COMMENT '系列序号',
    `activity_id`     bigint(20) DEFAULT NULL COMMENT '活动序号',
    `collection_id`   bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_name` varchar(32)                                            DEFAULT NULL COMMENT '作品名称',
    `cover_file_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '封面文件地址',
    `quantity`        int(11) DEFAULT NULL COMMENT '数量',
    `total_quantity`  int(11) DEFAULT NULL COMMENT '总数量',
    `remain_quantity` int(255) DEFAULT NULL COMMENT '剩余数量',
    `create_datetime` datetime                                               DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY               `index1` (`activity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动注册赠送藏品';

CREATE TABLE `lxa_invitation_activity_registered_send_record`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `series_id`            bigint(20) NOT NULL COMMENT '系列序号',
    `activity_id`          bigint(20) NOT NULL COMMENT '活动序号',
    `record_id`            bigint(20) NOT NULL COMMENT '记录序号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户序号',
    `collection_id`        bigint(20) NOT NULL COMMENT '作品序号',
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品序号',
    `ref_user`             bigint(20) NOT NULL COMMENT '触发用户',
    `create_datetime`      datetime NOT NULL COMMENT '创建时间',
    `create_time`          bigint(20) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动注册藏品分配记录';

CREATE TABLE `lxa_invitation_activity_registered_series`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`     bigint(20) DEFAULT NULL COMMENT '活动序号',
    `status`          varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"待使用","1":"使用中","2":"已用完"}',
    `send_count`      int(11) DEFAULT '0' COMMENT '赠送人数',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    `order_no`        int(11) NOT NULL COMMENT '优先级',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='拉新活动注册赠送系列';

CREATE TABLE `lxa_invitation_activity_user_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道序号',
    `activity_id`     bigint(20) DEFAULT NULL COMMENT '活动序号',
    `activity_type`   varchar(4)   DEFAULT '0' COMMENT '活动类型 0:实名,1:购买',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '新用户',
    `user_referee`    bigint(20) DEFAULT NULL COMMENT '推荐人',
    `status`          varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"待实名","1":"已实名"}',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index01` (`user_id`,`channel_id`),
    KEY               `index02` (`user_id`),
    KEY               `idx_status` (`status`),
    KEY               `index` (`user_referee`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户拉新记录';

CREATE TABLE `lxa_invitation_activity_user_sum`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`            bigint(20) DEFAULT NULL COMMENT '活动序号',
    `user_id`                bigint(20) DEFAULT NULL COMMENT '用户',
    `collection_id`          bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_size`        int(11) DEFAULT '0' COMMENT '已获藏品数量',
    `invitation_size`        int(11) DEFAULT '0' COMMENT '邀请人数',
    `invitation_finish_size` int(11) DEFAULT '0' COMMENT '已实名邀请人数',
    `create_datetime`        datetime NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index01` (`activity_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户拉新信息';

CREATE TABLE `mall_activity_finish_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `goods_id`        bigint(20) DEFAULT NULL COMMENT '商品序号',
    `mobile`          varchar(32)    DEFAULT NULL COMMENT '手机号',
    `status`          varchar(4)     DEFAULT NULL COMMENT '状态 dict={"0":"待填写","1":"已填写"}',
    `amount`          decimal(18, 2) DEFAULT NULL COMMENT '精粹数量',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赋能商城中奖记录';

CREATE TABLE `mall_goods_activity`
(
    `id`                             bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `status`                         varchar(4) DEFAULT NULL COMMENT '状态 dict={"0":"进行中","1":"已解锁","2":"已终止","3":"已结束"}',
    `exchange_function`              varchar(4) DEFAULT NULL COMMENT '兑换功能是否开启,0:关闭,1:开启',
    `unlock_datetime`                datetime   DEFAULT NULL COMMENT '解锁时间',
    `close_datetime`                 datetime   DEFAULT NULL COMMENT '兑换终止时间',
    `actual_close_datetime`          datetime   DEFAULT NULL COMMENT '实际兑换终止时间',
    `end_datetime`                   datetime   DEFAULT NULL COMMENT '活动结束时间',
    `actual_end_datetime`            datetime   DEFAULT NULL COMMENT '实际活动结束时间',
    `integral_double_start_datetime` datetime   DEFAULT NULL COMMENT '积分兑换翻倍开始时间',
    `integral_double_end_datetime`   datetime   DEFAULT NULL COMMENT '积分兑换翻倍结束时间',
    `double_size`                    int(11) DEFAULT NULL COMMENT '倍数',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赋能商城活动';

CREATE TABLE `mall_goods_activity_join_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `total_amount`    decimal(18, 2) DEFAULT '0.00' COMMENT '总转化精粹',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=300 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赋能商城参与记录';

CREATE TABLE `mall_goods_additional_information`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `goods_id`        bigint(20) NOT NULL COMMENT '商品序号',
    `record_id`       bigint(20) DEFAULT NULL COMMENT '购买记录序号',
    `content`         varchar(255) DEFAULT NULL COMMENT '附加内容',
    `status`          varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"待使用","1":"已使用","2":"已废除"}',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2397 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品附加信息';

CREATE TABLE `mall_goods_buy_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户序号',
    `supplier_id`       bigint(20) NOT NULL COMMENT '供应商序号',
    `address_id`        bigint(20) DEFAULT NULL COMMENT '地址序号',
    `goods_id`          bigint(20) NOT NULL COMMENT '商品序号',
    `goods_name`        varchar(255)   NOT NULL COMMENT '商品名称',
    `pic`               varchar(255)   NOT NULL COMMENT '封面图',
    `original_price`    decimal(18, 2) NOT NULL         DEFAULT '0.00' COMMENT '原价',
    `price`             decimal(18, 2) NOT NULL COMMENT '价值',
    `integral_price`    decimal(18, 2) NOT NULL COMMENT '积分价格',
    `pay_price`         decimal(18, 2) NOT NULL COMMENT '支付积分',
    `quantity`          int(11) NOT NULL DEFAULT '0' COMMENT '数量',
    `goods_type`        varchar(4)     NOT NULL COMMENT '商品类型 dict={"0":"实物商品","1":"虚拟商品"}',
    `status`            varchar(4)     NOT NULL COMMENT '状态 dict={"0":"待发货","1":"已发货","2":"已完成"}',
    `information_id`    bigint(20) DEFAULT NULL COMMENT '附加信息',
    `receiver`          varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '收件人姓名',
    `re_mobile`         varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '收件人电话',
    `re_address`        varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '收货地址/活动地址',
    `create_datetime`   datetime       NOT NULL COMMENT '创建时间',
    `deliverer`         varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '发货人',
    `delivery_datetime` datetime                        DEFAULT NULL COMMENT '发货时间/活动时间',
    `logistics_company` varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '物流公司编号',
    `logistics_code`    varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '物流单号',
    `logistics_trace`   varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '物流轨迹',
    `updater`           bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32)                     DEFAULT NULL COMMENT '更新人',
    `update_datetime`   datetime                        DEFAULT NULL COMMENT '完成时间',
    `remark`            varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '取消原因',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1957 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='兑现记录';

CREATE TABLE `mall_goods_category`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `parent_id`       bigint(20) DEFAULT NULL COMMENT '父id',
    `name`            varchar(32)                    DEFAULT NULL COMMENT '分类名称',
    `goods_type`      varchar(4)                     DEFAULT NULL COMMENT '商品类型 "0"="实物商品","1"="虚拟商品"',
    `status`          varchar(4) NOT NULL COMMENT '状态 dict={"0":"下架","1":"上架"}',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='商品分类';

CREATE TABLE `mall_goods_integral`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `category_id`      bigint(20) DEFAULT NULL COMMENT '分类',
    `supplier_id`      int(11) DEFAULT NULL COMMENT '供应商',
    `name`             varchar(255)                   DEFAULT NULL COMMENT '商品名称',
    `pic`              varchar(255)                   DEFAULT NULL COMMENT '封面图',
    `original_price`   decimal(18, 2)                 DEFAULT NULL COMMENT '原价',
    `price`            decimal(18, 2)                 DEFAULT NULL COMMENT '价值',
    `integral_price`   decimal(18, 2)                 DEFAULT NULL COMMENT '积分价格',
    `total_quantity`   int(11) DEFAULT '0' COMMENT '总数量',
    `remain_quantity`  int(11) DEFAULT '0' COMMENT '库存',
    `goods_type`       varchar(4)                     DEFAULT NULL COMMENT '商品类型 dict={"0":"实物商品","1":"虚拟商品"}',
    `type`             varchar(4)                     DEFAULT NULL COMMENT '类型 dict={"0":"正常商品","1":"特殊商品","2":"神秘商品"}',
    `status`           varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"待上架","1":"上架中","2":"已下架"}',
    `sold_status`      varchar(4)                     DEFAULT NULL COMMENT '售卖状态 dict={"0":"未解锁","1":"售卖中","2":"已售罄"}',
    `show_pic`         varchar(512)                   DEFAULT NULL COMMENT '展示图',
    `content_pic`      varchar(1024)                  DEFAULT NULL COMMENT '详情图',
    `play_pic`         varchar(255)                   DEFAULT NULL COMMENT '玩法说明图',
    `unlock_condition` decimal(18, 2)                 DEFAULT NULL COMMENT '解锁条件',
    `creater`          bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`     varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime`  datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`          bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`     varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`  datetime                       DEFAULT NULL COMMENT '更新时间',
    `update_time`      bigint(32) DEFAULT NULL COMMENT '更新时间戳',
    `order_no`         int(11) DEFAULT '0' COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=247 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='积分商品';

CREATE TABLE `mall_goods_supplier`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`            varchar(32)                    DEFAULT NULL COMMENT '供货商名称',
    `status`          varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"下架","1":"上架"}',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='赋能商城供货商';

CREATE TABLE `mall_integral_exchange`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `integral_price`  decimal(18, 2) DEFAULT NULL COMMENT '获得积分数',
    `quantity`        int(11) DEFAULT NULL COMMENT '单次兑换藏品数',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=620378839171997697 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='兑换记录';

CREATE TABLE `mall_integral_exchange_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户',
    `exchange_id`          bigint(20) NOT NULL COMMENT '兑换序号',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `token_id`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '代币编号',
    `integral_price`       decimal(18, 2)                                         DEFAULT NULL COMMENT '积分兑换单价',
    `create_datetime`      datetime                                               DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3251 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='兑换明细';

CREATE TABLE `meta_user_entry_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户序号',
    `ticket_type`     varchar(4) DEFAULT NULL COMMENT '门票类型',
    `ticket_id`       bigint(20) DEFAULT NULL COMMENT '门票序号',
    `create_time`     bigint(20) NOT NULL COMMENT '进入时间戳',
    `create_datetime` datetime NOT NULL COMMENT '进入时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4433 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户进入元宇宙记录';

CREATE TABLE `nft_approve_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `ref_type`        varchar(4)   DEFAULT NULL COMMENT '类型 dict={"0":"发行方申请","1":"作品申请"}',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '关联序号',
    `creater`         bigint(20) DEFAULT NULL COMMENT '审核人',
    `creater_name`    varchar(64)  DEFAULT NULL COMMENT '审核人',
    `create_datetime` datetime     DEFAULT NULL COMMENT '审核时间',
    `opinion`         varchar(255) DEFAULT NULL COMMENT '审核意见',
    `history_data`    text COMMENT '历史数据',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=83 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='审核记录';

CREATE TABLE `nft_article_period`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `article_id` bigint(20) DEFAULT NULL COMMENT '文章序号',
    `period_id`  bigint(20) DEFAULT NULL COMMENT '期数序号',
    `order_no`   int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文章相关藏品';

CREATE TABLE `nft_blindbox_user`
(
    `id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`  bigint(20) DEFAULT NULL COMMENT '用户编号',
    `quantity` int(11) DEFAULT NULL COMMENT '数量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户每月免费抽取次数';

CREATE TABLE `nft_bsn_task`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`                 varchar(4)   DEFAULT NULL COMMENT '类型dict={"0":"新建分类","1":"铸造NFT"}',
    `task_id`              varchar(64)  DEFAULT NULL COMMENT '任务id',
    `tx_hash`              varchar(128) DEFAULT NULL COMMENT '交易hash',
    `name`                 varchar(64)  DEFAULT NULL COMMENT '名称',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品id',
    `token_id`             bigint(20) DEFAULT NULL COMMENT 'tokenId(成功后回写)',
    `class_id`             varchar(64)  DEFAULT NULL COMMENT '分类id',
    `nft_id`               varchar(64)  DEFAULT NULL COMMENT 'nft id',
    `owner`                varchar(64)  DEFAULT NULL COMMENT '拥有者地址',
    `create_datetime`      datetime NOT NULL COMMENT '创建时间',
    `status`               varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"确认中","1":"已成功","2":"已失败"}',
    `remark`               varchar(128) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21484 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='bsn分类铸造/nft铸币';

CREATE TABLE `nft_challenge`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `company_id`            bigint(20) NOT NULL DEFAULT '0' COMMENT '机构id',
    `channel_id`            bigint(20) NOT NULL COMMENT '分发渠道',
    `name`                  varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `cover_pic_url`         varchar(255)                                                  DEFAULT NULL COMMENT '封面图',
    `start_time`            timestamp NULL DEFAULT NULL COMMENT '开始时间',
    `effective_hours`       int(20) NOT NULL DEFAULT '0' COMMENT '有效时长',
    `end_time`              timestamp NULL DEFAULT NULL COMMENT '结束时间',
    `actual_end_time`       timestamp NULL DEFAULT NULL COMMENT '实际结束时间',
    `type`                  varchar(32)                                                   DEFAULT NULL COMMENT '兑换类型 dict={"0":"NFT","1":"实物"}',
    `award_ref_id`          bigint(20) DEFAULT NULL COMMENT '奖品id',
    `award_quantity`        int(20) DEFAULT NULL COMMENT '奖品总数量',
    `award_remain_quantity` int(11) DEFAULT NULL COMMENT '剩余奖品数量',
    `total_collection_num`  int(11) DEFAULT NULL COMMENT '共需收集的作品数',
    `read_count`            int(11) DEFAULT '0' COMMENT '浏览次数',
    `status`                varchar(4)                                                    DEFAULT '0' COMMENT '状态{"0":"待审核","1":"审核不通过","2":"审核通过",”3”:”已下架"}',
    `start_status`          varchar(4)                                                    DEFAULT '0' COMMENT '开始状态{"0":"待开始","1":"已开始","2":"已结束","2":"提前结束"}',
    `apply_user_id`         bigint(20) DEFAULT NULL COMMENT '申请人',
    `apply_note`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `apply_datetime`        datetime                                                      DEFAULT NULL COMMENT '申请时间',
    `approve_user_id`       bigint(20) DEFAULT NULL COMMENT '审批人',
    `approve_note`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `approve_datetime`      datetime                                                      DEFAULT NULL COMMENT '审批时间',
    `updater`               bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_datetime`       datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `location`              varchar(32)                                                   DEFAULT NULL COMMENT '位置',
    `order_no`              int(11) NOT NULL DEFAULT '0' COMMENT '序号',
    `remark`                varchar(255)                                                  DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=156 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='挑战';

CREATE TABLE `nft_challenge_collection`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT,
    `back_type`           varchar(4) DEFAULT '0' COMMENT '回收类型 0:回收,1:无需回收',
    `challenge_id`        bigint(20) NOT NULL DEFAULT '0' COMMENT '挑战id',
    `collection_id`       bigint(20) NOT NULL DEFAULT '0' COMMENT '作品id',
    `collection_quantity` int(20) NOT NULL DEFAULT '1' COMMENT '作品数量',
    `condition_id`        bigint(20) DEFAULT NULL COMMENT '条件id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='挑战作品关联表';

CREATE TABLE `nft_challenge_condition`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT,
    `challenge_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '挑战id',
    `name`         varchar(255) NOT NULL DEFAULT '0' COMMENT '名称',
    `quantity`     int(20) NOT NULL DEFAULT '0' COMMENT '满足条件的数量',
    `back_type`    varchar(32)  NOT NULL DEFAULT '0' COMMENT '回收类型 0:回收,1:赋能',
    `order_no`     int(11) NOT NULL DEFAULT '1' COMMENT '序号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=359 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='挑战条件作品明细';

CREATE TABLE `nft_challenge_condition_detail`
(
    `id`                        bigint(20) NOT NULL AUTO_INCREMENT,
    `challenge_id`              bigint(20) NOT NULL DEFAULT '0' COMMENT '挑战id',
    `condition_id`              bigint(20) NOT NULL DEFAULT '0' COMMENT '条件id',
    `collection_id`             bigint(20) NOT NULL DEFAULT '0' COMMENT '作品id',
    `collection_name`           varchar(255) DEFAULT NULL,
    `collection_cover_file_url` varchar(255) DEFAULT NULL,
    `collection_quantity`       int(20) NOT NULL DEFAULT '1' COMMENT '作品需要拥有数量',
    `lock_condition`            varchar(4)   DEFAULT '0' COMMENT '作品参与条件：0=不限制 1=可流转 2=不可流转',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7823 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='挑战条件作品明细';

CREATE TABLE `nft_challenge_order`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT,
    `challenge_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '挑战id',
    `challenge_type`    varchar(4)                                                   DEFAULT NULL COMMENT '兑换类型 dict={"0":"NFT","1":"实物"}',
    `company_id`        bigint(20) DEFAULT '0' COMMENT '发行方序号',
    `channel_id`        bigint(20) NOT NULL DEFAULT '0' COMMENT '渠道id',
    `user_id`           bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
    `award_ref_id`      bigint(20) DEFAULT NULL COMMENT '奖品id',
    `award_name`        varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `award_pic`         varchar(255)                                                 DEFAULT NULL COMMENT '奖品封面图',
    `status`            varchar(4) NOT NULL                                          DEFAULT '0' COMMENT '状态 dict={"0":"待发货","1":"已发放","2":"发放失败"}',
    `create_time`       timestamp  NOT NULL                                          DEFAULT CURRENT_TIMESTAMP COMMENT '兑换时间',
    `receiver`          varchar(255)                                                 DEFAULT NULL COMMENT '收件人姓名',
    `re_mobile`         varchar(32)                                                  DEFAULT NULL COMMENT '收件人电话',
    `re_address`        varchar(255)                                                 DEFAULT NULL COMMENT '收货地址/活动地址',
    `deliverer`         varchar(32)                                                  DEFAULT NULL COMMENT '发货人',
    `delivery_datetime` datetime                                                     DEFAULT NULL COMMENT '发货时间/活动时间',
    `logistics_company` varchar(32)                                                  DEFAULT NULL COMMENT '物流公司编号',
    `logistics_code`    varchar(32)                                                  DEFAULT NULL COMMENT '物流单号',
    `logistics_trace`   varchar(255)                                                 DEFAULT NULL COMMENT '物流轨迹',
    `updater`           bigint(20) DEFAULT NULL COMMENT '处理人',
    `updater_name`      varchar(32)                                                  DEFAULT NULL COMMENT '处理人名称',
    `update_time`       datetime                                                     DEFAULT NULL COMMENT '处理时间',
    `remark`            varchar(255)                                                 DEFAULT NULL COMMENT '备注',
    `is_distribution`   varchar(4)                                                   DEFAULT NULL COMMENT '是否分配 0:否,1:是',
    PRIMARY KEY (`id`),
    KEY                 `index` (`challenge_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=21832 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='挑战兑换订单';

CREATE TABLE `nft_challenge_order_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `back_type`            varchar(4) DEFAULT '0' COMMENT '回收类型 0:回收,1:无需回收',
    `order_id`             bigint(20) NOT NULL DEFAULT '0' COMMENT '订单id',
    `challenge_id`         bigint(20) NOT NULL DEFAULT '0' COMMENT '挑战id',
    `condition_id`         bigint(20) DEFAULT NULL COMMENT '条件id',
    `user_id`              bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
    `collection_detail_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '藏品id',
    `lock_condition`       varchar(4) DEFAULT NULL COMMENT '藏品参与条件：作品参与条件：0=不限制 1=可流转 2=不可流转',
    PRIMARY KEY (`id`),
    KEY                    `idx_conditionid_challengeid` (`condition_id`,`challenge_id`)
) ENGINE=InnoDB AUTO_INCREMENT=134409 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='挑战兑换订单明细';

CREATE TABLE `nft_channel_sms_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `channel_type`    varchar(4)   DEFAULT NULL COMMENT '渠道类型 dict={"0":"易宝"}',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '关联序号',
    `ref_type`        varchar(4)   DEFAULT NULL COMMENT '发送类型 dict={"0":"个人商户入驻"}',
    `request`         varchar(255) DEFAULT NULL COMMENT '入参',
    `respond`         varchar(255) DEFAULT NULL COMMENT '回参',
    `request_no`      varchar(64)  DEFAULT NULL COMMENT '入网请求号',
    `request_type`    varchar(32)  DEFAULT NULL COMMENT '访问类型',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32)  DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1115 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='三方短信重发记录';

CREATE TABLE `nft_collection`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT,
    `three_channel_type`      varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT '0' COMMENT '三方渠道类型 0:麦塔,1:山海经',
    `three_channel_id`        bigint(20) DEFAULT NULL COMMENT '三方渠道id',
    `collection_hash`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL COMMENT '作品hash',
    `user_id`                 bigint(20) DEFAULT NULL COMMENT '用户编号',
    `name`                    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `serial_name`             varchar(64) COLLATE utf8mb4_bin NOT NULL COMMENT '系列名称',
    `category`                varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '类别(0=版权 1=衍生)',
    `plate_category`          varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '板块类别',
    `type`                    varchar(1) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '类型 dict={"0":"自创","1":"外来"}',
    `buy_type`                varchar(4) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '购买方式 dict={"0":"口令购买","1":"普通购买"}',
    `level_type`              varchar(1) COLLATE utf8mb4_bin                                DEFAULT '' COMMENT '级别类型 0;普通 1:高级 2:稀有 3 :珍奇 4:传说',
    `file_type`               varchar(2) COLLATE utf8mb4_bin  NOT NULL COMMENT '文件类型 dict={"0":"图片","1":"音频","2":"视频"}',
    `content_type`            varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '文件类型，0=文字 1=图片',
    `content`                 longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    `cover_file_url`          varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '封面文件地址',
    `file_url`                text COLLATE utf8mb4_bin COMMENT '文件地址',
    `author_id`               bigint(20) DEFAULT NULL COMMENT '发行方',
    `divide_author_id`        bigint(20) DEFAULT NULL COMMENT '分账主体',
    `produced_id`             bigint(20) DEFAULT NULL COMMENT '出品方',
    `total_quantity`          int(10) DEFAULT '1' COMMENT '总数量',
    `market_quantity`         int(10) DEFAULT '1' COMMENT '流通总量',
    `remain_quantity`         int(10) DEFAULT '1' COMMENT '发行方剩余数量',
    `plat_remain_quantity`    int(10) DEFAULT '0' COMMENT '平台剩余数量',
    `tags`                    varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '标签 多个标签逗号隔开',
    `right_content`           longtext COLLATE utf8mb4_bin COMMENT '权益内容',
    `right_type`              varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '权益类型0=文字 1=图片 2=暂无权益',
    `chain_type`              varchar(20) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '链类型',
    `contract_id`             bigint(32) DEFAULT NULL COMMENT '合约id',
    `pay_amount`              decimal(10, 2)                                                DEFAULT '0.00' COMMENT '支付金额',
    `pay_type`                varchar(32) COLLATE utf8mb4_bin                               DEFAULT '0.00' COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`          varchar(32) COLLATE utf8mb4_bin                               DEFAULT '0.00' COMMENT '支付订单号',
    `pay_status`              varchar(32) COLLATE utf8mb4_bin                               DEFAULT '0.00' COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`            datetime                                                      DEFAULT NULL COMMENT '支付时间',
    `create_datetime`         datetime                                                      DEFAULT NULL COMMENT '创建时间',
    `token_import_id`         bigint(20) DEFAULT NULL COMMENT '代币导入id',
    `status`                  varchar(2) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '状态 0:导入中 1:可使用',
    `channel_transfer_status` varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '作品同步三方状态 0:未同步 1:已同步',
    `pay_balance_amount`      decimal(18, 2)                                                DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`         decimal(18, 2)                                                DEFAULT NULL COMMENT '现金支付金额',
    `storage_fee`             decimal(18, 2)                                                DEFAULT NULL COMMENT '存储费(config表的pre_file_storage_fee)',
    `file_size`               decimal(10, 2)                                                DEFAULT NULL COMMENT '文件大小(MB)',
    `commission_amount`       decimal(18, 2)                                                DEFAULT '0.00' COMMENT '平台佣金收入',
    `down_status`             varchar(1) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '下架状态 0:否 1:是',
    `module_ids`              varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '模型ids，多个用逗号隔开',
    `lock_time`               int(11) DEFAULT '0' COMMENT '锁仓时间(小时)',
    `updater`                 varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '更新人',
    `updater_name`            varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`         datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `max_exchange_time`       int(11) DEFAULT NULL COMMENT '最大兑换次数',
    `biz_type`                varchar(4) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '业务类型 0:版权,1:衍生,2盲盒,3:抽签,4:diy,5:幸运抽奖',
    `transform_limit_time`    int(11) DEFAULT '0' COMMENT '转赠限制(小时)',
    `android_ab`              varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT 'android ab包',
    `ios_ab`                  varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT 'iOS ab包',
    `u3d_flag`                varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '拥有是否可进入3d藏馆标志(1是0否)',
    `ticket_type`             varchar(4) CHARACTER SET utf8mb4                              DEFAULT NULL COMMENT '元宇宙门票类型',
    `remark`                  varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '备注',
    `integral_price`          decimal(18, 2)                                                DEFAULT '0.00' COMMENT '精粹价值',
    `single_max_quantity`     int(11) NOT NULL DEFAULT '-1' COMMENT '单人拥有最大数量',
    `plat_divide_rate`        decimal(8, 2)                                                 DEFAULT '0.00' COMMENT '平台分成',
    `account_integral`        decimal(18, 2)                                                DEFAULT '0.00' COMMENT '可得积分',
    `use_flag`                varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '使用标识 0:未使用,1:使用',
    `use_way`                 varchar(4) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '使用用途 0:期数',
    `note`                    varchar(45) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '备注',
    `class_id`                varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '分类id',
    `publish_xmeta`           varchar(32) COLLATE utf8mb4_bin                               DEFAULT '0' COMMENT '是否已发布到XMeta平台(0=未发布 1=已发布)',
    `publish_xmeta_style`     char(4) COLLATE utf8mb4_bin                                   DEFAULT '0' COMMENT '发布到Xmeta方式 dict={"0":"手动发布","1":"权益卡发布"}',
    `exchange_xmeta`          char(4) COLLATE utf8mb4_bin                                   DEFAULT '0' COMMENT '是否可兑换藏品XMeta上架 dict={"0":"不可兑换","1":"可兑换"}',
    `red_packet_flag`         varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '是否红包道具(1=是 0=否)',
    `special_3d_flag`         varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '个人馆特殊3d处理(0=不处理 1=个人馆转3d处理)',
    `meta_biz_type`           varchar(4) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '元宇宙关联类型 dict={"0":"渔船","1":"房屋"}',
    `meta_biz_id`             bigint(20) DEFAULT NULL COMMENT '元宇宙关联序号',
    PRIMARY KEY (`id`, `serial_name`) USING BTREE,
    UNIQUE KEY `index` (`three_channel_type`,`three_channel_id`),
    UNIQUE KEY `index_01` (`collection_hash`),
    KEY                       `index_02` (`author_id`,`status`,`three_channel_type`,`buy_type`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='时刻';

CREATE TABLE `nft_collection_buy_order`
(
    `id`                            bigint(20) NOT NULL AUTO_INCREMENT,
    `biz_type`                      varchar(32)             DEFAULT NULL COMMENT '业务类型 0=单品购买',
    `biz_id`                        bigint(20) DEFAULT NULL COMMENT '业务关联id',
    `parent_biz_id`                 bigint(20) DEFAULT NULL,
    `blindbox_collection_detail_id` bigint(32) DEFAULT NULL COMMENT '盲盒关联的藏品id',
    `user_id`                       bigint(20) DEFAULT NULL COMMENT '用户编号',
    `collection_id`                 bigint(20) DEFAULT NULL COMMENT '作品id',
    `author_id`                     bigint(20) DEFAULT NULL COMMENT '发行方',
    `price`                         decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '价格',
    `discount_price`                decimal(18, 2)          DEFAULT NULL COMMENT '折扣价',
    `quantity`                      int(11) DEFAULT NULL COMMENT '数量',
    `pay_amount`                    decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
    `create_time`                   timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `pay_type`                      varchar(32)             DEFAULT NULL COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`                varchar(32)             DEFAULT NULL COMMENT '支付订单号',
    `pay_status`                    varchar(32)             DEFAULT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`                  datetime                DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount`            decimal(18, 2)          DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`               decimal(18, 2)          DEFAULT NULL COMMENT '现金支付金额',
    `commission_amount`             decimal(18, 2)          DEFAULT '0.00' COMMENT '平台佣金收入',
    `company_amount`                decimal(18, 2)          DEFAULT '0.00' COMMENT '发行方收入',
    `word`                          varchar(32)             DEFAULT NULL COMMENT '口令',
    `channel_id`                    bigint(20) DEFAULT NULL COMMENT '分发渠道',
    `promote_channel_id`            bigint(20) DEFAULT NULL,
    `buy_source`                    varchar(4)              DEFAULT '0' COMMENT '购买来源(0=麦塔平台 1=元宇宙)',
    `pit_id`                        bigint(20) DEFAULT NULL COMMENT '坑位序号',
    PRIMARY KEY (`id`),
    KEY                             `period_index` (`biz_id`),
    KEY                             `nft_collection_buy_order_idx1` (`biz_id`,`pay_status`,`biz_type`),
    KEY                             `idx_paystatus_biztype` (`pay_status`,`biz_type`),
    KEY                             `pay_code_index` (`pay_order_code`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数字藏品型号购买订单';

CREATE TABLE `nft_collection_channel_word`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_id`   bigint(20) DEFAULT NULL COMMENT '作品id',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道id',
    `word`            varchar(32)  DEFAULT NULL COMMENT '口令',
    `status`          varchar(8) NOT NULL COMMENT '状态(0=可使用 1=已作废)',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`word`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品机构口令';

CREATE TABLE `nft_collection_detail`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT,
    `three_channel_type`     varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT '0' COMMENT '三方渠道类型 0:麦塔,1:山海经',
    `three_channel_id`       bigint(20) DEFAULT NULL COMMENT '三方渠道id',
    `collection_detail_hash` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '藏品hash',
    `collection_id`          bigint(20) NOT NULL COMMENT '藏品id',
    `plate_category`         varchar(32) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '板块类别',
    `order_number`           int(10) DEFAULT NULL COMMENT '序号',
    `user_type`              varchar(4) COLLATE utf8mb4_bin                               DEFAULT '0' COMMENT '用户类型 0:用户;1:机构',
    `user_id`                bigint(20) NOT NULL COMMENT '用户编号',
    `owner_type`             varchar(4) COLLATE utf8mb4_bin                               DEFAULT '0' COMMENT '拥有者类型 0:用户;1:机构',
    `owner_id`               bigint(20) NOT NULL COMMENT '拥有者id',
    `contract_token_id`      int(11) DEFAULT NULL COMMENT '合约代币编号',
    `token_id`               varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '代币编号',
    `sell_type`              varchar(1) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '售卖方式  dict={"0":"一口价","1":"竞拍","2":"时刻包"}',
    `sell_id`                bigint(20) DEFAULT NULL,
    `sell_price`             decimal(18, 8)                                               DEFAULT NULL COMMENT '当时出售价格',
    `buy_channel`            varchar(32) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '购买渠道(0=一级市场 1=二级市场)',
    `buy_price`              decimal(18, 8)                                               DEFAULT NULL COMMENT '购买价格',
    `buy_datetime`           datetime                                                     DEFAULT NULL COMMENT '购买时间',
    `lock_time`              int(11) DEFAULT '0' COMMENT '锁仓时间(小时)',
    `lock_datetime`          datetime                                                     DEFAULT NULL COMMENT '锁仓时间',
    `status`                 varchar(2) COLLATE utf8mb4_bin   NOT NULL COMMENT '状态 dict={"0":"待提取","1":"已提取","2":"售卖中"}',
    `create_datetime`        datetime                                                     DEFAULT NULL COMMENT '创建时间',
    `update_time`            bigint(32) DEFAULT NULL COMMENT '更新时间戳',
    `ref_id`                 bigint(11) DEFAULT NULL,
    `ref_type`               varchar(32) COLLATE utf8mb4_bin                              DEFAULT NULL,
    `challenge_number`       int(11) DEFAULT '0' COMMENT '赋能次数',
    `source`                 varchar(4) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '来源 0:版权,1:衍生,2盲盒,3:抽签,4:diy,5:幸运抽奖',
    `transform_limit_time`   int(11) DEFAULT '0' COMMENT '转赠限制(小时)',
    `second_auction_time`    datetime                                                     DEFAULT NULL COMMENT '二次送拍时间',
    `remark`                 varchar(255) COLLATE utf8mb4_bin                             DEFAULT NULL,
    `integral_flag`          varchar(4) COLLATE utf8mb4_bin                               DEFAULT '0' COMMENT '积分兑换标识 0:未兑换,1:已兑换',
    `integral_price`         decimal(18, 2)                                               DEFAULT NULL,
    `yao_flag`               varchar(4) COLLATE utf8mb4_bin                               DEFAULT '0' COMMENT '爻兑换标识 0:未兑换,1:已兑换',
    `unlock_datetime`        datetime                                                     DEFAULT NULL COMMENT '解锁时间',
    `serial_no`              varchar(32) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '序列号',
    `exchange_xmeta`         char(4) COLLATE utf8mb4_bin                                  DEFAULT '0' COMMENT '可兑换藏品XMeta上架标识 dict={"0":"不可兑换","1":"可兑换","2":"已兑换"}',
    PRIMARY KEY (`id`),
    UNIQUE KEY `collection_detail_hash_index` (`collection_detail_hash`),
    UNIQUE KEY `index` (`three_channel_type`,`three_channel_id`),
    KEY                      `index_1` (`collection_id`),
    KEY                      `idx_collectionid_ownerid_status` (`collection_id`,`owner_id`,`status`,`owner_type`),
    KEY                      `index_query1` (`owner_type`,`owner_id`,`status`),
    KEY                      `nft_collection_detail_idx1` (`owner_id`),
    KEY                      `idx_collectionid_status` (`collection_id`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=192 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='藏品型号';

CREATE TABLE `nft_collection_detail_exchange_card`
(
    `id`                           bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
    `collection_id`                bigint(20) NOT NULL COMMENT '作品id',
    `collection_detail_id`         bigint(20) NOT NULL COMMENT '藏品型号id',
    `publish_collection_id`        bigint(20) NOT NULL COMMENT '上架作品id',
    `publish_collection_detail_id` bigint(20) NOT NULL COMMENT '上架藏品型号id',
    `status`                       varchar(4) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '状态 dict={"0":"待使用","1":"已使用"}',
    `create_datetime`              datetime                         DEFAULT NULL COMMENT '创建时间',
    `update_datetime`              datetime                         DEFAULT NULL COMMENT '更新时间',
    `remark`                       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unqiue1` (`collection_detail_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='藏品型号兑换Xmeta上架卡';

CREATE TABLE `nft_collection_detail_modify_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4)   NOT NULL COMMENT '类型(0=修改藏品名称)',
    `collection_id`   bigint(20) NOT NULL COMMENT '作品id',
    `before_value`    varchar(256) NOT NULL COMMENT '之前值',
    `modify_value`    varchar(256) NOT NULL COMMENT '修改值',
    `status`          varchar(32)  NOT NULL COMMENT '状态(0=待审核 1=审核通过 2=审核不通过)',
    `apply_user`      bigint(20) NOT NULL COMMENT '申请人',
    `apply_datetime`  datetime     DEFAULT NULL COMMENT '申请时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品名称修改记录';

CREATE TABLE `nft_collection_detail_record`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品型号id',
    `user_type`            varchar(4) COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '用户类型 0:用户;1:机构',
    `user_id`              bigint(32) NOT NULL COMMENT '用户编号',
    `owner_type`           varchar(4) COLLATE utf8mb4_bin NOT NULL DEFAULT '0' COMMENT '拥有者类型 0:用户;1:机构',
    `owner_id`             bigint(32) NOT NULL COMMENT '拥有者id',
    `price`                decimal(10, 2) unsigned DEFAULT '0.00' COMMENT '成交价格',
    `trade_type`           varchar(4) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '售卖方式  dict={"0":"一口价","1":"竞拍","2":"时刻包"}',
    `trade_id`             bigint(20) DEFAULT NULL COMMENT '交易id',
    `status`               varchar(1) COLLATE utf8mb4_bin          DEFAULT NULL COMMENT '现在拥有状态 dict={"0":"否","1":"是",} ',
    `start_datetime`       datetime                                DEFAULT NULL COMMENT '开售时间',
    `sell_datetime`        datetime                                DEFAULT NULL COMMENT '售出时间',
    `create_datetime`      datetime                                DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_collectiondetailid` (`collection_detail_id`),
    KEY                    `idx_userid_ownertype` (`user_id`,`owner_type`),
    KEY                    `trade_type` (`trade_type`)
) ENGINE=InnoDB AUTO_INCREMENT=205 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='藏品型号流转记录';

CREATE TABLE `nft_collection_detail_transfer_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `transfer_record_id`   bigint(20) DEFAULT NULL COMMENT '转赠订单序号',
    `create_datetime`      datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY                    `idx_transferrecordid_collectiondetailid` (`transfer_record_id`,`collection_detail_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='转赠订单藏品';

CREATE TABLE `nft_collection_detail_transfer_record`
(
    `id`                 bigint(20) NOT NULL COMMENT '序号',
    `from_user_id`       bigint(20) NOT NULL COMMENT '来源用户序号',
    `to_user_id`         bigint(20) DEFAULT NULL COMMENT '新用户序号',
    `fee`                decimal(18, 2) DEFAULT NULL COMMENT '转赠手续费',
    `quantity`           int(11) DEFAULT NULL COMMENT '数量',
    `type`               varchar(4)     DEFAULT '0' COMMENT '转赠类型 0:C端转赠,1:管理端操作',
    `pay_type`           varchar(32)    DEFAULT NULL COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`     varchar(32)    DEFAULT NULL COMMENT '支付订单号',
    `pay_status`         varchar(32) NOT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`       datetime       DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount` decimal(18, 2) DEFAULT '0.00' COMMENT '余额支付金额',
    `pay_cash_amount`    decimal(18, 2) DEFAULT '0.00' COMMENT '现金支付金额',
    `create_datetime`    datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY                  `idx_touserid_paystatus` (`to_user_id`,`pay_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='时刻型号转赠记录';

CREATE TABLE `nft_collection_period`
(
    `id`                         bigint(20) NOT NULL AUTO_INCREMENT,
    `pit_id`                     bigint(20) DEFAULT NULL COMMENT '坑位序号',
    `category`                   varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '种类(0=版权 1=衍生品)',
    `plate_category`             varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '板块类别',
    `name`                       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `introduce`                  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `cover_file_url`             varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '封面图地址',
    `plan_pic`                   varchar(255) COLLATE utf8mb4_bin                              DEFAULT NULL COMMENT '计划预览图',
    `file_type`                  varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '文件类型 0:图片,1:音频,2:视频,3:3d文件 4:混合',
    `tags`                       varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  DEFAULT NULL,
    `content`                    varchar(1024) COLLATE utf8mb4_bin                             DEFAULT NULL COMMENT '作品故事',
    `author_ids`                 bigint(20) DEFAULT NULL COMMENT '发行方',
    `start_sell_date`            datetime                       NOT NULL COMMENT '开始发售时间',
    `end_sell_date`              datetime                                                      DEFAULT NULL COMMENT '结束发售时间',
    `total_quantity`             int(10) NOT NULL DEFAULT '1' COMMENT '总数量',
    `remain_quantity`            int(10) NOT NULL DEFAULT '1' COMMENT '剩余数量',
    `price`                      decimal(10, 2)                 NOT NULL                       DEFAULT '0.00' COMMENT '价格',
    `buy_max`                    int(11) NOT NULL DEFAULT '0' COMMENT '单人最大购买份数',
    `order_no`                   int(11) DEFAULT NULL COMMENT '序号',
    `status`                     varchar(2) COLLATE utf8mb4_bin NOT NULL                       DEFAULT '0' COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `sold_status`                varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '售卖状态 dict={"0":"正常","1":"已售罄"}',
    `start_status`               varchar(4) COLLATE utf8mb4_bin                                DEFAULT '0' COMMENT '开始状态 dict={"0":"待开始","1":"售卖中","2":"已结束"}',
    `lock_time`                  int(11) DEFAULT '0' COMMENT '锁仓时间(小时)',
    `advance_mins`               int(11) DEFAULT '0' COMMENT '提前抢购分钟数',
    `right_content`              longtext COLLATE utf8mb4_bin COMMENT '权益内容',
    `right_type`                 varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '权益类型0=文字 1=图片 2=暂无权益',
    `updater`                    bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`               varchar(32) COLLATE utf8mb4_bin                               DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`            datetime                                                      DEFAULT NULL COMMENT '更新时间',
    `priority_add_quantity_flag` char(1) COLLATE utf8mb4_bin                                   DEFAULT NULL COMMENT '优先购计算数量标志(1=叠加 0=不叠加)',
    `priority_number`            int(11) DEFAULT '0' COMMENT '优先购买数量',
    `priority_draw_param`        int(11) DEFAULT '1' COMMENT '优先购抽签参数',
    `draw_param`                 int(11) DEFAULT '1' COMMENT '抽签参数',
    `draw_note`                  longtext COLLATE utf8mb4_bin COMMENT '当期玩法说明',
    `transform_limit_time`       int(11) DEFAULT '0' COMMENT '转赠限制(小时)',
    `word_flag`                  char(1) COLLATE utf8mb4_bin                                   DEFAULT '0' COMMENT '口令标志(0=不是口令 1=是口令)',
    `channel_id`                 bigint(20) DEFAULT NULL COMMENT '分发渠道',
    `create_type`                varchar(4) COLLATE utf8mb4_bin                                DEFAULT NULL COMMENT '创建类型 0:平台代创,1:发行方创建',
    `meta_release_flag`          varchar(4) COLLATE utf8mb4_bin NOT NULL                       DEFAULT '0' COMMENT '是否在元宇宙同步发售 0=否,1=是',
    `collection_number`          int(11) DEFAULT '1' COMMENT '期数藏品数量',
    PRIMARY KEY (`id`),
    KEY                          `index` (`author_ids`),
    KEY                          `category_index` (`category`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='作品期数';

CREATE TABLE `nft_collection_period_extra_buy_chance`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `ref_type`        varchar(4) NOT NULL COMMENT '关联类型 dict={"0":"拉新活动"}',
    `ref_id`          bigint(20) NOT NULL COMMENT '关联序号',
    `period_id`       bigint(20) NOT NULL COMMENT '期数序号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户序号',
    `number`          int(11) NOT NULL COMMENT '次数',
    `create_datetime` datetime   NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='期数额外购买机会';

CREATE TABLE `nft_collection_period_join_record`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `period_id`          bigint(20) DEFAULT NULL COMMENT '期数序号',
    `order_no`           bigint(20) DEFAULT NULL COMMENT '顺序',
    `user_id`            bigint(20) DEFAULT NULL COMMENT '参与用户',
    `collection_id`      bigint(20) DEFAULT NULL COMMENT '中签作品',
    `price`              decimal(18, 2)          DEFAULT NULL COMMENT '参与金额',
    `discount_price`     decimal(18, 2)          DEFAULT NULL COMMENT '折扣价',
    `status`             varchar(4)              DEFAULT NULL COMMENT '状态 dict={"0":"已报名","1":"已中签","2":"未中签"}',
    `pay_amount`         decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
    `pay_type`           varchar(32)             DEFAULT NULL COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`     varchar(32)             DEFAULT NULL COMMENT '支付订单号',
    `pay_status`         varchar(32)             DEFAULT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`       datetime                DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount` decimal(18, 2)          DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`    decimal(18, 2)          DEFAULT NULL COMMENT '现金支付金额',
    `commission_amount`  decimal(18, 2)          DEFAULT '0.00' COMMENT '平台佣金收入',
    `create_datetime`    datetime                DEFAULT NULL COMMENT '参与时间',
    `update_datetime`    datetime                DEFAULT NULL COMMENT '公布时间',
    `join_time`          bigint(32) DEFAULT NULL COMMENT '报名时间戳',
    `is_deal`            varchar(4)              DEFAULT NULL COMMENT '是否需要处理 0:否,1:是',
    `priority_flag`      varchar(4)              DEFAULT '0' COMMENT '优先购标识 0否,1:是',
    `white_flag`         int(11) DEFAULT '0' COMMENT '白名单分组标识 0=非白名单 1=白名单',
    `join_millis`        bigint(20) DEFAULT '0' COMMENT '报名时间数字 ',
    `group`              int(11) DEFAULT NULL COMMENT '普通分组',
    `priority_group`     int(11) DEFAULT NULL COMMENT '优先购分组',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx3` (`period_id`,`order_no`),
    KEY                  `idx2` (`period_id`,`user_id`,`pay_status`),
    KEY                  `idx1` (`period_id`,`status`,`priority_flag`,`join_time`),
    KEY                  `idx_periodid_paystatus_orderno_status` (`period_id`,`pay_status`,`order_no`,`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期数抽签区报名记录';

CREATE TABLE `nft_collection_period_join_record_history`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `period_id`          bigint(20) DEFAULT NULL COMMENT '期数序号',
    `order_no`           bigint(20) DEFAULT NULL COMMENT '顺序',
    `user_id`            bigint(20) DEFAULT NULL COMMENT '参与用户',
    `collection_id`      bigint(20) DEFAULT NULL COMMENT '中签作品',
    `price`              decimal(18, 2)          DEFAULT NULL COMMENT '参与金额',
    `discount_price`     decimal(18, 2)          DEFAULT NULL COMMENT '折扣价',
    `status`             varchar(4)              DEFAULT NULL COMMENT '状态 dict={"0":"已报名","1":"已中签","2":"未中签"}',
    `pay_amount`         decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '支付金额',
    `pay_type`           varchar(32)             DEFAULT NULL COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`     varchar(32)             DEFAULT NULL COMMENT '支付订单号',
    `pay_status`         varchar(32)             DEFAULT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`       datetime                DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount` decimal(18, 2)          DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`    decimal(18, 2)          DEFAULT NULL COMMENT '现金支付金额',
    `commission_amount`  decimal(18, 2)          DEFAULT '0.00' COMMENT '平台佣金收入',
    `create_datetime`    datetime                DEFAULT NULL COMMENT '参与时间',
    `update_datetime`    datetime                DEFAULT NULL COMMENT '公布时间',
    `join_time`          bigint(32) DEFAULT NULL COMMENT '报名时间戳',
    `is_deal`            varchar(4)              DEFAULT NULL COMMENT '是否需要处理 0:否,1:是',
    `priority_flag`      varchar(4)              DEFAULT '0' COMMENT '优先购标识 0否,1:是',
    `white_flag`         int(11) DEFAULT '0' COMMENT '白名单分组标识 0=非白名单 1=白名单',
    `join_millis`        bigint(20) DEFAULT '0' COMMENT '报名时间数字 ',
    `group`              int(11) DEFAULT NULL COMMENT '普通分组',
    `priority_group`     int(11) DEFAULT NULL COMMENT '优先购分组',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx3` (`period_id`,`order_no`),
    KEY                  `idx2` (`period_id`,`user_id`,`pay_status`),
    KEY                  `idx1` (`period_id`,`status`,`priority_flag`,`join_time`),
    KEY                  `idx_status_price` (`status`,`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期数抽签区报名记录';

CREATE TABLE `nft_collection_period_order_queue`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `uuid`          varchar(64) NOT NULL COMMENT 'UUID',
    `collection_id` bigint(20) unsigned NOT NULL COMMENT '藏品ID',
    `period_id`     bigint(20) unsigned NOT NULL COMMENT '期数ID',
    `user_id`       bigint(20) unsigned DEFAULT NULL COMMENT '用户ID',
    `order_id`      bigint(20) unsigned DEFAULT NULL COMMENT '订单ID',
    `order_time`    datetime             DEFAULT NULL COMMENT '下单时间',
    `status`        tinyint(2) NOT NULL DEFAULT '0' COMMENT '状态：0.未下单，1.已下单，2.已完成，3.已作废',
    `gmt_create`    datetime    NOT NULL COMMENT '创建时间',
    `gmt_modified`  datetime    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `idx_uuid_unique` (`uuid`),
    KEY             `idx_collection_period` (`collection_id`,`period_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='藏品-期数采购队列表';

CREATE TABLE `nft_collection_period_priority_buy`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id`     bigint(20) NOT NULL COMMENT '期数id',
    `collection_id` bigint(20) NOT NULL COMMENT '作品编号',
    `quantity`      int(11) DEFAULT '1' COMMENT '数量',
    `advance_mins`  int(11) DEFAULT NULL COMMENT '优先抢购分钟数',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='作品期数优先购买权';

CREATE TABLE `nft_collection_period_relation`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `category`        varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '分类(0=可售卖 1=隐藏款)',
    `period_id`       bigint(20) NOT NULL COMMENT '期数id',
    `collection_id`   bigint(20) NOT NULL COMMENT '产品编号',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    `price`           decimal(10, 2) NOT NULL         DEFAULT '0.00' COMMENT '价格',
    `total_quantity`  int(11) DEFAULT NULL COMMENT '总数量',
    `remain_quantity` int(11) DEFAULT NULL COMMENT '剩余数量',
    PRIMARY KEY (`id`),
    KEY               `category_index` (`period_id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='藏品期数关联表';

CREATE TABLE `nft_collection_period_send`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id`       bigint(20) DEFAULT NULL COMMENT '期数序号',
    `send_flag`       varchar(4) DEFAULT '1' COMMENT '是否推送',
    `create_datetime` datetime   DEFAULT NULL COMMENT '创建时间',
    `send_datetime`   datetime   DEFAULT NULL COMMENT '推送时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期数推送表';

CREATE TABLE `nft_collection_period_white_join`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id`       bigint(20) DEFAULT NULL COMMENT '期数id',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户id',
    `mobile`          varchar(32) DEFAULT NULL COMMENT '手机号',
    `number`          int(11) DEFAULT '1' COMMENT '报名次数',
    `create_datetime` datetime    DEFAULT NULL COMMENT '添加时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品期数白名单报名';

CREATE TABLE `nft_collection_right`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `collection_id` bigint(20) DEFAULT NULL COMMENT '作品id',
    `type`          varchar(4) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '权益类型(0=免提现手续费 1=免费抽盲盒)',
    `ref_id`        bigint(20) DEFAULT NULL COMMENT '权益关联id(类型=盲盒时的系列id)',
    `cycle_type`    int(11) DEFAULT '0' COMMENT '权益周期类型(0=永久 1=按年 2=按月 3=按日)',
    `cycle_time`    int(11) DEFAULT '0' COMMENT '权益周期次数(-1=无限次)',
    `status`        varchar(4) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态(0=可用 1=作废)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='作品权益表';

CREATE TABLE `nft_collection_right_company`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `plate_category`  varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '板块类别',
    `company_id`      bigint(20) NOT NULL COMMENT '发行方',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '权益序号',
    `ref_type`        varchar(4)                                            DEFAULT NULL COMMENT '关联类型',
    `number`          int(11) DEFAULT '0' COMMENT '使用次数',
    `status`          varchar(4)                                            DEFAULT '0' COMMENT '状态 dict={"0":"进行中","1":"已完成"}',
    `updater`         bigint(11) DEFAULT '0' COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '更新人名',
    `update_datetime` datetime                                              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`plate_category`,`company_id`,`ref_id`)
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='权益所属发行方';

CREATE TABLE `nft_collection_right_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `right_id`          bigint(20) DEFAULT NULL COMMENT '权益序号',
    `right_company_id`  bigint(20) DEFAULT NULL COMMENT '权益发行方序号',
    `collection_id`     bigint(20) DEFAULT NULL COMMENT '权益藏品',
    `drop_type`         varchar(4)     DEFAULT '0' COMMENT '空投类型 0:期数,1:手动',
    `ref_type`          varchar(4)     DEFAULT NULL COMMENT '权益类型',
    `ref_id`            bigint(20) DEFAULT NULL COMMENT '关联序号',
    `ref_collection_id` bigint(20) DEFAULT NULL COMMENT '关联藏品',
    `advance_mins`      int(11) DEFAULT '0' COMMENT '优先购时间',
    `discount_rate`     decimal(18, 2) DEFAULT '0.00' COMMENT '折扣比例,1不打折',
    `drop_number`       int(11) DEFAULT '0' COMMENT '单次空投份数',
    `create_datetime`   datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`collection_id`,`ref_type`,`ref_id`,`drop_type`)
) ENGINE=InnoDB AUTO_INCREMENT=19 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='权益使用明细';

CREATE TABLE `nft_collection_rights_detail`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT,
    `collection_id`         bigint(20) DEFAULT NULL COMMENT '作品id',
    `type`                  varchar(4) COLLATE utf8mb4_bin     DEFAULT NULL COMMENT '类型(0=期数权益 1=元宇宙权益 2=权益中心 3=社区权益 4=其他权益)',
    `ticket_type`           varchar(4) CHARACTER SET utf8mb4   DEFAULT NULL COMMENT '元宇宙门票类型',
    `create_type`           varchar(255) CHARACTER SET utf8mb4 DEFAULT '0' COMMENT '创建类型 0:正式,1:临时',
    `name`                  varchar(32) COLLATE utf8mb4_bin    DEFAULT NULL COMMENT '权益名称',
    `content`               varchar(2048) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '权益内容',
    `number_flag`           varchar(4) COLLATE utf8mb4_bin     DEFAULT '0' COMMENT '数量标识 dict={"0":"固定数量","1":"无限次"}',
    `total_number`          int(11) DEFAULT '0' COMMENT '总次数',
    `remain_number`         int(11) DEFAULT '0' COMMENT '剩余次数',
    `advance_mins`          int(11) DEFAULT '0' COMMENT '优先购时间',
    `discount_rate`         decimal(18, 2)                     DEFAULT '0.00' COMMENT '折扣比例,1不打折',
    `drop_number`           int(11) DEFAULT '0' COMMENT '单次空投份数',
    `ticket_time`           int(11) DEFAULT '0' COMMENT '时效(分钟)',
    `ticket_start_datetime` datetime                           DEFAULT NULL COMMENT '开始时间',
    `ticket_end_datetime`   datetime                           DEFAULT NULL COMMENT '结束时间',
    `updater`               bigint(11) DEFAULT '0' COMMENT '更新人',
    `updater_name`          varchar(32) COLLATE utf8mb4_bin    DEFAULT '0' COMMENT '更新人名',
    `update_datetime`       datetime                           DEFAULT NULL COMMENT '更新时间',
    `order_no`              int(11) DEFAULT '0' COMMENT '顺序',
    `send_flag`             varchar(4) COLLATE utf8mb4_bin     DEFAULT '0' COMMENT '极光推送标识 0:无推送,1:推送',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=71 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='作品权益明细表';

CREATE TABLE `nft_collection_sale_demand`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_id`     bigint(20) NOT NULL COMMENT '作品序号',
    `chain`             varchar(255)                                           DEFAULT NULL COMMENT '链路',
    `buy_type`          varchar(4)                                             DEFAULT NULL COMMENT '期望购买方式 dict={"0":"普通购买","1":"口令购买"}',
    `price`             decimal(18, 2)                                         DEFAULT NULL COMMENT '预发售价格',
    `quantity`          int(11) DEFAULT NULL COMMENT '预发售数量',
    `content`           varchar(1024)                                          DEFAULT NULL COMMENT '赋能玩法',
    `other_object_flag` varchar(4)                                             DEFAULT NULL COMMENT '是否有实物衍生品 dict={"0":"无","1":"有"}',
    `expect_date_start` datetime                                               DEFAULT NULL COMMENT '预计排期时间开始',
    `expect_date_end`   datetime                                               DEFAULT NULL COMMENT '预计排期时间结束',
    `mandate_url`       text COMMENT 'IP完整授权链路文件',
    `creater`           bigint(32) DEFAULT NULL COMMENT '创建人',
    `creater_name`      varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '创建人名称',
    `create_datetime`   datetime                                               DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`collection_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='数字藏品发售需求';

CREATE TABLE `nft_collection_transfer_channel_record`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_id`    bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_hash`  varchar(64)                    DEFAULT NULL COMMENT '作品hash',
    `channel_biz_code` varchar(32)                    DEFAULT NULL COMMENT '渠道编号',
    `status`           varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"已删除","1":"已转出"}',
    `content`          longtext COMMENT '入参',
    `creater`          bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`     varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名称',
    `create_datetime`  datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`          bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`     varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`  datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品转入渠道记录';

CREATE TABLE `nft_collection_u3d_props`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_id`   bigint(20) DEFAULT NULL COMMENT '作品id',
    `props_id`        bigint(20) DEFAULT NULL COMMENT '道具id',
    `updater`         varchar(32)  DEFAULT NULL COMMENT '最近修改人',
    `update_datetime` datetime     DEFAULT NULL COMMENT '最近修改时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_01` (`collection_id`,`props_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='藏品对应的道具(一个藏品对应多个道具)';

CREATE TABLE `nft_company`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT,
    `name`                     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `short_name`               varchar(64)                                                    DEFAULT NULL COMMENT '简称',
    `type`                     varchar(4)                                                     DEFAULT NULL COMMENT '类型 dict={"0":"企业","1":"个人"}',
    `logo`                     varchar(255) NOT NULL COMMENT 'logo',
    `cover_file_url`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin         DEFAULT NULL COMMENT '封面文件地址',
    `introduce`                varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `introduce_pdf`            varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '发行方介绍文件',
    `plat_divide_rate`         decimal(8, 2)                                                  DEFAULT NULL COMMENT '平台分成',
    `re_mobile`                varchar(32)                                                    DEFAULT NULL COMMENT '联系人手机号',
    `re_real_name`             varchar(32)                                                    DEFAULT NULL COMMENT '联系人姓名',
    `licence_no`               varchar(64)                                                    DEFAULT NULL COMMENT '营业执照号',
    `licence_url`              varchar(255)                                                   DEFAULT NULL COMMENT '营业执照url',
    `divide_flag`              varchar(32)                                                    DEFAULT NULL COMMENT '分账标志',
    `legal_licence_type`       varchar(32)                                                    DEFAULT NULL COMMENT '法人证件类型',
    `legal_licence_no`         varchar(64)                                                    DEFAULT NULL COMMENT '法人证件号',
    `legal_licence_front_url`  varchar(255)                                                   DEFAULT NULL,
    `legal_licence_back_url`   varchar(255)                                                   DEFAULT NULL,
    `legal_real_name`          varchar(32)                                                    DEFAULT NULL,
    `legal_mobile`             varchar(32)                                                    DEFAULT NULL,
    `open_account_licence_no`  varchar(64)                                                    DEFAULT NULL,
    `open_account_licence_url` varchar(255)                                                   DEFAULT NULL,
    `settle_card_type`         varchar(32)                                                    DEFAULT '0' COMMENT '结算卡类型(0=对公 1=对私)',
    `settle_card_no`           varchar(32)                                                    DEFAULT NULL,
    `settle_bank_code`         varchar(32)                                                    DEFAULT NULL,
    `province`                 varchar(32)                                                    DEFAULT NULL,
    `city`                     varchar(32)                                                    DEFAULT NULL,
    `district`                 varchar(32)                                                    DEFAULT NULL,
    `address`                  varchar(255)                                                   DEFAULT NULL,
    `contact_name`             varchar(32)                                                    DEFAULT NULL,
    `contact_mobile`           varchar(32)                                                    DEFAULT NULL,
    `contact_licence_no`       varchar(32)                                                    DEFAULT NULL,
    `contact_email`            varchar(32)                                                    DEFAULT NULL,
    `service_phone`            varchar(32)                                                    DEFAULT NULL,
    `request_no`               varchar(32)                                                    DEFAULT NULL,
    `merchant_no`              varchar(32)                                                    DEFAULT NULL,
    `status`                   varchar(32)                                                    DEFAULT NULL COMMENT '状态(0=待上架,1=易宝分账申请中,2=已上架,3=已下架)',
    `divide_status`            varchar(32)                                                    DEFAULT NULL COMMENT '分帐状态(0=待申请,1=申请中,2=申请成功,3=申请失败)',
    `location`                 varchar(4)                                                     DEFAULT '0',
    `order_no`                 int(11) DEFAULT '0' COMMENT '序号',
    `updater`                  bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_datetime`          datetime                                                       DEFAULT NULL COMMENT '更新时间',
    `remark`                   varchar(255)                                                   DEFAULT NULL COMMENT '备注',
    `bond_price`               decimal(18, 2)                                                 DEFAULT '0.00' COMMENT '入驻保证金',
    `is_look`                  varchar(4)                                                     DEFAULT '0' COMMENT '入驻成功是否已查看 0:否,1:是',
    `main_user`                bigint(20) DEFAULT '0' COMMENT '主用户',
    `plat_flag`                varchar(4)                                                     DEFAULT '0' COMMENT '平台发行方标识 0:否,1:是',
    `independence_plat_flag`   varchar(4)                                                     DEFAULT '0' COMMENT '独立平台标识 0:非独立,1:独立',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=632723725393076225 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='机构';

CREATE TABLE `nft_company_apply_settle_record`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_id`       bigint(20) DEFAULT NULL COMMENT '发行方序号',
    `account_id`       bigint(20) DEFAULT NULL COMMENT '账户序号',
    `amount`           decimal(18, 2)                 DEFAULT NULL COMMENT '结算金额',
    `invoice_pdf`      varchar(1024)                  DEFAULT NULL COMMENT '发票',
    `status`           varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"待结算","1":"已到账","2":"结算失败"}',
    `settle_bank_code` varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '结算银行',
    `settle_card_no`   varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '结算银行卡号',
    `creater`          bigint(20) DEFAULT NULL COMMENT '申请人',
    `creater_name`     varchar(64)                    DEFAULT NULL COMMENT '申请人',
    `create_datetime`  datetime                       DEFAULT NULL COMMENT '申请时间',
    `updater`          bigint(20) DEFAULT NULL COMMENT '审核人',
    `updater_name`     varchar(64)                    DEFAULT NULL COMMENT '审核人',
    `update_datetime`  datetime                       DEFAULT NULL COMMENT '审核时间',
    `remark`           varchar(255)                   DEFAULT NULL COMMENT '失败原因',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=298 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='发行方申请结算记录';

CREATE TABLE `nft_company_entity`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `company_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '机构id',
    `status`          varchar(4)                                                   DEFAULT '0' COMMENT '状态 0:待审核,1:审核中,2:审核通过,3:审核失败',
    `send_quantity`   int(20) NOT NULL DEFAULT '0' COMMENT '已发放数量',
    `name`            varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `content`         varchar(1000)                                                DEFAULT NULL COMMENT '实物内容',
    `image_url`       varchar(255)                                                 DEFAULT NULL COMMENT '实物图片',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)                                                  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `auditr`          bigint(20) DEFAULT NULL COMMENT '审核人',
    `auditr_name`     varchar(32)                                                  DEFAULT NULL COMMENT '审核人名称',
    `audit_datetime`  datetime                                                     DEFAULT NULL COMMENT '审核时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=17 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='实物';

CREATE TABLE `nft_company_modify_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_id`      bigint(20) NOT NULL COMMENT '发行方序号',
    `status`          varchar(4)  DEFAULT NULL COMMENT '状态 dict={"0":"待审核","1":"审核成功","2":"审核失败"}',
    `content`         text COMMENT '审核内容',
    `create_datetime` datetime    DEFAULT NULL COMMENT '申请时间',
    `updater`         bigint(20) DEFAULT NULL COMMENT '审核人',
    `updater_name`    varchar(32) DEFAULT NULL COMMENT '审核人名称',
    `update_datetime` datetime    DEFAULT NULL COMMENT '审核时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='发行方信息修改记录';

CREATE TABLE `nft_contract`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `contract_address`   varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '合约地址',
    `protocol`           varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '协议',
    `chain`              varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '链信息',
    `logo`               varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '图标',
    `token_general_type` varchar(4) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT 'Token产生类型(0=自增，1=uuid)',
    `status`             varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '状态0=待上架 1=已上架',
    `updater`            bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`       varchar(32) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`    datetime                         DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='合约信息';

CREATE TABLE `nft_contract_token`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `user_id`              bigint(20) DEFAULT NULL COMMENT '用户编号',
    `user_type`            varchar(4) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '用户类型 0:用户,1:机构',
    `type`                 varchar(1) COLLATE utf8mb4_bin   DEFAULT '0' COMMENT '类型 dict={"0":"自创","1":"外来"}',
    `contract_id`          bigint(32) NOT NULL COMMENT '合约编号',
    `contract_address`     varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '合约地址',
    `token_id`             varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '代币编号',
    `address`              varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT '归属地址',
    `status`               char(2) COLLATE utf8mb4_bin      DEFAULT NULL COMMENT '状态 0;待分配  1:已分配 2:已导出',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '时刻id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '时刻详情id',
    `ref_id`               bigint(32) DEFAULT NULL COMMENT '业务id',
    `ref_type`             varchar(32) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '业务类型',
    `create_datetime`      datetime                         DEFAULT NULL COMMENT '创建时间',
    `updater`              varchar(32) COLLATE utf8mb4_bin  DEFAULT NULL COMMENT '更新人',
    `update_datetime`      datetime                         DEFAULT NULL COMMENT '更新时间',
    `remark`               varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique` (`token_id`,`contract_id`),
    KEY                    `index1_query` (`collection_detail_id`,`status`),
    KEY                    `index4_query` (`collection_id`),
    KEY                    `index2_query` (`type`,`status`),
    KEY                    `index3_query` (`ref_type`,`collection_id`,`status`,`type`),
    KEY                    `index_query` (`type`,`status`,`collection_id`)
) ENGINE=InnoDB AUTO_INCREMENT=987090 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='代币归属信息';

CREATE TABLE `nft_contract_token_export`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `user_id`            bigint(20) NOT NULL COMMENT '用户编号',
    `from_address`       varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '提币地址',
    `to_address`         varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '转币地址',
    `quantity`           int(11) DEFAULT NULL COMMENT '数量',
    `status`             varchar(1) COLLATE utf8mb4_bin   DEFAULT NULL COMMENT '状态 0 ；待审核 1:审核不通过 2；审核通过',
    `remark`             varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '备注',
    `apply_note`         varchar(255) CHARACTER SET utf8  DEFAULT NULL COMMENT '申请说明',
    `apply_datetime`     datetime                         DEFAULT NULL COMMENT '申请时间',
    `approve_user`       bigint(20) DEFAULT NULL COMMENT '审批人',
    `approve_note`       varchar(255) CHARACTER SET utf8  DEFAULT NULL COMMENT '审批说明',
    `approve_datetime`   datetime                         DEFAULT NULL COMMENT '审批时间',
    `back_user`          bigint(20) DEFAULT NULL COMMENT '打回人',
    `back_note`          varchar(255) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '打回说明',
    `back_datetime`      datetime                         DEFAULT NULL COMMENT '打回时间',
    `pay_amount`         decimal(10, 2)                   DEFAULT '0.00' COMMENT '支付金额',
    `pay_type`           varchar(32) COLLATE utf8mb4_bin  DEFAULT '0.00' COMMENT '支付方式 dict={"0":"余额支付","1":"支付宝","2":"微信"}',
    `pay_order_code`     varchar(32) COLLATE utf8mb4_bin  DEFAULT '0.00' COMMENT '支付订单号',
    `pay_status`         varchar(32) COLLATE utf8mb4_bin  DEFAULT '0.00' COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`       datetime                         DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount` decimal(18, 2)                   DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`    decimal(18, 2)                   DEFAULT NULL COMMENT '现金支付金额',
    `commission_amount`  decimal(18, 2)                   DEFAULT '0.00' COMMENT '平台佣金收入',
    `broadcast_user`     bigint(20) DEFAULT NULL,
    `broadcast_note`     varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
    `broadcast_datetime` datetime                         DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='代币导出记录';

CREATE TABLE `nft_contract_token_export_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `export_id`            bigint(20) DEFAULT NULL COMMENT '导出id',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='代币导出明细';

CREATE TABLE `nft_contract_token_in_pool`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `token_id`      varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '代币编号',
    `collection_id` bigint(20) DEFAULT NULL COMMENT '时刻id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=313 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='代币池信息';

CREATE TABLE `nft_contract_token_in_pool15`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `token_id`      varchar(128) COLLATE utf8mb4_bin NOT NULL COMMENT '代币编号',
    `collection_id` bigint(20) DEFAULT NULL COMMENT '时刻id',
    `quantity`      int(11) DEFAULT NULL COMMENT '数量',
    `status`        varchar(4) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '状态(0=待铸币，1=已铸币)',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1174 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='代币池信息';

CREATE TABLE `nft_drop_order`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`                     varchar(4) DEFAULT NULL COMMENT '空投类型 dict={"0":"按用户空投","1":"按藏品空投"}',
    `collection_id`            bigint(20) NOT NULL COMMENT '空投藏品',
    `ref_collection_id`        bigint(20) DEFAULT NULL COMMENT '关联藏品',
    `drop_number`              int(11) DEFAULT '0' COMMENT '单次空投份数',
    `total_drop_number`        int(255) DEFAULT '0' COMMENT '空投总份数',
    `drop_user_number`         int(11) DEFAULT '0' COMMENT '空投总人数',
    `success_drop_user_number` int(11) DEFAULT '0' COMMENT '空投成功人数',
    `failure_drop_user_number` int(11) DEFAULT '0' COMMENT '空投失败人数',
    `creater`                  bigint(20) NOT NULL COMMENT '操作人',
    `creater_kind`             varchar(4)  NOT NULL COMMENT '操作人类型',
    `creatr_name`              varchar(32) NOT NULL COMMENT '操作人名称',
    `create_datetime`          datetime    NOT NULL COMMENT '操作时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=668948183963148289 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='空投记录';

CREATE TABLE `nft_drop_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `order_id`        bigint(20) DEFAULT NULL COMMENT '订单序号',
    `collection_id`   bigint(20) NOT NULL COMMENT '空投藏品',
    `type`            varchar(4)   DEFAULT NULL COMMENT '空投类型 0:按用户空投,1:按藏品空投',
    `status`          varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"失败","1":"成功"}',
    `mobile`          varchar(64)  DEFAULT NULL COMMENT '手机号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户',
    `remark`          varchar(255) DEFAULT NULL COMMENT '失败原因',
    `creater`         bigint(20) NOT NULL COMMENT '操作人',
    `creater_kind`    varchar(4)  NOT NULL COMMENT '操作人类型',
    `creatr_name`     varchar(32) NOT NULL COMMENT '操作人名称',
    `create_datetime` datetime    NOT NULL COMMENT '操作时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=28915 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='空投记录明细';

CREATE TABLE `nft_exchange_code`
(
    `id`               bigint(20) NOT NULL COMMENT '编号',
    `exchange_no`      varchar(32) NOT NULL COMMENT '兑换码',
    `collection_id`    bigint(20) NOT NULL COMMENT '作品id',
    `company_id`       bigint(20) DEFAULT NULL COMMENT '发行方id',
    `quantity`         int(11) NOT NULL COMMENT '数量',
    `lock_time`        int(11) DEFAULT '0' COMMENT '锁仓时间(小时)',
    `status`           varchar(32) NOT NULL COMMENT '状态(0=待使用，1=已使用，2=已作废)',
    `type`             varchar(4)  NOT NULL DEFAULT '0' COMMENT '类型 dict={"0":"发行方","1":"平台"}',
    `creator`          bigint(20) DEFAULT NULL COMMENT '创建人',
    `creator_name`     varchar(32)          DEFAULT NULL COMMENT '创建人名称',
    `create_time`      datetime    NOT NULL COMMENT '创建时间',
    `updater`          bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`     varchar(32)          DEFAULT NULL COMMENT '更新人名称',
    `update_time`      datetime             DEFAULT NULL COMMENT '更新时间',
    `exchange_user_id` bigint(20) DEFAULT NULL COMMENT '兑换用户id',
    `exchange_time`    datetime             DEFAULT NULL COMMENT '兑换时间',
    `export_batch_id`  int(11) DEFAULT NULL COMMENT '批次号',
    `export_flag`      varchar(4)           DEFAULT '0' COMMENT '是否已导出',
    `export_time`      datetime             DEFAULT NULL COMMENT '导出时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `exchange_no_UNIQUE` (`exchange_no`),
    KEY                `index1` (`company_id`,`status`),
    KEY                `user_index` (`exchange_user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='兑换码记录';

CREATE TABLE `nft_lottery_activity`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`                 varchar(4)                     NOT NULL COMMENT '类型 dict={"0":"数字藏品","1":"实物"}',
    `name`                 varchar(64)                    NOT NULL COMMENT '名称',
    `pic`                  varchar(255)                   NOT NULL COMMENT '封面图',
    `content`              varchar(1024)                  DEFAULT NULL COMMENT '实物介绍',
    `status`               varchar(4)                     NOT NULL COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `start_sell_date`      datetime                       DEFAULT NULL COMMENT '报名开始时间',
    `start_status`         varchar(4)                     NOT NULL COMMENT '状态 dict={"0":"进行中","1":"已结束","2":"已公布"}',
    `rule_pic`             varchar(255)                   DEFAULT NULL COMMENT '规则图片',
    `ref_id`               bigint(20) DEFAULT NULL COMMENT '作品序号',
    `award_name`           varchar(64)                    DEFAULT NULL COMMENT '奖励名称',
    `total_quantity`       int(11) NOT NULL DEFAULT '0' COMMENT '总数量',
    `join_quantity`        int(11) DEFAULT '0' COMMENT '报名人数',
    `sold_quantity`        int(11) DEFAULT '0' COMMENT '中签数量',
    `draw_param`           int(11) DEFAULT '0' COMMENT '抽签参数',
    `end_datetime`         datetime                       DEFAULT NULL COMMENT '报名结束时间',
    `calculation_datetime` datetime                       DEFAULT NULL COMMENT '计算时间',
    `bring_out_datetime`   datetime                       DEFAULT NULL COMMENT '公布时间',
    `creater`              bigint(20) NOT NULL COMMENT '创建人',
    `creater_name`         varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名',
    `create_datetime`      datetime                       NOT NULL COMMENT '创建时间',
    `updater`              bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`         varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`      datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='抽奖活动';

CREATE TABLE `nft_lottery_activity_collection`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`     bigint(20) NOT NULL COMMENT '活动序号',
    `activity_name`   varchar(64)                                            NOT NULL COMMENT '活动名称',
    `collection_id`   bigint(20) NOT NULL COMMENT '藏品序号',
    `collection_name` varchar(32)                                            NOT NULL COMMENT '藏品名称',
    `cover_file_url`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '封面文件地址',
    `quantity`        int(11) DEFAULT NULL COMMENT '数量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='抽奖资格';

CREATE TABLE `nft_lottery_activity_join_detail`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`          bigint(20) NOT NULL COMMENT '活动序号',
    `activity_name`        varchar(64)                                            NOT NULL COMMENT '活动名称',
    `join_id`              bigint(20) NOT NULL COMMENT '报名记录序号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户',
    `collection_id`        bigint(20) NOT NULL COMMENT '作品序号',
    `collection_name`      varchar(32)                                            NOT NULL COMMENT '藏品名称',
    `cover_file_url`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '封面文件地址',
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品序号',
    `token_id`             varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '代币编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=860 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='抽奖活动报名详情';

CREATE TABLE `nft_lottery_activity_join_order`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`       bigint(20) NOT NULL COMMENT '活动序号',
    `activity_name`     varchar(32) NOT NULL COMMENT '活动名称',
    `user_id`           bigint(20) NOT NULL COMMENT '用户序号',
    `status`            varchar(4)  NOT NULL COMMENT '状态 dict={"0":"待开奖","1":"已中奖","2":"未中奖","3":"已加地址","4":"已完成"}',
    `join_time`         bigint(20) NOT NULL COMMENT '报名次数',
    `sold_quantity`     int(11) DEFAULT NULL COMMENT '中签数量',
    `create_datetime`   datetime    NOT NULL COMMENT '创建时间',
    `update_datetime`   datetime                        DEFAULT NULL COMMENT '更新时间',
    `receiver`          varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '收件人姓名',
    `re_mobile`         varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '收件人电话',
    `re_address`        varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '收货地址/活动地址',
    `deliverer`         varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '发货人',
    `delivery_datetime` datetime                        DEFAULT NULL COMMENT '发货时间/活动时间',
    `logistics_company` varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '物流公司编号',
    `logistics_code`    varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '物流单号',
    `remark`            varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=294 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='抽奖活动用户参与订单';

CREATE TABLE `nft_lottery_activity_join_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`     bigint(20) NOT NULL COMMENT '活动序号',
    `activity_name`   varchar(32) NOT NULL COMMENT '活动名称',
    `status`          varchar(4)  NOT NULL COMMENT '活动 dict={"0":"已报名","1":"已中奖","2":"未中奖","3":"已发货"}',
    `user_id`         bigint(20) NOT NULL COMMENT '用户序号',
    `collection_id`   bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `collection_name` varchar(32)                   DEFAULT NULL COMMENT '藏品名称',
    `order_no`        int(11) DEFAULT '0' COMMENT '顺序',
    `create_datetime` datetime    NOT NULL COMMENT '创建时间',
    `create_time`     bigint(32) NOT NULL COMMENT '创建时间戳',
    `update_datetime` datetime                      DEFAULT NULL COMMENT '更新时间',
    `join_millis`     bigint(20) DEFAULT '0' COMMENT '报名时间数字 ',
    `group`           bigint(11) DEFAULT NULL COMMENT '普通分组',
    `is_deal`         varchar(4) CHARACTER SET utf8 DEFAULT '0' COMMENT '是否需要处理 0:否,1:是',
    `order_id`        bigint(32) DEFAULT NULL,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=809 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='抽奖活动报名记录';

CREATE TABLE `nft_meta_ticket_record`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `right_id`             bigint(20) NOT NULL COMMENT '门票配置类型',
    `ticket_type`          varchar(4) NOT NULL COMMENT '门票类型',
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品',
    `status`               varchar(4) NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"失效","1":"生效中"}',
    `failure_datetime`     datetime            DEFAULT NULL COMMENT '失效时间',
    `creater`              bigint(20) DEFAULT NULL COMMENT '使用人',
    `create_datetime`      datetime            DEFAULT NULL COMMENT '使用时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元宇宙门票使用记录';

CREATE TABLE `nft_period_auction`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT,
    `name`               varchar(32)             DEFAULT NULL COMMENT '竞拍名称',
    `period_id`          bigint(20) NOT NULL COMMENT '期数编号',
    `bond`               decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '保证金',
    `start_price`        decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '起拍价',
    `status`             varchar(4)     NOT NULL DEFAULT '0.00' COMMENT '状态 dict={"0":"售卖中","1":"已结束","2”:"已流拍","3":"已违约","4":"已完成",}',
    `price_auction`      decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '加价幅度',
    `final_price`        decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '成交价',
    `last_user_id`       bigint(20) NOT NULL DEFAULT '0' COMMENT '最后出价用户id',
    `current_price`      decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '当前价格',
    `already_pay_amount` decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '已支付金额',
    `auction_times`      int(11) NOT NULL DEFAULT '0' COMMENT '竞价次数',
    `start_time`         timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开拍时间',
    `end_time`           timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '结束时间',
    `time_limit`         int(10) NOT NULL DEFAULT '0' COMMENT '拍卖时限(分)',
    `delayed_second`     int(11) NOT NULL DEFAULT '0' COMMENT '延迟秒数',
    `create_time`        timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`        timestamp NULL DEFAULT NULL COMMENT '更改时间',
    `delayed_time`       timestamp NULL DEFAULT NULL COMMENT '最终结束时间',
    `is_divides`         varchar(4)              DEFAULT '0' COMMENT '是否分账 0:不分,1:分',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_01` (`period_id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='期数竞拍';

CREATE TABLE `nft_period_auction_bond_record`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id`            bigint(20) NOT NULL COMMENT '期数编号',
    `collection_detail_id` bigint(20) DEFAULT '0' COMMENT '藏品id',
    `user_id`              bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
    `status`               varchar(3) CHARACTER SET utf8  NOT NULL DEFAULT '0' COMMENT 'dict={"0":"已缴纳","1":"已退还","2":"已违约扣除"}',
    `pay_type`             varchar(4)                              DEFAULT NULL COMMENT '支付类型 {0:余额,6:宝付,7:易宝}',
    `pay_order_code`       varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '支付订单号',
    `pay_status`           varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `pay_datetime`         datetime                                DEFAULT NULL COMMENT '支付时间',
    `pay_balance_amount`   decimal(18, 2)                          DEFAULT NULL COMMENT '余额支付金额',
    `pay_cash_amount`      decimal(18, 2)                          DEFAULT NULL COMMENT '现金支付金额',
    `commission_amount`    decimal(18, 2)                          DEFAULT '0.00' COMMENT '平台佣金收入',
    `create_datetime`      datetime                       NOT NULL COMMENT '参与时间',
    `update_datetime`      datetime                                DEFAULT NULL COMMENT '退还/扣除时间',
    `refund_flag`          char(3) CHARACTER SET utf8     NOT NULL DEFAULT '0' COMMENT '是否需要退还 dict={"0":"否","1":"是","2":"需要退还，已处理"}',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='期数竞拍保证金记录';

CREATE TABLE `nft_period_auction_delayed_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id`       bigint(20) NOT NULL DEFAULT '0' COMMENT '期数编号',
    `user_id`         bigint(20) NOT NULL,
    `create_datetime` datetime                        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '延迟时的创建时间',
    `end_datetime`    datetime                        NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '结束时间',
    `remark`          varchar(100) CHARACTER SET utf8 NOT NULL DEFAULT '' COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='期数竞拍延时记录';

CREATE TABLE `nft_period_auction_record`
(
    `id`        bigint(20) NOT NULL AUTO_INCREMENT,
    `period_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '期数id',
    `user_id`   bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
    `price`     decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '出价金额',
    `post_time` timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '出价时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='期数竞拍出价记录';

CREATE TABLE `nft_period_channel_word`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `period_id`       bigint(20) DEFAULT NULL COMMENT '作品id',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道id',
    `word`            varchar(32)  DEFAULT NULL COMMENT '口令',
    `status`          varchar(32) NOT NULL COMMENT '状态(0=可使用 1=已作废)',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='作品机构口令';

CREATE TABLE `nft_period_discount_detail`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `ref_type`      varchar(32) COLLATE utf8mb4_bin DEFAULT NULL COMMENT '类型 0=期数 1=幸运抽奖',
    `ref_id`        bigint(20) NOT NULL COMMENT '关联id',
    `collection_id` bigint(20) NOT NULL COMMENT '作品id',
    `discount_rate` decimal(18, 2) NOT NULL COMMENT '折扣比例,1不打折',
    `discount_time` int(11) DEFAULT '0' COMMENT '折扣次数,默认0不限制，由期数限制',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=314 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='一级市场折扣明细';

CREATE TABLE `nft_period_user_read`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户编号',
    `period_id`       bigint(20) DEFAULT NULL COMMENT '期数id',
    `put_on_ts`       bigint(20) DEFAULT NULL COMMENT '上架时间',
    `create_datetime` datetime DEFAULT NULL COMMENT '申请时间',
    PRIMARY KEY (`id`),
    KEY               `idx_pu_userid` (`user_id`,`period_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9106 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期数阅读';

CREATE TABLE `nft_produced`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `ref_type`        varchar(4)                     DEFAULT NULL COMMENT '类型 dict={"0":"平台","1":"三方"}',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '发行方序号',
    `name`            varchar(64)                    DEFAULT NULL COMMENT '名称',
    `pic`             varchar(255)                   DEFAULT NULL COMMENT 'logo',
    `status`          varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"下架","1":"上架"}',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)                   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='出品方';

CREATE TABLE `nft_settle_account_jour`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `type`              varchar(4) CHARACTER SET utf8   NOT NULL COMMENT '类型 0:待结算金额,1:结算金额',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `account_number`    varchar(32) CHARACTER SET utf8  NOT NULL COMMENT '账户编号',
    `biz_category`      varchar(32) CHARACTER SET utf8                                DEFAULT NULL COMMENT '业务大类',
    `biz_category_note` varchar(32) CHARACTER SET utf8                                DEFAULT NULL COMMENT '业务大类',
    `biz_type`          varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '业务小类',
    `biz_note`          varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '业务小类说明',
    `ref_no`            varchar(255) CHARACTER SET utf8                               DEFAULT NULL COMMENT '系统内部参考订单号',
    `ref_user_id`       bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
    `trans_amount`      decimal(18, 8)                  NOT NULL COMMENT '变动金额',
    `pre_amount`        decimal(18, 8)                                                DEFAULT NULL COMMENT '变动前金额',
    `post_amount`       decimal(18, 8)                                                DEFAULT NULL COMMENT '变动后金额',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_datetime`   datetime                        NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=81757 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='结算账户流水';

CREATE TABLE `nft_u3d_my_collection`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT,
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品Id',
    `user_id`              bigint(20) DEFAULT NULL COMMENT '用户',
    `order_no`             int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='u3d我的作品位置';

CREATE TABLE `nft_u3d_props`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`            varchar(32)  DEFAULT NULL COMMENT '名称',
    `type`            varchar(32)  DEFAULT NULL COMMENT '类型(0=默认道具 1=指定道具)',
    `props_code`      varchar(32)  DEFAULT NULL COMMENT '道具编码',
    `updater`         varchar(32)  DEFAULT NULL COMMENT '最近修改人',
    `update_datetime` datetime     DEFAULT NULL COMMENT '最近修改时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='元宇宙道具';

CREATE TABLE `nft_user_payment_account_info`
(
    `id`                      bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`                 bigint(20) DEFAULT NULL COMMENT '用户序号',
    `name`                    varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci   DEFAULT NULL,
    `short_name`              varchar(64) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '简称',
    `introduce`               varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `divide_flag`             varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '分账标志',
    `plat_divide_rate`        decimal(18, 2)                                                 DEFAULT NULL COMMENT '平台分成',
    `legal_licence_type`      varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '法人证件类型',
    `legal_licence_no`        varchar(64) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '法人证件号',
    `legal_licence_front_url` varchar(255) CHARACTER SET utf8                                DEFAULT NULL COMMENT '证件照片1',
    `legal_licence_back_url`  varchar(255) CHARACTER SET utf8                                DEFAULT NULL COMMENT '证件照片2',
    `legal_real_name`         varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '真实姓名',
    `legal_mobile`            varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '手机号',
    `settle_card_no`          varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '结算银行卡号',
    `settle_bank_code`        varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '结算银行',
    `settle_bank_name`        varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '结算银行名称',
    `province`                varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '省',
    `city`                    varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '市',
    `district`                varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '区',
    `address`                 varchar(255) CHARACTER SET utf8                                DEFAULT NULL COMMENT '详细地址',
    `request_no`              varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '易宝申请订单号',
    `merchant_no`             varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '易宝商户号',
    `status`                  varchar(32) CHARACTER SET utf8                                 DEFAULT NULL COMMENT '状态(0=审核中,1=审核通过,2=审核失败,3=已解约)',
    `create_datetime`         datetime                                                       DEFAULT NULL COMMENT '创建时间',
    `updater`                 bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`            varchar(32)                                                    DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`         datetime                                                       DEFAULT NULL COMMENT '更新时间',
    `remark`                  varchar(255) CHARACTER SET utf8                                DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户收款账户';

CREATE TABLE `nft_user_settle_account`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`             varchar(4)     DEFAULT NULL COMMENT '类型 C:用户，P:平台，A:机构',
    `user_id`          bigint(20) DEFAULT NULL COMMENT '用户序号',
    `to_settle_amount` decimal(18, 2) DEFAULT '0.00' COMMENT '代结算金额',
    `on_settle_amount` decimal(18, 2) DEFAULT '0.00' COMMENT '结算中金额',
    `settle_amount`    decimal(19, 2) DEFAULT '0.00' COMMENT '已结算金额',
    `create_datetime`  datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6190 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户结算账户';

CREATE TABLE `nft_user_settle_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`           bigint(20) DEFAULT NULL COMMENT '用户序号',
    `settle_account_id` bigint(20) DEFAULT NULL COMMENT '结算账户序号',
    `amount`            decimal(18, 2) DEFAULT NULL COMMENT '结算金额',
    `status`            varchar(4)     DEFAULT NULL COMMENT '状态 dict={"0":"代结算","1":"已结算"}',
    `settle_datetime`   datetime       DEFAULT NULL COMMENT '结算时间',
    `create_datetime`   datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6445 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户结算记录';

CREATE TABLE `nft_vip_user_buy_collection`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户id',
    `collection_id`   bigint(20) DEFAULT NULL COMMENT '藏品id',
    `quantity`        int(11) DEFAULT NULL COMMENT '数量',
    `status`          varchar(32) DEFAULT NULL COMMENT '状态0=待使用 1=已使用 2=作废',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='VIP用户购买藏品特权';

CREATE TABLE `nya_activity`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`               varchar(64)  DEFAULT NULL COMMENT '活动名称',
    `pic`                varchar(255) DEFAULT NULL COMMENT '活动图',
    `rule_note`          text COMMENT '活动规则',
    `collection_id`      bigint(20) DEFAULT NULL COMMENT '合成作品序号',
    `invite_send_time`   int(11) DEFAULT '1' COMMENT '邀请赠送次数',
    `register_send_time` int(11) DEFAULT '1' COMMENT '注册赠送次数',
    `is_real_name`       varchar(4)   DEFAULT '0' COMMENT '抽奖是否需要实名 0:否；1:是',
    `status`             varchar(4)   DEFAULT '0' COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `start_status`       varchar(4)   DEFAULT '0' COMMENT '开始状态 dict={"0":"待开始","1":"进行中","2":"已结束"}',
    `weight_type`        varchar(4)   DEFAULT '0' COMMENT '权重类型 0:总数,1:剩余数量',
    `start_datetime`     datetime     DEFAULT NULL COMMENT '活动开始时间',
    `end_datetime`       datetime     DEFAULT NULL COMMENT '活动结束时间',
    `creater`            bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`       varchar(32)  DEFAULT NULL COMMENT '创建人名',
    `create_datetime`    datetime     DEFAULT NULL COMMENT '创建时间',
    `updater`            bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`       varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`    datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='新春活动';

CREATE TABLE `nya_activity_award`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`     bigint(20) DEFAULT NULL COMMENT '活动序号',
    `type`            varchar(4)     DEFAULT NULL COMMENT '奖励类型 dict={"0":"nft","1":"实物"}',
    `award_id`        bigint(20) DEFAULT NULL COMMENT '奖励序号',
    `award_number`    int(11) DEFAULT NULL COMMENT '奖励份数',
    `level`           int(255) DEFAULT NULL COMMENT '奖励等级(1=第一名 2=第二名)',
    `price`           decimal(10, 2) DEFAULT NULL COMMENT '估值',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_name`     varchar(32)    DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)    DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='新春活动奖励';

CREATE TABLE `nya_activity_card`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `card_id`           bigint(4) DEFAULT NULL COMMENT '卡片序号会重复',
    `activity_id`       bigint(20) DEFAULT NULL COMMENT '活动序号',
    `name`              varchar(64)  DEFAULT NULL COMMENT '卡片名',
    `pic`               varchar(255) DEFAULT NULL COMMENT '图片',
    `total_quantity`    int(11) DEFAULT '0' COMMENT '总数量',
    `remain_quantity`   int(11) DEFAULT NULL COMMENT '剩余数量',
    `exchange_quantity` int(11) DEFAULT NULL COMMENT '兑换需要的个数',
    `creater`           bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_name`       varchar(32)  DEFAULT NULL COMMENT '创建人名',
    `create_datetime`   datetime     DEFAULT NULL COMMENT '创建时间',
    `updater`           bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`   datetime     DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='活动卡片';

CREATE TABLE `nya_activity_sum`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `user_referee`    bigint(20) DEFAULT NULL COMMENT '推荐人',
    `activity_id`     bigint(20) DEFAULT NULL COMMENT '活动序号',
    `award_id`        bigint(20) DEFAULT NULL COMMENT '奖品序号',
    `number`          int(11) DEFAULT NULL COMMENT '自身次数',
    `exchange_number` int(11) DEFAULT NULL COMMENT '兑换次数',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=102 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='活动兑换汇总';

CREATE TABLE `nya_activity_user_card`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户序号',
    `activity_id`       bigint(20) DEFAULT NULL COMMENT '活动序号',
    `card_id`           bigint(20) DEFAULT NULL COMMENT '卡片序号',
    `exchange_id`       bigint(20) DEFAULT NULL COMMENT '合成序号',
    `status`            varchar(4) DEFAULT '0' COMMENT '状态 dict={"0":"待抽取","1":"已抽取","2":"已兑换","3":"已废除"}',
    `source`            varchar(4) DEFAULT NULL COMMENT '来源 dict={"0":"免费获得","1":"邀请好友","2":"平台赠送"}',
    `source_user_id`    bigint(20) DEFAULT NULL COMMENT '邀请用户',
    `create_datetime`   datetime   DEFAULT NULL COMMENT '获得时间',
    `use_datetime`      datetime   DEFAULT NULL COMMENT '抽取时间',
    `exchange_datetime` datetime   DEFAULT NULL COMMENT '合成时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=54947 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户卡片';

CREATE TABLE `nya_activity_user_exchange`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `activity_id`          bigint(20) DEFAULT NULL COMMENT '活动记录',
    `user_id`              bigint(20) DEFAULT NULL COMMENT '用户序号',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `status`               varchar(4) DEFAULT NULL COMMENT '状态 dict={"0":"待分配","1":"已分配"}',
    `create_datetime`      datetime   DEFAULT NULL COMMENT '兑换时间',
    PRIMARY KEY (`id`),
    KEY                    `user_id` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1976 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='活动用户兑换记录';

CREATE TABLE `third_channel_merchant`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4)                     DEFAULT '1' COMMENT '类型 dict={"0":"主站","1":"三方"}',
    `name`            varchar(64)                    DEFAULT NULL COMMENT '渠道名称',
    `sms_prefix`      varchar(64)                    DEFAULT NULL COMMENT '短信前缀',
    `pay_back_url`    varchar(64)                    DEFAULT NULL COMMENT '支付成功后回调地址',
    `invite_url`      varchar(255)                   DEFAULT NULL COMMENT '邀请注册链接',
    `status`          varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='分发渠道商';

CREATE TABLE `third_company_channel`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_id`      bigint(20) DEFAULT NULL COMMENT '发行方序号',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道商序号',
    `status`          varchar(4)                     DEFAULT NULL COMMENT '状态 dict={"0":"废弃","1":"启用"}',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '创建人名',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='发行方渠道关联表';

CREATE TABLE `tmm_cuser`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `user_id`               bigint(20) NOT NULL COMMENT '用户ID',
    `member_flag`           varchar(32)    NOT NULL DEFAULT '0' COMMENT '是否激活(1默认已激活)',
    `authentication_status` varchar(4)     NOT NULL DEFAULT '0' COMMENT '实名认证状态 dict={"-1":"认证不通过","0":"未认证","1":"认证中","2":"认证通过"}',
    `rate`                  decimal(18, 4) NOT NULL DEFAULT '0.0000' COMMENT '佣金比例',
    `create_datetime`       datetime                DEFAULT NULL COMMENT '创建时间',
    `is_specific_account`   varchar(4)              DEFAULT '0' COMMENT '是否是特殊账户 0:不是，1：是',
    `virtual_income`        decimal(18, 4)          DEFAULT '1.0000' COMMENT 'C端用户虚拟收益',
    PRIMARY KEY (`id`),
    UNIQUE KEY `USERID` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=350683 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='C端用户';

CREATE TABLE `tmm_income_daily`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
    `income_date`     datetime       NOT NULL COMMENT '日期',
    `amount`          decimal(18, 8) NOT NULL COMMENT '分佣总额',
    `create_datetime` datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=486 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='每日分佣（平台统计）';

CREATE TABLE `tmm_income_daily_user`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户ID',
    `node_type`       varchar(8) DEFAULT NULL COMMENT '当日等级',
    `income_date`     datetime       NOT NULL COMMENT '日期',
    `amount`          decimal(18, 8) NOT NULL COMMENT '当日盈利',
    `create_datetime` datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=38676 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户每日分佣';

CREATE TABLE `tmm_stat_trade_data_day`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `current_day`              varchar(32)    NOT NULL COMMENT '日期(yyyy-MM-dd)',
    `register_end_count`       int(11) NOT NULL COMMENT '截止注册人数',
    `login_count`              int(11) DEFAULT NULL COMMENT '每日登录人数',
    `person_visit_count`       int(11) DEFAULT '0' COMMENT '每日访问人数',
    `first_trade_count`        int(11) NOT NULL COMMENT '一级市场抢购截止交易数量',
    `first_trade_amount`       decimal(18, 2) NOT NULL COMMENT '一级市场抢购截止交易总额',
    `diy_trade_count`          int(11) NOT NULL COMMENT 'diy截止交易数量',
    `diy_trade_amount`         decimal(18, 2) NOT NULL COMMENT 'diy截止交易总额',
    `blind_box_trade_count`    int(11) NOT NULL COMMENT '盲盒截止交易数量',
    `blind_box_trade_amount`   decimal(18, 2) NOT NULL COMMENT '盲盒截止交易总额',
    `buyout_trade_count`       int(11) NOT NULL COMMENT '一口价截止交易数量',
    `buyout_trade_amount`      decimal(18, 2) NOT NULL COMMENT '一口价截止交易总额',
    `auction_trade_count`      int(11) NOT NULL COMMENT '竞拍截止交易数量',
    `auction_trade_amount`     decimal(18, 2) NOT NULL COMMENT '竞拍截止交易总额',
    `re_purchase_count`        int(11) NOT NULL COMMENT '重新购买总交易数量',
    `transfer_introduce_count` int(11) NOT NULL COMMENT '转介绍用户数',
    `withdraw_amount`          decimal(18, 2) DEFAULT NULL COMMENT '日提现量',
    `charge_amount`            decimal(18, 2) DEFAULT NULL COMMENT '日充值量',
    `draw_join_amount`         decimal(18, 2) DEFAULT NULL COMMENT '累计抽奖报名金额',
    `draw_success_amount`      decimal(18, 2) DEFAULT NULL COMMENT '累计抽奖成功金额',
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_index` (`current_day`)
) ENGINE=InnoDB AUTO_INCREMENT=370 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='每日统计交易数据';

CREATE TABLE `tstd_account`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
    `account_number`       varchar(64)    NOT NULL COMMENT '账户编号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户编号',
    `currency`             varchar(32)    NOT NULL COMMENT '币种',
    `type`                 varchar(4)     NOT NULL COMMENT '类别',
    `status`               varchar(2)     NOT NULL COMMENT '状态（1正常 2程序冻结 3人工冻结）',
    `amount`               decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '总资产',
    `available_amount`     decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '可用余额',
    `frozen_amount`        decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '冻结金额',
    `lock_amount`          decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '锁仓金额',
    `in_amount`            decimal(18, 8)          DEFAULT '0.********' COMMENT '净流入金额',
    `create_datetime`      datetime       NOT NULL COMMENT '创建时间',
    `last_order`           varchar(32)             DEFAULT NULL COMMENT '最近一次变动对应的流水编号',
    `cloud_account_number` varchar(64)             DEFAULT NULL COMMENT '云钱包账户编号',
    `out_amount`           decimal(18, 8)          DEFAULT '0.********' COMMENT '总出金',
    PRIMARY KEY (`id`),
    UNIQUE KEY `account_number_UNIQUE` (`account_number`),
    UNIQUE KEY `index6` (`user_id`,`currency`),
    KEY                    `index3` (`type`),
    KEY                    `index5` (`currency`,`user_id`),
    KEY                    `index4` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=513192 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='账户';

CREATE TABLE `tstd_address`
(
    `id`          bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `user_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '用户uid（用户类型对应的id）',
    `name`        varchar(50)  NOT NULL DEFAULT '' COMMENT '收货人姓名',
    `user_type`   char(3)               DEFAULT NULL COMMENT '0用户,1供应商',
    `is_default`  char(3)      NOT NULL DEFAULT '0' COMMENT '是否默认地址1是，0否,一个用户只能有一个默认地址',
    `province`    varchar(50)  NOT NULL DEFAULT '' COMMENT '省份名称',
    `province_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '省份ID对应area表中的id',
    `city`        varchar(50)  NOT NULL DEFAULT '' COMMENT '城市名称',
    `city_id`     bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '城市ID对应area表中的id',
    `county`      varchar(255) NOT NULL DEFAULT '0' COMMENT '区/县',
    `county_id`   bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID对应area表中的id',
    `address`     varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
    `phone`       varchar(50)  NOT NULL DEFAULT '' COMMENT '电话收货人电话',
    `status`      char(3)      NOT NULL DEFAULT '1' COMMENT '1启用, 0禁用',
    `post_time`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
    PRIMARY KEY (`id`),
    KEY           `index` (`user_id`,`is_default`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=10291 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收货地址表';

CREATE TABLE `tstd_area`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '索引ID',
    `name`       varchar(50) NOT NULL DEFAULT '' COMMENT '地区名称',
    `pid`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '地区父ID',
    `sort`       tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
    `deep`       tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '地区深度，从1开始',
    `short_name` varchar(50) NOT NULL DEFAULT '',
    `enabled`    tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用（1：是；0：否）',
    PRIMARY KEY (`id`),
    KEY          `area_parent_id` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=5150 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地区表';

CREATE TABLE `tstd_area_yeepay`
(
    `id`         bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '索引ID',
    `code`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '编码',
    `name`       varchar(50) NOT NULL DEFAULT '' COMMENT '地区名称',
    `pid`        bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '地区父ID',
    `sort`       tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '排序',
    `deep`       tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '地区深度，从1开始',
    `short_name` varchar(50) NOT NULL DEFAULT '',
    `enabled`    tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '是否启用（1：是；0：否）',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`code`),
    KEY          `area_parent_id` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=4176 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='地区表';

CREATE TABLE `tstd_bankcard`
(
    `id`              bigint(32) NOT NULL COMMENT '编号',
    `type`            varchar(32)  NOT NULL COMMENT '类型（C端 SYS系统）',
    `user_id`         bigint(32) NOT NULL COMMENT '用户编号',
    `bank_user_name`  varchar(255) NOT NULL COMMENT '银行卡户名',
    `channel_bank_id` bigint(32) DEFAULT NULL COMMENT '渠道银行编号',
    `bank_code`       varchar(32)  NOT NULL COMMENT '银行编号',
    `bank_name`       varchar(255) NOT NULL COMMENT '银行名称',
    `subbranch`       varchar(255) NOT NULL DEFAULT '' COMMENT '开户支行名称',
    `bankcard_number` varchar(32)  NOT NULL COMMENT '银行卡号',
    `status`          varchar(4)   NOT NULL COMMENT '状态（0失效 1生效）',
    `default_flag`    varchar(45)  NOT NULL DEFAULT '0' COMMENT '是否默认（0否 1是）',
    `create_datetime` datetime     NOT NULL COMMENT '创建时间',
    `updater`         bigint(32) unsigned DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)           DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime              DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)          DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='银行卡';

CREATE TABLE `tstd_bank_channel`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `bank_info_id` bigint(20) NOT NULL COMMENT '银行编号',
    `channel_type` varchar(4)   DEFAULT NULL COMMENT '渠道类型 dict={"6":"宝付","7":"易宝"}',
    `status`       varchar(4) NOT NULL COMMENT '状态 dict={"0":"下架","1":"上架"}',
    `remark`       varchar(255) DEFAULT NULL COMMENT '备注',
    `order_on`     int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_o1` (`bank_info_id`,`channel_type`)
) ENGINE=InnoDB AUTO_INCREMENT=68 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='渠道银行信息';

CREATE TABLE `tstd_bank_info`
(
    `id`        bigint(32) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `bank_code` varchar(32) NOT NULL COMMENT '银行编号',
    `bank_name` varchar(32) NOT NULL COMMENT '银行名称',
    `type`      varchar(4)   DEFAULT NULL COMMENT '类型1:储蓄卡,2:信用卡',
    `logo`      text COMMENT 'Logo',
    `status`    varchar(4)  NOT NULL COMMENT '状态（0下架 1上架）',
    `remark`    varchar(255) DEFAULT NULL COMMENT '备注',
    `order_on`  int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_o1` (`bank_code`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=43 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='银行信息';

CREATE TABLE `tstd_business_channel`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道序号',
    `biz_type`        varchar(4)  DEFAULT NULL COMMENT '业务类型',
    `status`          varchar(4)  DEFAULT NULL COMMENT '是否启用 0:不启用,1:启用',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) DEFAULT NULL COMMENT '更新人名',
    `update_datetime` datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `channel_type` (`channel_id`,`biz_type`)
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务渠道关系表';

CREATE TABLE `tstd_category`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4) NOT NULL COMMENT '类型 0:期数',
    `biz_type`        varchar(32)                                                  DEFAULT NULL COMMENT '业务类型',
    `name`            varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `name_key`        varchar(32)                                                  DEFAULT NULL,
    `status`          varchar(32)                                                  DEFAULT '0' COMMENT '状态 0=不启用 1=启用',
    `order_no`        int(11) DEFAULT '0' COMMENT '展示顺序',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(255)                                                 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                                                     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)                                                 DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`type`,`name_key`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='期数种类';

CREATE TABLE `tstd_channel_bank`
(
    `id`           bigint(32) NOT NULL COMMENT '编号',
    `pay_type`     varchar(4)   DEFAULT NULL COMMENT '支付类型 0:支付宝,1:微信,2:银行卡',
    `bank_code`    varchar(32) NOT NULL COMMENT '银行编号',
    `bank_name`    varchar(32) NOT NULL COMMENT '银行名称',
    `channel_type` varchar(4)   DEFAULT NULL COMMENT '渠道类型',
    `channel_bank` varchar(32)  DEFAULT NULL COMMENT '渠道给银行的代号',
    `logo`         text COMMENT 'Logo',
    `status`       varchar(4)  NOT NULL COMMENT '状态（0下架 1上架）',
    `remark`       varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='渠道银行';

CREATE TABLE `tstd_charge`
(
    `id`                     bigint(20) NOT NULL COMMENT '编号',
    `account_number`         varchar(32)    NOT NULL COMMENT '账户编号',
    `account_type`           varchar(4)     NOT NULL COMMENT '账户类型',
    `user_kind`              varchar(32)    NOT NULL COMMENT '用户类型（C端用户 CLINIC诊所用户）',
    `user_id`                bigint(20) NOT NULL COMMENT '用户编号',
    `amount`                 decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '充值金额',
    `currency`               varchar(8)     NOT NULL COMMENT '币种',
    `biz_type`               varchar(32)    NOT NULL COMMENT '关联业务类型',
    `biz_note`               varchar(255)   NOT NULL COMMENT '关联业务备注',
    `biz_no`                 varchar(255)            DEFAULT NULL COMMENT '关联订单号',
    `bill_flag`              varchar(4)              DEFAULT '0' COMMENT '是否开票（0否 1是）',
    `status`                 varchar(4)     NOT NULL COMMENT '状态（1待支付 2支付失败 3支付成功）',
    `apply_user`             bigint(20) NOT NULL COMMENT '申请人',
    `apply_note`             varchar(255)            DEFAULT NULL COMMENT '申请说明',
    `apply_datetime`         datetime       NOT NULL COMMENT '申请时间',
    `channel_type`           varchar(32)    NOT NULL COMMENT '支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）',
    `channel_bank`           varchar(255)            DEFAULT NULL COMMENT '渠道银行',
    `channel_account_info`   varchar(255)            DEFAULT NULL COMMENT '支付渠道账号信息',
    `channel_account_number` varchar(255)            DEFAULT NULL COMMENT '支付渠道账号',
    `channel_order`          varchar(255)            DEFAULT NULL COMMENT '支付渠道单号',
    `pay_group`              varchar(32)             DEFAULT NULL COMMENT '订单分组组号（信息代表）',
    `pay_user`               varchar(32)             DEFAULT NULL COMMENT '支付回录人',
    `pay_note`               varchar(255)            DEFAULT NULL COMMENT '支付回录说明',
    `pay_datetime`           datetime                DEFAULT NULL COMMENT '支付回录时间',
    PRIMARY KEY (`id`),
    KEY                      `idx_status_paydatetime_amount` (`status`,`pay_datetime`,`amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='充值订单';

CREATE TABLE `tstd_express`
(
    `id`       bigint(1) unsigned NOT NULL AUTO_INCREMENT COMMENT '索引ID',
    `name`     varchar(50)  NOT NULL DEFAULT '',
    `code`     varchar(50)  NOT NULL DEFAULT '',
    `letter`   char(1)      NOT NULL DEFAULT '',
    `order_no` tinyint(4) NOT NULL DEFAULT '0',
    `url`      varchar(100) NOT NULL DEFAULT '',
    `status`   tinyint(4) unsigned NOT NULL DEFAULT '1',
    `kdn_code` varchar(50)           DEFAULT '' COMMENT '快递鸟编码',
    PRIMARY KEY (`id`),
    UNIQUE KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='快递公司';

CREATE TABLE `tstd_forum_action`
(
    `id`              bigint(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `type`            varchar(32) NOT NULL COMMENT '类型（1阅读 2点赞）',
    `ref_id`          bigint(32) NOT NULL COMMENT '帖子编号',
    `user_id`         bigint(32) NOT NULL COMMENT '用户编号',
    `create_datetime` datetime    NOT NULL COMMENT '创建时间',
    `read_flag`       varchar(4)  NOT NULL DEFAULT '0' COMMENT '是否已读（0否 1是）',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=65408 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='帖子互动';

CREATE TABLE `tstd_forum_keyword`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '编号（自增长）',
    `word`            varchar(255) DEFAULT NULL COMMENT '词语',
    `level`           varchar(4)   DEFAULT NULL COMMENT '作用等级',
    `reaction`        varchar(4)   DEFAULT NULL COMMENT '反应 dict={"1":"直接拦截","2":"替换","3":"审核"}',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tstd_forum_log`
(
    `id`              bigint(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `type`            varchar(32) NOT NULL COMMENT '类型(1关注 2点赞 3评论)',
    `type_note`       varchar(32) NOT NULL COMMENT '类型说明(关注，取消关注 点赞，取消点赞，评论)',
    `rel_id`          varchar(32) NOT NULL COMMENT '关联编号(关注是用户编号，点赞是帖子的编号，评论是评论的编号)',
    `rel_user_id`     varchar(32) NOT NULL COMMENT '关联用户编号',
    `pre_id`          varchar(32)  DEFAULT NULL COMMENT '上级编号(关注是用户编号，点赞是帖子的编号，评论是帖子的编号)',
    `post_id`         bigint(32) DEFAULT NULL COMMENT '帖子编号',
    `user_id`         bigint(32) NOT NULL COMMENT '用户编号',
    `status`          char(1)      DEFAULT NULL COMMENT '阅读标志 0=未读 1已读',
    `create_datetime` datetime    NOT NULL COMMENT '创建时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=14934 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='论坛操作日志';

CREATE TABLE `tstd_his_static`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户编号',
    `first_amount`    decimal(18, 2) DEFAULT NULL COMMENT '一级中签折扣金额',
    `luck_amount`     decimal(18, 2) DEFAULT NULL COMMENT '幸运抽奖折扣金额',
    `withdraw_amount` decimal(18, 2) DEFAULT NULL COMMENT '提现折扣金额',
    `total_amount`    decimal(18, 2) DEFAULT NULL COMMENT '总折扣金额',
    `status`          varchar(4)     DEFAULT NULL COMMENT '状态 0:待处理，1:已处理',
    `create_datetime` datetime       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7222 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='史诗折扣统计';

CREATE TABLE `tstd_identify_config`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `minutes`         int(11) DEFAULT NULL COMMENT '间隔分钟',
    `time`            int(11) DEFAULT NULL COMMENT '机会次数',
    `prompt`          varchar(64) DEFAULT NULL COMMENT '提示语',
    `status`          varchar(4)  DEFAULT NULL COMMENT '状态 dict={"0":"废弃","1":"启用"}',
    `order_no`        int(11) DEFAULT NULL COMMENT '配置顺序',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) DEFAULT NULL COMMENT '更新人名',
    `update_datetime` datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_01` (`order_no`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='实名认证间隔规则';

CREATE TABLE `tstd_identify_order`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(32) NOT NULL COMMENT '用户编号',
    `status`          varchar(8) NOT NULL COMMENT '状态 dict={"0":"待调用三方","1":"三方认证成功","2":"三方认证失败待人工认证","3":"人工认证通过","4":"人工认证失败"}',
    `real_name`       varchar(32)   DEFAULT NULL COMMENT '真实姓名',
    `id_no`           varchar(64)   DEFAULT NULL COMMENT '身份证号码',
    `front_image`     text COMMENT '身份证正面照片',
    `back_image`      text COMMENT '身份证反面照片',
    `face_image`      text COMMENT '人脸照片',
    `biz_code`        varchar(64)   DEFAULT NULL,
    `certify_id`      varchar(64)   DEFAULT NULL,
    `create_datetime` datetime   NOT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `update_datetime` datetime      DEFAULT NULL COMMENT '修改时间',
    `remark`          varchar(1024) DEFAULT NULL COMMENT '备注',
    `next_datetime`   datetime      DEFAULT NULL COMMENT '下次解封时间',
    `order_no`        int(20) DEFAULT NULL COMMENT '配置顺序',
    PRIMARY KEY (`id`),
    KEY               `idx_userid_status` (`user_id`,`status`),
    KEY               `idx_idno_status` (`id_no`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=668953498452500481 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='实名认证订单';

CREATE TABLE `tstd_income`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键编号',
    `user_id`         bigint(20) NOT NULL COMMENT '收益人用户ID',
    `ref_id`          bigint(20) NOT NULL COMMENT '关联订单编号',
    `ref_user_id`     bigint(20) DEFAULT NULL COMMENT '关联用户编号',
    `type`            varchar(8)     NOT NULL COMMENT '收益类型（0=自身收益 1=推荐收益 2=节点收益）',
    `team_type`       varchar(8)              DEFAULT NULL COMMENT '团队类型dict={"0":"1代","1":"2代"}',
    `node_type`       varchar(8)              DEFAULT NULL COMMENT '节点类型dict={"0":"1星","1":"2星","2":"3星"}',
    `amount_type`     varchar(8)     NOT NULL DEFAULT '0' COMMENT '金额类型dict={"0":"消耗金","1":"推荐商家收入金","2":"分享收入金"}',
    `income_time`     datetime       NOT NULL COMMENT '收益时间',
    `base_amount`     decimal(18, 8) NOT NULL COMMENT '分成基数',
    `rate`            decimal(18, 4) NOT NULL COMMENT '分成比例',
    `amount`          decimal(18, 8) NOT NULL COMMENT '收益',
    `fee`             decimal(18, 8) NOT NULL COMMENT '手续费',
    `real_amount`     decimal(18, 8) NOT NULL COMMENT '实际收益',
    `status`          varchar(8)     NOT NULL DEFAULT '0' COMMENT '收益状态(0=未结算 1=已结算`)',
    `settle_time`     datetime                DEFAULT NULL COMMENT '结算时间',
    `settle_amount`   decimal(18, 8)          DEFAULT NULL COMMENT '结算金额',
    `create_datetime` datetime       NOT NULL COMMENT '创建时间',
    `remark`          varchar(255)            DEFAULT NULL COMMENT '收益说明',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=289882 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='收益';

CREATE TABLE `tstd_invoice_order`
(
    `id`               bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`          bigint(32) NOT NULL COMMENT '用户编号',
    `order_type`       varchar(4)     NOT NULL COMMENT '关联订单类型dict={"0":"购买藏品","1":"幸运抽奖","2":"平台服务费(寄售)","3":"平台服务费(提现手续费)","4":"权益购买"}',
    `order_id`         bigint(32) NOT NULL COMMENT '关联订单id',
    `order_note`       varchar(64)    NOT NULL COMMENT '订单说明',
    `amount`           decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '开票金额',
    `company_type`     varchar(4)     NOT NULL DEFAULT '0' COMMENT '发行方类型 0:发行方,1:平台',
    `company_id`       bigint(32) DEFAULT NULL COMMENT '公司编号',
    `company_name`     varchar(255)            DEFAULT NULL COMMENT '公司名称',
    `create_datetime`  datetime       NOT NULL COMMENT '产生时间',
    `create_ts`        bigint(32) DEFAULT NULL COMMENT '产生时间戳',
    `status`           varchar(4)     NOT NULL COMMENT '状态dict={"0":"待申请发票","1":"已申请发票"}',
    `invoice_apply_id` bigint(32) DEFAULT NULL COMMENT '申请发票id',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3269 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户开票订单明细';

CREATE TABLE `tstd_jour`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `type`              varchar(32)    NOT NULL COMMENT '流水类型（1余额流水 2冻结流水）',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `account_number`    varchar(64)    NOT NULL COMMENT '账户编号',
    `account_type`      varchar(4)     NOT NULL COMMENT '账户类型',
    `currency`          varchar(32)                                                   DEFAULT NULL COMMENT '币种',
    `biz_category`      varchar(32)    NOT NULL COMMENT '业务大类',
    `biz_category_note` varchar(32)    NOT NULL COMMENT '业务大类',
    `biz_type`          varchar(255)   NOT NULL COMMENT '业务小类',
    `biz_note`          varchar(255)   NOT NULL COMMENT '业务小类说明',
    `ref_no`            varchar(255)   NOT NULL COMMENT '系统内部参考订单号',
    `ref_user_id`       bigint(20) DEFAULT NULL COMMENT '关联的用户ID',
    `trans_amount`      decimal(18, 8) NOT NULL COMMENT '变动金额',
    `pre_amount`        decimal(18, 8)                                                DEFAULT NULL COMMENT '变动前金额',
    `post_amount`       decimal(18, 8)                                                DEFAULT NULL COMMENT '变动后金额',
    `prev_jour_code`    varchar(32)                                                   DEFAULT NULL COMMENT '上一条流水编号',
    `status`            varchar(4)                                                    DEFAULT NULL COMMENT '状态 1待对账 3已对账且账已平 4账不平待调账审批 5已对账且账不平 6无需对账 11待入账 12已入账 13入账失败 14待出账 15出账成功 16出账失败',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_datetime`   datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY                 `index3` (`biz_type`),
    KEY                 `index2` (`currency`),
    KEY                 `index5` (`status`),
    KEY                 `index4` (`user_id`),
    KEY                 `index7` (`ref_no`),
    KEY                 `index8` (`account_number`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=678884045303848961 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='账户流水';

CREATE TABLE `tstd_jpush_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `ref_type`        varchar(4)   DEFAULT NULL COMMENT '关联类型',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '序号',
    `platform`        varchar(64)  DEFAULT NULL COMMENT '推送平台',
    `title`           varchar(255) DEFAULT NULL COMMENT '推送标题',
    `notice_info`     text COMMENT '推送消息',
    `alias`           text COMMENT '推送目标',
    `activity_key`    varchar(64)  DEFAULT NULL COMMENT '指定活动入参',
    `activity_action` varchar(64)  DEFAULT NULL COMMENT '指定活动类型',
    `status`          varchar(4) NOT NULL COMMENT '状态 dict={"0":"失败","1":"成功"}',
    `creater`         bigint(32) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(64)  DEFAULT NULL COMMENT '创建人姓名',
    `create_datetime` datetime   NOT NULL COMMENT '创建时间',
    `content`         text,
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=145013 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='极光推送记录';

CREATE TABLE `tstd_jpush_registration`
(
    `user_id`         bigint(20) NOT NULL COMMENT '用户序号',
    `registration_id` varchar(255) DEFAULT NULL COMMENT '设备序号',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='极光设备别名关联表';

CREATE TABLE `tstd_lock`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `biz_type`        varchar(4)  NOT NULL COMMENT '类型',
    `ref_code`        varchar(32) NOT NULL COMMENT '关联编号',
    `create_datetime` datetime    NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index2` (`biz_type`,`ref_code`)
) ENGINE=InnoDB AUTO_INCREMENT=12230025 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='业务锁';

CREATE TABLE `tstd_login_error_config`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `target`          varchar(32) CHARACTER SET utf8 NOT NULL DEFAULT 'C' COMMENT '针对人群',
    `status`          varchar(4)                              DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"启用"}',
    `time`            int(11) NOT NULL COMMENT '次数',
    `type`            varchar(255)                   NOT NULL COMMENT '处理类型 dict={"0":"无处理","1":"验证码校验","2":"锁定"}',
    `lock_hour`       int(11) DEFAULT NULL COMMENT '锁定小时',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)                             DEFAULT NULL COMMENT '更新人名',
    `update_datetime` datetime                                DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)                            DEFAULT NULL COMMENT '错误提醒',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`target`,`time`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='登录密码输入错误处理机制';

CREATE TABLE `tstd_login_error_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4)   DEFAULT NULL COMMENT '错误类型0:密码错误,1:验证码错误',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `user_kind`       varchar(4)   DEFAULT NULL COMMENT '用户类型',
    `login_name`      varchar(20)  DEFAULT NULL COMMENT '登录名',
    `time`            int(11) DEFAULT NULL COMMENT '错误次数',
    `config_id`       bigint(20) DEFAULT NULL COMMENT '配置序号',
    `config_type`     varchar(4)   DEFAULT NULL COMMENT '处理类型 dict={"0":"无处理","1":"验证码校验","2":"锁定"}',
    `lock_hour`       int(11) DEFAULT NULL COMMENT '锁定时间',
    `unlock_datetime` datetime     DEFAULT NULL COMMENT '失效时间',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    `create_time`     bigint(20) DEFAULT NULL COMMENT '创建时间戳',
    `remark`          varchar(255) DEFAULT NULL COMMENT '错误提醒',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=47590 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='登录密码输入错误记录';

CREATE TABLE `tstd_marketing_activity_collection`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `ref_type`        varchar(4)     NOT NULL COMMENT '活动类型',
    `ref_id`          bigint(20) NOT NULL COMMENT '活动id',
    `collection_id`   bigint(20) NOT NULL COMMENT '作品编号',
    `collection_name` varchar(64)    NOT NULL COMMENT '作品名称',
    `collection_pic`  varchar(255)   NOT NULL COMMENT '作品图片',
    `price`           decimal(10, 2) NOT NULL DEFAULT '0.00' COMMENT '价格',
    `total_quantity`  int(11) DEFAULT NULL COMMENT '总数量',
    `remain_quantity` int(11) DEFAULT NULL COMMENT '剩余数量',
    `order_no`        int(11) DEFAULT NULL COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='营销活动关联藏品';

CREATE TABLE `tstd_notice_window`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4)    NOT NULL COMMENT '类型 dict={"0":"协议","1":"公告"}',
    `name`            varchar(64)   NOT NULL COMMENT '名称',
    `config_key`      varchar(64)            DEFAULT NULL COMMENT '系统参数key',
    `content`         varchar(1024) NOT NULL COMMENT '公告内容',
    `status`          varchar(4)    NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"下架","1":"上架"}',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)            DEFAULT NULL COMMENT '更新人名',
    `update_datetime` datetime               DEFAULT NULL COMMENT '更新时间',
    `order_no`        int(11) DEFAULT '0' COMMENT '顺序',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='公告弹窗管理';

CREATE TABLE `tstd_notice_window_real_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `notice_id`       bigint(20) DEFAULT NULL COMMENT '弹窗序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index02` (`notice_id`,`user_id`),
    KEY               `index01` (`user_id`),
    KEY               `index` (`notice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tstd_operator_log`
(
    `id`           bigint(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `user_id`      bigint(10) unsigned NOT NULL COMMENT '操作者uid',
    `operate_time` datetime      NOT NULL COMMENT '操作时间',
    `operate_ts`   bigint(20) DEFAULT NULL COMMENT '操作时间戳',
    `ip`           varchar(64)   NOT NULL DEFAULT '0' COMMENT '操作ip',
    `userAgent`    varchar(6000) NOT NULL DEFAULT '' COMMENT '浏览器头信息',
    `url`          varchar(255)  NOT NULL DEFAULT '' COMMENT '操作url',
    `get`          varchar(6000) NOT NULL DEFAULT '' COMMENT '表单get内容',
    `post`         longtext COMMENT '表单post内容',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3099762 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='操作日志';

CREATE TABLE `tstd_pay_record`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `user_id`       bigint(20) NOT NULL COMMENT '用户编号',
    `pay_type`      varchar(64)    NOT NULL COMMENT '支付渠道 wechat、alipy等',
    `pay_method`    varchar(64)    NOT NULL COMMENT '支付方式 app、h5、web等',
    `amount`        decimal(18, 2) NOT NULL COMMENT '支付金额',
    `create_time`   datetime       NOT NULL COMMENT '创建时间',
    `callback_time` datetime                DEFAULT NULL COMMENT '回调时间',
    `status`        varchar(8)     NOT NULL DEFAULT '0' COMMENT '状态dict={"0":"待回调","1":"支付成功","2":"支付失败"}',
    `biz_type`      varchar(8)     NOT NULL COMMENT '业务类型dict={"0":"充值","1":"一口价订单支付"}',
    `biz_code`      bigint(20) NOT NULL COMMENT '关联业务编号',
    `biz_status`    varchar(8)     NOT NULL DEFAULT '0' COMMENT '状态dict={"0":"待处理","1":"已处理"}',
    `request`       varchar(2000)           DEFAULT NULL COMMENT '请求入参',
    `response`      varchar(2000)           DEFAULT NULL COMMENT '返回参数',
    `trade_no`      varchar(200)            DEFAULT NULL COMMENT '第三方订单号',
    `remark`        varchar(255)            DEFAULT NULL COMMENT '备注',
    `user_bind_id`  bigint(20) DEFAULT NULL COMMENT '用户卡bin序号',
    `ref_id`        bigint(20) DEFAULT NULL COMMENT '关联业务序号',
    PRIMARY KEY (`id`),
    KEY             `tstd_pay_record_idx1` (`biz_code`,`biz_type`),
    KEY             `userid_index` (`user_id`,`pay_type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=668954067980263425 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='支付记录';

CREATE TABLE `tstd_sms`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `target`          varchar(32) NOT NULL DEFAULT 'C' COMMENT '针对人群',
    `type`            varchar(4)  NOT NULL DEFAULT '2' COMMENT '消息类型dict={"1":"系统公告","2":"我的消息"}',
    `title`           varchar(255)         DEFAULT NULL COMMENT '标题',
    `content`         longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci,
    `synopsis`        text COMMENT '简介',
    `status`          varchar(4)           DEFAULT '0' COMMENT '状态dict={"0":"草稿","1":"已发送","2":"已撤回"}',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户编号',
    `creator`         varchar(32)          DEFAULT NULL COMMENT '创建人',
    `creator_name`    varchar(32)          DEFAULT NULL COMMENT '创建人名称',
    `create_datetime` datetime             DEFAULT NULL COMMENT '创建时间',
    `updater`         varchar(32)          DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)          DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime             DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)         DEFAULT NULL COMMENT '备注',
    `ref_type`        varchar(32)          DEFAULT NULL COMMENT '关联类型(消息分类编号)',
    `ref_no`          varchar(32)          DEFAULT NULL COMMENT '关联编号(消息分类)',
    `is_read`         varchar(4)  NOT NULL DEFAULT '0' COMMENT '用户是否已读站内信 0：未读，1：已读',
    `is_send`         varchar(4)           DEFAULT '0' COMMENT '是否推送 0:否,1:是',
    `activity_key`    varchar(64)          DEFAULT NULL COMMENT '指定活动入参',
    `activity_action` varchar(64)          DEFAULT NULL COMMENT '指定活动类型',
    `channel_id`      bigint(20) DEFAULT '1' COMMENT '发布渠道方',
    `company_id`      bigint(20) DEFAULT NULL COMMENT '发行方序号',
    PRIMARY KEY (`id`),
    KEY               `idx_userid_status` (`user_id`,`status`),
    KEY               `index3` (`status`,`target`,`user_id`,`type`),
    KEY               `index2` (`type`,`target`),
    KEY               `idx_issend` (`is_send`)
) ENGINE=InnoDB AUTO_INCREMENT=54 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='系统公告';

CREATE TABLE `tstd_sms_company`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_id`        bigint(20) NOT NULL COMMENT '申请发行方',
    `send_type`         varchar(4) CHARACTER SET utf8   NOT NULL COMMENT '推送类型 dict={"0":"全局","1":"相关藏品"}',
    `status`            varchar(4) CHARACTER SET utf8   NOT NULL DEFAULT '0' COMMENT '状态dict={"0":"草稿","1":"已发送","2":"已撤回","3":"审核中","4":"审核成功","5":"审核失败"}',
    `title`             varchar(255) CHARACTER SET utf8 NOT NULL COMMENT '标题',
    `content`           longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
    `is_send`           varchar(4) CHARACTER SET utf8            DEFAULT '0' COMMENT '是否推送 0:否,1:是',
    `ref_collection_id` varchar(255) CHARACTER SET utf8          DEFAULT NULL COMMENT '关联藏品',
    `create_datetime`   datetime                                 DEFAULT NULL COMMENT '创建时间',
    `updater`           bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32) CHARACTER SET utf8           DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`   datetime                                 DEFAULT NULL COMMENT '更新时间',
    `auditr`            bigint(20) DEFAULT NULL COMMENT '审核人',
    `auditr_name`       varchar(32) CHARACTER SET utf8           DEFAULT NULL COMMENT '审核人名称',
    `audit_datetime`    datetime                                 DEFAULT NULL COMMENT '审核时间',
    `send_datetime`     datetime                                 DEFAULT NULL COMMENT '推送时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='发行方公告申请';

CREATE TABLE `tstd_sms_read`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户编号',
    `sms_code`        bigint(20) DEFAULT NULL COMMENT '消息编号',
    `receive_way`     varchar(4) DEFAULT NULL COMMENT '接受方式(站内消息，APP推送,短信)',
    `status`          varchar(4) DEFAULT NULL COMMENT '状态 0-未阅读 1-已阅读 2-已删除',
    `create_datetime` datetime   DEFAULT NULL COMMENT '推送时间',
    `read_datetime`   datetime   DEFAULT NULL COMMENT '阅读时间',
    `delete_datetime` datetime   DEFAULT NULL COMMENT '删除时间',
    PRIMARY KEY (`id`),
    KEY               `idx_smscode_userid` (`sms_code`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3733567 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='公告阅读记录';

CREATE TABLE `tstd_statistics_collection`
(
    `id`                     bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `collection_size`        int(11) DEFAULT NULL COMMENT '藏品数量',
    `collection_total_price` decimal(10, 2) DEFAULT NULL COMMENT '藏品总金额',
    `create_datetime`        date           DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='每日流通藏品汇总';

CREATE TABLE `tstd_statistics_except_user`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4) NOT NULL COMMENT '类型 dict={"0":"统计余额"}',
    `user_id`         bigint(20) NOT NULL COMMENT '排除用户',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime    DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index01` (`type`,`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='统计排除用户';

CREATE TABLE `tstd_subscription_send`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `ref_type`        varchar(4)   DEFAULT NULL COMMENT '关联业务 dict={"0":"期数"}',
    `ref_id`          bigint(20) DEFAULT NULL COMMENT '关联序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户',
    `registration_id` varchar(255) DEFAULT NULL COMMENT '设备序号',
    `status`          varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"待推送","1":"已推送","2":"取消推送"}',
    `send_contnet`    varchar(255) DEFAULT NULL COMMENT '推送内容',
    `send_datetime`   datetime     DEFAULT NULL COMMENT '推送时间',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时机',
    `update_datetime` datetime     DEFAULT NULL COMMENT '发送时机',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`ref_type`,`ref_id`,`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=200 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='订阅推送';

CREATE TABLE `tstd_third_back_record`
(
    `back_request_time` varchar(255) DEFAULT NULL,
    `back_finish_time`  varchar(255) DEFAULT NULL,
    `pay_code`          varchar(255) DEFAULT NULL,
    `back_request_no`   varchar(255) DEFAULT NULL,
    `back_status`       varchar(255) DEFAULT NULL,
    `amount`            varchar(255) DEFAULT NULL,
    `amount1`           varchar(255) DEFAULT NULL,
    `amount2`           varchar(255) DEFAULT NULL,
    `fee`               varchar(255) DEFAULT NULL,
    `sy_jour_no`        varchar(255) DEFAULT NULL,
    `third_jour_no`     varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tstd_third_divide_detail`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `ref_id`          bigint(20) DEFAULT '0' COMMENT '关联id',
    `type`            varchar(4)     DEFAULT NULL COMMENT '分账类型(0=渠道商 1=平台)',
    `order_type`      varchar(4)     DEFAULT NULL COMMENT '分账订单类型',
    `order_id`        bigint(20) DEFAULT '0' COMMENT '订单号',
    `pay_order_no`    varchar(32) NOT NULL COMMENT '支付订单号',
    `pay_third_no`    varchar(32) NOT NULL COMMENT '支付三方订单号',
    `merchant_no`     varchar(32)    DEFAULT NULL COMMENT '商户号',
    `amount`          decimal(18, 2) DEFAULT '0.00' COMMENT '分账金额',
    `create_time`     datetime       DEFAULT NULL COMMENT '分账时间',
    `status`          varchar(4)     DEFAULT NULL COMMENT '状态(0=待处理，1=处理中 2=成功 3=失败)',
    `update_datetime` datetime       DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY               `idx_type_status` (`type`,`status`),
    KEY               `idx_status` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=668947932992774145 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='支付三方分账明细单';

CREATE TABLE `tstd_third_payback_detail`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `order_type`      varchar(4)     DEFAULT NULL COMMENT '订单类型',
    `order_id`        bigint(20) DEFAULT '0' COMMENT '订单号',
    `company_id`      bigint(20) DEFAULT '0' COMMENT '机构编号',
    `merchant_no`     bigint(20) DEFAULT '0' COMMENT '商户号',
    `pay_order_no`    varchar(32) NOT NULL COMMENT '支付订单号',
    `pay_third_no`    varchar(32) NOT NULL COMMENT '支付三方订单号',
    `amount`          decimal(18, 2) DEFAULT '0.00' COMMENT '退款金额',
    `create_time`     datetime       DEFAULT NULL COMMENT '退款申请时间',
    `status`          varchar(4)     DEFAULT NULL COMMENT '状态(0=待处理，1=处理中 2=成功 3=失败)',
    `update_datetime` datetime       DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='订单三方退款明细单';

CREATE TABLE `tstd_transfer`
(
    `id`               bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单编号',
    `user_id`          bigint(20) NOT NULL COMMENT '用户编号',
    `currency`         varchar(64)    NOT NULL COMMENT '币种',
    `amount`           decimal(18, 8) NOT NULL COMMENT '数量',
    `fee`              decimal(18, 8) NOT NULL DEFAULT '0.********' COMMENT '手续费',
    `status`           varchar(8)     NOT NULL COMMENT '状态0 审核中 1审核通过 2审核不通过',
    `group`            varchar(32)             DEFAULT NULL COMMENT '批次号',
    `apply_user`       bigint(20) NOT NULL COMMENT '申请人',
    `apply_datetime`   datetime       NOT NULL COMMENT '申请时间',
    `approve_user`     bigint(20) DEFAULT NULL COMMENT '审批人',
    `approve_datetime` datetime                DEFAULT NULL COMMENT '审批时间',
    `approve_note`     varchar(255)            DEFAULT NULL COMMENT '审批说明',
    `settle_user`      bigint(20) DEFAULT NULL COMMENT '结算人',
    `settle_datetime`  datetime                DEFAULT NULL COMMENT '结算时间',
    `settle_note`      varchar(255)            DEFAULT NULL COMMENT '结算说明',
    `remark`           varchar(255)            DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='转账订单';

CREATE TABLE `tstd_user_apply_invoice_record`
(
    `id`                   bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`              bigint(32) DEFAULT NULL COMMENT '用户id',
    `amount`               decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '开票金额',
    `type`                 varchar(4)              DEFAULT NULL COMMENT '类型dict={"0":"电子发票","1":"纸质发票"}',
    `invoice_type`         varchar(4)              DEFAULT NULL COMMENT '抬头类型dict={"0":"个人","1":"企业单位"}',
    `invoice_name`         varchar(128)            DEFAULT NULL COMMENT '抬头名称',
    `tax_number`           varchar(32)             DEFAULT NULL COMMENT '税号',
    `register_address`     varchar(64)             DEFAULT NULL COMMENT '注册地',
    `register_phone`       varchar(64)             DEFAULT NULL COMMENT '注册电话',
    `open_branch`          varchar(128)            DEFAULT NULL COMMENT '开户支行名称',
    `bank_account`         varchar(64)             DEFAULT NULL COMMENT '银行账号',
    `receive_type`         varchar(4)              DEFAULT NULL COMMENT '接收类型dict={"0":"邮件","1":"手机短信"}',
    `receive_content`      varchar(64)             DEFAULT NULL COMMENT '接收内容',
    `status`               varchar(4)              DEFAULT NULL COMMENT '状态dict={"0":"待开票","1":"已开票","2":"开票失败"}',
    `open_company_type`    varchar(4)              DEFAULT NULL COMMENT '开票方类型dict={"0":"发行方","1":"平台"}',
    `open_company_id`      bigint(32) DEFAULT NULL COMMENT '开票方id',
    `apply_datetime`       datetime                DEFAULT NULL COMMENT '申请时间',
    `open_company_user_id` bigint(32) DEFAULT NULL COMMENT '开票方用户id',
    `open_handle_datetime` datetime                DEFAULT NULL COMMENT '开票处理时间',
    `open_invoice_pdf`     varchar(256)            DEFAULT NULL COMMENT '发票链接',
    `remark`               varchar(256)            DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户申请开票记录';

CREATE TABLE `tstd_user_auth_channel`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道id',
    `channel_uid`     varchar(32) DEFAULT NULL COMMENT '三方渠道用户',
    `create_datetime` datetime    DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_01` (`user_id`,`channel_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户三方渠道关联表';

CREATE TABLE `tstd_user_back`
(
    `id`         bigint(20) NOT NULL AUTO_INCREMENT,
    `user_id`    bigint(20) NOT NULL DEFAULT '0' COMMENT '用户id',
    `login_name` varchar(32) NOT NULL COMMENT '登录名',
    `amount`     decimal(18, 2) DEFAULT NULL COMMENT '金额',
    `remark`     varchar(255)   DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户返回的钱';

CREATE TABLE `tstd_user_bind_card`
(
    `id`                bigint(32) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`           bigint(32) NOT NULL COMMENT '用户编号',
    `channel_bank_id`   bigint(32) DEFAULT NULL COMMENT '渠道银行编号',
    `bank_code`         varchar(32)  NOT NULL COMMENT '银行编号',
    `bank_name`         varchar(255) NOT NULL COMMENT '银行名称',
    `card_no`           varchar(32)  NOT NULL COMMENT '银行卡号',
    `card_type`         varchar(32)  NOT NULL COMMENT '银行卡类型',
    `card_real_name`    varchar(255) NOT NULL COMMENT '银行卡户名',
    `card_id_no`        varchar(255) NOT NULL COMMENT '银行卡身份证',
    `mobile`            varchar(32)  NOT NULL COMMENT '绑定的手机号',
    `pre_sign_code`     varchar(32)  NOT NULL COMMENT '预签约唯一码',
    `sms_check_code`    varchar(32) DEFAULT NULL COMMENT '短信验证码',
    `security_code`     varchar(32) DEFAULT NULL COMMENT '安全码',
    `expiry_date`       varchar(32) DEFAULT NULL COMMENT '有效期',
    `status`            varchar(4)   NOT NULL COMMENT '状态（0预绑定 1已绑定 2已解绑）',
    `protocol_no`       varchar(32) DEFAULT NULL COMMENT '签约协议号',
    `pre_bind_datetime` datetime    DEFAULT NULL COMMENT '预绑定时间',
    `bind_datetime`     datetime    DEFAULT NULL COMMENT '绑卡时间',
    `unbind_datetime`   datetime    DEFAULT NULL COMMENT '解绑时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index_02` (`protocol_no`),
    KEY                 `index_01` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户绑定快捷支付';

CREATE TABLE `tstd_user_invoice`
(
    `id`               bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`             varchar(64)  DEFAULT NULL COMMENT '类型dict={"0":"个人","1":"企业单位"}',
    `name`             varchar(255) DEFAULT NULL COMMENT '抬头名称',
    `tax_no`           varchar(32)  DEFAULT NULL COMMENT '税号',
    `register_address` varchar(64)  DEFAULT NULL COMMENT '注册地',
    `register_phone`   varchar(64)  DEFAULT NULL COMMENT '注册电话',
    `open_branch`      varchar(128) DEFAULT NULL COMMENT '开户支行名称',
    `bank_account`     varchar(64)  DEFAULT NULL COMMENT '银行账号',
    `updater`          bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`     varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`  datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`           varchar(64)  DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户开票抬头';

CREATE TABLE `tstd_user_node_level`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `type`              varchar(32) NOT NULL COMMENT '类型 dict={"0":"会员等级","1":"节点等级"}',
    `way`               varchar(32) NOT NULL COMMENT '设置方式 dict={"0":"程序自动","1":"人工手动"}',
    `node_level_auto`   varchar(32) DEFAULT '0' COMMENT '节点等级（自动）',
    `node_level_manual` varchar(32) DEFAULT '0' COMMENT '节点等级（手动）',
    `create_time`       datetime    NOT NULL COMMENT '创建时间',
    `updater`           bigint(20) DEFAULT NULL COMMENT '更新人',
    `update_time`       datetime    DEFAULT NULL COMMENT '更新时间',
    `location`          varchar(4)  DEFAULT '0' COMMENT '位置',
    `order_no`          int(11) DEFAULT '0' COMMENT '展示顺序',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index2` (`user_id`,`type`),
    KEY                 `tstd_user_node_level_idx1` (`user_id`,`type`,`way`,`node_level_auto`,`node_level_manual`)
) ENGINE=InnoDB AUTO_INCREMENT=350686 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='节点用户';

CREATE TABLE `tstd_user_relation`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`         bigint(32) DEFAULT NULL COMMENT '用户编号',
    `to_user`         bigint(32) DEFAULT NULL COMMENT '关系人编号',
    `type`            varchar(4) DEFAULT NULL COMMENT '关系类型',
    `status`          varchar(4) DEFAULT NULL COMMENT '状态',
    `create_datetime` datetime   DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13472 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tstd_user_reset_redord`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT,
    `content`         varchar(255) DEFAULT NULL COMMENT '重置用户的C端id',
    `creater`         bigint(32) DEFAULT NULL COMMENT '创建人',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户实名批量重置记录';

CREATE TABLE `tstd_version_log`
(
    `id`              bigint(32) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `title`           varchar(255) NOT NULL COMMENT '标题',
    `content`         text         NOT NULL COMMENT '内容',
    `order_no`        int(11) DEFAULT NULL COMMENT 'UI序号',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='版本日志';

CREATE TABLE `tstd_withdraw`
(
    `id`                     bigint(32) NOT NULL COMMENT '编号',
    `account_number`         varchar(32)    NOT NULL COMMENT '账户编号',
    `account_type`           varchar(4)     NOT NULL COMMENT '类别（C端账号，P平台账号）',
    `currency`               varchar(32)    NOT NULL COMMENT '币种',
    `biz_type`               varchar(32)    NOT NULL COMMENT '业务类型（withdraw取现 transfer 内部划转）',
    `amount`                 decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '取现金额',
    `fee`                    decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '手续费',
    `fee_deduction_type`     varchar(4)              DEFAULT NULL COMMENT '手续费抵扣类型(0=不抵扣,1=拥有某藏品)',
    `fee_deduction_ref_id`   varchar(32)             DEFAULT NULL COMMENT '手续费抵扣类关联id',
    `actual_fee`             decimal(18, 2)          DEFAULT NULL COMMENT '实际手续费',
    `actual_amount`          decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '实际到账金额',
    `balance_amount`         decimal(18, 2) NOT NULL COMMENT '提现时账户余额',
    `bill_flag`              varchar(32)             DEFAULT NULL COMMENT '是否开票(1是 0否)',
    `channel_type`           varchar(32)    NOT NULL COMMENT '支付渠道类型（内部账inner 人工线下off_line 支付宝支付alipay 微信公众号支付wechat）',
    `channel_bank_code`      varchar(32)             DEFAULT NULL COMMENT '渠道银行代号',
    `channel_bank`           varchar(32)             DEFAULT NULL COMMENT '渠道银行',
    `channel_account_info`   varchar(255)            DEFAULT NULL COMMENT '支付渠道账号信息',
    `channel_account_number` varchar(255)            DEFAULT NULL COMMENT '支付渠道账号',
    `channel_order`          varchar(255)            DEFAULT NULL COMMENT '支付渠道单号',
    `status`                 varchar(4)     NOT NULL COMMENT '状态（1待审批 2审批不通过 3审批通过待支付 4支付失败 5支付成功）',
    `apply_user`             bigint(32) NOT NULL COMMENT '申请人',
    `apply_user_kind`        varchar(32)             DEFAULT NULL COMMENT '用户类型',
    `apply_note`             varchar(255)            DEFAULT NULL COMMENT '申请说明',
    `apply_datetime`         datetime       NOT NULL COMMENT '申请时间',
    `approve_user`           bigint(32) DEFAULT NULL COMMENT '审批人',
    `approve_note`           varchar(255)            DEFAULT NULL COMMENT '审批说明',
    `approve_datetime`       datetime                DEFAULT NULL COMMENT '审批时间',
    `pay_user`               varchar(255)            DEFAULT NULL COMMENT '支付回录人',
    `pay_note`               varchar(1024)           DEFAULT NULL COMMENT '支付回录说明',
    `pay_fee`                decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '支付渠道手续费',
    `pay_datetime`           datetime                DEFAULT NULL COMMENT '支付回录时间',
    `order_no`               int(11) DEFAULT NULL COMMENT '序号',
    PRIMARY KEY (`id`),
    KEY                      `index3` (`currency`),
    KEY                      `index5` (`account_number`,`status`),
    KEY                      `index4` (`apply_datetime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='提现订单';

CREATE TABLE `tstd_withdraw_apply_by_oss`
(
    `user_id`             bigint(20) NOT NULL,
    `login_name`          varchar(255) DEFAULT NULL,
    `real_name`           varchar(255) DEFAULT NULL,
    `id_no`               varchar(255) DEFAULT NULL,
    `amount`              varchar(255) DEFAULT NULL,
    `available_amount`    varchar(255) DEFAULT NULL,
    `alipay_name`         varchar(255) DEFAULT NULL,
    `alipay_account_info` varchar(255) DEFAULT NULL,
    PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tstd_withdraw_rule`
(
    `id`                         int(11) NOT NULL AUTO_INCREMENT,
    `kind`                       varchar(32)    NOT NULL COMMENT '针对对象',
    `type`                       varchar(32)    NOT NULL COMMENT '类型（转账transfer 取现withdraw）',
    `name`                       varchar(32)    NOT NULL COMMENT '规则名称',
    `symbol`                     varchar(32)    NOT NULL COMMENT '币种',
    `withdraw_min`               decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '最小取现金额',
    `withdraw_max`               decimal(18, 2)          DEFAULT NULL COMMENT '单笔最大提币量',
    `withdraw_step`              decimal(18, 2)          DEFAULT NULL COMMENT '提币步长',
    `withdraw_limit`             decimal(18, 2)          DEFAULT NULL COMMENT '每人每日提现额度',
    `withdraw_fee_take_location` varchar(8)     NOT NULL DEFAULT '0' COMMENT '取现手续费扣除位置: 0取现金额中 1余额中',
    `withdraw_fee_type`          varchar(8)     NOT NULL DEFAULT '0' COMMENT '手续费类型 0=绝对值 1=百分比',
    `withdraw_fee`               decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '取现手续费',
    `approve_flag`               varchar(32)    NOT NULL DEFAULT '1' COMMENT '是否需要审核 0否 1是',
    `withdraw_rule`              varchar(255)            DEFAULT NULL COMMENT '提币规则',
    `withdraw_min1`              decimal(18, 2)          DEFAULT NULL COMMENT '第一次提现最低金额',
    `withdraw_min2`              decimal(18, 2)          DEFAULT NULL COMMENT '第二次提现最低金额',
    `withdraw_min3`              decimal(18, 2)          DEFAULT NULL COMMENT '第三次开始提现最低金额',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index2` (`kind`,`type`,`symbol`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='取现/转账规则';

CREATE TABLE `tsys_action`
(
    `id`     bigint(20) NOT NULL COMMENT 'ID',
    `type`   varchar(255) COLLATE utf8_bin NOT NULL COMMENT '类型',
    `name`   varchar(255) COLLATE utf8_bin NOT NULL COMMENT '名称',
    `code`   varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '操作编码',
    `url`    varchar(255) COLLATE utf8_bin NOT NULL COMMENT '拦截URL',
    `input`  varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '入参',
    `output` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '出参',
    `status` varchar(255) COLLATE utf8_bin NOT NULL COMMENT '状态',
    `remark` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='系统接口';

CREATE TABLE `tsys_article`
(
    `id`              bigint(32) NOT NULL COMMENT '编号',
    `type_id`         bigint(32) NOT NULL COMMENT '类型编号',
    `type`            varchar(32)  NOT NULL COMMENT '文章类型',
    `cover`           varchar(255)          DEFAULT NULL COMMENT '封面图',
    `pic`             text COMMENT '缩略图',
    `title`           varchar(255) NOT NULL COMMENT '标题',
    `subtitle`        varchar(255) NOT NULL COMMENT '副标题',
    `content_type`    varchar(4)   NOT NULL DEFAULT '0' COMMENT '内容类型 0:富文本,1:图片',
    `content`         text         NOT NULL COMMENT '内容',
    `status`          varchar(32)  NOT NULL COMMENT '状态（0下架 1上架）',
    `order_no`        int(11) DEFAULT NULL COMMENT 'UI序号',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道来源',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)           DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime              DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255)          DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='文章';

CREATE TABLE `tsys_article_type`
(
    `id`              bigint(20) NOT NULL,
    `name`            varchar(255) NOT NULL COMMENT '分类名称',
    `type`            varchar(4)   NOT NULL COMMENT '类型 0:关于我们,1:数字学院',
    `icon`            varchar(255) DEFAULT NULL COMMENT '图标',
    `status`          varchar(32)  NOT NULL COMMENT '状态（0下架 1上架）',
    `order_no`        int(11) DEFAULT NULL COMMENT 'UI序号',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)  DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          text COMMENT '备注',
    `location`        varchar(5)   NOT NULL COMMENT '位置',
    `channel_id`      bigint(20) DEFAULT NULL COMMENT '渠道来源',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文章类型';

CREATE TABLE `tsys_channel_config`
(
    `id`           bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `company_id`   bigint(20) DEFAULT NULL COMMENT '所属发行方',
    `channel_name` varchar(64)  DEFAULT NULL COMMENT '渠道名称',
    `click_number` int(11) DEFAULT '0' COMMENT '点击次数',
    `channel_url`  varchar(255) DEFAULT NULL COMMENT 'url',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渠道配置';

CREATE TABLE `tsys_cnavigate`
(
    `id`                  bigint(20) NOT NULL,
    `name`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `type`                varchar(32)                                                  DEFAULT NULL COMMENT '类型',
    `url`                 varchar(255)                                                 DEFAULT NULL COMMENT '访问Url',
    `url_param_flag`      varchar(255)                                                 DEFAULT NULL COMMENT 'url跳转是否需要参数',
    `url_param`           varchar(255)                                                 DEFAULT NULL COMMENT 'url参数',
    `pic`                 varchar(255) NOT NULL COMMENT '图片',
    `status`              varchar(4)   NOT NULL COMMENT '状态(1 已发布,0待发布,2已下架)',
    `location`            varchar(32)                                                  DEFAULT NULL COMMENT '位置',
    `action`              varchar(32)                                                  DEFAULT NULL COMMENT '动作(0不能点击 1跳转本系统 2跳转外部链接 3跳转外部系统(需授权))',
    `group_name`          varchar(32)                                                  DEFAULT NULL COMMENT '分组',
    `order_no`            int(11) DEFAULT NULL COMMENT '相对位置编号',
    `parent_id`           bigint(20) DEFAULT NULL COMMENT '父编号',
    `remark`              varchar(255)                                                 DEFAULT NULL COMMENT '备注',
    `channel_id`          bigint(20) DEFAULT NULL COMMENT '来源渠道',
    `channel_system_code` varchar(32)                                                  DEFAULT NULL COMMENT '渠道编号',
    `clients`             varchar(32)                                                  DEFAULT 'Android,iOS,h5' COMMENT '客户端',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='导航';

CREATE TABLE `tsys_config`
(
    `id`          int(11) NOT NULL AUTO_INCREMENT,
    `type`        varchar(255) COLLATE utf8_bin NOT NULL,
    `key`         varchar(255) COLLATE utf8_bin NOT NULL,
    `value`       longtext COLLATE utf8_bin     NOT NULL,
    `updater`     varchar(255) COLLATE utf8_bin NOT NULL,
    `update_time` timestamp NULL DEFAULT NULL,
    `remark`      varchar(255) COLLATE utf8_bin DEFAULT NULL,
    `front_flag`  varchar(4) COLLATE utf8_bin   DEFAULT NULL COMMENT '前端标识 0:不展示1:展示',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=537 DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='系统参数';

CREATE TABLE `tsys_dict`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT,
    `type`            varchar(32)  DEFAULT NULL COMMENT '类型（0父类 1子类）',
    `parent_key`      varchar(255) DEFAULT NULL COMMENT '父亲key',
    `key`             varchar(255) DEFAULT NULL COMMENT 'key',
    `value`           varchar(255) DEFAULT NULL COMMENT 'value',
    `group_no`        int(11) DEFAULT NULL COMMENT '组号',
    `order_no`        int(11) DEFAULT NULL COMMENT '序号',
    `updater`         varchar(32)  DEFAULT NULL COMMENT '最近修改人',
    `update_datetime` datetime     DEFAULT NULL COMMENT '最近修改人',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1286 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='数据字典';

CREATE TABLE `tsys_group`
(
    `id`          bigint(20) NOT NULL COMMENT '编号',
    `parent_id`   bigint(20) DEFAULT NULL COMMENT '父编号',
    `kind`        varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '类型',
    `name`        varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '名称',
    `order_no`    int(11) DEFAULT NULL COMMENT '展示顺序',
    `creater`     varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '创建人',
    `create_time` timestamp                                        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`     varchar(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL COMMENT '更新人',
    `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL COMMENT '备注',
    `company_id`  bigint(20) DEFAULT NULL COMMENT '公司编号',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户组';

CREATE TABLE `tsys_group_role`
(
    `group_id` bigint(20) NOT NULL COMMENT '用户组编号',
    `role_id`  bigint(20) NOT NULL COMMENT '角色编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户组角色关联表';

CREATE TABLE `tsys_invite_config`
(
    `id`           bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_level`   varchar(32)    NOT NULL COMMENT '用户等级',
    `level`        int(11) NOT NULL COMMENT '第几代用户',
    `rate`         decimal(18, 8) NOT NULL COMMENT '推荐收益比例',
    `rate2`        decimal(18, 8) NOT NULL COMMENT '推荐收益比例(高佣区)',
    `creator`      bigint(32) NOT NULL COMMENT '创建人',
    `creator_name` varchar(32)    NOT NULL COMMENT '创建人名称',
    `create_time`  datetime       NOT NULL COMMENT '创建时间',
    `updater`      bigint(32) DEFAULT NULL COMMENT '最后更新人',
    `updater_name` varchar(32)  DEFAULT NULL COMMENT '最后更新人名称',
    `update_time`  datetime     DEFAULT NULL COMMENT '最后更新时间',
    `remark`       varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='邀请配置';

CREATE TABLE `tsys_language_resource`
(
    `id`      int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `table`   varchar(255) NOT NULL COMMENT '表名',
    `ref_id`  varchar(255) NOT NULL COMMENT '记录编号',
    `column`  varchar(255) NOT NULL COMMENT '列名',
    `zn_data` text COMMENT '中文数据',
    `en_data` text COMMENT '英文数据',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index2` (`table`,`column`,`ref_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='语言资源';

CREATE TABLE `tsys_lock_diy_user`
(
    `login_name` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `tsys_member_config`
(
    `id`             bigint(32) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`           varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '等级名称',
    `level`          varchar(32)                    NOT NULL COMMENT '等级',
    `diamond_number` decimal(18, 4)                 NOT NULL COMMENT '钻石累计总量最小值',
    `fans_number`    int(10) NOT NULL COMMENT '粉丝数量最小值',
    `real_status`    int(1) DEFAULT NULL COMMENT '是否需要实名 0：否 1：是',
    `privilege`      varchar(255)                            DEFAULT NULL COMMENT '特权明细',
    `creator`        varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人',
    `creator_name`   varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名称',
    `create_time`    datetime                       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`        varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '最后更新人',
    `updater_name`   varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '最后更新人名称',
    `update_time`    datetime                                DEFAULT NULL COMMENT '最后更新时间',
    `remark`         varchar(255) CHARACTER SET utf8         DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='会员等级制度配置';

CREATE TABLE `tsys_menu`
(
    `id`         bigint(20) NOT NULL COMMENT '编号',
    `parent_id`  bigint(20) DEFAULT NULL COMMENT '父编号',
    `type`       varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '类型（菜单/按钮）',
    `kind`       varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '端',
    `name`       varchar(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '名称',
    `logo`       varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'logo',
    `url`        varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'url',
    `order_no`   bigint(20) DEFAULT NULL COMMENT '序号',
    `location`   varchar(8)                                       DEFAULT '0' COMMENT '展示位置',
    `remark`     varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '备注',
    `admin_flag` varchar(4) CHARACTER SET utf8 COLLATE utf8_bin   DEFAULT '0' COMMENT '超管标识',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='菜单';

CREATE TABLE `tsys_menu_action`
(
    `menu_id`   bigint(20) NOT NULL COMMENT '菜单编号',
    `action_id` bigint(20) NOT NULL COMMENT '资源编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='菜单资源关联表';

CREATE TABLE `tsys_node_config`
(
    `id`                        bigint(32) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name`                      varchar(32)    NOT NULL COMMENT '节点名称',
    `level`                     varchar(32)    NOT NULL COMMENT '节点星级',
    `team_performance`          decimal(18, 8) NOT NULL COMMENT '团队业绩最小值',
    `team_number`               int(11) NOT NULL COMMENT '团队有效人数',
    `first_team_number`         int(11) NOT NULL COMMENT '直推团队最小数量',
    `first_team_performance`    decimal(18, 8) NOT NULL COMMENT '直推团队业绩最小值',
    `first_person_level`        int(11) NOT NULL COMMENT '直推人员星级',
    `first_person_level_number` int(11) NOT NULL COMMENT '直推人员星级节点人数最小值',
    `rate`                      decimal(18, 2) NOT NULL COMMENT '节点收益比例1',
    `rate1`                     decimal(18, 2) NOT NULL COMMENT '节点收益比例2',
    `creator`                   varchar(32)    NOT NULL COMMENT '创建人',
    `creator_name`              varchar(32)    NOT NULL COMMENT '创建人名称',
    `create_time`               datetime       NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updater`                   varchar(32)             DEFAULT NULL COMMENT '最后更新人',
    `updater_name`              varchar(32)             DEFAULT NULL COMMENT '最后更新人名称',
    `update_time`               datetime                DEFAULT NULL COMMENT '最后更新时间',
    `remark`                    varchar(255)            DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='星级节点配置';

CREATE TABLE `tsys_notifier`
(
    `id`         int(11) NOT NULL AUTO_INCREMENT,
    `type`       varchar(8)  NOT NULL COMMENT '类型: 0提币通知人',
    `start_date` tinyint(3) unsigned NOT NULL COMMENT '开始时间0-23',
    `end_date`   tinyint(3) unsigned NOT NULL COMMENT '结束时间0-23',
    `name`       varchar(32) NOT NULL COMMENT '姓名',
    `phone`      varchar(32) DEFAULT NULL COMMENT '手机号码',
    `email`      text COMMENT '邮箱',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='通知人';

CREATE TABLE `tsys_permission_role`
(
    `role_id`       bigint(20) NOT NULL COMMENT '角色编号',
    `resource_id`   bigint(20) NOT NULL COMMENT '资源编号',
    `resource_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT '资源类型',
    UNIQUE KEY `index1` (`role_id`,`resource_id`,`resource_type`),
    KEY             `index2` (`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色权限关联表';

CREATE TABLE `tsys_role`
(
    `id`          bigint(20) NOT NULL,
    `name`        char(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `kind`        char(255) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL,
    `creator`     char(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL,
    `create_time` timestamp NULL DEFAULT NULL,
    `updater`     char(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL,
    `update_time` timestamp NULL DEFAULT NULL,
    `remark`      char(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL,
    `company_id`  bigint(20) DEFAULT NULL,
    `alias`       char(255) CHARACTER SET utf8 COLLATE utf8_bin          DEFAULT NULL,
    `is_default`  char(8) CHARACTER SET utf8 COLLATE utf8_bin   NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='角色';

CREATE TABLE `tsys_user`
(
    `id`                  bigint(20) NOT NULL COMMENT '编号',
    `kind`                varchar(32)    NOT NULL COMMENT '类型',
    `type`                varchar(32)             DEFAULT NULL COMMENT '创作者类型(0=机构 1=艺术家)',
    `meta_role_id`        bigint(20) DEFAULT NULL COMMENT '元宇宙关联角色',
    `login_name`          varchar(32)    NOT NULL COMMENT '登录名称',
    `nickname`            varchar(32)             DEFAULT NULL COMMENT '昵称',
    `mobile`              varchar(32)             DEFAULT NULL COMMENT '手机号',
    `email`               varchar(32)             DEFAULT NULL COMMENT '邮箱',
    `photo`               varchar(1024)           DEFAULT NULL COMMENT '头像',
    `sex`                 varchar(8)              DEFAULT NULL COMMENT '性别',
    `age`                 int(11) DEFAULT NULL COMMENT '年龄',
    `identify_style`      varchar(4)              DEFAULT '0' COMMENT '实名跳转方式(0=人脸识别 1=人工提交认证)',
    `id_kind`             varchar(4)              DEFAULT NULL COMMENT '证件类型 dict={"1":"身份证","2":"护照"}',
    `id_no`               varchar(64)             DEFAULT NULL COMMENT '证件号码',
    `real_name`           varchar(32)             DEFAULT NULL COMMENT '真实名称',
    `province`            varchar(32)             DEFAULT NULL COMMENT '省',
    `city`                varchar(32)             DEFAULT NULL COMMENT '市',
    `area`                varchar(32)             DEFAULT NULL COMMENT '区',
    `address`             varchar(255)            DEFAULT NULL COMMENT '详细地址',
    `willing_value`       decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '愿力值余额',
    `login_pwd`           varchar(255)   NOT NULL COMMENT '登录密码',
    `login_pwd_strength`  varchar(4)     NOT NULL COMMENT '登录密码强度',
    `salt`                varchar(6)              DEFAULT NULL COMMENT '干扰码',
    `trade_pwd`           varchar(255)            DEFAULT NULL COMMENT '交易密码',
    `trade_pwd_strength`  varchar(4)              DEFAULT NULL COMMENT '交易密码强度',
    `status`              varchar(32)    NOT NULL COMMENT '状态',
    `login_status`        varchar(32)    NOT NULL DEFAULT '0' COMMENT '登录状态状态 0:正常,1:验证码,2:锁定中',
    `recovery_status`     varchar(4)     NOT NULL DEFAULT '0' COMMENT '清退状态 dict={"0":"正常","1":"已清退"}',
    `identify_status`     varchar(4)     NOT NULL DEFAULT '0' COMMENT '实名状态 dict={"0":"未认证","1":"三方认证成功","2":"三方认证失败待人工认证","3":"人工认证中","4":"人工认证通过","5":"人工认证失败","6":"平台重置","7":"h5认证成功"}',
    `grouping`            varchar(64)             DEFAULT NULL COMMENT '分组',
    `register_datetime`   datetime       NOT NULL COMMENT '创建时间',
    `register_ip`         varchar(32)             DEFAULT NULL COMMENT '注册IP',
    `invite_no`           int(11) NOT NULL AUTO_INCREMENT COMMENT '邀请码',
    `user_referee`        bigint(20) DEFAULT NULL COMMENT '推荐人',
    `introduce`           text COMMENT '个人介绍',
    `remark`              varchar(255)            DEFAULT NULL COMMENT '备注',
    `last_login_datetime` datetime                DEFAULT NULL COMMENT '最后登录时间',
    `updater`             bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`        varchar(32)             DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`     datetime                DEFAULT NULL COMMENT '更新时间',
    `company_id`          bigint(20) NOT NULL DEFAULT '-1' COMMENT '公司编号',
    `author_rate`         decimal(18, 2)          DEFAULT NULL COMMENT '机构分成比例',
    `black_times`         int(4) DEFAULT '0' COMMENT '黑名单次数',
    `channel_id`          bigint(20) DEFAULT NULL COMMENT '渠道群主fuserId',
    `channel_flag`        varchar(255)            DEFAULT NULL COMMENT '渠道标记',
    `channel_type`        varchar(1)              DEFAULT NULL COMMENT '用户归属渠道商类型 0=个人渠道商 1=机构渠道商',
    `is_channel`          varchar(4)     NOT NULL DEFAULT '0' COMMENT '是否为渠道',
    `official_kol_flag`   varchar(4)              DEFAULT NULL COMMENT '官方指定KOL标志(0=否 1=是)',
    `block_address`       varchar(255)            DEFAULT NULL COMMENT '区块链地址',
    `google_secret`       varchar(32)             DEFAULT '0' COMMENT 'google验证码',
    `tag`                 varchar(32)             DEFAULT NULL COMMENT '标签',
    `channel_code`        varchar(32)             DEFAULT NULL,
    `relation_company_id` bigint(20) DEFAULT NULL COMMENT '关联发行方',
    `channel_merchant_id` bigint(20) DEFAULT NULL COMMENT '来源渠道',
    `channel_uid`         varchar(32)             DEFAULT NULL COMMENT '渠道uid',
    `yao_flag`            varchar(4)              DEFAULT '0' COMMENT '开通爻账户标识 0:未开通,1:已开通',
    `series_no`           bigint(20) DEFAULT NULL COMMENT '关联单号',
    PRIMARY KEY (`id`),
    UNIQUE KEY `invite_no_UNIQUE` (`invite_no`),
    UNIQUE KEY `index1` (`kind`,`mobile`),
    UNIQUE KEY `block_address_UNIQUE` (`block_address`),
    KEY                   `index_referee` (`user_referee`),
    KEY                   `tsys_user_idx2` (`kind`,`login_name`,`login_pwd`),
    KEY                   `idx_channelid_channeltype` (`channel_id`,`channel_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1000341188 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户';

CREATE TABLE `tsys_user_bak`
(
    `id`       bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`  bigint(20) DEFAULT NULL,
    `quantity` int(11) DEFAULT NULL COMMENT '数量',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18557 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户';

CREATE TABLE `tsys_user_group`
(
    `user_id`  bigint(20) NOT NULL COMMENT '用户编号',
    `group_id` bigint(20) NOT NULL COMMENT '用户组编号'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户用户组管理表';

CREATE TABLE `tsys_user_log`
(
    `id`              int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID主键',
    `user_id`         varchar(32)  NOT NULL COMMENT '用户编号',
    `user_name`       varchar(64)  NOT NULL COMMENT '用户名称',
    `content`         varchar(255) NOT NULL COMMENT '内容',
    `type`            varchar(32)  NOT NULL COMMENT '分类',
    `type_note`       varchar(32)  NOT NULL COMMENT '类型说明',
    `ip`              varchar(255) DEFAULT NULL COMMENT 'ip',
    `client`          varchar(7)   DEFAULT NULL COMMENT '客户端（1Web 2Android 3IOS）',
    `location`        varchar(255) DEFAULT NULL COMMENT '操作时定位',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间时间',
    PRIMARY KEY (`id`),
    KEY               `index_utype` (`user_id`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=1627080 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户日志';

CREATE TABLE `tsys_user_recovery_collection`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `recovery_id`          bigint(20) DEFAULT NULL COMMENT '清退记录序号',
    `user_id`              bigint(20) DEFAULT NULL COMMENT '清退用户',
    `collection_id`        bigint(20) DEFAULT NULL COMMENT '作品序号',
    `collection_detail_id` bigint(20) DEFAULT NULL COMMENT '藏品序号',
    `collection_name`      varchar(100) DEFAULT NULL COMMENT '藏品名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='清退藏品';

CREATE TABLE `tsys_user_recovery_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '清退用户',
    `cny_amount`      decimal(18, 2) DEFAULT NULL COMMENT '清退人名币',
    `diamond_amount`  decimal(18, 2) DEFAULT NULL COMMENT '清退钻石',
    `creater`         bigint(20) DEFAULT NULL COMMENT '操作人',
    `creater_name`    varchar(32)    DEFAULT NULL COMMENT '操作人名称',
    `create_datetime` datetime       DEFAULT NULL COMMENT '操作时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='平台清退用户记录';

CREATE TABLE `tsys_user_role`
(
    `user_id` bigint(20) NOT NULL COMMENT '用户编号',
    `role_id` bigint(20) NOT NULL COMMENT '角色编号',
    PRIMARY KEY (`user_id`, `role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户角色关联表';

CREATE TABLE `tsys_user_sms_notice`
(
    `id`         bigint(20) NOT NULL COMMENT '编号',
    `login_name` varchar(32) DEFAULT NULL COMMENT '登录名',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='用户短信通知';

CREATE TABLE `ttask_statistic`
(
    `id`                       bigint(20) NOT NULL AUTO_INCREMENT,
    `new_user_count`           int(11) NOT NULL COMMENT '新增用户数',
    `active_user_count`        int(11) NOT NULL COMMENT '活跃用户数',
    `user_count`               bigint(20) NOT NULL COMMENT '截至当日用户总数',
    `charge_total`             decimal(18, 8) NOT NULL COMMENT '当日充值(usdt)',
    `turn_out_total`           decimal(18, 8) NOT NULL COMMENT '当日提取(usdt)',
    `net_total`                decimal(18, 8) NOT NULL COMMENT '净流入(usdt)',
    `balance_total`            decimal(18, 8) NOT NULL COMMENT '总余额(usdt)',
    `entrust_amount`           decimal(18, 8) NOT NULL COMMENT '当前持仓金额(usdt)',
    `entrust_amount_history`   decimal(18, 8) NOT NULL COMMENT '历史持仓金额(usdt)',
    `operate_amount`           decimal(18, 8) NOT NULL COMMENT '总操盘资金',
    `business_amount`          decimal(18, 8) NOT NULL COMMENT '总运营资金',
    `cycle_count`              int(11) NOT NULL COMMENT '当前仓位个数',
    `cycle_history_count`      int(11) NOT NULL COMMENT '历史仓位个数',
    `cycle_count_total`        int(11) NOT NULL COMMENT '总仓位个数',
    `profit_amount`            decimal(18, 8) NOT NULL COMMENT '用户总盈利',
    `commission_amount`        decimal(18, 8) NOT NULL COMMENT '平台佣金收入',
    `commission_out`           decimal(18, 8) NOT NULL COMMENT '分佣总支出',
    `new_user_operate_amount`  decimal(18, 8) NOT NULL COMMENT '新用户新增操盘资金',
    `old_user_operate_amount`  decimal(18, 8) NOT NULL COMMENT '老用户新增操盘资金',
    `total_add_operate_amount` decimal(18, 8) NOT NULL COMMENT '当日新增操盘资金',
    `create_time`              timestamp NULL DEFAULT NULL COMMENT '日期',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='每日报表';

CREATE TABLE `yg_fish_boat`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`              varchar(255)   NOT NULL COMMENT '名称',
    `type`              varchar(4)     NOT NULL DEFAULT '0' COMMENT '类型 dict={"0":"小渔船","1":"钓鱼船","2":"远洋舰"}',
    `region_type`       varchar(4)     NOT NULL DEFAULT '0' COMMENT '捕鱼区域 dict={"0":"不可捕鱼","1":"近海捕鱼","2":"远海捕鱼"}',
    `image`             varchar(255)   NOT NULL COMMENT '图片',
    `speed`             decimal(10, 2) NOT NULL COMMENT '速度(公里/小时)',
    `endurance_time`    decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '续航时间(小时)',
    `endurance_mileage` decimal(10, 2) NOT NULL COMMENT '续航里程(公里)',
    `fishing_time`      int(11) NOT NULL DEFAULT '0' COMMENT '捕鱼间隔时间(分钟)',
    `deadweight`        decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '载重量(公斤)',
    `deadweight_once`   decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '单次捕鱼满载量(公斤)',
    `load_factor`       decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '单次满载率',
    `sail_num_month`    int(11) NOT NULL DEFAULT '0' COMMENT '月度出海次数',
    `status`            varchar(4)     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `creater`           bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`      varchar(32)             DEFAULT NULL COMMENT '创建人名称',
    `create_datetime`   datetime                DEFAULT NULL COMMENT '创建时间',
    `updater`           bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32)             DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`   datetime                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='渔光渔船';

CREATE TABLE `yg_fish_boat_user`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`                 varchar(64) DEFAULT NULL COMMENT '名称',
    `user_id`              bigint(20) NOT NULL COMMENT '用户ID',
    `boat_id`              bigint(20) NOT NULL COMMENT '船类型ID',
    `boat_type`            varchar(4)                     NOT NULL COMMENT '船类型',
    `collection_id`        bigint(20) NOT NULL COMMENT '作品ID',
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品ID',
    `status`               varchar(4)  DEFAULT NULL COMMENT '状态 dict={"0":"空闲","1":"出海"}',
    `get_time`             datetime                       NOT NULL COMMENT '获得日期',
    `creater`              bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`         varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名',
    `create_datetime`      datetime    DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COMMENT='渔光用户渔船';

CREATE TABLE `yg_fish_buy_order`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户ID',
    `biz_id`          bigint(20) NOT NULL COMMENT '关联ID',
    `biz_type`        varchar(4)     NOT NULL COMMENT '关联类型 dict={"0":"祈福","1":"灯光秀"}',
    `price`           decimal(18, 2) NOT NULL         DEFAULT '0.00' COMMENT '价格',
    `quantity`        int(11) NOT NULL COMMENT '数量',
    `pay_amount`      decimal(18, 2) NOT NULL         DEFAULT '0.00' COMMENT '支付金额',
    `pay_order_code`  varchar(32)                     DEFAULT NULL COMMENT '支付订单号',
    `pay_type`        varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '支付方式 dict={"0":"贝壳支付"}',
    `pay_status`      varchar(32) CHARACTER SET utf8  DEFAULT NULL COMMENT '支付状态 dict={"0":"待支付","1":"已支付","2":"支付失败"}',
    `create_datetime` datetime       NOT NULL COMMENT '创建时间',
    `pay_datetime`    datetime                        DEFAULT NULL COMMENT '支付时间',
    `remarks`         varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='渔光购买订单';

CREATE TABLE `yg_fish_ocean_pond`
(
    `id`                 bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `name`               varchar(255)   NOT NULL COMMENT '名称',
    `type`               varchar(4)     NOT NULL COMMENT '类型 dict={"0":"近海鱼","1":"深海鱼"}',
    `image`              varchar(255)   NOT NULL COMMENT '图片',
    `total_quantity`     decimal(10, 3) NOT NULL COMMENT '总投放量(KG)',
    `remaining_quantity` decimal(10, 3) NOT NULL COMMENT '剩余数量(KG)',
    `is_repurchase`      varchar(4)     NOT NULL DEFAULT '0' COMMENT '是否官方回购 0-否 1-是',
    `repurchase_price`   decimal(10, 2)          DEFAULT NULL COMMENT '回购价格 贝/公斤',
    `status`             varchar(4)     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `creater`            bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`       varchar(32)             DEFAULT NULL COMMENT '创建人名称',
    `create_datetime`    datetime                DEFAULT NULL COMMENT '创建时间',
    `updater`            bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`       varchar(32)             DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`    datetime                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='渔光海洋鱼池';

CREATE TABLE `yg_fish_pond_jour`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `variety_id`        varchar(32)                                                   DEFAULT NULL COMMENT '鱼货id',
    `biz_category`      varchar(32)    NOT NULL COMMENT '业务大类',
    `biz_category_note` varchar(32)    NOT NULL COMMENT '业务大类',
    `biz_type`          varchar(255)   NOT NULL COMMENT '业务小类',
    `biz_note`          varchar(255)   NOT NULL COMMENT '业务小类说明',
    `ref_no`            varchar(255)   NOT NULL COMMENT '系统内部参考订单号',
    `trans_amount`      decimal(18, 8) NOT NULL COMMENT '变动金额',
    `pre_amount`        decimal(18, 8)                                                DEFAULT NULL COMMENT '变动前金额',
    `post_amount`       decimal(18, 8)                                                DEFAULT NULL COMMENT '变动后金额',
    `prev_jour_code`    varchar(32)                                                   DEFAULT NULL COMMENT '上一条流水编号',
    `status`            varchar(4)                                                    DEFAULT NULL COMMENT '状态 ',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_datetime`   datetime       NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=669365697448779777 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='鱼货账户流水';

CREATE TABLE `yg_fish_pond_sale_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `variety_id`        bigint(20) NOT NULL COMMENT '鱼品种ID',
    `repurchase_price`  decimal(10, 2) NOT NULL COMMENT '回购价格 贝/公斤',
    `repurchase_number` decimal(18, 2) NOT NULL COMMENT '回收数量',
    `total_amount`      decimal(18, 2) NOT NULL COMMENT '回收总金额',
    `create_datetime`   datetime       NOT NULL COMMENT '创建时间',
    `user_id`           bigint(20) NOT NULL COMMENT '用户ID',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8 COMMENT='渔光鱼货出售记录';

CREATE TABLE `yg_fish_pond_user`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) NOT NULL COMMENT '用户ID',
    `variety_id`      bigint(20) NOT NULL COMMENT '鱼品种ID',
    `total_quantity`  decimal(10, 2) NOT NULL COMMENT '总数量(KG)',
    `create_datetime` datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE,
    UNIQUE KEY `index` (`user_id`,`variety_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4376 DEFAULT CHARSET=utf8mb4 COMMENT='渔光用户鱼塘';

CREATE TABLE `yg_fish_pray_prop`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4) NOT NULL COMMENT '类型 dict={"0":"好运","1":"恋爱","2":"健康","3":"事业","4":"财运","5":"平安"}',
    `name`            varchar(32)         DEFAULT NULL COMMENT '名称',
    `image`           varchar(255)        DEFAULT NULL COMMENT '图片',
    `price`           decimal(10, 2)      DEFAULT NULL COMMENT '价格',
    `total_quantity`  int(10) NOT NULL DEFAULT '1' COMMENT '总数量',
    `remain_quantity` int(10) NOT NULL DEFAULT '1' COMMENT '剩余数量',
    `start_datetime`  datetime            DEFAULT NULL COMMENT '灯光秀开始时间',
    `end_datetime`    datetime            DEFAULT NULL COMMENT '灯光秀结束时间',
    `status`          varchar(4) NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"待上架","1":"已上架","2":"已下架"}',
    `order_no`        int(11) NOT NULL DEFAULT '0' COMMENT '排序字段(越大越靠前)',
    `creater`         bigint(20) DEFAULT NULL COMMENT '创建人',
    `creater_name`    varchar(32)         DEFAULT NULL COMMENT '创建人名称',
    `create_datetime` datetime            DEFAULT NULL COMMENT '创建时间',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)         DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime            DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='渔光祈福道具/门票';

CREATE TABLE `yg_fish_pray_tree_position`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `position`        varchar(255) NOT NULL           DEFAULT '0' COMMENT '位置',
    `status`          varchar(4)   NOT NULL           DEFAULT '0' COMMENT '状态 dict={"0":"未使用","1":"使用中"}',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户ID',
    `prop_id`         bigint(20) DEFAULT NULL COMMENT '祈福道具ID',
    `ref_no`          varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '系统内部参考订单号',
    `create_datetime` datetime                        DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime                        DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51 DEFAULT CHARSET=utf8mb4 COMMENT='渔光祈福树点位';

CREATE TABLE `yg_fish_role`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `sex`             varchar(255) NOT NULL DEFAULT '0' COMMENT '性别 0:男,1:女',
    `type`            varchar(255) NOT NULL DEFAULT '0' COMMENT '类型 dict={"0":"岛民","1":"渔民","2":"商人"}',
    `name`            varchar(32)  NOT NULL DEFAULT '' COMMENT '名称',
    `is_catch_fish`   varchar(4)   NOT NULL DEFAULT '0' COMMENT '是否具有捕鱼权限 0-否 1-是',
    `is_boat_race`    varchar(4)   NOT NULL DEFAULT '0' COMMENT '是否具有赛船权限 0-否 1-是',
    `is_manage`       varchar(4)   NOT NULL COMMENT '是否具有经营权限 0-否 1-是',
    `updater`         bigint(20) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32)           DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime              DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COMMENT='渔光角色';

CREATE TABLE `yg_fish_sail_record`
(
    `id`                    bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`               bigint(20) NOT NULL COMMENT '用户ID',
    `user_boat_id`          bigint(20) NOT NULL COMMENT '用户船ID',
    `boat_id`               bigint(20) NOT NULL COMMENT '渔船ID',
    `boat_type`             varchar(4)     NOT NULL DEFAULT '0' COMMENT '类型 dict={"0":"小渔船","1":"钓鱼船","2":"远洋舰"}',
    `region_type`           varchar(4)     NOT NULL DEFAULT '0' COMMENT '捕鱼区域 dict={"0":"不可捕鱼","1":"近海捕鱼","2":"远海捕鱼"}',
    `speed`                 decimal(10, 2) NOT NULL COMMENT '速度(海里/小时)',
    `endurance_time`        int(11) NOT NULL DEFAULT '0' COMMENT '续航时间(小时)',
    `endurance_mileage`     decimal(10, 2) NOT NULL COMMENT '续航里程',
    `fishing_time`          int(11) NOT NULL DEFAULT '0' COMMENT '捕鱼间隔时间(分钟)',
    `deadweight`            decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '载重量',
    `deadweight_once`       decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '单次捕鱼满载量',
    `load_factor`           decimal(18, 2) NOT NULL DEFAULT '0.00' COMMENT '单次满载率',
    `order_no`              int(11) DEFAULT '0' COMMENT '当前已捕次数',
    `status`                varchar(4)     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"已出海","1":"捕鱼中","2":"已返航"}',
    `sail_datetime`         datetime       NOT NULL COMMENT '出海时间',
    `next_fishing_datetime` datetime                DEFAULT NULL COMMENT '下次捕鱼时间',
    `back_datetime`         datetime                DEFAULT NULL COMMENT '返航时间',
    `actual_back_datetime`  datetime                DEFAULT NULL COMMENT '实际返航时间',
    `create_time`           bigint(20) NOT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=37 DEFAULT CHARSET=utf8mb4 COMMENT='渔光出海记录';

CREATE TABLE `yg_fish_sail_record_detail`
(
    `id`                  bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `frequency_record_id` bigint(20) NOT NULL COMMENT '捕鱼记录ID',
    `variety_id`          bigint(20) NOT NULL COMMENT '鱼品种ID',
    `order_no`            int(11) DEFAULT '0' COMMENT '当前捕鱼次数',
    `fishing_quantity`    decimal(10, 2) NOT NULL COMMENT '捕捞数量(KG)',
    `total_quantity`      decimal(10, 2) DEFAULT NULL COMMENT '本次捕捞数量(KG)',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=222 DEFAULT CHARSET=utf8mb4 COMMENT='渔光捕鱼记录详情';

CREATE TABLE `yg_fish_transfer_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `from_user_id`    bigint(20) NOT NULL COMMENT '来源用户序号',
    `to_user_id`      bigint(20) NOT NULL COMMENT '新用户序号',
    `currency`        varchar(32)    NOT NULL COMMENT '币种',
    `quantity`        decimal(18, 2) NOT NULL        DEFAULT '0.00' COMMENT '数量',
    `keyword`         varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '转赠查询关键字',
    `create_datetime` datetime                       DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=668880943699992577 DEFAULT CHARSET=utf8mb4 COMMENT='渔光贝壳转增记录';

CREATE TABLE `yg_fish_user_exhibits`
(
    `id`                   bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`              bigint(20) NOT NULL COMMENT '用户ID',
    `collection_id`        bigint(20) NOT NULL COMMENT '作品id',
    `collection_detail_id` bigint(20) NOT NULL COMMENT '藏品id',
    `position`             int(11) NOT NULL COMMENT '位置',
    `create_datetime`      datetime DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `index` (`user_id`,`position`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='渔光用户展品';

CREATE TABLE `yg_fish_user_log`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`            varchar(4) DEFAULT NULL COMMENT '类型 dict={"0":"登录","1":"观看灯光秀"}',
    `status`          varchar(4) DEFAULT NULL COMMENT '状态 dict={"0":"进行中","1":"已完成"}',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '用户序号',
    `role_id`         bigint(20) DEFAULT NULL COMMENT '角色序号',
    `online_duration` int(11) DEFAULT '0' COMMENT '在线时长(分钟)',
    `create_datetime` datetime   DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime   DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=105 DEFAULT CHARSET=utf8 COMMENT='渔光用户操作日志';

CREATE TABLE `yg_shell_invitation_user_record`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `user_id`         bigint(20) DEFAULT NULL COMMENT '新用户',
    `user_referee`    bigint(20) DEFAULT NULL COMMENT '推荐人',
    `status`          varchar(4)   DEFAULT NULL COMMENT '状态 dict={"0":"待实名","1":"已实名","2":"已购买"}',
    `create_datetime` datetime     DEFAULT NULL COMMENT '创建时间',
    `update_datetime` datetime     DEFAULT NULL COMMENT '更新时间',
    `remark`          varchar(255) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`),
    KEY               `index` (`user_id`) USING BTREE,
    KEY               `index01` (`user_referee`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='用户拉新记录';

CREATE TABLE `yg_shell_login_rule`
(
    `id`              bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `day`             int(11) NOT NULL DEFAULT '0' COMMENT '天数',
    `amount`          decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '获得渔贝数量',
    `extra_amount`    decimal(18, 2)                 NOT NULL DEFAULT '0.00' COMMENT '额外获得渔贝数量',
    `status`          varchar(4)                     NOT NULL DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `creater`         bigint(32) NOT NULL COMMENT '创建人',
    `creater_name`    varchar(32) CHARACTER SET utf8 NOT NULL COMMENT '创建人名称',
    `create_datetime` datetime                       NOT NULL COMMENT '创建时间',
    `updater`         bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`    varchar(32) CHARACTER SET utf8          DEFAULT NULL COMMENT '更新人名称',
    `update_datetime` datetime                                DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='连续登录送渔贝规则';

CREATE TABLE `yg_shell_task_config`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '序号',
    `type`              varchar(4) NOT NULL COMMENT '类型 dict={"0":"登录","1":"购买藏品","2":"元宇宙在线","3":"邀请好友并实名","4":"邀请好友并实名购买"}',
    `status`            varchar(4) NOT NULL            DEFAULT '0' COMMENT '状态 dict={"0":"关闭","1":"开启"}',
    `online_duration`   int(11) DEFAULT '0' COMMENT '在线时长(分钟)',
    `invitation_number` int(11) DEFAULT '0' COMMENT '邀请好友数',
    `amount_spent`      decimal(18, 2)                 DEFAULT '0.00' COMMENT '消耗的金额',
    `amount`            decimal(18, 2)                 DEFAULT '0.00' COMMENT '获得渔贝数量',
    `updater`           bigint(32) DEFAULT NULL COMMENT '更新人',
    `updater_name`      varchar(32) CHARACTER SET utf8 DEFAULT NULL COMMENT '更新人名称',
    `update_datetime`   datetime                       DEFAULT NULL COMMENT '更新时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='渔贝获取任务配置';

CREATE TABLE `yg_shell_task_record`
(
    `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
    `user_id`           bigint(20) NOT NULL COMMENT '用户编号',
    `currency`          varchar(32)  NOT NULL COMMENT '币种',
    `biz_category`      varchar(32)  NOT NULL COMMENT '业务大类',
    `biz_category_note` varchar(32)  NOT NULL COMMENT '业务大类',
    `biz_type`          varchar(255) NOT NULL COMMENT '业务小类',
    `biz_note`          varchar(255) NOT NULL COMMENT '业务小类说明',
    `ref_no`            varchar(64)  NOT NULL COMMENT '系统内部参考订单号',
    `amount`            decimal(18, 2)                                                DEFAULT '0.00' COMMENT '变动金额',
    `remark`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
    `create_datetime`   datetime     NOT NULL COMMENT '创建时间',
    `create_time`       bigint(20) NOT NULL COMMENT '创建时间',
    `series_no`         bigint(20) DEFAULT NULL COMMENT '关联单号',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=80 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='渔贝周期任务记录';
