package com.std.core;

import com.github.pagehelper.PageHelper;
import com.std.core.config.AliOSSConfig;
import com.std.core.config.AliOcrConfig;
import com.std.core.config.JiGuangConfig;
import com.std.core.enums.ECurrency;
import com.std.core.mapper.JpushRecordMapper;
import com.std.core.service.IAliOcrService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.IIdentifyOrderService;
import com.std.core.service.IScheduleService;
import com.std.core.service.IUserService;
import com.std.core.service.IUserSettleRecordService;
import com.std.core.util.ChuanglanUtil.IdNoCheckUtil;
import com.std.core.util.jiguang.JiGuangSendUtil;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Scanner;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.bind.annotation.RestController;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RestController
@Slf4j
public class Test {

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private IUserService userService;

    @Resource
    private IScheduleService scheduleService;
    @Resource
    private IUserSettleRecordService userSettleRecordService;

    @Resource
    private IIdentifyOrderService identifyOrderService;

    @Resource
    private AliOcrConfig aliOcrConfig;

    @Resource
    private AliOSSConfig aliOSSConfig;

    @Resource
    private IAliOcrService aliOcrService;

    @Resource
    private ICollectionRightsDetailService collectionRightsDetailService;
    @Resource
    private JpushRecordMapper jpushRecordMapper;

    @Resource
    private JiGuangConfig jiGuangConfig;

    @Resource
    private IdNoCheckUtil idNoCheckUtil;

    @org.junit.Test
    public void test() {
        scheduleService.doSynchronousCompanyYeepaySettleRecord();
    }

    @org.junit.Test
    public void test01() {
//        identifyOrderService.checkImage("330281199802063318", "杨成炯", "https://metat.oss-accelerate.aliyuncs.com/ANDROID_1653482710882_4000_3000.jpg");
//        collectionRightsDetailService.dealHistoryDate();
        if (JiGuangSendUtil.deleteAlias(jiGuangConfig.getMASTER_SECRET(), jiGuangConfig.getAPP_KEY(), "503356083155509248")) {
            System.out.println("true");
        }
    }
    

    @org.junit.Test
    public void collectionDetailOrderNumberDeal() {
        int i = 1;

        while (true) {
            log.info("当前查询次数：" + i);
            PageHelper.startPage(i, 200);
            List<Long> collectionIdList = collectionDetailService.detailCollectionIdGruop();
            if (CollectionUtils.isEmpty(collectionIdList)) {
                break;
            }
            collectionDetailService.collectionDetailOrderNumberDeal(collectionIdList);
            i++;
        }
    }

    public static void main(String[] args) throws IOException {
        System.out.println("aaaa");
        // 建立套接字
        try (var s = new ServerSocket(8189)) {
            // 建立一个负责监听8189的服务器
            try (Socket incoming = s.accept()) {
                InputStream inStream = incoming.getInputStream();
                OutputStream outStream = incoming.getOutputStream();

                try (var in = new Scanner(inStream, String.valueOf(StandardCharsets.UTF_8))) {
                    var out = new PrintWriter(new OutputStreamWriter(outStream, StandardCharsets.UTF_8), true);
                    out.println("Hello! Enter BYE to exit");
                    var done = false;
                    while (!done && in.hasNextLine()) {
                        String line = in.nextLine();
                        out.println("Echo:" + line);
                        if (line.trim().equals("BYE")) {
                            done = true;
                        }
                    }
                }
            }
        }
    }

    @org.junit.Test
    public void testIdNoCheck() {
//        String name = "万XX";
//        String idNo = "4209841999XXXXXXXX";
//        String mobile = "13797063309";
//        idNoCheckUtil.checkIdNoThreeElements(name,idNo,mobile);
    }
}
