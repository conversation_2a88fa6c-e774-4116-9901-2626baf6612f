package com.std.core.culturalChain;

import com.alibaba.fastjson.JSONObject;
import com.std.core.enums.EErrorCode;
import com.std.core.util.DateUtil;
import com.std.core.util.HttpClientUtils;
import com.std.core.util.SHA256Utils;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;

@Slf4j
public class asset {

    public static void main(String[] args) {
//        assetCreate();
//        assetUpdate();
//        assetTransfer();
//        assetList();
//        assetUpdatePrice();
//        assetCancelListing();
        asset();
        assetDelete();
    }
    private static void assetCreate(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("collectionHash", "5499");
        map.put("assetHash", "724797");
        map.put("name", "银河眼光波刃龙");
        map.put("number", "1");
        map.put("logoURL", "https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg");



        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/create";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void assetUpdate(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "724797");
        map.put("name", "银河眼光波刃龙");
        map.put("logoURL", "https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg");



        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/update";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void assetTransfer(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "B4D7E7E7B0442F6A74D8B5CB5B3A3D5F");
        map.put("userHash", "02BEDC80079FE96FBA1ED6FCC9AF065B");



        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/transfer";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void assetList(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "724797");
        map.put("userHash", "503356083155509248");
        map.put("fixedPrice", "110");



        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/list";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void assetUpdatePrice(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "724797");
        map.put("userHash", "503356083155509248");
        map.put("fixedPrice", "210");



        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/updatePrice";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void assetCancelListing(){
        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "724797");
        map.put("userHash", "503356083155509248");




        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/cancelListing";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void asset(){

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "B4D7E7E7B0442F6A74D8B5CB5B3A3D5F");


        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset";

        String date=null;
        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
            date=httpPost.getString("data");
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        System.out.println(date);
    }

    private static void assetDelete(){
        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("assetHash", "B4D7E7E7B0442F6A74D8B5CB5B3A3D5F");
//        map.put("userHash", "73F4A4793B45714B5DCBFBA3FD9DE32C");




        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/asset/delete";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

}
