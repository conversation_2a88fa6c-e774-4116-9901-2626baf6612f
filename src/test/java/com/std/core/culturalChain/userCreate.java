package com.std.core.culturalChain;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.enums.EErrorCode;
import com.std.core.pojo.domain.CulturalchainUser;
import com.std.core.util.*;
import jnr.posix.windows.SystemTime;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;

import java.util.*;

@Slf4j
public class userCreate {

    public static void main(String[] args) {
//        create();
        user();
//        System.out.println(DateUtil.getTimestamp());

    }

    private static void create() {
//        Map<String, String> map = new TreeMap<String, String>(
//                new Comparator<String>() {
//                    @Override
//                    public int compare(String o1, String o2) {
//                        return o1.compareTo(o2);
//                    }
//                }
//        );
//
//        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
//        map.put("nickname", "zhangsan");
//        map.put("realName", "张三");
//        map.put("number", "330291199406053319");
//        map.put("userHash", IdGeneratorUtil.generator().toString());
//        map.put("avatarURL", "https://metat.oss-accelerate.aliyuncs.com/1660201444935.jpg");
//        map.put("orgType", "individual");
//        map.put("type", "idCard");
        String urlParam = new StringBuffer()
                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
                .append("&avatarURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660201444935.jpg")
                .append("&nickname=").append("zhangsan")
                .append("&number=").append("330291199406053319")
                .append("&orgType=").append("individual")
                .append("&realName=").append("张三")
                .append("&type=").append("idCard")
                .append("&userHash=").append(IdGeneratorUtil.generator().toString()).toString();

        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/user/create";

        Map<String, String> hearderMap = new HashMap();
        hearderMap.put("X-Auth-Id","224af7af30fe065b7787bc2e641e57e507d79f8b");
        hearderMap.put("X-Auth-Data",sha256);
        OkHttpUtils.doAccessHTTPPostForm(url, urlParam, hearderMap);
//        try {
//            JSONObject httpPost = HttpClientUtils.httpPost(url, jsonObject, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
//            String code = httpPost.getString("code");
//            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
//                System.out.println("失败"+code);
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }


    }
    private static void user() {
        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("userHash", "02BEDC80079FE96FBA1ED6FCC9AF065B");


        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }


        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
        // 调用三方授权
        String url = "https://culturalchain.net/api/user";

        String date=null;
        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
            date=httpPost.getString("data");
        } catch (Exception e) {
            log.error(e.getMessage());
        }
        System.out.println(date);
        JSONObject json = JSONObject.parseObject(date);
        CulturalchainUser culturalchainUser = JSONObject.toJavaObject(json, CulturalchainUser.class);

        System.out.println(culturalchainUser);
    }

    public static String generateSignature(final Map<String, String> data, String appKey) {
        Set<String> keySet = data.keySet();
        String[] keyArray = keySet.toArray(new String[keySet.size()]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if ("sign".equals(k)) {
                continue;
            }
            if (data.get(k) instanceof String) {
                // 参数值为空，则不参与签名
                if (data.get(k).trim().length() > 0) {
                    sb.append(k).append("=").append(data.get(k).trim()).append("&");
                }
            }
        }
        sb.append(appKey);
        System.out.println(sb);
        return SHA256Utils.getSHA256(sb.toString());
    }


    public static String getAsciiSort(Map<String, Object> map) {
        // 移除值为空的
        map.entrySet().removeIf(entry -> Objects.isNull(entry.getValue()) || "".equals(entry.getValue()));

        List<Map.Entry<String, Object>> infoIds = new ArrayList<Map.Entry<String, Object>>(map.entrySet());
        // 对所有传入参数按照字段名的 ASCII 码从小到大排序（字典序）
//        infoIds.sort((o1, o2) -> o1.getKey().substring(0, 1).compareToIgnoreCase(o2.getKey().substring(0, 1)));
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> infoId : infoIds) {
            sb.append(infoId.getKey());
            sb.append("=");
            sb.append(infoId.getValue());
            sb.append("&");
        }
        return sb.substring(0, sb.length() - 1);
    }

    public static Object mapToObject(Map<String, String> map, Class<?> beanClass) throws Exception {
        if (map == null)
            return null;

        Object obj = beanClass.newInstance();

        org.apache.commons.beanutils.BeanUtils.populate(obj, map);

        return obj;
    }

}
