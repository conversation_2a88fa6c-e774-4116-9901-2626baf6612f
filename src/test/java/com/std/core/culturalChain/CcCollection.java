package com.std.core.culturalChain;

import com.alibaba.fastjson.JSONObject;
import com.std.core.enums.EErrorCode;
import com.std.core.util.DateUtil;
import com.std.core.util.HttpClientUtils;
import com.std.core.util.SHA256Utils;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CcCollection {


    public static void main(String[] args) {
        collection();
//        create();
//        collectionDelete();
    }

    private static void create() {

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("collectionHash", "5499");
        map.put("name", "银河眼光波刃龙");
        map.put("logoURL", "https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg");

        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }

//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499")
//                .append("&name=").append("银河眼光波刃龙")
//                .append("&logoURL=").append("https://metat.oss-accelerate.aliyuncs.com/1660270287861.jpeg").toString();

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        System.out.println(asciiSort);
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/collection/create";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private void update() {
        String urlParam = new StringBuffer()
                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
                .append("&collectionHash=").append("https://metat.oss-accelerate.aliyuncs.com/1660201444935.jpg")
                .append("&name=").append("zhangsan")
                .append("&logoURL=").append("330291199406053319").toString();

        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/collection/update";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    private static void collection() {
//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499").toString();

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("collectionHash", "30584C50E4FB791555254EF5D591B778");

        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/collection";

        String data = null;
        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
            }
            data = httpPost.getString("data");
        } catch (Exception e) {
            log.error(e.getMessage());
        }

        System.out.println(data);
    }

    private static void collectionDelete() {
//        String urlParam = new StringBuffer()
//                .append("TIMESTAMP=").append(Long.toString(DateUtil.getTimestamp()))
//                .append("&collectionHash=").append("5499").toString();

        Map<String, String> map = new TreeMap<String, String>(new Comparator<String>() {
            @Override
            public int compare(String obj1, String obj2) {
                // 降序排序
                return obj1.compareTo(obj2);
            }
        });

        map.put("TIMESTAMP", Long.toString(DateUtil.getTimestamp()));
        map.put("collectionHash", "B4978723797E87AB22733C3E49FC6FF2");

        StringBuffer urlParamBuffer = new StringBuffer();
        int i = 1;
        for (String s : map.keySet()) {
            if (i == 1) {
                urlParamBuffer = urlParamBuffer.append(s + "=").append(map.get(s));
            } else {
                urlParamBuffer = urlParamBuffer.append("&" + s + "=").append(map.get(s));
            }
            i++;
        }

        String urlParam = urlParamBuffer.toString();
        System.out.println(urlParam);

        String asciiSort = urlParam.concat("&88172309ecc09831e7e126ad8d0f6d595cb4283d");
        String sha256 = SHA256Utils.getSHA256(asciiSort);
        sha256 = sha256.toUpperCase();
        System.out.println(sha256);
//        // 调用三方授权
        String url = "https://culturalchain.net/api/collection/delete";

        try {
            JSONObject httpPost = HttpClientUtils.httpPost(url, urlParam, sha256, "224af7af30fe065b7787bc2e641e57e507d79f8b");
            String code = httpPost.getString("code");
            if (!EErrorCode.SUCCESS.getCode().equals(code)) {
                System.out.println("失败" + code);
                System.out.println(httpPost);
            }

            System.out.println("成功" + code);
        } catch (Exception e) {
            log.error(e.getMessage());
        }


    }
}
