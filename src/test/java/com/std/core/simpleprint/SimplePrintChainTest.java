package com.std.core.simpleprint;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.JsonUtil;
import com.std.core.simpleprint.request.SimplePrintCreateReq;
import com.std.core.simpleprint.request.SimplePrintMintReq;
import com.std.core.simpleprint.request.SimplePrintQueryReq;
import com.std.core.simpleprint.request.SimplePrintTransferReq;
import com.std.core.simpleprint.response.SimplePrintCreateRes;
import com.std.core.simpleprint.response.SimplePrintMintRes;
import com.std.core.simpleprint.response.SimplePrintQueryRes;
import com.std.core.simpleprint.response.SimplePrintTransferRes;
import com.std.core.util.DateUtil;
import com.std.core.util.OkHttpUtils;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.SortedMap;
import java.util.TreeMap;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;

/**
 * <AUTHOR> xieyj
 * @since : 2022/5/7 4:33 下午
 */
@Slf4j
public class SimplePrintChainTest {

    private static String URL = "http://*************:80";

    private static String API_ID = "metatest";

    private static String API_SECRET = "20BF8Af0144d43ddD116f237f74d448F6";

    public static void main(String[] args) {
        //产生地址1
        String password1 = "111111";
        String address1 = createAddress(password1);
        System.out.println("address1:" + address1);

        //产生地址2
        String password2 = "222222";
        String address2 = createAddress(password2);
        System.out.println("address2:" + address2);

        //铸造
        String uri = "https://metat.oss-accelerate.aliyuncs.com/1636991155535.jpg";
        String description = "https://metat.oss-accelerate.aliyuncs.com/1636991155535.jpg";
        String nftHash = mint(address1, description, password1, uri);
        System.out.println("nftHash:" + nftHash);

        //转移
        String txHash = tranfer(address1, password1, address2, password2, nftHash);
        System.out.println("txHash:" + txHash);

        //查询token具体信息
        System.out.println(JsonUtil.getJson(query(nftHash)));
    }

    /**
     * 链上账户创建:创建链上账户，设置密码获取链上账户地址
     */
    private static String createAddress(String password) {
        SimplePrintCreateReq request = new SimplePrintCreateReq();
        request.setReq_time(DateUtil.getNow(com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
        request.setApi_id(API_ID);
        request.setPassword(password);
        request.setNonce_str(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));

        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("api_id", request.getApi_id());
        nativeObj.put("nonce_str", request.getNonce_str());
        nativeObj.put("req_time", request.getReq_time());
        nativeObj.put("password", request.getPassword());

        String stringA = signatureMd5(nativeObj);
        String stringSignTemp = stringA + "&api_secret=" + API_SECRET;
        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();

        request.setSign(sign);
        String json = OkHttpUtils.doAccessHTTPPostJson(URL + "/v2/account/new", JsonUtil.getJson(request));
        SimplePrintCreateRes response = JsonUtil.getObject(json, SimplePrintCreateRes.class);
        //请求结果:{"code":0,"message":"请求成功","result":"0xabc2c083a4b2accde749e16c08cb3553cdce87e7"}
        if (!"0".equals(response.getCode())) {
            log.error("创建地址请求报错[{}]", response.getMsg());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "创建地址请求异常");
        }

        return response.getResult();
    }

    /**
     * 藏品铸造接口:将数字藏品铸造在指定账户地址下，返回藏品链上标识
     */
    private static String mint(String address, String description, String password, String uri) {
        SimplePrintMintReq request = new SimplePrintMintReq();
        request.setReq_time(DateUtil.getNow(com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
        request.setApi_id(API_ID);
        request.setPassword(password);
        request.setNonce_str(UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32));

        request.setAddress(address);
        request.setDescription(description);
        request.setPassword(password);
        request.setUri(uri);

        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("api_id", request.getApi_id());
        nativeObj.put("nonce_str", request.getNonce_str());
        nativeObj.put("req_time", request.getReq_time());
        nativeObj.put("address", request.getAddress());
        nativeObj.put("description", request.getDescription());
        nativeObj.put("password", request.getPassword());
        nativeObj.put("uri", request.getUri());

        String stringA = signatureMd5(nativeObj);
        String stringSignTemp = stringA + "&api_secret=" + API_SECRET;
        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();

        request.setSign(sign);
        String json = OkHttpUtils.doAccessHTTPPostJson(URL + "/v2/mint", JsonUtil.getJson(request));
        SimplePrintMintRes response = JsonUtil.getObject(json, SimplePrintMintRes.class);

        //0xad61e2ce169f18dd89dbf46ae310f9c3ce81b48046919c2520258bc4a41bd13f
//        {"API_useTimes":2,"NFT_hash":"0x3bb087f151ef982df2aae1737af9fad6bfa6a7aced22582de945a2a714ec42a7","code":0,"message":"请求成功"}
        if (!"0".equals(response.getCode())) {
            log.error("铸造地址请求报错[{}]", response.getMsg());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "铸造请求异常");
        }

        return response.getNFT_hash();
    }

    /**
     * 藏品所有权转让接口:数字藏品所有权变更。
     */
    private static String tranfer(String ownerAddress, String ownerPassword, String buyAddress, String buyerPassword, String nftHash) {
        SimplePrintTransferReq request = new SimplePrintTransferReq();
        request.setReq_time(DateUtil.getNow(com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
        request.setApi_id(API_ID);
        request.setOwner_address(ownerAddress);
        request.setOwner_password(ownerPassword);
        request.setBuyer_address(buyAddress);
        request.setBuyer_password(buyerPassword);
        request.setNft_hash(nftHash);

        SortedMap<String, String> nativeObj = new TreeMap<>();
        nativeObj.put("api_id", request.getApi_id());
        nativeObj.put("nonce_str", request.getNonce_str());
        nativeObj.put("req_time", request.getReq_time());
        //入参
        nativeObj.put("owner_address", request.getOwner_address());
        nativeObj.put("owner_password", request.getOwner_password());
        nativeObj.put("buyer_address", request.getBuyer_address());
        nativeObj.put("buyer_password", request.getBuyer_password());
        nativeObj.put("nft_hash", request.getNft_hash());

        String stringA = signatureMd5(nativeObj);
        String stringSignTemp = stringA + "&api_secret=" + API_SECRET;
        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();

        request.setSign(sign);
        String json = OkHttpUtils.doAccessHTTPPostJson(URL + "/v2/item/transfer", JsonUtil.getJson(request));
        SimplePrintTransferRes response = JsonUtil.getObject(json, SimplePrintTransferRes.class);

        if (!"0".equals(response.getCode())) {
            log.error("所有权变更请求报错[{}]", response.getMsg());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所有权变更请求异常");
        }

        return response.getTx_hash();
    }

    /**
     * 藏品所有权转让接口:数字藏品所有权变更。
     */
    private static SimplePrintQueryRes query(String nftHash) {
        SimplePrintQueryReq request = new SimplePrintQueryReq();
//        request.setReq_time(DateUtil.getNow(com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
//        request.setApi_id(API_ID);
        request.setNft_hash(nftHash);
//
//        SortedMap<String, String> nativeObj = new TreeMap<>();
//        nativeObj.put("api_id", request.getApi_id());
//        nativeObj.put("nonce_str", request.getNonce_str());
//        nativeObj.put("req_time", request.getReq_time());
//        //入参
//        nativeObj.put("nft_hash", request.getNft_hash());
//
//        String stringA = signatureMd5(nativeObj);
//        String stringSignTemp = stringA + "&api_secret=" + API_SECRET;
//        String sign = DigestUtils.md5Hex(stringSignTemp).toUpperCase();
//
//        request.setSign(sign);
        String json = OkHttpUtils.doAccessHTTPGetJson(URL + "/v2/enquiry?nft_hash=" + request.getNft_hash());
        SimplePrintQueryRes response = JsonUtil.getObject(json, SimplePrintQueryRes.class);

        if (!"0".equals(response.getCode())) {
            log.error("查询nftToken请求报错[{}]", response.getMsg());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "查询nftToken请求报错");
        }

        return response;
    }

    private static String signatureMd5(SortedMap<String, String> sortedMap) {
        StringBuffer sb = new StringBuffer();
        Set es = sortedMap.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k)
                    && !"key".equals(k)) {
                if (it.hasNext()) {
                    sb.append(k + "=" + v + "&");
                } else {
                    sb.append(k + "=" + v);
                }
            }
        }

        return sb.toString();
    }

}

    
    