package com.std.core;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.InputStreamReader;

public class FileRead {

    public static void main(String[] args) {
        String result = "";

        try {
//BufferedReader是可以按行读取文件
            FileInputStream inputStream = new FileInputStream("/Users/<USER>/Desktop/2022-06-03_1.log");
            BufferedReader bufferedReader = new BufferedReader(new InputStreamReader(inputStream));

            String str = null;
            int i = 1;
            while ((str = bufferedReader.readLine()) != null) {
                try {
//                    System.out.println(str.substring(str.lastIndexOf("请求[") + 3, str.length() - 1));
                    String a = str.substring(str.indexOf("userId\":"), str.indexOf("userId\":") + 26);
                    if (a.contains("userId")) {
                        //System.out.println(i++);
                        if (!result.contains(a.substring(8))) {
                            result = result + "'" + a.substring(8) + "',";
                        }
                    }
                } catch (Exception e) {
                    //System.out.println(e.getMessage());
                }
            }

//close
            inputStream.close();
            bufferedReader.close();
        } catch (Exception e) {

        }
        System.out.println(result);
    }
}
