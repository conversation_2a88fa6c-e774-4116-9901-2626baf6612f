package com.std.core.bsn;

import static com.std.core.util.wechat.MD5Util.md5;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.JsonUtil;
import com.std.core.bsn.response.NftsTransferDataRes;
import com.std.core.util.IdGeneratorUtil;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
public class NFTTestDemo {

    /**
     * 平台编号
     */
    private static String PLAT_CODE = "MTKJ";

    /**
     * 加密key
     */
    private static String KEY = "6PmOpkC8mmRlvfeUDJKy0pYGkSQCBdFk";

    /**
     * api前缀
     */
    private static String BASE_URL = "https://ddc.wanlinsl.com/api";

    /**
     * 创建账户
     */
    private static String CREATE_ACCOUNT_URL = "/accountsv/add";
    /**
     * 查询账户
     */
    private static String QUERY_ACCOUNT_URL = "/accountsv/query";

    /**
     * 查询上链结果
     */
    private static String QUERY_TASKSV_URL = "/tasksv/info";

    /**
     * 添加类别
     */
    private static String ADD_CLASSES_URL = "/nftclzsv/addclasses";

    /**
     * 查询NFT类别(查多个)
     */
    private static String QUERY_CLASSES_URL = "/nftclzsv/qryclasses";

    /**
     * 查询NFT类别详情(根据ID)
     */
    private static String GET_CLASSES_INFO_URL = "/nftclzsv/qryclassesinfo";

    /**
     * 转让NFT类别
     */
    private static String TRANSFER_CLASSES = "/nftclzsv/transclass";

    /**
     * 发行NFT:NFT 是链上唯一的、可识别的资产，由用户自己在区块链上发行一个 NFT
     */
    private static String ISSUES_NFT_URL = "/nftsv/issue";

    /**
     * 发行NFT:NFT 是链上唯一的、可识别的资产，由用户自己在区块链上发行一个 NFT
     */
    private static String NFTSV_BATCH_ISSUE = "/nftsv/batchissue";

    /**
     * 批量发行NFT
     */
    private static String BATCH_ISSUES_NFT_URL = "/nftsv/batchissue";

    /**
     * 查询NFT
     */
    private static String NFTSV_INFO = "/nftsv/info";

    /**
     * 转让NFT
     */
    private static String TRANS_NFTSV = "/nftsv/trans";

    /**
     * 批量转让NFT
     */
    private static String BATCH_TRANS_NFTSV = "/nftsv/batchtrans";

    public static void main(String[] args) {
        String address = "iaa1mr8hfjcw7q770uhyrjsmxy87ffhs005rvu78nf";

        // 创建分类
        //String taskId = "e038675e45684d6c97a638b730b09c64";
        //taskId = addClasses("乡村振兴", address);
        //System.out.println("taskId:" + taskId);
//        queryTaskInfo(taskId);
//        {"sign":"B2CFF7D1C2111EF2008373F68D5706F1","retData":{"nft_id":"","class_id":"avata5epho8dfqinzr8o7glbnnofkoxj","tx_hash":"B20F7E5A1426A78A08B82BA21BB375109BBE8BFEE2BB0326604EC0AF00582E14","block_height":17407521,"message":"","type":"issue_class","status":1,"timestamp":"2022-09-27 07:18:59 +0000 UTC"},"retCode":"0000","retMsg":"成功！"}

//        String classId = "avata5epho8dfqinzr8o7glbnnofkoxj";
//        String taskIdNft = "f8e76a27cb1a4f61896e70d468adf550";
//        for (int a = 1; a <= 9; a++) {
//            taskIdNft = issueNFTBatch(classId, "乡村振兴", null,
//                    "https://metaculture.oss-accelerate.aliyuncs.com/1663508505271.jpg",
//                    "[{\"address\":\"https://metaculture.oss-accelerate.aliyuncs.com/1663508284744.jpg\",\"type\":\"0\"}]",
//                    address,
//                    1);
//            System.out.println("taskIdNft:" + taskIdNft);
//            //查询交易信息
//            try {
//                Thread.sleep(1000);
//            } catch (Exception e) {
//
//            }
//        }
        detailNFT("avataulfgerkmfutc2flnm0gdgjne9e7", "avata5epho8dfqinzr8o7glbnnofkoxj");


    }

    public static void main1(String[] args) {
//       创建地址
//        List<String> list = createAccount(1);
//        for (String accountAddress : list) {
//            System.out.println("address:" + accountAddress);
//        }
        //address1:iaa1lynf5fc7v77074j2cv243ykexxctqg7e5pdfhp
        //address2:iaa1a3fqwsxm7kues59337tzrgz8gyytu6d97vvymc
        //查询地址
//        String address = "iaa1lynf5fc7v77074j2cv243ykexxctqg7e5pdfhp";
        //queryAccount(address);

//        创建分类
//        String taskId = null;
//        "1fe965987b7d4f919977869edaccc65e";
//        taskId = addClasses("小美人鱼", address);
//        System.out.println("taskId:" + taskId);
//        //查询交易信息
//        try {
//            Thread.sleep(5000);
//        } catch (Exception e) {
//
//        }
//        taskId = "b21fdf7ff1c746f2b51fd23a11a19e2a";
//        queryTaskInfo(taskId);
        //class_id=avataji9ciaxxbyt7xlpt7dyfr69qklp

        String address = "iaa1mr8hfjcw7q770uhyrjsmxy87ffhs005rvu78nf";
        String classId = "avatagzjjkgfcwjkbsd6yiyjpvat3abp";
        String taskIdNft = "f8e76a27cb1a4f61896e70d468adf550";
        issueNFTBatch(classId, "地澈天清原创汉服-明制红衣", null,
                "https://metaculture.oss-accelerate.aliyuncs.com/*************.jpg",
                "[{\"address\":\"https://metaculture.oss-accelerate.aliyuncs.com/*************.mp4\",\"firstAddress\":\"https://metaculture.oss-accelerate.aliyuncs.com/*************.mp4?x-oss-process=video/snapshot,t_1,f_jpg\",\"type\":\"2\"}]",
                address,
                150);
        System.out.println("taskIdNft:" + taskIdNft);

//        try {
//            Thread.sleep(5000);
//        } catch (Exception e) {
//
//        }
//        查询交易信息
//        queryTaskInfo(taskIdNft);
//
//        //查询交易信息
//        classId = "avatagzjjkgfcwjkbsd6yiyjpvat3abp";
//        String nftId = "avata0jcu5ztpzxkmo4ywhkoof2lgz0o";
//        detailNFT(nftId, classId);

        //转赠nft
//        String owerAddress = "iaa1a3fqwsxm7kues59337tzrgz8gyytu6d97vvymc";
//        String recipient = "iaa1mr8hfjcw7q770uhyrjsmxy87ffhs005rvu78nf";
//        System.out.println(transNFT(owerAddress, recipient, classId, nftId));

//        taskId = "833c731633574e488b24fca386590e64";
//        查询交易信息
//        queryTaskInfo(taskId);

        //查询交易信息
        //String classId = "avatac8rfczoezfsbntpgxp7gucaybyn";
        //String nftId = "avataxrx2ttdtiiztq8ockjae4juyv17";
//        detailNFT(nftId, classId);

        //转赠nft
        //String owerAddress = "iaa14su7g30p9wdmt623r2qask9ueny85aakdvhgqr";
        //String recipient = "iaa1rkc8uydngasaf6jxzf5rs37z0aaysk7ay8w42p";
        //taskId = transNFT(owerAddress, recipient, classId, nftId);
        //System.out.println("transferTaskId" + taskId);

//        NftsTransferDataRes nftsTransferDataRes = new NftsTransferDataRes();
//        nftsTransferDataRes.setRecipient(recipient);
//        List<NftRes> nfts = new ArrayList<>();
//        nfts.add(new NftRes(classId, nftId));
//        nftsTransferDataRes.setNfts(nfts);
//        List<NftsTransferDataRes> transferList = new ArrayList<>();
//        transferList.add(nftsTransferDataRes);
//        batchTransNFT(owerAddress, transferList);
    }

    /**
     * 创建账户 {"sign":"********************************","retData":{"accounts":["iaa1usurdywj055ukyx7wrqe9xvrd86swavcv93h7n"]},"retCode":"0000","retMsg":"成功！"}
     */
    public static List<String> createAccount(Integer count) {
        Map<String, Object> map = baseMap1();
        map.put("count", count);

        JSONObject jsonObject = getJsonObject1(CREATE_ACCOUNT_URL, map);

        if (!"0000".equals(jsonObject.getString("retCode"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), jsonObject.getString("retMsg"));
        }

        JSONArray jsonArray = jsonObject.getJSONObject("retData").getJSONArray("accounts");
        return jsonArray.toJavaList(String.class);
    }


    /**
     * 查询账户
     *
     * @param account 账户地址
     */
    public static JSONObject queryAccount(String account) {
        Map<String, Object> map = baseMap1();
        map.put("account", account);

        JSONObject jsonObject = getJsonObject1(QUERY_ACCOUNT_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 查询上链结果
     *
     * @param taskId 任务编号
     */
    public static JSONObject queryTaskInfo(String taskId) {
        Map<String, Object> map = baseMap1();
        map.put("taskId", taskId);

        JSONObject jsonObject = getJsonObject1(QUERY_TASKSV_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 创建NFT类别,返回taskId
     */
    public static String addClasses(String className, String owner) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("className", className);
        map.add("owner", owner);

        JSONObject jsonObject = getJsonObject(ADD_CLASSES_URL, map);
        if (!"0000".equals(jsonObject.getString("retCode"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), jsonObject.getString("message"));
        }
        System.out.println(jsonObject.getJSONObject("retData"));
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 查询 NFT 类别详情
     */
    public static JSONObject getClasses(String id) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("id", id);

        JSONObject jsonObject = getJsonObject(GET_CLASSES_INFO_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 发行NFT
     */
    public static String issueNFT(String classId, String name, MultipartFile multipartFile, String data) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("classId", classId);
        map.add("name", name);
        map.add("nftFile", multipartFile);
        map.add("uri", data);
        map.add("uriHash", "0000000001");
        map.add("data", data);
        JSONObject jsonObject = getJsonObject(ISSUES_NFT_URL, map);
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 批量发行NFT
     */
    public static String issueNFTBatch(String classId, String name, MultipartFile multipartFile, String uri, String data, String recipient,
            Integer count) {
        Map<String, Object> map = baseMap1();
        map.put("classId", classId);
        map.put("name", name);
        //map.add("nftFile", multipartFile);
        map.put("uri", uri);
        map.put("uriHash", md5(uri));
        map.put("data", data);
        map.put("recipient", recipient);
        map.put("count", count);

        JSONObject jsonObject = getJsonObject1(NFTSV_BATCH_ISSUE, map);
        return jsonObject.getJSONObject("retData").get("operation_id").toString();
    }

    /**
     * 转让NFT
     */
    public static String transNFT(String owerAddress, String recipient, String classId, String nftId) {
        Map<String, Object> map = baseMap1();
        map.put("ower", owerAddress);
        map.put("nftId", nftId);
        map.put("recipient", recipient);
        map.put("classId", classId);

        JSONObject jsonObject = getJsonObject1(TRANS_NFTSV, map);
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 批量转让NFT
     */
    public static String batchTransNFT(String owerAddress, List<NftsTransferDataRes> nftsTransferDataList) {
        Map<String, Object> map = baseMap1();
        map.put("ower", owerAddress);
        map.put("transData", nftsTransferDataList);

        System.out.println(JsonUtil.getJson(map));
        JSONObject jsonObject = getJsonObject1(BATCH_TRANS_NFTSV, map);
        //2c7953d688004f07b8987581cc011118
        return null;//jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 查询 NFT 详情
     */
    public static JSONObject detailNFT(String nftId, String classId) {
        Map<String, Object> map = baseMap1();
        map.put("nftId", nftId);
        map.put("classId", classId);
        JSONObject jsonObject = getJsonObject1(NFTSV_INFO, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 生成签名
     */
    private static String createSign(MultiValueMap<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k).size() > 0 && map.get(k).get(0) != null && map.get(k).get(0).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).get(0).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    private static String createSign1(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k) != null && map.get(k).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    /**
     * 发起请求
     */
    private static JSONObject getJsonObject(String url, MultiValueMap<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign(map);
        map.add("sign", sign);

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }

    private static JSONObject getJsonObject1(String url, Map<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign1(map);
        map.put("sign", sign);

        System.out.println(JSON.toJSONString(map));

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }

    private static MultiValueMap<String, Object> baseMap() {
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("platCode", PLAT_CODE);
        map.add("transactionId", IdGeneratorUtil.generator().toString());
        map.add("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.add("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

    private static Map<String, Object> baseMap1() {
        Map<String, Object> map = new HashMap<>();
        map.put("platCode", PLAT_CODE);
        map.put("transactionId", IdGeneratorUtil.generator().toString());
        map.put("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.put("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

}
