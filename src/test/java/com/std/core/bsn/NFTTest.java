package com.std.core.bsn;

import static com.std.core.util.wechat.MD5Util.md5;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.JsonUtil;
import com.std.core.bsn.response.NftsTransferDataRes;
import com.std.core.util.IdGeneratorUtil;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 */
public class NFTTest {

    /**
     * 平台编号
     */
    private static String PLAT_CODE = "test123";

    /**
     * 加密key
     */
    private static String KEY = "VqMok5QCWWuvhvmTlpbaQj7QvczgJ5Z5";

    /**
     * 平台编号
     */
//    private static String PLAT_CODE = "MTKJ";

    /**
     * 加密key
     */
//    private static String KEY = "6PmOpkC8mmRlvfeUDJKy0pYGkSQCBdFk";

    /**
     * api前缀
     */
    private static String BASE_URL = "https://ddc.wanlinsl.com/api";
    /**
     * 创建账户
     */
    private static String CREATE_ACCOUNT_URL = "/accountsv/add";
    /**
     * 查询账户
     */
    private static String QUERY_ACCOUNT_URL = "/accountsv/query";

    /**
     * 查询上链结果
     */
    private static String QUERY_TASKSV_URL = "/tasksv/info";

    /**
     * 添加类别
     */
    private static String ADD_CLASSES_URL = "/nftclzsv/addclasses";

    /**
     * 查询NFT类别(查多个)
     */
    private static String QUERY_CLASSES_URL = "/nftclzsv/qryclasses";

    /**
     * 查询NFT类别详情(根据ID)
     */
    private static String GET_CLASSES_INFO_URL = "/nftclzsv/qryclassesinfo";

    /**
     * 转让NFT类别
     */
    private static String TRANSFER_CLASSES = "/nftclzsv/transclass";

    /**
     * 发行NFT:NFT 是链上唯一的、可识别的资产，由用户自己在区块链上发行一个 NFT
     */
    private static String ISSUES_NFT_URL = "/nftsv/issue";

    /**
     * 发行NFT:NFT 是链上唯一的、可识别的资产，由用户自己在区块链上发行一个 NFT
     */
    private static String NFTSV_BATCH_ISSUE = "/nftsv/batchissue";

    /**
     * 批量发行NFT
     */
    private static String BATCH_ISSUES_NFT_URL = "/nftsv/batchissue";

    /**
     * 查询NFT
     */
    private static String NFTSV_INFO = "/nftsv/info";

    /**
     * 转让NFT
     */
    private static String TRANS_NFTSV = "/nftsv/trans";

    /**
     * 批量转让NFT
     */
    private static String BATCH_TRANS_NFTSV = "/nftsv/batchtrans";

    public static void main(String[] args) {
//        // 创建地址
//        List<String> list = createAccount(1);
//        for (String accountAddress : list) {
//            System.out.println("address:" + accountAddress);
//        }
//        //查询地址
//        String address = "iaa14su7g30p9wdmt623r2qask9ueny85aakdvhgqr";
//        queryAccount(address);

        //String address = "iaa179mxhhuv74nyg8z4d8lkunp70c75ua2r0llkvd";
//        创建分类
//        String taskId = null;
//        taskId = addClasses("敦煌", address);
//        System.out.println("taskId:" + taskId);
//        //查询交易信息
//        try {
//            Thread.sleep(5000);
//        } catch (Exception e) {
//
//        }
        //String taskId = "27f8b7ed94f34803b8438827d672854f";
        //queryTaskInfo(taskId);
        //class_id=avataji9ciaxxbyt7xlpt7dyfr69qklp

        String classId = "avataji9ciaxxbyt7xlpt7dyfr69qklp";
        String address = "iaa14su7g30p9wdmt623r2qask9ueny85aakdvhgqr";
        String taskId = issueNFTBatch(classId, "敦煌榆林窟文殊变", null,
                "https://metat.oss-accelerate.aliyuncs.com/*************.jpg",
                "[{\"address\":\"http://metat.oss-accelerate.aliyuncs.com/kapai.mtl\",\"type\":\"3\"},{\"address\":\"http://metat.oss-accelerate.aliyuncs.com/kapai.obj\",\"type\":\"3\"},{\"address\":\"http://metat.oss-accelerate.aliyuncs.com/tietu512_kapai.bmp\",\"type\":\"3\"}]",
                address,
                1000);
//        taskId = "60f639eda72f43dbba6d6c3481564697";
//        System.out.println("taskId:" + taskId);
//        查询交易信息
//        String taskId = "33eea64b5fcc4e31b76feecf92bd981c";
//        queryTaskInfo(taskId);

        //查询交易信息
//        String classId = "avataw6lzxherrxklhzkvrkiryekdp9x";
//        String nftId = "avatawiidod0vt15ytrvslhwhznohhu1";
//        detailNFT(nftId, classId);

        //转赠nft
//        String owerAddress = "iaa179mxhhuv74nyg8z4d8lkunp70c75ua2r0llkvd";
//        String recipient = "iaa10u9emypz5nhg5n4g0vd2vdg2ugf5nlmwxm99rg";
//        transNFT(owerAddress, recipient, classId, nftId);

//        FileSystemResource file = new FileSystemResource(new File("/Users/<USER>/Downloads/1647430023922.jpg"));

//        File file = new File("/Users/<USER>/Downloads/1652773596850.jpg");

//        将File格式转换为MultipartFile格式
        // taskId = "";
//        try {
////            FileInputStream fileInputStream = new FileInputStream(file);
////            MultipartFile multipartFile = new MockMultipartFile(file.getName(), file.getName(),
////                    ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
//
//            taskId = issueNFT("avatak1nm7pp1gcolektzquhsp6natw5", "nft first(测试)", null,
//                    "https://metaculture.oss-accelerate.aliyuncs.com/android_1654092244297.jpg");
//            System.out.println("taskId:" + taskId);

//        taskId = issueNFTBatch("avatak1nm7pp1gcolektzquhsp6natw5", "黑达摩BLACK", null,
//                "https://metaculture.oss-accelerate.aliyuncs.com/1639995454298.jpg", address, 1);
//        System.out.println("taskId:" + taskId);

//        } catch (Exception e) {
//            System.out.println(e.getMessage());
//        }

//        taskId = "833c731633574e488b24fca386590e64";
//        查询交易信息
//        queryTaskInfo(taskId);

        //查询交易信息
//        String classId = "avatap02mxkja4noddqp60t5yo0siety";
//        String nftId = "avataezubcmh2j06jn0wtdkmjbexmto7";
//        detailNFT(nftId, classId);

        //转赠nft
//        String owerAddress = "iaa179mxhhuv74nyg8z4d8lkunp70c75ua2r0llkvd";
//        String recipient = "iaa1y55fgzhycxc7azcpfrrumra9djguxljfk8pxuc";
//        transNFT(owerAddress, recipient, classId, nftId);

//        NftsTransferDataRes nftsTransferDataRes = new NftsTransferDataRes();
//        nftsTransferDataRes.setRecipient(recipient);
//        List<NftRes> nfts = new ArrayList<>();
//        nfts.add(new NftRes(classId, nftId));
//        nftsTransferDataRes.setNfts(nfts);
//        List<NftsTransferDataRes> transferList = new ArrayList<>();
//        transferList.add(nftsTransferDataRes);
//        batchTransNFT(owerAddress, transferList);
    }

    /**
     * 创建账户 {"sign":"********************************","retData":{"accounts":["iaa1usurdywj055ukyx7wrqe9xvrd86swavcv93h7n"]},"retCode":"0000","retMsg":"成功！"}
     */
    public static List<String> createAccount(Integer count) {
        Map<String, Object> map = baseMap1();
        map.put("count", count);

        JSONObject jsonObject = getJsonObject1(CREATE_ACCOUNT_URL, map);

        if (!"0000".equals(jsonObject.getString("retCode"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), jsonObject.getString("retMsg"));
        }

        JSONArray jsonArray = jsonObject.getJSONObject("retData").getJSONArray("accounts");
        return jsonArray.toJavaList(String.class);
    }


    /**
     * 查询账户
     *
     * @param account 账户地址
     */
    public static JSONObject queryAccount(String account) {
        Map<String, Object> map = baseMap1();
        map.put("account", account);

        JSONObject jsonObject = getJsonObject1(QUERY_ACCOUNT_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 查询上链结果
     *
     * @param taskId 任务编号
     */
    public static JSONObject queryTaskInfo(String taskId) {
        Map<String, Object> map = baseMap1();
        map.put("taskId", taskId);

        JSONObject jsonObject = getJsonObject1(QUERY_TASKSV_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 创建NFT类别,返回taskId
     */
    public static String addClasses(String className, String owner) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("className", className);
        map.add("owner", owner);

        JSONObject jsonObject = getJsonObject(ADD_CLASSES_URL, map);
        if (!"0000".equals(jsonObject.getString("retCode"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), jsonObject.getString("message"));
        }
        System.out.println(jsonObject.getJSONObject("retData"));
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 查询 NFT 类别详情
     */
    public static JSONObject getClasses(String id) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("id", id);

        JSONObject jsonObject = getJsonObject(GET_CLASSES_INFO_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 发行NFT
     */
    public static String issueNFT(String classId, String name, MultipartFile multipartFile, String data) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("classId", classId);
        map.add("name", name);
        map.add("nftFile", multipartFile);
        map.add("uri", data);
        map.add("uriHash", "0000000001");
        map.add("data", data);
        JSONObject jsonObject = getJsonObject(ISSUES_NFT_URL, map);
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 批量发行NFT
     */
    public static String issueNFTBatch(String classId, String name, MultipartFile multipartFile, String uri, String data, String recipient,
            Integer count) {
        Map<String, Object> map = baseMap1();
        map.put("classId", classId);
        map.put("name", name);
        //map.add("nftFile", multipartFile);
        map.put("uri", uri);
        map.put("uriHash", md5(uri));
        map.put("data", data);
        map.put("recipient", recipient);
        map.put("count", count);

        JSONObject jsonObject = getJsonObject1(NFTSV_BATCH_ISSUE, map);
        return jsonObject.getJSONObject("retData").get("operation_id").toString();
    }

    /**
     * 转让NFT
     */
    public static String transNFT(String owerAddress, String recipient, String classId, String nftId) {
        Map<String, Object> map = baseMap1();
        map.put("ower", owerAddress);
        map.put("nftId", nftId);
        map.put("recipient", recipient);
        map.put("classId", classId);

        JSONObject jsonObject = getJsonObject1(TRANS_NFTSV, map);
        return jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 批量转让NFT
     */
    public static String batchTransNFT(String owerAddress, List<NftsTransferDataRes> nftsTransferDataList) {
        Map<String, Object> map = baseMap1();
        map.put("ower", owerAddress);
        map.put("transData", nftsTransferDataList);

        System.out.println(JsonUtil.getJson(map));
        JSONObject jsonObject = getJsonObject1(BATCH_TRANS_NFTSV, map);
        //2c7953d688004f07b8987581cc011118
        return null;//jsonObject.getJSONObject("retData").get("task_id").toString();
    }

    /**
     * 查询 NFT 详情
     */
    public static JSONObject detailNFT(String nftId, String classId) {
        Map<String, Object> map = baseMap1();
        map.put("nftId", nftId);
        map.put("classId", classId);
        JSONObject jsonObject = getJsonObject1(NFTSV_INFO, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 生成签名
     */
    private static String createSign(MultiValueMap<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k).size() > 0 && map.get(k).get(0) != null && map.get(k).get(0).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).get(0).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    private static String createSign1(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k) != null && map.get(k).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    /**
     * 发起请求
     */
    private static JSONObject getJsonObject(String url, MultiValueMap<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign(map);
        map.add("sign", sign);

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }

    private static JSONObject getJsonObject1(String url, Map<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign1(map);
        map.put("sign", sign);

        System.out.println(JSON.toJSONString(map));

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }

    private static MultiValueMap<String, Object> baseMap() {
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("platCode", PLAT_CODE);
        map.add("transactionId", IdGeneratorUtil.generator().toString());
        map.add("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.add("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

    private static Map<String, Object> baseMap1() {
        Map<String, Object> map = new HashMap<>();
        map.put("platCode", PLAT_CODE);
        map.put("transactionId", IdGeneratorUtil.generator().toString());
        map.put("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.put("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

}
