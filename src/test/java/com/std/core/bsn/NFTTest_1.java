package com.std.core.bsn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.std.common.utils.DateUtil;
import com.std.core.util.IdGeneratorUtil;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.DigestUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 */
public class NFTTest_1 {

    /**
     * 平台编号
     */
    private static String PLAT_CODE = "test123";
    /**
     * 加密key
     */
    private static String KEY = "VqMok5QCWWuvhvmTlpbaQj7QvczgJ5Z5";
    /**
     * api前缀
     */
    private static String BASE_URL = "https://ddc.wanlinsl.com/api";
    /**
     * 创建账户
     */
    private static String CREATE_ACCOUNT_URL = "/accountsv/add";
    /**
     * 查询账户
     */
    private static String QUERY_ACCOUNT_URL = "/accountsv/query";

    /**
     * 查询上链结果
     */
    private static String QUERY_TASKSV_URL = "/tasksv/info";

    /**
     * 添加分类
     */
    private static String ADD_CLASSES_URL = "/nftclzsv/addclasses";


    public static void main(String[] args) {

        createAccount(1);
//        queryAccount("iaa1uevzcda05h0x8sgrn6sf7wh85u37sscscg8qtw");
//        addClasses("aa","iaa1uevzcda05h0x8sgrn6sf7wh85u37sscscg8qtw");
    }

    /**
     * 创建账户
     *
     * @param count 数量
     */
    public static JSONObject createAccount(Integer count) {
        Map<String, Object> map = baseMap1();
        map.put("count", count);

        JSONObject jsonObject = getJsonObject1(CREATE_ACCOUNT_URL, map);

        System.out.println(jsonObject.toJSONString());

//        //todo 返回结构待定
//        if (!"200".equals(jsonObject.getString("code"))){
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),jsonObject.getString("message"));
//        }
//
//        JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONObject("data").getJSONArray("accounts");
//        List<String> list = jsonArray.toJavaList(String.class);
//
//        System.out.println(list);

        return jsonObject;
    }

    /**
     * 查询账户
     *
     * @param account 账户地址
     */
    public static JSONObject queryAccount(String account) {
        Map<String, Object> map = baseMap1();
        map.put("account", account);

        JSONObject jsonObject = getJsonObject1(QUERY_ACCOUNT_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 查询上链结果
     *
     * @param taskId 任务编号
     */
    public static JSONObject queryTaskInfo(String taskId) {
        Map<String, Object> map = baseMap1();
        map.put("taskId", taskId);

        JSONObject jsonObject = getJsonObject1(QUERY_TASKSV_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }

    /**
     * 创建NFT类别
     */
    public static JSONObject addClasses(String className, String owner) {
        MultiValueMap<String, Object> map = baseMap();
        map.add("className", className);
        map.add("owner", owner);

        JSONObject jsonObject = getJsonObject(ADD_CLASSES_URL, map);

        System.out.println(jsonObject.toJSONString());

        return jsonObject;
    }


    /**
     * 生成签名
     */
    private static String createSign(MultiValueMap<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k).size() > 0 && map.get(k).get(0) != null && map.get(k).get(0).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).get(0).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    private static String createSign1(Map<String, Object> map) {
        Set<String> keySet = map.keySet();
        String[] keyArray = keySet.toArray(new String[0]);
        Arrays.sort(keyArray);
        StringBuilder sb = new StringBuilder();
        for (String k : keyArray) {
            if (map.get(k) != null && map.get(k).toString().trim().length() > 0) {
                sb.append(k).append("=").append(map.get(k).toString().trim()).append("&");
            }
        }
        sb.append("key=").append(KEY);
        System.out.println("拼接：" + sb);

        return DigestUtils.md5DigestAsHex(sb.toString().getBytes(StandardCharsets.UTF_8)).toUpperCase(Locale.ROOT);
    }

    /**
     * 发起请求
     */
    private static JSONObject getJsonObject(String url, MultiValueMap<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign(map);
        map.add("sign", sign);

        System.out.println(JSON.toJSONString(map));

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }

    private static JSONObject getJsonObject1(String url, Map<String, Object> map) {
        url = BASE_URL.concat(url);
        String sign = createSign1(map);
        map.put("sign", sign);

        System.out.println(JSON.toJSONString(map));

        HttpHeaders headers = new HttpHeaders();
        List<MediaType> acceptableMediaTypes = new ArrayList<>();
        acceptableMediaTypes.add(MediaType.APPLICATION_JSON_UTF8);
        headers.setAccept(acceptableMediaTypes);

        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(map, headers);
        ResponseEntity<String> exchange = new RestTemplate().exchange(url, HttpMethod.POST, requestEntity, String.class);
        return JSON.parseObject(exchange.getBody());
    }


    private static MultiValueMap<String, Object> baseMap() {
        MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
        map.add("platCode", PLAT_CODE);
        map.add("transactionId", IdGeneratorUtil.generator().toString());
        map.add("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.add("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

    private static Map<String, Object> baseMap1() {
        Map<String, Object> map = new HashMap<>();
        map.put("platCode", PLAT_CODE);
        map.put("transactionId", IdGeneratorUtil.generator().toString());
        map.put("timestamp", DateUtil.dateToStr(new Date(), "yyyyMMddHHmmss"));
        //10 位随机字符
        map.put("nonce", IdGeneratorUtil.generateShortUuid(10));
        return map;
    }

}
