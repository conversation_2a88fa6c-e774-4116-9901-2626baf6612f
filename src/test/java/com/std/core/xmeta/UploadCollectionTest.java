package com.std.core.xmeta;

import com.xmeta.opensdk.util.AESUtil;

/**
 * <AUTHOR> xieyj
 * @since : 2022/11/10 13:53
 */
public class UploadCollectionTest {

    private String gatewayUrl = "http://127.0.0.1:2879/xmeta/gateway/forward";

//    public static void main(String[] args) {
//        //业务处理
//        //封装请求参数
//        SyncUserInfoDTO syncUserInfoDTO = new SyncUserInfoDTO();
//        syncUserInfoDTO.setPhone("18767101909");
//
//        String objEncrypt = JSONUtil.toJsonStr(syncUserInfoDTO);
//        objEncrypt = AESUtil.encode("EtwaF6+cDYHezmrPIlfvyQ==", objEncrypt);
//        CommonReq commonReq = new CommonReq();
//        commonReq.setObjEncrypt(objEncrypt);
//        commonReq.setAppId("0a429c7cd0c94885b241f77f9c997dba");
//        commonReq.setMethod("xmeta.mall.user.verify");
//        commonReq.setNonce(IdUtil.simpleUUID());
//        commonReq.setTimestamp(String.valueOf(System.currentTimeMillis()));
//        commonReq.setVersion("1.2.0");
//        String s = encodeSign(commonReq);
//        commonReq.setSign(s);
//
//        System.out.println(JsonUtil.getJson(commonReq));
//    }
//
//    private static String encodeSign(CommonReq commonReq) {
//        Map<String, String> encodeSign = new HashMap<>(16);
//        encodeSign.put(BaseParameterConstant.APPID, commonReq.getAppId());
//        encodeSign.put(BaseParameterConstant.METHOD, commonReq.getMethod());
//        encodeSign.put(BaseParameterConstant.NONCE, commonReq.getNonce());
//        encodeSign.put(BaseParameterConstant.OBJ_ENCRYPT, commonReq.getObjEncrypt());
//        encodeSign.put(BaseParameterConstant.TIMESTAMP, commonReq.getTimestamp());
//        encodeSign.put(BaseParameterConstant.VERSION, commonReq.getVersion());
//
//        String s = RSASignUtil.splicingParamString(encodeSign);
//        return RSASignUtil
//                .sign("MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2sVbaGMnuY/QtQgv+LwHYPUgqNU0R97ySrqhlMgoybAl5716spKyOpukNckMw81PEMnwcPDNAN/Ni4iPacD/BHG41EWZD/IJ1tdbxPo3kfHBkp8DlokLWPNMdh3Dn8SxJkNL1JiYqfKvFkYTlasLjEscmnGLronoz19yst1KcgyhkoSU45oylV/fDF4trdcHzkkU4ZJKgcWQH64D7q/grzK+/WX4X2EbM6OPv3E9nQReTM5N+uoPn3cbhgbAlYP0MAyJVwgWbSoIzEBzrSZdG4jCiTNSq7vPFHX5RDzsF+urL3cJfo7ydjk3Mi7gfMXY5Tt51lfeU+J5FLCfcM8WFAgMBAAECggEAfRpnTSYYcWSE+tD1gXQTIU6lsjGZuyoYOaHcsp9XmDufKZ6fCD4j4/OsN4b9N/IIdJvVnUgkGF+4DQiqYJTpM4YSeciWbQvnElAZBk49wC7ukB7H2vgIKMiyENCEom/mUeIYc8NR81o4DiyeArPfDLv5IBmLK6yxXfr9pFBNA9sop/OCYSzBp0v3K3SGyYMcnyDtsr4emCnfbyXo9QtlQ/RNUiNXHaNHDu7U3TcVKxWSm1I2IhuQvK+0flh9xRcVAgjg6SXtFOhhgj3GzV1g5Se0tE8ywvZjc4c/z93C8ru4j7quysW/On6gxQnpwn8Hv2mumSPpWdoB39IoVdlY6QKBgQD+MnUJMkxuDPE6J+PgT3+j9PpEKdRLnCtOvNwazsVhVzE98bsdaqAFJJq6BmTrGxs5U88nltl0JcBkw97+b1RKymaXlVdc816wI7848i3B/nP+sl1uoebfCnqnpkzurlHKKAOCeBnZA20Vm5EsknsM/vbjmolX6ZITBGsOctBnBwKBgQC3/Q1rDM1IaG4NDGOsw2VrdVC6ra6PVbqxod9sxo0b4e9fArMZoQhzNEpyFUyG7u8+udlC3ejfJ93UfN6rSli2RArNQLnBCwJFQABCXLqBA+ItQnRzxTwFv/irhjsQ7zi1ygqzDj7MBNYTFKiL0jW7GCHYdCa5WhU8VBeE8zrgEwKBgQCb3mC5hOgsyy7X1EH894JzDPKw/V06B8befxwQLB4H9CkzrJhqD0mUHb/qtaUILRTicCukQPGSMNvqbdi895MeUUHbTS3Gx0yWgTTuPEImpFqIIOL9xbUFqy1Ow8CE0stARIzyYaan1Vv8VeoNBL0DWQwWwFnbWFPD6X+j2m9D3QKBgC1ZyKbsXuVMKGQ6+46oEyWroxyAlxhpjz0do07SmhGXXUNUDQVyNfz/lKIOMfDIkFwrvyvGOxJWZ01rAg/CoZ87olX5cE/vAL5FCktRQ/9tD6EMUlv+786TBR5yVbjU3mugw4ZjKoNrZcEKRIlCZVAaoK0T5D+nvrhCGhIBSi/VAoGAY5JUjjg9j4pIoYxYZ5pjK9vQm867msLjECakNQDc7rMuzkqaeZLAmt95/ABZxOjAwevyc1/+LwBmrATyfLd6MKXq9OyggS0u7SJK04BEibIFPAUQZDzHWnWsBzl8XT0j2ezn8BIbhjcBHMpi88ShbCXgkd6Md7iQ/cyYDydYJfU="
//                        , s);
//    }

    public static void main(String[] args) {
        String content = "u2lEAvlkNBbzRxF1h8ounDOjnke5Zmc637U2rEs20jo=";
        String objEncrypt = AESUtil.decode("EtwaF6+cDYHezmrPIlfvyQ==", content);
        System.out.println(objEncrypt);
    }
}

    
    