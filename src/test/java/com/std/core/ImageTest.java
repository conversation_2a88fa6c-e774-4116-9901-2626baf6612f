package com.std.core;

//导入需要的包

//import com.sun.image.codec.jpeg.JPEGCodec;
//import com.sun.image.codec.jpeg.JPEGImageEncoder;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ImageTest {

    public static void main(String[] args) {
        Integer totalQuantity = 1000;
        Integer currentQuantity = 101;

//        String path1 = "/Users/<USER>/Desktop/pitu/1/";
//        String path2 = "/Users/<USER>/Desktop/pitu/2/";
//        String path3 = "/Users/<USER>/Desktop/pitu/3/";
//        String path4 = "/Users/<USER>/Desktop/pitu/4/";
//        String[] arrStr1 = {"xsws.png", "cfdy.png", "dfzr.png", "rfdg.png", "zqdl.png"};
//        String[] arrStr2 = {"01.png", "02.png", "03.png", "04.png", "05.png"};
//        String[] arrStr3 = {"01.png", "02.png", "03.png", "04.png", "05.png", "06.png", "07.png", "08.png", "09.png", "10.png"};
//        String[] arrStr4 = {"01.png", "02.png", "03.png", "04.png", "05.png", "06.png", "07.png", "08.png", "09.png", "10.png", "11.png",
//                "12.png"};
//        String targetFilePath = "/Users/<USER>/Desktop/pitu/";
//        int totalCount = 0;
//        for (String s1 : arrStr1) {
//            for (String s2 : arrStr2) {
//                for (String s3 : arrStr3) {
//                    for (String s4 : arrStr4) {
//                        String fileName = mergeImage(path1 + s1, path2 + s2, path3 + s3, path4 + s4,
//                                targetFilePath + s1.replace(".png", "") + "/");
//                        totalCount++;
//                        log.info("=====第[{}]张图片[{}]产生=====", totalCount, fileName);
//                    }
//                }
//            }
//        }
    }

    public static String mergeImage(String backPic, String firstPic, String secondPic, String thirdPic, String targetPicPath) {
//        try {
//            //创建四个文件对象（注：这里四张图片的宽度都是相同的，因此下文定义BufferedImage宽度就取第一只的宽度就行了）
//            File _file1 = new File(backPic);
//            File _file2 = new File(firstPic);
//            File _file3 = new File(secondPic);
//            File _file4 = new File(thirdPic);
//
//            Image src1 = javax.imageio.ImageIO.read(_file1);
//            Image src2 = javax.imageio.ImageIO.read(_file2);
//            Image src3 = javax.imageio.ImageIO.read(_file3);
//            Image src4 = javax.imageio.ImageIO.read(_file4);
//
//            //获取图片的宽度
//            int width = src1.getWidth(null);
//            //获取各个图像的高度
//            int height1 = src1.getHeight(null);
//
//            //构造一个类型为预定义图像类型之一的 BufferedImage。 宽度为第一只的宽度，高度为各个图片高度之和
//            BufferedImage tag = new BufferedImage(width, height1, BufferedImage.TYPE_INT_RGB);
//            //创建输出流
//            String targetFileName = _file1.getName().concat("_").concat(_file2.getName()).concat("_").concat(_file3.getName()).concat("_")
//                    .concat(_file4.getName());
//            String resultName = targetPicPath + targetFileName.replaceAll(".png", "") + ".png";
//            FileOutputStream out = new FileOutputStream(resultName);
//            //绘制合成图像
//            Graphics g = tag.createGraphics();
//            g.drawImage(src1, 0, 0, width, height1, null);
//            g.drawImage(src2, 0, 0, width, height1, null);
//            g.drawImage(src3, 0, 0, width, height1, null);
//            g.drawImage(src4, 0, 0, width, height1, null);
//
//            g.drawImage(
//                    src1.getScaledInstance(width, height1, Image.SCALE_SMOOTH),
//                    width, height1, null);
//
//            // 释放此图形的上下文以及它使用的所有系统资源。
//            g.dispose();
//            //将绘制的图像生成至输出流
//            JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(out);
//            encoder.encode(tag);
//            //关闭输出流
//            out.close();
//            return resultName;
//        } catch (Exception e) {
//            log.error(e.getMessage());
//        }
//
        return null;
    }
}
