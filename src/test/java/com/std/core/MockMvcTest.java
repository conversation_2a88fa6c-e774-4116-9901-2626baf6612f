package com.std.core;

import com.std.core.controller.CollectionDetailController;
import org.junit.Before;
import org.junit.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MockMvcBuilder;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

public class MockMvcTest {
    private MockMvc mvc;

    @Before()
    public void setMvc(){
        mvc = MockMvcBuilders.standaloneSetup(new CollectionDetailController()).build();
    }

    @Test
    public void getCollectionDetail() throws Exception {

    }
}
