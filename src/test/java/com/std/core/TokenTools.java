package com.std.core;

/**
 * <AUTHOR> xieyj
 * @since : 2022/5/7 4:33 下午
 */
public class TokenTools {

    public static void main(String[] args) {
        int count = 114;
        int start = 1417;
        String tokenId = "[";
        String quantity = "[";

        for (int i = 0; i < 114; i++) {
            tokenId = tokenId + (start + i) + ",";
            quantity = quantity + "1,";
        }

        tokenId = tokenId + "]";
        quantity = quantity + "]";

        System.out.println(tokenId);
        System.out.println(quantity);
        System.out.println(quantity.length());
    }

}

    
    