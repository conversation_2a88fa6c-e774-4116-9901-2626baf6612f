package com.std.core;

import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.FishPrayProp;
import com.std.core.pojo.domain.User;
import com.std.core.service.*;

import java.math.BigDecimal;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class CoreApplicationTests extends BaseController {

    @Autowired
    private IScheduleService scheduleService;

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IWechatService wechatService;


//    @Autowired
//    private AsyncExecutorUtilFil asyncExecutorUtilFil;

    @Autowired
    private IStatTradeDataDayService statTradeDataDayService;

    @Autowired
    private IYeePayService yeePayService;

    @Test
    public void doTestLength() {
        System.out.println(yeePayService.replaceBankCode("SZPA"));
        System.out.println(yeePayService.replaceBankCode("ABC"));
    }

    @Test
    public void doTestPeriodJoin() {
//        scheduleService.doPeriodDrawStrawsEndTrigger();
        //scheduleService.doDealDrawStrawsJoinRecordTrigger();
//        scheduleService.doPayBack();
//        scheduleService.doQueryPayBackResult();

        scheduleService.doPeriodAuctionBondReturnFreezeTrigger();
    }

    @Test
    public void doTestPeriodJoinRecord() {
        scheduleService.doDealDrawStrawsJoinRecordTrigger();
    }


    @Test
    public void doStaticBizData() {
        statTradeDataDayService.doStaticAll();
    }

//    @Test
//    public void test() {
//
////        smsOutService.sendSmsCaptcha("***********", "11", "");  503033087601680384  503032959411167232
//        collectionBuyOrderService.doCallback("503032959411167232", EBigOrderPayType.WECHAT.getCode());
//    }

    @Test
    public void callbackAlipay() {
        String result = "gmt_create=2021-12-22+02%3A51%3A10&charset=UTF-8&seller_email=***********%40163.com&subject=%E5%AF%BC%E5%87%BA%E8%97%8F%E5%93%81%E6%94%AF%E4%BB%98&sign=G1uk8FQhyArr4Lg3Io0XIGt7OaThvqwxGb%2BRDLgdhZcemYZgRgrHQk70nRG6GqaRVoeG0w8wqL%2FB0zQ1jaSU%2Fnw9Ucy9sq%2FRoL%2BZBmUwuggs%2BdkEObp38KzGtTAKRvLW52CGpUnpFI9%2F92JFArnbFukXnEefIOtD2GD%2B5LPR1Nn8bRFnUg25zT5tbnb3PuBJxqOKRhsl953SSr9FgKflZQt7cUgd5eqAG%2B5%2BpTHErKtqs3yKBEi8QK2A%2F2IpL%2F43VAEUG97viUy%2FZt5wQVqc%2BjxxXVC5k1YPYfRGAdZIJHsZqGK4HbGYximmVT0wr%2FI2XtAkISGaGaAICBQomgTsEA%3D%3D&body=%E5%AF%BC%E5%87%BA%E8%97%8F%E5%93%81%E6%94%AF%E4%BB%98&buyer_id=****************&invoice_amount=100.00&notify_id=2021122200222025112098781436520237&fund_bill_list=%5B%7B%22amount%22%3A%22100.00%22%2C%22fundChannel%22%3A%22ALIPAYACCOUNT%22%7D%5D&notify_type=trade_status_sync&trade_status=TRADE_SUCCESS&receipt_amount=100.00&app_id=****************&buyer_pay_amount=100.00&sign_type=RSA2&seller_id=****************&gmt_payment=2021-12-22+02%3A51%3A11&notify_time=2021-12-22+02%3A54%3A37&version=1.0&out_trade_no=503166595833798656&total_amount=100.00&trade_no=2021122222001498781442436377&auth_app_id=****************&buyer_logon_id=myb***%40hotmail.com&point_amount=0.00";
        alipayService.doCallback(result);
    }


    @Test
    public void doAlipayRefund() {
        String payBizCode = "504439014099787776";
        BigDecimal payCashAmount = new BigDecimal("0.2");
        String reason = "退还";
        alipayService.doRefund(payBizCode, payCashAmount, reason);
    }

    @Test
    public void doWechatRefund() {
        Long payBizCode = 0L;
        BigDecimal payCashAmount = BigDecimal.ZERO;
        String reason = "退还";
        wechatService.doRefund(payBizCode, payCashAmount, payCashAmount, reason);
    }

    //
    @Test
    public void callbackWechat() {
        String result = "<xml><return_code><![CDATA[SUCCESS]]></return_code>\n"
                + "<return_msg><![CDATA[OK]]></return_msg>\n"
                + "<result_code><![CDATA[SUCCESS]]></result_code>\n"
                + "<mch_id><![CDATA[**********]]></mch_id>\n"
                + "<appid><![CDATA[wx1ef7949d8a04a200]]></appid>\n"
                + "<openid><![CDATA[ouisx6RRk73GNZKTa8pJNI8ZgV2o]]></openid>\n"
                + "<is_subscribe><![CDATA[N]]></is_subscribe>\n"
                + "<trade_type><![CDATA[APP]]></trade_type>\n"
                + "<trade_state><![CDATA[SUCCESS]]></trade_state>\n"
                + "<bank_type><![CDATA[CMB_CREDIT]]></bank_type>\n"
                + "<total_fee>1</total_fee>\n"
                + "<fee_type><![CDATA[CNY]]></fee_type>\n"
                + "<cash_fee>1</cash_fee>\n"
                + "<cash_fee_type><![CDATA[CNY]]></cash_fee_type>\n"
                + "<transaction_id><![CDATA[4200001352202201058079588497]]></transaction_id>\n"
                + "<out_trade_no><![CDATA[508409559615021056]]></out_trade_no>\n"
                + "<attach><![CDATA[CL-META]]></attach>\n"
                + "<time_end><![CDATA[**************]]></time_end>\n"
                + "<trade_state_desc><![CDATA[支付成功]]></trade_state_desc>\n"
                + "<nonce_str><![CDATA[li8J0izMWS3E8Fnl]]></nonce_str>\n"
                + "<sign><![CDATA[5722055BBB67A155C3606E1E02323E49]]></sign>\n"
                + "</xml>";
        wechatService.doCallback(result);
    }

//    @Test
//    public void buyBlindBoxThread() {
//        int i = 1;
//        while (true) {
//            PageHelper.startPage(i, 100);
//            List<User> userList = userService.list(new User());
//            if (CollectionUtils.isEmpty(userList)) {
//                break;
//            }
//            asyncExecutorUtilFil.buyBlindBox(userList);
//            i++;
//        }
//
//    }

//
//    @Test
//    public void parseToken() {
//        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0NTIxOTk3MTM0OTY4OTk1ODQiLCJleHAiOjE2MzA2NzgwNjR9.fWcMcSdMBXyW83YGEH1a849_ep1x8vh2tP_osbIekKI";
//        Long userId = JWTUtil.getUserInfo(token);
//        System.out.println(userId.toString());
//    }
//
//    @Test
//    public void testNodeLevelRefreshNew() {
//
//        userNodeLevelService.doNodeLevelRefreshNew(457610048349413376L, BigDecimal.ZERO);
//    }
//
//    @Test
//    public void testSynAuctionBondRefund() {
//        scheduleService.doSynAuctionBondRefund();
//    }
//
//    @Test
//    public void testToTree() {
//        FishPrayProp prop = new FishPrayProp();
//        prop.setId(1L);
//        User user = new User();
//        user.setId(1L);
//        fishPrayTreeRecordService.toTree(prop,user);
//    }
//    public static void main(String[] args) {
//
//    }

}
