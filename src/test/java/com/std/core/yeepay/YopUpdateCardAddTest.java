package com.std.core.yeepay;

import com.std.common.utils.JsonUtil;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.settle.SettleClient;
import com.yeepay.yop.sdk.service.settle.SettleClientBuilder;
import com.yeepay.yop.sdk.service.settle.request.SettleCardAddRequest;
import com.yeepay.yop.sdk.service.settle.response.SettleCardAddResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopUpdateCardAddTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final SettleClient api = SettleClientBuilder.builder().build();

    public static void main(String[] args) {
        SettleCardAddRequest request = new SettleCardAddRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setBankCardNo("9559980310108151914");
        request.setBankCardType("DEBIT_CARD");
        request.setBankCode("ABC");
        request.setDefaultSettleCard(true);
        try {
            System.out.println(JsonUtil.getJson(request));
            SettleCardAddResponse response = api.settleCardAdd(request);
            System.out.println("result:" + response.getResult());
        } catch (YopClientException e) {
            System.out.println(e);
        }
    }
}
