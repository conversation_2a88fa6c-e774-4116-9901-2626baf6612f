package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.divide.DivideClient;
import com.yeepay.yop.sdk.service.divide.DivideClientBuilder;
import com.yeepay.yop.sdk.service.divide.request.ApplyRequest;
import com.yeepay.yop.sdk.service.divide.request.CompleteRequest;
import com.yeepay.yop.sdk.service.divide.request.QueryRequest;
import com.yeepay.yop.sdk.service.divide.response.ApplyResponse;
import com.yeepay.yop.sdk.service.divide.response.CompleteResponse;
import com.yeepay.yop.sdk.service.divide.response.QueryResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopDivideTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final DivideClient api = DivideClientBuilder.builder().build();

    public static void main(String[] args) {
//        companyDivide();
        //        completeDivide();

        queryResult();
    }

    //        returnCode: NIG00000
//        returnMsg: 请求成功
//        requestNo: M00000000001004
//        applicationNo: TYSHRW20220328171309255191
//        merchantNo: ***********
//        applicationStatus: COMPLETED
//        auditOpinion: null
//        progressDescription: null
    public static void companyDivide() {
        ApplyRequest request = new ApplyRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setOrderId("538178882529140736");
        request.setUniqueOrderNo("1013202203280000003382977523");
        request.setDivideRequestId("***********************");
        request.setDivideDetail(
                "[{\"ledgerNo\":\"***********\",\"amount\":\"0.1\",\"divideDetailDesc\":\"供应商结算\" }]");
        request.setAccountLinkInfo(null);

        try {
            ApplyResponse response = api.apply(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling DivideClient#apply");
            e.printStackTrace();
        }
    }

    public static void completeDivide() {
        CompleteRequest request = new CompleteRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setOrderId("537804732958515200");
        request.setUniqueOrderNo("1013202203270000003379332205");
        request.setDivideRequestId("101320220327000000337933220502");

        try {
            CompleteResponse response = api.complete(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling DivideClient#complete");
            e.printStackTrace();
        }
    }

    public static void queryResult() {
        QueryRequest request = new QueryRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setDivideRequestId("101320220327000000337933220502");
        request.setOrderId("537804732958515200");
        request.setUniqueOrderNo("1013202203270000003379332205");
        try {
            QueryResponse response = api.query(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling DivideClient#query");
            e.printStackTrace();
        }
    }


}
