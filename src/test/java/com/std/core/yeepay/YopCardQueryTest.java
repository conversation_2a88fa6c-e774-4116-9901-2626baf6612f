package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.frontcashier.FrontcashierClient;
import com.yeepay.yop.sdk.service.frontcashier.FrontcashierClientBuilder;
import com.yeepay.yop.sdk.service.frontcashier.request.GetcardbinRequest;
import com.yeepay.yop.sdk.service.frontcashier.response.GetcardbinResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br> https://open.yeepay.com/docs/apis/ptssfk/bangkazhifu/options__rest__v1.0__frontcashier__getcardbin
 */
public class YopCardQueryTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final FrontcashierClient api = FrontcashierClientBuilder.builder().build();

    public static void main(String[] args) {
        GetcardbinRequest request = new GetcardbinRequest();
        request.setBankCardNo("6228480316159888668");
        try {
            GetcardbinResponse response = api.getcardbin(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling FrontcashierClient#getcardbin");
            e.printStackTrace();
        }
    }
}
