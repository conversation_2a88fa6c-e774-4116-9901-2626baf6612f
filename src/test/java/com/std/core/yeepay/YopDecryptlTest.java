package com.std.core.yeepay;

import com.yeepay.yop.sdk.security.DigitalEnvelopeUtils;
import com.yeepay.yop.sdk.security.rsa.RSAKeyUtils;
import java.net.URLDecoder;
import java.security.PrivateKey;


/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopDecryptlTest {
//
//    // 该Client线程安全，请使用单例模式，多次请求共用
//    private static final TradeClient api = TradeClientBuilder.builder().build();

    public static void main(String[] args) {
        String cipherText = URLDecoder
                .decode("ZXwR6nVENpa2I1hOyQjLqkA4AuH7ZTGNOD5uT47epuLvCd8FHXrq2s4hX4Sd948XM3HQA5xdPmR03VJ621F7l8T0WAm_5LbUEJKXceSe8QIu26DQSomvKhgGzn-Ji2lbFe7Qc9StP4nFOPHlM6KFPcTP7HhNkhO_yVYD9oIxvB_25I3Ak4jFhTCrYpnVqdIKUBn9-Ml5Jwo3CkhJ9eYVufCI9R8NUt_u2FK2s9mk-AI22c-wm8vb4P9iVnt76sURmKzlQZe5HJ75w_-sJ1NKYBC3spl9rrrfvSyS_BM7yR8I3lh69OiAjQdf9rQAL7AsrA2PCoYeVc6qhPtZk6Hv8w%242kTcZFIzDTbgPpLVUJepYpXnYHxEFHAteBbXQ8EO8yUxDGQwyje8w_DFbhr8u4bQlnWxRwIW11qmigJCbHiB8J2z8PsOWHKUUjTRLLgiyMz_3lDkMpeH0uynskzP5yJ16k7wXC26D0RWXFaigZIfgPjP5yIxXC9yCfoa4osZAgXsKrDzpWzQjClkWWF96z17jC6FzriwUOGALjxLWrDLfG6dZ_rPuFFHF9bYR0yt5pmmrD-K8vZLIZ-hECc_3z5tw9AN1FoK40VicrTurvsuITwf_vVLKLCIr8XmUGJBL_S29zuqoZ24O_FF9ED9CywzP34p0jiYoD-K_bdjNugTf9PTdPhS3dFp5iLbHp6MAcqkXvolu2R_yW6SksbGhcoNsQL_i7ZNFtAyOcoE_iTkXMDMu5yA8RSeGRjE2s369PN0f2gy0oUIReZ34mz8h7pGCzYszZNMKcdPTtWzWRx1DNXfqmgxoVvpM1uMyVNMVovcEwYo9BjbNCoiuvIAPmMlebv-C9MKHkGZ99NYIz8Rg3sc8gbpf_nvu1XKWfBOlbomZzFjnI7e98x6UOedhOp0NAuODUqiWkXarAhr-UQnTyxdPEWaMPbMCdF51DWKvU1L9aZR1q3Yx7snSIvvkyjT365NWx7ZkTuLcsF3U3rVOZ359hOrqZZIrOaL64ILUkk%24AES%24SHA256");
        String privateKeyStr = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnnsSLuctvsiMji8sIJb1AhnWnBu7L+bJQIbwYlA4FRt8RS8fHMHHIOPwL4tBZExAif9/vtC/edHa52B7qbnLo7y8UgcKDWv/xjkafJYFxW7Pqde4QDQSydfTMvZOQC1+Ic18mcMA6U4LxE4lBR6ZEz7IAxGxbBvVIpepqBooh7aKNaEIWfHUj8RAN9Ev6KWUlCXD6zUcg1yo9zAmbUlnSvh28Hh/+Q3ZIg9aaIMsJ2enw2O7fYtpAp8UkRR2YSNgDN0JxrCd9crCRZObb0G+a65atnN+YW9BPJtekPLcBeJ5nRK1YRtBqyabiVdz2gR3k7qzsKyjDYMYR5S3Ar0urAgMBAAECggEABcdSGB2omS0S2lsfNz2LIZYK+fOJfqSBUd5dl9MqPGI+p+r6spoC3ZjGzeqqj+12NzN2v79kCh988iPrCt9Bjsb04ATSv6+nlENHDhygKA/MsZbY2AhzHUXQcIodKWo6x8RVGsY+zYBjxRfboJ1BZTFSAr1yBmR4jHlk/xJLB530JLkw8ybI4JsbMa1BXC8N7cKsn90nKQmEKW7tOy28vTcIokzC/0j7PLFC5qI1tSULsmkzio9gZTXVk9Lokdqpz6CN9RKN3Y4CVQK7VKIv9B9WN04TfbpL0X6LpMMgn2gp8TqJF7LEpXEqdXR1Kacqzl6g8hPj5kPAaPdNlfIw7QKBgQD/8u0GlgDK3KTF7CBd0f/hEnGdiPMMzGYSAWlQeRw+6WkPmcsqXKDJcPf79P0997QfHdQFxW7wI7zNNhBEtQWB+l7y1boxeEWPzCva2jEpqMb2nGxXYC/lK4GJw2oK+JFySYG4pQ2vLnoSqn+JLGfEMSMod4+BXlqxUU2s97fr5wKBgQDnqplhN18w6SQv2FOL0IpCH/sk1mnnHAOn+YQ89RtacVTEBkDoUM4uPuHq/zf5NHmZZglc5VdD0FHBPtwAflfxTl2SwfR50T/mOA6AkUErgbclw5tqAY/jwtZidCfu+LtCweS5f2B/Ir/1k6k1SNe4JsnFmYGAa1mQ8pCmPpOJnQKBgCKcGM0RL54+49EXsvC82tr2gYKJ6EPMx+ibZEBLgDRJn7lKVJyv1dYnfoEFidD9U8/5WphFMFEso/Uj4GNKDyqaToiLuhfCabWeKDf51TgK6IykbkoKaa+ViHi2gfplzlUj93SebRFJOZyYjZJg/M8czeJE+JTHy3+6vMQs4yP1AoGAfCuVkGT0cavUyIxDo4IASRcJLqJ6GvSKBSOZPG8H3T83duX/LkX5bRIpVyK6pqj4sD7fr9oBHwjueQyvMNiHXIBraTjqR8zfWg8bvs+cRax+NRCWIYh6zlJlRFoL+Wt6O3rflIlweFRw3TjrDfNgJIUtyurPXbysQiOxYlnSDIUCgYEA8CZo+wjszR3oIZWX2fqDrWVvL3qAshBYZqzoe3xkKznm8sbjc7BQiG+pBaQ80a046+9Q3+ObDpn0KrSHC84Q0Kv7SdJ2k03RFwT0urVu2rJzHXeaEQRGaNnfpiTx85KgayDQfq7Va45F/Y2cEAf5ioivdCIJhBElrVupdcQNYpY=";
        String appKey = "app_10086476614";

        PrivateKey isvPrivateKey = RSAKeyUtils.string2PrivateKey(privateKeyStr);
        //若不想从配置文件中获取商户私钥，可通过此方法设置商户私钥
        String plaintTextWithPrivateKey = DigitalEnvelopeUtils.decrypt(cipherText, isvPrivateKey);
        System.out.println(plaintTextWithPrivateKey);
    }
}
