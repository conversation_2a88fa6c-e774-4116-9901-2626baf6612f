package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.frontcashier.FrontcashierClient;
import com.yeepay.yop.sdk.service.frontcashier.FrontcashierClientBuilder;
import com.yeepay.yop.sdk.service.frontcashier.request.GetcardbinRequest;
import com.yeepay.yop.sdk.service.frontcashier.response.GetcardbinResponse;
import com.yeepay.yop.sdk.service.mer.MerClient;
import com.yeepay.yop.sdk.service.mer.MerClientBuilder;
import com.yeepay.yop.sdk.service.mer.request.RegisterQueryV2Request;
import com.yeepay.yop.sdk.service.mer.response.RegisterQueryV2Response;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br> https://open.yeepay.com/docs/apis/ptssfk/bangkazhifu/options__rest__v1.0__frontcashier__getcardbin
 */
public class YopRegisterQueryTest {



    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final MerClient api = MerClientBuilder.builder().build();

    public static void main(String[] args) {
        RegisterQueryV2Request request = new RegisterQueryV2Request();
        request.setRequestNo("587722240947396608");
        try {
            RegisterQueryV2Response response = api.registerQueryV2(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling FrontcashierClient#getcardbin");
            e.printStackTrace();
        }
    }
}
