package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.mer.MerClient;
import com.yeepay.yop.sdk.service.mer.MerClientBuilder;
import com.yeepay.yop.sdk.service.mer.request.NotifyRepeatV2Request;
import com.yeepay.yop.sdk.service.mer.response.NotifyRepeatV2Response;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopSmsTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final MerClient api = MerClientBuilder.builder().build();

    public static void main(String[] args) {
        NotifyRepeatV2Request request = new NotifyRepeatV2Request();
        request.setRequestNo("549151520235724800");
//        request.setApplicationNo("applicationNo_example");
        request.setType("MOBILE");
        try {
            NotifyRepeatV2Response response = api.notifyRepeatV2(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.out.println(e);
        }
    }

}
