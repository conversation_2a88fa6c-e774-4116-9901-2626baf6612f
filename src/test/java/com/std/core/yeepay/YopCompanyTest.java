package com.std.core.yeepay;

import com.std.common.utils.JsonUtil;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.mer.MerClient;
import com.yeepay.yop.sdk.service.mer.MerClientBuilder;
import com.yeepay.yop.sdk.service.mer.request.RegisterContributeMerchantV2Request;
import com.yeepay.yop.sdk.service.mer.request.RegisterQueryV2Request;
import com.yeepay.yop.sdk.service.mer.response.RegisterContributeMerchantV2Response;
import com.yeepay.yop.sdk.service.mer.response.RegisterQueryV2Response;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopCompanyTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final MerClient api = MerClientBuilder.builder().build();

    public static void importCompany(String requestNo) {
        String businessRole = "SHARE_MERCHANT";
//        String merchantSubjectInfo = "{ \"signName\":\"杭州橙链科技有限公司\", \"shortName\":\"杭州橙链科技有限公司\" }";

        String licenceUrl = "http://staticres.yeepay.com/jcptb-merchant-netinjt05/2022/03/29/merchant-1648527164924-bd645308-a460-4e62-87e0-23518fa1f222-uMlIwrZerQnpFDMjiSQV.jpg";
        String signName = "杭州橙链科技有限公司";
        String signType = "ENTERPRISE";
        String licenceNo = "91330110MA27YM0D9K";
        String shortName = "杭州橙链科技有限公司";
        String legalName = "宓永宝";
        String legalLicenceType = "ID_CARD";
        String legalLicenceNo = "330281198908118212";
        String legalLicenceFrontUrl = "http://staticres.yeepay.com/jcptb-merchant-netinjt05/2022/03/27/merchant-1648363243902-878e07d4-b7d6-4f4b-a29d-ce3c835f1fc3-oySdAHoXkNGPBrkJEduE.jpg";
        String legalLicenceBackUrl = "http://staticres.yeepay.com/jcptb-merchant-netinjt05/2022/03/27/merchant-1648363244491-1972a06a-9e26-4fb3-a18b-0dba7687868e-EcfJlrXUoirohZpbgxfU.jpg";

        String merchantSubjectInfo = "{\"licenceUrl\":\"" + licenceUrl + "\",\"signName\":\"" + signName + "\",\"signType\":\"" + signType
                + "\",\"licenceNo\":\"" + licenceNo + "\",\"shortName\":\"" + shortName + "\"}";
        String merchantCorporationInfo =
                "{\"legalName\":\"" + legalName + "\",\"legalLicenceType\":\"" + legalLicenceType + "\", \"legalLicenceNo\":\""
                        + legalLicenceNo + "\", "
                        + "\"legalLicenceFrontUrl\":\"" + legalLicenceFrontUrl + "\","
                        + " \"legalLicenceBackUrl\":\"" + legalLicenceBackUrl + "\"}";

        String contactName = "谢延径";
        String contactMobile = "18767101909";
        String contactEmail = "<EMAIL>";
        String contactLicenceNo = "330326199007015211";
        String servicePhone = "0571-86162013";
        String merchantContactInfo =
                "{ \"contactName\":\"" + contactName + "\", \"contactMobile\":\"" + contactMobile + "\", \"contactEmail\":\"" + contactEmail
                        + "\", \"contactLicenceNo\":\"" + contactLicenceNo + "\" ,\"servicePhone\":\"" + servicePhone + "\"}";

        String province = "330000";
        String city = "330100";
        String district = "330110";
        String address = "仓前街道余杭塘路2699号2幢501B-01";

        String businessAddressInfo =
                "{ \"province\":\"" + province + "\", \"city\":\"" + city + "\", \"district\":\"" + district + "\", \"address\":\""
                        + address + "\" }";

        String settlementDirection = "BANKCARD";
        String bankAccountType = "ENTERPRISE_ACCOUNT";
        String bankCardNo = "1202220909900099055";
        String bankCode = "ICBC";

        String settlementAccountInfo = "{ \"settlementDirection\":\"" + settlementDirection + "\",\"bankAccountType\":\"" + bankAccountType
                + "\", \"bankCardNo\":\"" + bankCardNo + "\",  \"bankCode\":\"" + bankCode + "\"}";
        String notifyUrl = "https://api.dev.metahz.com/callback/public/yeepay_micro_notice";

        String productInfo = "[{\"productCode\":\"T1\",\"rateType\":\"SINGLE_FIXED\",\"paymentMethod\":\"REAL_TIME\",\"fixedRate\":\"0\"}]";

        RegisterContributeMerchantV2Request request = new RegisterContributeMerchantV2Request();
        request.setRequestNo(requestNo);
        request.setBusinessRole(businessRole);
        request.setMerchantSubjectInfo(merchantSubjectInfo);
        request.setMerchantCorporationInfo(merchantCorporationInfo);
        request.setMerchantContactInfo(merchantContactInfo);
        request.setBusinessAddressInfo(businessAddressInfo);
        request.setSettlementAccountInfo(settlementAccountInfo);
        request.setNotifyUrl(notifyUrl);
        request.setProductInfo(productInfo);

        try {
            System.out.println(JsonUtil.getJson(request).toString());
            RegisterContributeMerchantV2Response response = api.registerContributeMerchantV2(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling MerClient#registerContributeMicroV2");
            e.printStackTrace();
        }
    }

    public static void getResult(String requestNo) {
        RegisterQueryV2Request request = new RegisterQueryV2Request();
        request.setRequestNo(requestNo);
        try {
            RegisterQueryV2Response response = api.registerQueryV2(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling MerClient#registerQueryV2");
            e.printStackTrace();
        }
    }

//    returnCode: NIG00000
//    returnMsg: 请求成功
//    requestNo: M00000000001003
//    applicationNo: TYSHRW20220327151440409548
//    applicationStatus: REVIEWING
//    merchantNo: 10086585974

    public static void main(String[] args) {
//        importCompany("538619592713707521");
        getResult("546497967356649472");
//        getResult("M00000000001004");
    }
}
