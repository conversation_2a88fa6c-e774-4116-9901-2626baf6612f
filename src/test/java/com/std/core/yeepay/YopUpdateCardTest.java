package com.std.core.yeepay;

import com.std.common.utils.JsonUtil;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.settle.SettleClient;
import com.yeepay.yop.sdk.service.settle.SettleClientBuilder;
import com.yeepay.yop.sdk.service.settle.request.SettleCardModifyRequest;
import com.yeepay.yop.sdk.service.settle.response.SettleCardModifyResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopUpdateCardTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final SettleClient api = SettleClientBuilder.builder().build();

    public static void main(String[] args) {
        SettleCardModifyRequest request = new SettleCardModifyRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setBankCardNo("1202090119900173469");
        request.setBankCardType("DEBIT_CARD");
        request.setBankCode("ICBC");
        request.setDefaultSettleCard(true);
        try {
            System.out.println(JsonUtil.getJson(request));
            SettleCardModifyResponse response = api.settleCardModify(request);
            System.out.println("result:" + response.getResult());
        } catch (YopClientException e) {
            System.out.println(e);
        }

//        众安集团有限公司
//        中国工商银行浙江萧山分行营业部
//        1202090119900173469
    }
}
