package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.TradeClientBuilder;
import com.yeepay.yop.sdk.service.trade.request.OrderQueryRequest;
import com.yeepay.yop.sdk.service.trade.response.OrderQueryResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopOrderQueryTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final TradeClient api = TradeClientBuilder.builder().build();

    public static void main(String[] args) {
        OrderQueryRequest request = new OrderQueryRequest();
        request.setParentMerchantNo("10086476614");
        request.setMerchantNo("10086476614");
        request.setOrderId("547565330780921856");
        try {
            OrderQueryResponse response = api.orderQuery(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling TradeClient#orderQuery");
            e.printStackTrace();
        }
    }

}
