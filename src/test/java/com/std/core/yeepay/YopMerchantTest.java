package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.mer.MerClient;
import com.yeepay.yop.sdk.service.mer.MerClientBuilder;
import com.yeepay.yop.sdk.service.mer.request.RegisterContributeMicroV2Request;
import com.yeepay.yop.sdk.service.mer.response.RegisterContributeMicroV2Response;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopMerchantTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final MerClient api = MerClientBuilder.builder().build();

    public static void main(String[] args) {
        String requestNo = "M00000000000003";
        String businessRole = "SETTLED_MERCHANT";
        String merchantSubjectInfo = "{ \"signName\":\"谢延径\", \"shortName\":\"谢延径\" }";
        String merchantCorporationInfo = "{ \"legalLicenceType\":\"ID_CARD\", \"legalLicenceNo\":\"330326199007015211\", \"legalLicenceFrontUrl\":\"http://staticres.yeepay.com/jcptb-merchant-netinjt05/2022/03/25/merchant-*************-bdca3d78-0f3d-4d26-8af9-e7086f792ace-zbtklTUNMBpdymSguLhw.jpg\", \"legalLicenceBackUrl\":\"http://staticres.yeepay.com/jcptb-merchant-netinjt05/2022/03/25/merchant-*************-c9056418-685e-4abf-8b46-f71a7a675bcf-pHFbbbneuBqhmRRduJmZ.jpg\",  \"mobile\":\"***********\"  }";
        String businessAddressInfo = "{ \"province\":\"330000\", \"city\":\"330100\", \"district\":\"330110\", \"address\":\"000000\" }";
        String accountInfo = "{ \"settlementDirection\":\"BANKCARD\",  \"bankAccountType\":\"DEBIT_CARD\", \"bankCardNo\":\"****************\",  \"bankCode\":\"CMBCHINA\"}";
        String notifyUrl = "https://api.dev.metahz.com/callback/public/yeepay_micro_notice";
        String productInfo = "[{\"productCode\":\"T1\",\"rateType\":\"SINGLE_FIXED\",\"paymentMethod\":\"REAL_TIME\",\"undertaker\":\"SETTLED_MERCHANT\",\"fixedRate\":\"0\"}]";
        String productQualificationInfo = "";
        String functionService = "";
        String functionServiceQualificationInfo = "";

        RegisterContributeMicroV2Request request = new RegisterContributeMicroV2Request();
        request.setRequestNo(requestNo);
        request.setBusinessRole(businessRole);
        request.setMerchantSubjectInfo(merchantSubjectInfo);
        request.setMerchantCorporationInfo(merchantCorporationInfo);
        request.setBusinessAddressInfo(businessAddressInfo);
        request.setAccountInfo(accountInfo);
        request.setNotifyUrl(notifyUrl);
        request.setProductInfo(productInfo);
        request.setProductQualificationInfo(productQualificationInfo);
        request.setFunctionService(functionService);
        request.setFunctionServiceQualificationInfo(functionServiceQualificationInfo);

        try {
            RegisterContributeMicroV2Response response = api.registerContributeMicroV2(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling MerClient#registerContributeMicroV2");
            e.printStackTrace();
        }
    }

//    public static void main(String[] args) {
//        RegisterQueryV2Request request = new RegisterQueryV2Request();
//        request.setRequestNo("537452977657749504");
//        try {
//            RegisterQueryV2Response response = api.registerQueryV2(request);
//            System.out.println(response.getResult());
//        } catch (YopClientException e) {
//            System.err.println("Exception when calling MerClient#registerQueryV2");
//            e.printStackTrace();
//        }
//    }


}
