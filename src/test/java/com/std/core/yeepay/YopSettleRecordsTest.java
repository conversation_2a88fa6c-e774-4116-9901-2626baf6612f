package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.settle.SettleClient;
import com.yeepay.yop.sdk.service.settle.SettleClientBuilder;
import com.yeepay.yop.sdk.service.settle.request.RecordsQueryRequest;
import com.yeepay.yop.sdk.service.settle.response.RecordsQueryResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
@Slf4j
public class YopSettleRecordsTest {


    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final SettleClient api = SettleClientBuilder.builder().build();

    public static void main(String[] args) {
        RecordsQueryRequest request = new RecordsQueryRequest();
        request.setParentMerchantNo("10086476614");
        request.setMerchantNo("10087395080");
//        request.setSettleRequestNo("settleRequestNo_example");
        request.setSettleRequestBeginTime("2022-04-23 00:00:00");
        request.setSettleRequestEndTime("2022-05-06 16:00:00");
        try {
            RecordsQueryResponse response = api.recordsQuery(request);
//            response.getResult().get
            System.out.println(response);
        } catch (YopClientException e) {
        }
    }

//    public static void main(String[] args) {
//         BalanceQueryRequest request = new BalanceQueryRequest();
//        request.setParentMerchantNo("10087070289");
//        request.setMerchantNo("10087070289");
//        request.setOperatePeriod("ALL");
////        request.setEndTime("2022-04-21 11:55:44");
//        try {
//            BalanceQueryResponse response = api.balanceQuery(request);
//            System.out.println(response);
//        } catch (YopClientException e) {
//        }
//    }
}
