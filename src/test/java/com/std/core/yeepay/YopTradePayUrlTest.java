package com.std.core.yeepay;

import com.std.core.util.DateUtil;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.TradeClientBuilder;


/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopTradePayUrlTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final TradeClient api = TradeClientBuilder.builder().build();

    public static void main(String[] args) {
        System.out.println(DateUtil.getTimestamp());
    }
}
