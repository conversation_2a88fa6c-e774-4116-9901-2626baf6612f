package com.std.core.yeepay;

import com.std.core.util.DateUtil;
import com.std.core.util.OrderNoGenerater;
import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.security.YopSignUtils;
import com.yeepay.yop.sdk.security.rsa.RSAKeyUtils;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.TradeClientBuilder;
import com.yeepay.yop.sdk.service.trade.request.OrderRequest;
import com.yeepay.yop.sdk.service.trade.response.OrderResponse;
import java.util.Date;


/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopTradePayTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final TradeClient api = TradeClientBuilder.builder().build();

    public static void main(String[] args) {
        OrderRequest request = new OrderRequest();
        request.setParentMerchantNo("10086476614");
        request.setMerchantNo("10086476614");
        request.setOrderId(OrderNoGenerater.generate("MT"));
        System.out.println(request.getOrderId());
        request.setOrderAmount("0.1");
        request.setGoodsName("数字藏品");
        request.setFundProcessType("DELAY_SETTLE");
        request.setNotifyUrl("http://api.dev.metahz.com/callback/public/alipay");
        request.setMemo("");
        request.setSubOrderDetail("");
        request.setExpiredTime(
                DateUtil.dateToStr(DateUtil.getRelativeDateOfMinute(new Date(), 5), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
        request.setRedirectUrl("");
        request.setCsUrl("");
        request.setBusinessInfo("");
        request.setTerminalInfo("");
        request.setYpPromotionInfo("");
        request.setPayerInfo(
                "{\"bankCardNo\":\"****************\",\"cardName\":\"谢延径\",\"idCardNo\":\"330326199007015211\",\"mobilePhoneNo\":\"***********\"}");
        try {
            OrderResponse response = api.order(request);
            System.out.println(response.getResult());

            String url = "https://cash.yeepay.com/cashier/std";

            String certType = "RSA2048";
            String priKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnnsSLuctvsiMji8sIJb1AhnWnBu7L+bJQIbwYlA4FRt8RS8fHMHHIOPwL4tBZExAif9/vtC/edHa52B7qbnLo7y8UgcKDWv/xjkafJYFxW7Pqde4QDQSydfTMvZOQC1+Ic18mcMA6U4LxE4lBR6ZEz7IAxGxbBvVIpepqBooh7aKNaEIWfHUj8RAN9Ev6KWUlCXD6zUcg1yo9zAmbUlnSvh28Hh/+Q3ZIg9aaIMsJ2enw2O7fYtpAp8UkRR2YSNgDN0JxrCd9crCRZObb0G+a65atnN+YW9BPJtekPLcBeJ5nRK1YRtBqyabiVdz2gR3k7qzsKyjDYMYR5S3Ar0urAgMBAAECggEABcdSGB2omS0S2lsfNz2LIZYK+fOJfqSBUd5dl9MqPGI+p+r6spoC3ZjGzeqqj+12NzN2v79kCh988iPrCt9Bjsb04ATSv6+nlENHDhygKA/MsZbY2AhzHUXQcIodKWo6x8RVGsY+zYBjxRfboJ1BZTFSAr1yBmR4jHlk/xJLB530JLkw8ybI4JsbMa1BXC8N7cKsn90nKQmEKW7tOy28vTcIokzC/0j7PLFC5qI1tSULsmkzio9gZTXVk9Lokdqpz6CN9RKN3Y4CVQK7VKIv9B9WN04TfbpL0X6LpMMgn2gp8TqJF7LEpXEqdXR1Kacqzl6g8hPj5kPAaPdNlfIw7QKBgQD/8u0GlgDK3KTF7CBd0f/hEnGdiPMMzGYSAWlQeRw+6WkPmcsqXKDJcPf79P0997QfHdQFxW7wI7zNNhBEtQWB+l7y1boxeEWPzCva2jEpqMb2nGxXYC/lK4GJw2oK+JFySYG4pQ2vLnoSqn+JLGfEMSMod4+BXlqxUU2s97fr5wKBgQDnqplhN18w6SQv2FOL0IpCH/sk1mnnHAOn+YQ89RtacVTEBkDoUM4uPuHq/zf5NHmZZglc5VdD0FHBPtwAflfxTl2SwfR50T/mOA6AkUErgbclw5tqAY/jwtZidCfu+LtCweS5f2B/Ir/1k6k1SNe4JsnFmYGAa1mQ8pCmPpOJnQKBgCKcGM0RL54+49EXsvC82tr2gYKJ6EPMx+ibZEBLgDRJn7lKVJyv1dYnfoEFidD9U8/5WphFMFEso/Uj4GNKDyqaToiLuhfCabWeKDf51TgK6IykbkoKaa+ViHi2gfplzlUj93SebRFJOZyYjZJg/M8czeJE+JTHy3+6vMQs4yP1AoGAfCuVkGT0cavUyIxDo4IASRcJLqJ6GvSKBSOZPG8H3T83duX/LkX5bRIpVyK6pqj4sD7fr9oBHwjueQyvMNiHXIBraTjqR8zfWg8bvs+cRax+NRCWIYh6zlJlRFoL+Wt6O3rflIlweFRw3TjrDfNgJIUtyurPXbysQiOxYlnSDIUCgYEA8CZo+wjszR3oIZWX2fqDrWVvL3qAshBYZqzoe3xkKznm8sbjc7BQiG+pBaQ80a046+9Q3+ObDpn0KrSHC84Q0Kv7SdJ2k03RFwT0urVu2rJzHXeaEQRGaNnfpiTx85KgayDQfq7Va45F/Y2cEAf5ioivdCIJhBElrVupdcQNYpY=";

            String timeStamp = DateUtil.getTimestamp().toString();
            String data = "appKey=app_10086476614&merchantNo=10086476614&token=" + response.getResult().getToken() + "&timestamp="
                    + timeStamp
                    + "&directPayType=&cardType=&userNo=&userType=&ext=";

            String signatureWithPrikey = YopSignUtils.sign(data, certType, RSAKeyUtils.string2PrivateKey(priKey));
            System.out.println(signatureWithPrikey);
            //Assert.assertTrue(StringUtils.isNotEmpty(signatureWithPrikey));
            //String signatureWithAppKey = YopSignUtils.sign(data, certType, "app_10086476614");
            //Assert.assertTrue(StringUtils.isNotEmpty(signatureWithAppKey));

            System.out.println(url + "?" + data + "&sign=" + signatureWithPrikey);
        } catch (YopClientException e) {
            System.err.println("Exception when calling TradeClient#order");
            e.printStackTrace();
        }
    }
}
