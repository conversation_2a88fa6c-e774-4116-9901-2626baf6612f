package com.std.core.yeepay;

import com.std.core.util.DateUtil;
import com.yeepay.yop.sdk.security.YopSignUtils;
import com.yeepay.yop.sdk.security.rsa.RSAKeyUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class YopSignUtilsTest {

    @Before
    public void setUp() {
        System.setProperty("yop.sdk.http", "true");
    }


    // RSA算法进行签名
    @Test
    public void testRsaSign() {
        System.setProperty("yop.sdk.config.env", "");
        System.setProperty("yop.sdk.config.file", "yop_sdk_config_default.json");
        String certType = "RSA2048";
        String priKey = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnnsSLuctvsiMji8sIJb1AhnWnBu7L+bJQIbwYlA4FRt8RS8fHMHHIOPwL4tBZExAif9/vtC/edHa52B7qbnLo7y8UgcKDWv/xjkafJYFxW7Pqde4QDQSydfTMvZOQC1+Ic18mcMA6U4LxE4lBR6ZEz7IAxGxbBvVIpepqBooh7aKNaEIWfHUj8RAN9Ev6KWUlCXD6zUcg1yo9zAmbUlnSvh28Hh/+Q3ZIg9aaIMsJ2enw2O7fYtpAp8UkRR2YSNgDN0JxrCd9crCRZObb0G+a65atnN+YW9BPJtekPLcBeJ5nRK1YRtBqyabiVdz2gR3k7qzsKyjDYMYR5S3Ar0urAgMBAAECggEABcdSGB2omS0S2lsfNz2LIZYK+fOJfqSBUd5dl9MqPGI+p+r6spoC3ZjGzeqqj+12NzN2v79kCh988iPrCt9Bjsb04ATSv6+nlENHDhygKA/MsZbY2AhzHUXQcIodKWo6x8RVGsY+zYBjxRfboJ1BZTFSAr1yBmR4jHlk/xJLB530JLkw8ybI4JsbMa1BXC8N7cKsn90nKQmEKW7tOy28vTcIokzC/0j7PLFC5qI1tSULsmkzio9gZTXVk9Lokdqpz6CN9RKN3Y4CVQK7VKIv9B9WN04TfbpL0X6LpMMgn2gp8TqJF7LEpXEqdXR1Kacqzl6g8hPj5kPAaPdNlfIw7QKBgQD/8u0GlgDK3KTF7CBd0f/hEnGdiPMMzGYSAWlQeRw+6WkPmcsqXKDJcPf79P0997QfHdQFxW7wI7zNNhBEtQWB+l7y1boxeEWPzCva2jEpqMb2nGxXYC/lK4GJw2oK+JFySYG4pQ2vLnoSqn+JLGfEMSMod4+BXlqxUU2s97fr5wKBgQDnqplhN18w6SQv2FOL0IpCH/sk1mnnHAOn+YQ89RtacVTEBkDoUM4uPuHq/zf5NHmZZglc5VdD0FHBPtwAflfxTl2SwfR50T/mOA6AkUErgbclw5tqAY/jwtZidCfu+LtCweS5f2B/Ir/1k6k1SNe4JsnFmYGAa1mQ8pCmPpOJnQKBgCKcGM0RL54+49EXsvC82tr2gYKJ6EPMx+ibZEBLgDRJn7lKVJyv1dYnfoEFidD9U8/5WphFMFEso/Uj4GNKDyqaToiLuhfCabWeKDf51TgK6IykbkoKaa+ViHi2gfplzlUj93SebRFJOZyYjZJg/M8czeJE+JTHy3+6vMQs4yP1AoGAfCuVkGT0cavUyIxDo4IASRcJLqJ6GvSKBSOZPG8H3T83duX/LkX5bRIpVyK6pqj4sD7fr9oBHwjueQyvMNiHXIBraTjqR8zfWg8bvs+cRax+NRCWIYh6zlJlRFoL+Wt6O3rflIlweFRw3TjrDfNgJIUtyurPXbysQiOxYlnSDIUCgYEA8CZo+wjszR3oIZWX2fqDrWVvL3qAshBYZqzoe3xkKznm8sbjc7BQiG+pBaQ80a046+9Q3+ObDpn0KrSHC84Q0Kv7SdJ2k03RFwT0urVu2rJzHXeaEQRGaNnfpiTx85KgayDQfq7Va45F/Y2cEAf5ioivdCIJhBElrVupdcQNYpY=";

        String timeStamp = DateUtil.getTimestamp().toString();
        String data =
                "appKey=app_10086476614&merchantNo=10086476614&token=45F2A37E1C46AEF7AE7DDF2103E9D8D461CCFF338773626C4D5F63896BB928BE&timestamp="
                        + timeStamp
                        + "&cardType=&userNo=&userType=&ext=";

        String signatureWithPrikey = YopSignUtils.sign(data, certType, RSAKeyUtils.string2PrivateKey(priKey));
        System.out.println(signatureWithPrikey);
        Assert.assertTrue(StringUtils.isNotEmpty(signatureWithPrikey));
        String signatureWithAppKey = YopSignUtils.sign(data, certType, "app_10086476614");
        Assert.assertTrue(StringUtils.isNotEmpty(signatureWithAppKey));
    }

    // RSA算法验证签名
    @Test
    public void testVerifyRsaSign() {
        String pubKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6p0XWjscY+gsyqKRhw9MeLsEmhFdBRhT2emOck/F1Omw38ZWhJxh9kDfs5HzFJMrVozgU+SJFDONxs8UB0wMILKRmqfLcfClG9MyCNuJkkfm0HFQv1hRGdOvZPXj3Bckuwa7FrEXBRYUhK7vJ40afumspthmse6bs6mZxNn/mALZ2X07uznOrrc2rk41Y2HftduxZw6T4EmtWuN2x4CZ8gwSyPAW5ZzZJLQ6tZDojBK4GZTAGhnn3bg5bBsBlw2+FLkCQBuDsJVsFPiGh/b6K/+zGTvWyUcu+LUj2MejYQELDO3i2vQXVDk7lVi2/TcUYefvIcssnzsfCfjaorxsuwIDAQAB";
        String signature = "jx5r9cwnQPk7MI7lu0z76OOVhmbZcfPF4zagVBbUaODVET3rBoKkmLYGIbq4t-LBZRKr4_TNb22M20bxpPcND4ZzDJtYQg9StBRfJte3fzJnyBYMh-xajXvdbcxBf41CVuvLC6-6U1z6b-_48-Ffl0mKuAeiLHWKya6z7h5QkknMqPdFvCXkENo8DAcBIn6C31I48riEvLKVEc_upSFZctuXI-flhzMrOT-r18_3Dj6V8eK9eLijI8QA59oj1B07IdAfrlrozk0l8aA-PnTvS87evz7dmUfClKiG8qHkfSffQNLTCtl-rVd9Ynxpw6CRuo96f3Os0nKNiEJUPeTDVg$SHA256";

        String timeStamp = DateUtil.getTimestamp().toString();
        System.out.println("timeStamp:" + timeStamp);
        String data =
                "appKey=app_10086476614&merchantNo=10086476614&token=45F2A37E1C46AEF7AE7DDF2103E9D8D461CCFF338773626C4D5F63896BB928BE&timestamp="
                        + timeStamp
                        + "&cardType=&userNo=&userType=&ext=";
        YopSignUtils.verify(data, signature, RSAKeyUtils.string2PublicKey(pubKey));
    }
}
