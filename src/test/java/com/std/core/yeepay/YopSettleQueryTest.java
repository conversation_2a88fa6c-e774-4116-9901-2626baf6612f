package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.settle.SettleClient;
import com.yeepay.yop.sdk.service.settle.SettleClientBuilder;
import com.yeepay.yop.sdk.service.settle.request.RecordsQueryRequest;
import com.yeepay.yop.sdk.service.settle.response.RecordsQueryResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopSettleQueryTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final SettleClient api = SettleClientBuilder.builder().build();

    public static void main(String[] args) {
        queryResult();
    }

    public static void queryResult() {
//        QueryRequest request = new QueryRequest();
//        request.setParentMerchantNo("10086476614");
//        request.setMerchantNo("10086476614");
//        request.setDivideRequestId("101320220327000000337933220502");
//        request.setOrderId("537804732958515200");
//        request.setUniqueOrderNo("1013202203270000003379332205");
//        try {
//            QueryResponse response = api.query(request);
//            System.out.println(response.getResult());
//        } catch (YopClientException e) {
//            System.err.println("Exception when calling DivideClient#query");
//            e.printStackTrace();
//        }

        RecordsQueryRequest request = new RecordsQueryRequest();
        request.setParentMerchantNo("10086476614");
        request.setMerchantNo("10086624791");
//        request.setSettleRequestNo("settleRequestNo_example");
        request.setSettleRequestBeginTime("2022-03-27 00:00:00");
        request.setSettleRequestEndTime("2022-04-19 23:59:59");
        try {
            RecordsQueryResponse response = api.recordsQuery(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.out.println(e.getMessage());
        }
    }
}
