package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.trade.TradeClient;
import com.yeepay.yop.sdk.service.trade.TradeClientBuilder;
import com.yeepay.yop.sdk.service.trade.request.RefundEndRequest;
import com.yeepay.yop.sdk.service.trade.request.RefundQueryRequest;
import com.yeepay.yop.sdk.service.trade.request.RefundRequest;
import com.yeepay.yop.sdk.service.trade.response.RefundEndResponse;
import com.yeepay.yop.sdk.service.trade.response.RefundQueryResponse;
import com.yeepay.yop.sdk.service.trade.response.RefundResponse;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopOrderBackTest {


    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final TradeClient api = TradeClientBuilder.builder().build();

    public static void main(String[] args) {
//        payBack();
//        getInfo();
        payBackClose();
    }

    private static void payBack() {
        RefundRequest request = new RefundRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setOrderId("543562010261200896");
        request.setRefundRequestId("543617718298091520");
        request.setUniqueOrderNo("1013202204120000003436395389");
        request.setRefundAmount("39.90");
        request.setDescription("用户申请退货并退款");
        request.setMemo("退款");
//        request.setRefundAccountType("FUND_ACCOUNT");
//        request.setNotifyUrl("notifyUrl_example");
//        request.setYpPromotionRefundInfo("ypPromotionRefundInfo_example");
        try {
            RefundResponse response = api.refund(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling TradeClient#refund");
            e.printStackTrace();
        }
    }

    private static void payBackClose() {
        RefundEndRequest request = new RefundEndRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setOrderId("543562010261200896");
        request.setRefundRequestId("543617718298091520");
//        request.setRefundAccountType("FUND_ACCOUNT");
//        request.setNotifyUrl("notifyUrl_example");
//        request.setYpPromotionRefundInfo("ypPromotionRefundInfo_example");
        try {
            RefundEndResponse response = api.refundEnd(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling TradeClient#refundEnd");
            e.printStackTrace();
        }
    }


    public static void getInfo() {
        RefundQueryRequest request = new RefundQueryRequest();
        request.setParentMerchantNo("***********");
        request.setMerchantNo("***********");
        request.setOrderId("543562010261200896");
        request.setRefundRequestId("543617718298091520");

//        request.setUniqueRefundNo("1013202203290000003386011968");
        try {
            RefundQueryResponse response = api.refundQuery(request);
            System.out.println(response.getResult());
        } catch (YopClientException e) {
            System.err.println("Exception when calling TradeClient#refundQuery");
            e.printStackTrace();
        }
    }


}
