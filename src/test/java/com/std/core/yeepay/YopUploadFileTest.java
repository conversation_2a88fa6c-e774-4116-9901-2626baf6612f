package com.std.core.yeepay;

import com.yeepay.yop.sdk.exception.YopClientException;
import com.yeepay.yop.sdk.service.sys.SysClient;
import com.yeepay.yop.sdk.service.sys.SysClientBuilder;
import com.yeepay.yop.sdk.service.sys.request.MerchantQualUploadRequest;
import com.yeepay.yop.sdk.service.sys.response.MerchantQualUploadResponse;
import java.io.File;

/**
 * Copyright: Copyright (c)2014<br> Company: 易宝支付(YeePay)<br>
 */
public class YopUploadFileTest {

    // 该Client线程安全，请使用单例模式，多次请求共用
    private static final SysClient api = SysClientBuilder.builder().build();

    public static void main(String[] args) {
//        System.out.println(getPicUrl("/Users/<USER>/Desktop/file/24641648297138_.pic_hd.jpg"));
        System.out.println(getPicUrl("/Users/<USER>/Desktop/file/clkj_khxk.jpg"));
//        System.out.println(getPicUrl("/Users/<USER>/Desktop/file/clkj_idno_fmian.jpg"));

//        System.out.println(getPicUrl("/Users/<USER>/Desktop/file/clkj_licence.jpg"));

    }

    private static String getPicUrl(String fileUrl) {
        MerchantQualUploadRequest request = new MerchantQualUploadRequest();
        request.setMerQual(new File(fileUrl));
        try {
            MerchantQualUploadResponse response = api.merchantQualUpload(request);

            return response.getResult().getMerQualUrl();
        } catch (YopClientException e) {
            e.printStackTrace();
        }

        return null;
    }
}
