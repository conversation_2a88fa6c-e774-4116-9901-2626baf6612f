import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

@Slf4j
public class Test {
    public static void main(String[] args) {
        try (FileChannel channel = new FileInputStream("doc/db/upgrade_v4.2.0.sql").getChannel()) {
            ByteBuffer buffer=ByteBuffer.allocate(10);
            int read = channel.read(buffer);
            buffer.flip();
            while (buffer.hasRemaining()){
                byte b = buffer.get();
                log.info("打印："+(char)b);
            }
        } catch (IOException e) {
        }
    }
}
