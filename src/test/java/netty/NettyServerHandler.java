package netty;

import com.alibaba.fastjson.JSON;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.AttributeKey;
import io.netty.util.CharsetUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

public class NettyServerHandler extends ChannelInboundHandlerAdapter {

    private static final ConcurrentHashMap<String, Channel> channelMap = new ConcurrentHashMap<>();

    // 两个map，一个key：userId，一个key：场景id
//    private static final ConcurrentHashMap<String, Channel> channelMap = new ConcurrentHashMap<>();


    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        System.out.println("新的客户端链接地址：" + ctx.channel().remoteAddress());
//        String str = "hello";
//        Map<Long,Map<Long,String>> map=new HashMap<>();
    }

    /**
     * 读取客户端的信息
     *
     * @param ctx
     * @param msg
     * @throws Exception
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        System.out.print("ctx = " + ctx.channel().remoteAddress() + " \t");
        String message = msg.toString();
        System.out.println("客户端发送的消息是 ： " + message);

        if (StringUtils.isBlank(message)) {
            return;
        }

        //去掉分隔符
        message = message.replace("|", "");

        System.out.println("客户端消息处理后是 ： " + message);

        SocketMessage webSocketMessage = null;

        SocketMessage webSocketMess = new SocketMessage();

        if (message.contains("JoinRoom")) {
            webSocketMessage = JSON.parseObject(message, SocketMessage.class);
//            //绑定用户
            online(ctx.channel(), webSocketMessage.getData().toString());

            webSocketMess.setRequestCode("UpdateRoom");
            List<UserAo> userList = new ArrayList<>();
            for (String s : channelMap.keySet()) {
                userList.add(JSON.parseObject(s, UserAo.class)) ;
            }

            userList.sort(Comparator.comparing(UserAo::getUserId));

            webSocketMess.setData(userList);


            channelMap.forEach((s, channel) -> {
                sendMsg(channel,JSON.toJSONString(webSocketMess));
            });

        } else if (message.contains("Move")) {
            webSocketMessage = JSON.parseObject(message, SocketMessage.class);
            //发送位置。推送给其他用户
            webSocketMess.setRequestCode("Move");
            webSocketMess.setData(webSocketMessage.getData());


            channelMap.forEach((s, channel) -> {
                if (!ctx.channel().equals(channel)) {
                    sendMsg(channel,JSON.toJSONString(webSocketMess));
                }
            });


        } else if (message.contains("start")) {
            webSocketMess.setRequestCode("StartGame");
            channelMap.forEach((s, channel) -> {
                sendMsg(channel,JSON.toJSONString(webSocketMess));
            });
        } else if (message.contains("close")) {
            channelMap.forEach((s, channel) -> {
                channelMap.remove(s);
            });
        }else if (message.contains("MsgPing")){
            webSocketMess.setRequestCode("MsgPong");
            webSocketMess.setData(System.currentTimeMillis());
            sendMsg(ctx.channel(),JSON.toJSONString(webSocketMess));
        } else {
            sendMsg(ctx.channel(),message);
        }
    }

    public  void sendMsg(Channel channel,String msg) {
        if (!channel.isActive()){
            channelMap.remove(getUserId(channel));
            return;
        }

        channel.writeAndFlush(Unpooled.copiedBuffer(msg+"|", CharsetUtil.UTF_8));
    }

    /**
     * 读完成
     */
    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
//        ctx.writeAndFlush(Unpooled.copiedBuffer("hello,客户端～～",CharsetUtil.UTF_8));
    }

    /**
     * 发生异常，关闭
     *
     * @param ctx
     * @param cause
     * @throws Exception
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        System.out.println(cause.getMessage());
        ctx.close();

    }


    /**
     * 判断一个通道是否有用户在使用
     * 可做信息转发时判断该通道是否合法
     *
     * @param channel
     * @return
     */
    public boolean hasUser(Channel channel) {
        AttributeKey<String> key = AttributeKey.valueOf("user");
        return (channel.hasAttr(key) || channel.attr(key).get() != null);
        //netty移除了这个map的remove方法,这里的判断谨慎一点
    }


    /**
     * 判断一个通道是否有用户在使用
     * 可做信息转发时判断该通道是否合法
     *
     * @param channel
     * @return
     */
    public String getUserId(Channel channel) {
        AttributeKey<String> key = AttributeKey.valueOf("user");
        if (hasUser(channel)) {
            return channel.attr(key).get();
        }
        return null;
    }

    /**
     * 上线一个用户
     *
     * @param channel
     * @param userId
     */
    public void online(Channel channel, String userId) {
        //先判断用户是否在系统中登录?
        //这部分代码个人实现,参考上面redis中的验证

        channelMap.put(userId, channel);
        AttributeKey<String> key = AttributeKey.valueOf("user");
        channel.attr(key).set(userId);
    }

    /**
     * 根据用户id获取该用户的通道
     *
     * @param userId
     * @return
     */
    public Channel getChannelByUserId(String userId) {
        return channelMap.get(userId);
    }

    /**
     * 判断一个用户是否在线
     *
     * @param userId
     * @return
     */
    public Boolean online(String userId) {
        return channelMap.containsKey(userId) && channelMap.get(userId) != null;
    }

}
