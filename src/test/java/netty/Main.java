package netty;

import java.util.Scanner;

public class Main {

    public static void main(String[] args) {
        String str = "";

        Scanner sc = new Scanner(System.in);

        while (true) {
            System.out.println("请输入");
            String inputVal = sc.nextLine();

            //包含
            if (inputVal.contains("|")) {
                String[] array = inputVal.split("\\|");
                str += array[0];

                System.out.println("输出:" + str);
                //如果最后一位是 |  表示结束了
                if (inputVal.endsWith("\\|")) {
                    str = "";
                    for (int i = 1; i < array.length; i++) {
                        System.out.println("输出:" + array[i]);
                    }
                } else {
                    //最后一个不是| 没有结束，把最后一个留存下来和下次拼接
                    str = array[array.length - 1];
                    for (int i = 1; i < array.length - 1; i++) {
                        System.out.println("输出:" + array[i]);
                    }
                }
            } else {
                str += inputVal;
            }
        }

    }
}
