package netty;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.util.CharsetUtil;
import java.util.Scanner;

public class NettyClient {

    public static void main(String[] args) throws InterruptedException {
        NioEventLoopGroup eventExecutors = new NioEventLoopGroup();

        try {
            Bootstrap bootstrap = new Bootstrap();
            bootstrap.group(eventExecutors)
                    .channel(NioSocketChannel.class)
                    .option(ChannelOption.SO_KEEPALIVE, true)
                    .handler(new ChannelInitializer<SocketChannel>() {

                        @Override
                        protected void initChannel(SocketChannel socketChannel) throws Exception {
                            ChannelPipeline channelPipeline = socketChannel.pipeline();
                            channelPipeline.addLast(new StringDecoder());
                            channelPipeline.addLast(new StringEncoder());
                            channelPipeline.addLast(new NettyClientHandler());
                        }
                    });
//            *************
            ChannelFuture channelFuture = bootstrap.connect("m.dev.hzyuzhouyuan.com", 80).sync();
            Scanner scanner = new Scanner(System.in);
            while (scanner.hasNextLine()) {
                String nextLine = scanner.nextLine();
                channelFuture.channel().writeAndFlush(Unpooled.copiedBuffer(nextLine + "|", CharsetUtil.UTF_8));

            }

            channelFuture.channel().closeFuture().sync();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            eventExecutors.shutdownGracefully();
        }

//        try {
//            Bootstrap bootstrap = new Bootstrap();
//            bootstrap.group(eventExecutors)
//                    .channel(NioSocketChannel.class)
//                    .handler(new ChannelInitializer<SocketChannel>() {
//                        @Override
//                        protected void initChannel(SocketChannel socketChannel) throws Exception {
////                            socketChannel.pipeline().addLast(new LengthFieldPrepender(4));
//                            socketChannel.pipeline().addLast(new NettyClientHandler());
//                        }
//                    });
//
//            System.out.println("客户端 OK 。。。");
//
//            ChannelFuture sync = bootstrap.connect("127.0.0.1", 8106).sync();
//            Scanner scanner = new Scanner(System.in);
//            while (scanner.hasNextLine()){
//                String nextLine = scanner.nextLine();
//                for (int i = 0; i < 100; i++) {
//                    sync.channel().writeAndFlush(Unpooled.copiedBuffer(nextLine, CharsetUtil.UTF_8));
//                }
//            }
//
//            sync.channel().closeFuture().sync();
//        } finally {
//            eventExecutors.shutdownGracefully();
//        }
    }
}

