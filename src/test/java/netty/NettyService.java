package netty;


import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;

import java.nio.charset.StandardCharsets;


/**
 * <AUTHOR>
 */
public class NettyService {


    public static void main(String[] args) throws InterruptedException {

        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup();

        try {
            ServerBootstrap serverBootstrap = new ServerBootstrap();
            serverBootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childHandler(new ChannelInitializer<SocketChannel>() {

                        @Override
                        protected void initChannel(SocketChannel socketChannel) throws Exception {
                            ChannelPipeline channelPipeline = socketChannel.pipeline();
                            channelPipeline.addLast(new DelimiterBasedFrameDecoder(20*1024, false,
                                    Unpooled.copiedBuffer("|", StandardCharsets.UTF_8)));
                            //先添加DelimiterBasedFrameDecoder，指定分隔符：END，并且包含分隔符
                            channelPipeline.addLast(new StringDecoder());
                            //然后添加StringDecoder
                            channelPipeline.addLast(new StringEncoder());
                            channelPipeline.addLast(new NettyServerHandler());
                        }
                    });

            ChannelFuture channelFuture = serverBootstrap.bind(8106).sync();
            channelFuture.channel().closeFuture().sync();
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }


//        EventLoopGroup boss = new NioEventLoopGroup();
//        EventLoopGroup work = new NioEventLoopGroup();
//
//        try {
//            ServerBootstrap serverBootstrap = new ServerBootstrap();
//
//            serverBootstrap.group(boss, work)
//                    .channel(NioServerSocketChannel.class)
//                    .option(ChannelOption.SO_BACKLOG, 1024)
//                    .childOption(ChannelOption.SO_BACKLOG, 1)
//                    .childHandler(new ChannelInitializer<SocketChannel>() {
//                        @Override
//                        protected void initChannel(SocketChannel socketChannel) throws Exception {
//                            socketChannel.pipeline().addLast(new DelimiterBasedFrameDecoder(1024,
//                                    false, Unpooled.copiedBuffer("END", StandardCharsets.UTF_8)));
//
////                            socketChannel.pipeline().addLast(new LengthFieldBasedFrameDecoder(Integer.MAX_VALUE, 0,
////                                    4,0,4));//编码
//                            socketChannel.pipeline().addLast(new NettyServerHandler());
//                        }
//                    });
//
//            System.out.println("服务器准备好了");
//
//            ChannelFuture sync = serverBootstrap.bind(8106).sync();
//
//            sync.channel().closeFuture().sync();
//        } finally {
//            boss.shutdownGracefully();
//            work.shutdownGracefully();
//        }

    }


}
