
import com.alibaba.fastjson.JSON;

import com.std.core.pojo.domain.avata.dto.CreateAccountDTO;
import com.std.core.pojo.domain.avata.dto.DealResultDTO;
import com.std.core.pojo.domain.avata.dto.NFTClassListDTO;
import com.std.core.pojo.domain.avata.dto.NFTOperateDTO;
import com.std.core.pojo.domain.avata.req.*;
import com.std.core.service.AvataService;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.wechat.MD5Util;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Locale;
import java.util.Objects;

/**
 * <AUTHOR> ***********
 * @date: 2022/5/26
 */
@RunWith(SpringRunner.class)
@SpringBootTest()
public class AvataProxyTest {

    @Resource
    private AvataService avataProxy;

    @Test
    public void testCreateAvataAccountFail() {
        System.out.println("创建账户错误示例");
        CreateAccountDTO createAccountDTO = avataProxy.createAccount("测试", "test1653568056132");
    }

    @Test
    public void testCreateAvataAccountSuccess() {
        System.out.println("创建账户正确示例");
        CreateAccountDTO createAccountDTO = avataProxy.createAccount("测试", "test" + System.currentTimeMillis());

        System.out.println(JSON.toJSONString(createAccountDTO));
    }

    @Test
    public void testQueryAvataAccountSuccess() {
        System.out.println("账户正确示例");
        CreateAccountDTO createAccountDTO = avataProxy.createAccount("测试", "test" + System.currentTimeMillis());
    }


    @Test
    public void testTransferNFT() {
        System.out.println("测试NFT转让");

        NFTTransferReq nftTransferReq = new NFTTransferReq();
        nftTransferReq.setClass_id("");
        nftTransferReq.setNft_id("");
        nftTransferReq.setOwner("");
        nftTransferReq.setOperation_id("test123456");
        nftTransferReq.setRecipient("");
        NFTOperateDTO nftPublishDTO = avataProxy.transferNFT(nftTransferReq);
    }

    @Test
    public void testCreateNFT() {
        NFTCreateReq nftCreateReq = new NFTCreateReq();
        //类别名称，可填写藏品名称
        nftCreateReq.setName("测试名称");
        //nft 类别id ,可以是 nft+藏品id
        nftCreateReq.setClass_id("zd1110");

        nftCreateReq.setOwner("iaa1mvwguesza5quuypxgnml9qwx29pq7kf4cfwq29");
        nftCreateReq.setOperation_id("test" + System.currentTimeMillis());
        avataProxy.createNFT(nftCreateReq);
    }

    @Test
    public void testQueryNFTClassList() {
        System.out.println("测试NFT类别查询");
        // 请求参数
        NFTClassListReq nftClassListReq = new NFTClassListReq();
        nftClassListReq.setOwner("iaa1mvwguesza5quuypxgnml9qwx29pq7kf4cfwq29");
//        nftClassListReq.setName("test123456");
        nftClassListReq.setLimit("10");
        NFTClassListDTO nftClassListDTO = avataProxy.queryNFTClassList(nftClassListReq);
    }


    @Test
    public void testPublishNFT() {
        System.out.println("测试NFT发行");

        NFTPublishReq nftPublishReq = new NFTPublishReq();
        //类别ID
        nftPublishReq.setClass_id("zd1110");
        //NFT名称。 可以是 藏品名称#Tokenid
        nftPublishReq.setName("test123456");

        nftPublishReq.setOperation_id("test"+  System.currentTimeMillis());
        nftPublishReq.setRecipient("");
        nftPublishReq.setData("");

        NFTOperateDTO nftPublishDTO = avataProxy.publishNFT(nftPublishReq);
    }



    @Test
    public void testQueryDealResult() {
        System.out.println("测试上链交易结果查询");
//        DealResultDTO dealResultDTO = avataProxy.queryDealResult("");
        DealResultDTO dealResultDTO2 = avataProxy.queryDealResult("test1672717854346");

        System.out.println(JSON.toJSONString(dealResultDTO2));

    }


    @Test
    public void testDestroyNFT() {
        System.out.println("NFT销毁Destroy");

        NFTDestoryReq nftDestoryReq = new NFTDestoryReq();
        nftDestoryReq.setClass_id("1");
        nftDestoryReq.setNft_id("");
        nftDestoryReq.setOwner("iaa1mvwguesza5quuypxgnml9qwx29pq7kf4cfwq29");
        nftDestoryReq.setOperation_id("test"+  System.currentTimeMillis());
        NFTOperateDTO nftPublishDTO = avataProxy.destroyNFT(nftDestoryReq);
    }

    @Test
    public void testRecordNFT() {
        System.out.println("数字作品存证");
        NFTRecordReq recordReq = new NFTRecordReq();
        recordReq.setIdentity_type(1);
        recordReq.setIdentity_name("zhouDong");
        recordReq.setIdentity_num("42070219940907687X");

        recordReq.setType(2);
        recordReq.setName("tes22t");
        recordReq.setDescription("tttttt1112tttttttttttttt");
        recordReq.setHash(Objects.requireNonNull(MD5Util.md5("1234232356哈哈哈哈哈哈")).toUpperCase(Locale.ROOT));
        recordReq.setHash_type(3);
        recordReq.setOperation_id("test"+  System.currentTimeMillis());
        NFTOperateDTO operateDTO = avataProxy.nftRecord(recordReq);

        System.out.println(JSON.toJSONString(operateDTO));
    }

}
