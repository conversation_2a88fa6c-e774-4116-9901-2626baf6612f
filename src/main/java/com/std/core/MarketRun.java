package com.std.core;

import com.std.core.netty.NettyServerHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.string.StringDecoder;
import io.netty.handler.codec.string.StringEncoder;
import io.netty.handler.timeout.IdleStateHandler;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Component
@Order(2)
@Slf4j
public class MarketRun implements CommandLineRunner {

    @Autowired
    private NettyServerHandler nettyServerHandler;

    @Override
    public void run(String... args) throws Exception {
        nettyStart();
    }

    public void nettyStart() throws InterruptedException {
        EventLoopGroup bossGroup = new NioEventLoopGroup();
        EventLoopGroup workerGroup = new NioEventLoopGroup();
        try {
            ServerBootstrap serverBootstrap = new ServerBootstrap();
            serverBootstrap.group(bossGroup, workerGroup)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, 128)
                    .childOption(ChannelOption.SO_KEEPALIVE, true)
                    .childHandler(new ChannelInitializer<SocketChannel>() {

                        @Override
                        protected void initChannel(SocketChannel socketChannel) throws Exception {
                            ChannelPipeline channelPipeline = socketChannel.pipeline();
                            channelPipeline.addLast(new DelimiterBasedFrameDecoder(20 * 1024, false,
                                    Unpooled.copiedBuffer("|", StandardCharsets.UTF_8)));
                            //先添加DelimiterBasedFrameDecoder，指定分隔符：END，并且包含分隔符
                            channelPipeline.addLast(new StringDecoder());
                            //心跳机制
                            channelPipeline.addLast("handler", new IdleStateHandler(0, 20, 0, TimeUnit.SECONDS));
//                            // 心跳断开处理逻辑
//                            channelPipeline.addLast(nettyServerHandler);
                            //然后添加StringDecoder
                            channelPipeline.addLast(new StringEncoder());
                            channelPipeline.addLast(nettyServerHandler);
                        }
                    });

            ChannelFuture channelFuture = serverBootstrap.bind(8107).sync();
            channelFuture.channel().closeFuture().sync();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            bossGroup.shutdownGracefully();
            workerGroup.shutdownGracefully();
        }
    }
}