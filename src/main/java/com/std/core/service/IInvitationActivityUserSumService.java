package com.std.core.service;

import com.std.core.pojo.domain.InvitationActivityUserSum;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityUserSumCreateReq;
import com.std.core.pojo.request.InvitationActivityUserSumListFrontReq;
import com.std.core.pojo.request.InvitationActivityUserSumListReq;
import com.std.core.pojo.request.InvitationActivityUserSumModifyReq;
import com.std.core.pojo.request.InvitationActivityUserSumPageFrontReq;
import com.std.core.pojo.request.InvitationActivityUserSumPageReq;
import com.std.core.pojo.response.InvitationActivityUserSumDetailRes;
import com.std.core.pojo.response.InvitationActivityUserSumListRes;
import com.std.core.pojo.response.InvitationActivityUserSumPageRes;
import com.std.core.pojo.response.InvitationActivityUserSumRankRes;
import java.util.List;

/**
 * 用户拉新信息Service
 *
 * <AUTHOR> ycj
 * @since : 2022-04-12 14:54
 */
public interface IInvitationActivityUserSumService {

    /**
     * 新增用户拉新信息
     *
     * @param req 新增用户拉新信息入参
     * @param operator 操作人
     */
    void create(InvitationActivityUserSumCreateReq req, User operator);

    /**
     * 删除用户拉新信息
     *
     * @param id 主键ID
     */
    void remove(Long id);

    /**
     * 修改用户拉新信息
     *
     * @param req 修改用户拉新信息入参
     * @param operator 操作人
     */
    void modify(InvitationActivityUserSumModifyReq req, User operator);

    /**
     * 详情查询用户拉新信息
     *
     * @param id 主键ID
     * @return 用户拉新信息详情数据
     */
    InvitationActivityUserSum detail(Long id);

    InvitationActivityUserSum detailForUpdate(Long id);

    InvitationActivityUserSum detail(Long activityId, Long userId);

    /**
     * 分页查询用户拉新信息
     *
     * @param req 分页查询用户拉新信息入参
     * @return 用户拉新信息分页数据
     */
    List<InvitationActivityUserSum> page(InvitationActivityUserSumPageReq req, User operator);

    /**
     * 列表查询用户拉新信息
     *
     * @param req 列表查询用户拉新信息入参
     * @return 用户拉新信息列表数据
     */
    List<InvitationActivityUserSum> list(InvitationActivityUserSumListReq req);

    /**
     * 前端详情查询用户拉新信息
     *
     * @param id 主键ID
     * @return 用户拉新信息详情数据
     */
    InvitationActivityUserSumDetailRes detailFront(Long id);

    /**
     * 前端分页查询用户拉新信息
     *
     * @param req 分页查询用户拉新信息入参
     * @return 用户拉新信息分页数据
     */
    List<InvitationActivityUserSumPageRes> pageFront(InvitationActivityUserSumPageFrontReq req);

    /**
     * 前端列表查询用户拉新信息
     *
     * @param req 列表查询用户拉新信息入参
     * @return 用户拉新信息列表数据
     */
    List<InvitationActivityUserSumListRes> listFront(InvitationActivityUserSumListFrontReq req);

    void addInvitationSize(InvitationActivityUserSum activityUserSum);

    void addInvitationFinishSize(InvitationActivityUserSum userSum);

    void addCollectionSize(InvitationActivityUserSum userSum);

    /**
     * 排名处理
     */
    InvitationActivityUserSumRankRes detailRank(User operator, Long activityId);
}