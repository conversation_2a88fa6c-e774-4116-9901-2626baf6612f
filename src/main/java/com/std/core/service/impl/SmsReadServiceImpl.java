package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ESmsReadStatus;
import com.std.core.enums.ESmsStatus;
import com.std.core.enums.ESmsTarget;
import com.std.core.enums.ESmsType;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.SmsReadMapper;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.Sms;
import com.std.core.pojo.domain.SmsRead;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SmsMyPageCompanyReq;
import com.std.core.pojo.request.SmsMyPageReq;
import com.std.core.pojo.request.SmsPageMyReq;
import com.std.core.pojo.request.SmsReadListReq;
import com.std.core.pojo.request.SmsReadPageReq;
import com.std.core.pojo.response.SmsUnreadMessagesRes;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICompanyService;
import com.std.core.service.ISmsReadService;
import com.std.core.service.ISmsService;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * 公告阅读记录ServiceImpl
 *
 * <AUTHOR> golder
 * @since : 2020-04-14 20:43
 */
@Service
public class SmsReadServiceImpl implements ISmsReadService {

    @Resource
    private SmsReadMapper smsReadMapper;

    @Resource
    private ISmsService smsService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICompanyService companyService;

    /**
     * 新增公告阅读记录
     *
     * @param smsCode 新增公告阅读记录入参
     * @param operator 操作人
     */
    @Override
    public void create(Long smsCode, User operator) {
        boolean result = isExist(smsCode, operator.getId());
        if (result) {
            return;
        }

        SmsRead smsRead = new SmsRead();
        smsRead.setSmsCode(smsCode);
        smsRead.setUserId(operator.getId());
        smsRead.setStatus(ESmsReadStatus.READ.getCode());
        smsRead.setCreateDatetime(new Date());
        smsRead.setReadDatetime(new Date());

        smsReadMapper.insertSelective(smsRead);
    }

    @Override
    public void create(Long smsCode, Long companyId) {
        boolean result = isExist(smsCode, companyId);
        if (result) {
            return;
        }

        SmsRead smsRead = new SmsRead();
        smsRead.setSmsCode(smsCode);
        smsRead.setUserId(companyId);
        smsRead.setStatus(ESmsReadStatus.READ.getCode());
        smsRead.setCreateDatetime(new Date());
        smsRead.setReadDatetime(new Date());

        smsReadMapper.insertSelective(smsRead);
    }

    /**
     * 删除公告阅读记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        smsReadMapper.deleteByPrimaryKey(id);
    }

    @Override
    public void removeBySmsCode(Long smsCode) {
        smsReadMapper.deleteBySmsCode(smsCode);
    }

    /**
     * 详情查询公告阅读记录
     *
     * @param id 主键ID
     * @return 公告阅读记录对象
     */
    @Override
    public SmsRead detail(Long id) {
        SmsRead smsRead = smsReadMapper.selectByPrimaryKey(id);
        if (null == smsRead) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return smsRead;
    }

    /**
     * 分页查询公告阅读记录
     *
     * @param req 分页查询公告阅读记录入参
     * @return 分页公告阅读记录对象
     */
    @Override
    public List<SmsRead> page(SmsReadPageReq req) {
        SmsRead condition = EntityUtils.copyData(req, SmsRead.class);

        return smsReadMapper.selectByCondition(condition);
    }

    /**
     * 列表查询公告阅读记录
     *
     * @param req 列表查询公告阅读记录入参
     * @return 列表公告阅读记录对象
     */
    @Override
    public List<SmsRead> list(SmsReadListReq req) {
        SmsRead condition = EntityUtils.copyData(req, SmsRead.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), SmsRead.class));

        return smsReadMapper.selectByCondition(condition);
    }

    @Override
    public boolean isExist(Long userId, Long smsCode) {
        SmsRead condition = new SmsRead();
        condition.setUserId(userId);
        condition.setSmsCode(smsCode);

        Integer count = smsReadMapper.selectCount(condition);
        if (count > 0) {
            return true;
        }

        return false;
    }

    @Override
    public List<Sms> page(SmsPageMyReq request, User operator) {

        Sms condition = new Sms();
        condition.setType(request.getType());
//        condition.setTarget(ESmsTarget.CUSTOMER.getCode());
        condition.setStatus(ESmsStatus.SENDED.getCode());
        condition.setUserId(operator.getId());
        // 如果是查全部
        if (StringUtils.isBlank(request.getType())) {
            // 如果不是登录状态只能看公告
            if (operator.getId() == null) {
                condition.setType(ESmsType.SYSTEM.getCode());
            }
        }

        // 获取商户渠道商
        ChannelMerchant channelMerchant = channelMerchantService.detailByFront(request.getChannelId());
        condition.setChannelId(channelMerchant.getId());
        condition.setOrderBy("t.id desc");
        return smsReadMapper.selectMySmsByCondition(condition);
    }

    /**
     * 系统公告未读数量
     */
    @Override
    public SmsUnreadMessagesRes getMyUnreadCount(User operator) {
        SmsUnreadMessagesRes smsUnreadMessagesRes = new SmsUnreadMessagesRes();
        Integer myUnreadCount = 0;
        Integer msgMyUnreadCount = 0;
        if (operator != null) {
            Sms condition = new Sms();
            condition.setUserId(operator.getId());
            condition.setTarget(EUserKind.C.getCode());
            condition.setStatus(ESmsStatus.SENDED.getCode());
            // 未读公告
            condition.setType(ESmsType.SYSTEM.getCode());
            myUnreadCount = smsReadMapper.selectMyUnreadCount(condition);

            // 未读消息
            condition.setTarget(operator.getKind());
            msgMyUnreadCount = smsReadMapper.selectMsgMyUnreadCount(condition);
        }

        smsUnreadMessagesRes.setMyUnreadCount(myUnreadCount + msgMyUnreadCount);
        smsUnreadMessagesRes.setMsgMyUnreadCount(msgMyUnreadCount);
        return smsUnreadMessagesRes;
    }

    @Override
    public SmsUnreadMessagesRes getMyUnreadExists(User operator, ChannelMerchant channelMerchant) {
        SmsUnreadMessagesRes smsUnreadMessagesRes = new SmsUnreadMessagesRes();
        Integer myUnreadCount = 0;
        if (operator != null) {

            //查询自己是否有消息未阅读，没有查询公告是否有阅读的
            Sms condition = new Sms();
            condition.setUserId(operator.getId());
            condition.setTarget(EUserKind.C.getCode());
            condition.setStatus(ESmsStatus.SENDED.getCode());
            condition.setChannelId(channelMerchant.getId());
            myUnreadCount = smsReadMapper.selectMsgMyUnreadCount(condition);

            if (myUnreadCount <= 0) {
                myUnreadCount = smsReadMapper.selectMyUnreadExists(condition);
            }
        }

        smsUnreadMessagesRes.setMyUnreadCount(myUnreadCount);
        smsUnreadMessagesRes.setMsgMyUnreadCount(myUnreadCount);
        return smsUnreadMessagesRes;
    }

    /**
     * 一键已读
     */
    @Override
    public void readAll(User operator, Long channelId) {
        List<Sms> smsList = new ArrayList<>();
        // 未读公告
        List<Sms> smsListP = smsService.getMyUnread(operator, channelId);
        if (CollectionUtils.isNotEmpty(smsListP)) {
            smsList.addAll(smsListP);
        }
        // 未读消息
        List<Sms> smsListC = smsService.getMsgMyUnread(operator, channelId);
        if (CollectionUtils.isNotEmpty(smsListC)) {
            smsList.addAll(smsListC);
        }

        for (Sms sms : smsList) {
            create(sms.getId(), operator);
        }
    }

    @Override
    public List<Sms> pageByType(SmsMyPageReq request, User operator) {
        Sms condition = new Sms();
        condition.setType(request.getType());
        condition.setStatus(ESmsStatus.SENDED.getCode());
        if (null != operator) {
            condition.setUserId(operator.getId());

            // 如果是查全部
            if (StringUtils.isNotBlank(request.getType()) && ESmsType.MY.getCode().equals(request.getType())) {
                List<String> typeList = new ArrayList<>();
                typeList.add(ESmsType.MY.getCode());
                typeList.add(ESmsType.BUY_COLLECTION.getCode());
                condition.setTypeList(typeList);
                condition.setType(null);
                condition.setSmsUser(operator.getId());
            } else if (StringUtils.isNotBlank(request.getType()) && ESmsType.SYSTEM.getCode().equals(request.getType())) {
                List<String> typeList = new ArrayList<>();
                typeList.add(ESmsType.SYSTEM.getCode());
                typeList.add(ESmsType.COMPANY_SYSTEM.getCode());
                condition.setTypeList(typeList);
                condition.setType(null);
                condition.setSmsCompanyUser(operator.getId());
            }
        } else {
            condition.setType(ESmsType.SYSTEM.getCode());
        }
        condition.setChannelId(request.getChannelId());
        condition.setOrderBy("t.id desc");

        List<Sms> smsList = smsReadMapper.selectMySmsByType(condition);

        Map<Long, String> map = new HashMap<>();
        for (Sms sms : smsList) {
            if (ESmsType.COMPANY_SYSTEM.getCode().equals(sms.getType())) {
                String companyName = map.get(sms.getCompanyId());
                if (StringUtils.isBlank(companyName)) {
                    Company company = companyService.detailSimple(sms.getCompanyId());
                    companyName = company.getName();
                    map.put(sms.getCompanyId(), companyName);
                }
                sms.setRefNo(companyName);
            }
        }

        return smsList;
    }

    @Override
    public List<Sms> companyPage(SmsMyPageCompanyReq req, User operator) {
        Sms condition = EntityUtils.copyData(req, Sms.class);
        condition.setStatus(ESmsStatus.SENDED.getCode());
        condition.setSmsUser(operator.getCompanyId());
        condition.setUserId(operator.getId());
        condition.setTarget(ESmsTarget.COMPANY.getCode());

        condition.setOrderBy("t.id desc");

        //获取
        return smsReadMapper.selectMySmsByType(condition);
    }

    @Override
    public void readAllCompany(User operator) {
        List<Sms> smsList = new ArrayList<>();
        // 未读公告
        List<Sms> smsListP = smsService.getCompanyUnread(operator.getCompanyId());
        if (CollectionUtils.isNotEmpty(smsListP)) {
            smsList.addAll(smsListP);
        }

        for (Sms sms : smsList) {
            create(sms.getId(), operator);
        }
    }
}
