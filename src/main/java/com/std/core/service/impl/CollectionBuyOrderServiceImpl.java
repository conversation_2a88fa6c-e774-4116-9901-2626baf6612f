package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.std.common.base.BaseIdReq;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChannelType;
import com.std.core.enums.ECollectionBuyOrderBizType;
import com.std.core.enums.ECollectionBuyOrderPayStatus;
import com.std.core.enums.ECollectionBuyOrderPayType;
import com.std.core.enums.ECollectionBuyOrderSource;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailRefType;
import com.std.core.enums.ECollectionDetailSource;
import com.std.core.enums.ECollectionPayStatus;
import com.std.core.enums.ECollectionPeriodCategory;
import com.std.core.enums.ECollectionPeriodSoldStatus;
import com.std.core.enums.ECollectionPeriodStartStatus;
import com.std.core.enums.ECollectionPeriodStatus;
import com.std.core.enums.ECompanyDivideStatus;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EIncomeAmountType;
import com.std.core.enums.EInvitationActivityType;
import com.std.core.enums.EInvitationActivityUserRecordStatus;
import com.std.core.enums.EInvoiceOrderOrderType;
import com.std.core.enums.EJourBizTypeSystem;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.EJourCommon;
import com.std.core.enums.ELockBizType;
import com.std.core.enums.EPayBackOrderType;
import com.std.core.enums.EPayRecordBizType;
import com.std.core.enums.EPayType;
import com.std.core.enums.EPeriodAuctionBondRecordStatus;
import com.std.core.enums.EPeriodAuctionStatus;
import com.std.core.enums.EPeriodDiscountRefType;
import com.std.core.enums.ESmsRefType;
import com.std.core.enums.ESystemAccount;
import com.std.core.enums.EThirdDivideDetailType;
import com.std.core.enums.EUserKind;
import com.std.core.enums.EUserYaoFlag;
import com.std.core.enums.EYeepayStatus;
import com.std.core.mapper.CollectionBuyOrderMapper;
import com.std.core.mapper.CollectionPeriodMapper;
import com.std.core.mapper.CollectionPeriodRelationMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.AwardEntity;
import com.std.core.pojo.domain.BabyBankPayInfo;
import com.std.core.pojo.domain.BabyPayInfo;
import com.std.core.pojo.domain.BankChannel;
import com.std.core.pojo.domain.BankInfo;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionBuyOrder;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.InvitationActivityUserRecord;
import com.std.core.pojo.domain.OrderClose;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.PeriodAuction;
import com.std.core.pojo.domain.PeriodAuctionBondRecord;
import com.std.core.pojo.domain.PeriodChannelWord;
import com.std.core.pojo.domain.PeriodDiscountDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserBindCard;
import com.std.core.pojo.domain.UserSettleAccount;
import com.std.core.pojo.domain.YeeBankPayInfo;
import com.std.core.pojo.request.CollectionBlindBoxBuyReq;
import com.std.core.pojo.request.CollectionBuyOrderAuctionCreateReq;
import com.std.core.pojo.request.CollectionBuyOrderCheckReq;
import com.std.core.pojo.request.CollectionBuyOrderCreateReq;
import com.std.core.pojo.request.CollectionBuyOrderListFrontReq;
import com.std.core.pojo.request.CollectionBuyOrderListReq;
import com.std.core.pojo.request.CollectionBuyOrderModifyReq;
import com.std.core.pojo.request.CollectionBuyOrderPageFrontReq;
import com.std.core.pojo.request.CollectionBuyOrderPageReq;
import com.std.core.pojo.request.CollectionPeriodRelationListReq;
import com.std.core.pojo.response.AdaPayInfo;
import com.std.core.pojo.response.AlipayPayOrderRes;
import com.std.core.pojo.response.CollectionBuyOrderAuctionPayRecordRes;
import com.std.core.pojo.response.CollectionBuyOrderAuctionPayRes;
import com.std.core.pojo.response.CollectionBuyOrderCollectionInfoRes;
import com.std.core.pojo.response.CollectionBuyOrderDetailRes;
import com.std.core.pojo.response.CollectionBuyOrderListRes;
import com.std.core.pojo.response.CollectionBuyOrderPageRes;
import com.std.core.pojo.response.CompanySalesStatisticalRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.pojo.response.WechatAppPayInfo;
import com.std.core.service.IAccountService;
import com.std.core.service.IAdapayService;
import com.std.core.service.IAlipayService;
import com.std.core.service.IBabyPayService;
import com.std.core.service.IBankChannelService;
import com.std.core.service.IBankInfoService;
import com.std.core.service.IBlindboxUserService;
import com.std.core.service.IBusinessChannelService;
import com.std.core.service.IChannelConfigService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionBuyOrderService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionPeriodExtraBuyChanceService;
import com.std.core.service.ICollectionPeriodPriorityBuyService;
import com.std.core.service.ICollectionPeriodRelationService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.service.ICollectionRightService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IConfigService;
import com.std.core.service.IIncomeService;
import com.std.core.service.IInvitationActivityUserRecordService;
import com.std.core.service.IInvoiceOrderService;
import com.std.core.service.ILockService;
import com.std.core.service.IMemberConfigService;
import com.std.core.service.IPayRecordService;
import com.std.core.service.IPeriodAuctionBondRecordService;
import com.std.core.service.IPeriodAuctionService;
import com.std.core.service.IPeriodChannelWordService;
import com.std.core.service.IPeriodDiscountDetailService;
import com.std.core.service.IShellTaskConfigService;
import com.std.core.service.ISmsService;
import com.std.core.service.IThirdDivideDetailService;
import com.std.core.service.IThirdPaybackDetailService;
import com.std.core.service.IUserBindCardService;
import com.std.core.service.IUserService;
import com.std.core.service.IUserSettleAccountService;
import com.std.core.service.IWechatService;
import com.std.core.service.IYaoTaskConfigService;
import com.std.core.service.IYeePayService;
import com.std.core.util.DateUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.LuckyDrawUtil;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstantsCache;
import com.yeepay.yop.sdk.service.trade.response.OrderQueryResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数字藏品型号购买订单ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-11-04 16:01
 */
@Service
@Slf4j
public class CollectionBuyOrderServiceImpl implements ICollectionBuyOrderService {

    @Resource
    private CollectionBuyOrderMapper collectionBuyOrderMapper;

    @Resource
    private IUserService userService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CollectionPeriodMapper collectionPeriodMapper;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private ISmsService smsService;

    @Resource
    private IAccountService accountService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private IConfigService configService;

    @Resource
    private IMemberConfigService memberConfigService;

    @Resource
    private ICollectionPeriodService collectionPeriodService;

    @Resource
    private IIncomeService incomeService;

    @Resource
    private ICollectionPeriodRelationService collectionPeriodRelationService;

    @Resource
    private CollectionPeriodRelationMapper collectionPeriodRelationMapper;

    @Resource
    private ICompanyService companyService;

    @Resource
    private ICollectionPeriodPriorityBuyService collectionPeriodPriorityBuyService;

    @Resource
    private ICollectionRightService collectionRightService;

    @Resource
    private ILockService lockService;

    @Resource
    private IUserBindCardService userBindCardService;

    @Resource
    private IBankChannelService bankChannelService;

    @Resource
    private IBankInfoService bankInfoService;

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private IAdapayService adapayService;

    @Resource
    private IBabyPayService babyPayService;

    @Resource
    private IYeePayService yeePayService;

    @Resource
    private IThirdDivideDetailService thirdDivideDetailService;

    @Resource
    private IPeriodAuctionService periodAuctionService;

    private static Long redisLockTime = 600L;

    @Resource
    private IPayRecordService payRecordService;

    @Resource
    private IPeriodAuctionBondRecordService periodAuctionBondRecordService;

    @Resource
    private IThirdPaybackDetailService thirdPaybackDetailService;

    @Resource
    private IBusinessChannelService businessChannelService;

    @Resource
    private IPeriodDiscountDetailService periodDiscountDetailService;

    @Resource
    private IPeriodChannelWordService periodChannelWordService;

    @Resource
    private IInvitationActivityUserRecordService invitationActivityUserRecordService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICollectionPeriodExtraBuyChanceService collectionPeriodExtraBuyChanceService;

    @Resource
    private IUserSettleAccountService userSettleAccountService;

    @Resource
    private IChannelConfigService channelConfigService;

    @Resource
    private IInvoiceOrderService invoiceOrderService;

    @Resource
    private IYaoTaskConfigService yaoTaskConfigService;

    @Resource
    private IBlindboxUserService blindboxUserService;

    @Resource
    private IShellTaskConfigService shellTaskConfigService;

    /**
     * 新增数字藏品型号购买订单
     *
     * @param req 新增数字藏品型号购买订单入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(CollectionBuyOrderCreateReq req, User operator, String client, Long channelId) {

//        // 校验图片验证码逻辑 -- Robin
//        String uuid = req.getUuid();
//        String verifyCode = req.getVerifyCode();
//        if (StringUtils.isBlank(uuid)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写UUID");
//        }
//        if (StringUtils.isBlank(verifyCode)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写验证码");
//        }
//        String key = String.format(RedisKeyList.MT_CAPTCHA_KEY, uuid);
//        String code = null;
//        if (redisUtil.hasKey(key)) {
//            code = redisUtil.get(key).toString();
//        }
//        if (StringUtils.isBlank(code)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "验证码已失效，请刷新后重试");
//        }
//        if (!verifyCode.equals(code)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "验证码不匹配，请重试");
//        }
//        redisUtil.del(key);
//
//        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先实名认证");
//        }

//        // 检查用户持有该藏品数量是否上限
//        Collection collection = collectionService.detailSimple(req.getCollectionId());
//        boolean flag = collectionDetailService.checkCollectionCount(req.getCollectionId(), collection.getSingleMaxQuantity(), req.getQuantity(), operator);
//        if (flag) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您拥有的" + collection.getName() + "数量已达上限");
//        }
        // 判断是否支持的交易方式
        businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(), req.getPayType());
        UserBindCard userBindCard = new UserBindCard();
        if (EPayType.BANK_APPLET.getId().equals(req.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(req.getPayType())) {
            if (null == req.getBindCardId()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
            }
            userBindCard = userBindCardService.detail(req.getBindCardId(), operator);
        }
        // 从缓存中获取信息 -> Robin
        CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(req.getPeriodId());

        if (!collectionPeriod.getChannelId().equals(channelId)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道错误");
        }

        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
            if (null == req.getCollectionId()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "collectionId不能为空");
            }

            req.setQuantity(1);

            //验证信息
            checkBuyCondition(req, operator, collectionPeriod);
            return doHandleBuyBiz(req, collectionPeriod, operator, userBindCard);
        } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())) {
            if (null == req.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写购买藏品份数");
            }

            // 衍生区只有一个藏品,collectionId直接获取
            if (null == req.getCollectionId()) {
                CollectionPeriodRelationListReq collectionPeriodRelationListReq = new CollectionPeriodRelationListReq();
                collectionPeriodRelationListReq.setPeriodId(req.getPeriodId());
                req.setCollectionId(collectionPeriodRelationService.list(collectionPeriodRelationListReq).get(0).getCollectionId());
            }

            //验证信息
            checkBuyCondition(req, operator, collectionPeriod);
            return doHandleBuyBiz(req, collectionPeriod, operator, userBindCard);
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数类型不正确");
        }

        /** Commit by Robin
         CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyForUpdate(req.getPeriodId());
         */
        // 从缓存中获取信息 -> Robin
//        CollectionPeriod collectionPeriod = this.getCollectionPeriod(req.getPeriodId());

        //校验用户权益
//        if (!memberConfigService.validMemberPrivilege(operator.getId(), EUserPrivilege.USER_PRIVILEGE_4.getCode())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您当前会员等级，不能购买藏品");
//        }

//        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未上架");
//        }
//        if (ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getSoldStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数已售罄");
//        }
//
//        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
//            req.setQuantity(1);
//            collectionBuyOrder.setQuantity(req.getQuantity());
//
//            //判断是否定向藏品，如果是；判断用户是否符合，不是则报错
//            VipUserBuyCollection vipUserBuyCollection = vipUserBuyCollectionService.detailByCollectionId(req.getCollectionId());
//            if (vipUserBuyCollection != null) {
//                if (!vipUserBuyCollection.getUserId().equals(operator.getId())) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品在抢购中，请稍后再试");
//                }
//            }
//        } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())) {
//            // 衍生区只有一个藏品
//            if (null == req.getQuantity()) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写购买藏品份数");
//            }
//
//            CollectionPeriodRelationListReq collectionPeriodRelationListReq = new CollectionPeriodRelationListReq();
//            collectionPeriodRelationListReq.setPeriodId(req.getPeriodId());
//            req.setCollectionId(collectionPeriodRelationService.list(collectionPeriodRelationListReq).get(0).getCollectionId());
//            collectionBuyOrder.setCollectionId(req.getCollectionId());
//
//        } else {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的期数类型");
//        }
//
//        long advanceMilSecond = 0;
//        if (null != operator) {
//            if (collectionPeriodPriorityBuyService.checkExist(req.getPeriodId(), operator)) {
//                advanceMilSecond = DateUtil.millisByMins(collectionPeriod.getAdvanceMins());
//            }
//        }
//        long times = collectionPeriod.getStartSellDate().getTime() - System.currentTimeMillis() - advanceMilSecond;
//        if (times > 0) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数不在售卖中");
//        }
//
//        /** Commit by Robin
//         if (collectionPeriod.getRemainQuantity() < req.getQuantity()) {
//         throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
//         }
//         */
//        Long remainQuantity = this.getCollectionPeriodOrderQueueSize(req.getCollectionId(), req.getPeriodId());
//        if (remainQuantity.intValue() < req.getQuantity()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
//        }
//
//        // 获取该用户已购买该藏品的份数
//        Integer count = getUserBuyCount(collectionPeriod.getId(), operator.getId());
//        //判断该用户购买的份数是否已超过该藏品的单人购买上限
//        if (count + req.getQuantity() > collectionPeriod.getBuyMax()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您购买的份数已超过该期藏品的单人购买上限" + collectionPeriod.getBuyMax() + "份");
//        }
//
//        // 更新Redis队列
//        List<String> uuidList = new ArrayList<>();
//        String orderQueueKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_ORDER_QUEUE_KEY, req.getCollectionId(), req.getPeriodId());
//        for (int i = 0; i < req.getQuantity(); i++) {
//            String uuid = redisUtil.pop(orderQueueKey);
//            CollectionPeriodOrderQueue params = new CollectionPeriodOrderQueue();
//            params.setUuid(uuid);
//            params.setUserId(operator.getId());
//            params.setStatus("1");
//            params.setOrderTime(new Date());
//            collectionPeriodOrderQueueMapper.updateByUUIDSelective(params);
//            uuidList.add(uuid);
//        }
//
//        // 更新期数剩余数量
//        collectionPeriod.setRemainQuantity(collectionPeriod.getRemainQuantity() - req.getQuantity());
//        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);
//
//        // 期数关联剩余数量变更 ---> @阿静：这里是为什么这样写？
//        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
//                .detailRelation(req.getPeriodId(), req.getCollectionId());
//        if (collectionPeriodRelation.getRemainQuantity() < req.getQuantity()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
//        }
//        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getRemainQuantity() - req.getQuantity());
//        collectionPeriodRelationMapper.updateByPrimaryKeySelective(collectionPeriodRelation);
//
//        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.PERIOD.getCode());
//        collectionBuyOrder.setBizId(collectionPeriod.getId());
//        collectionBuyOrder.setUserId(operator.getId());
//        collectionBuyOrder.setCollectionId(req.getCollectionId());
//        collectionBuyOrder.setPrice(collectionPeriod.getPrice());
//        collectionBuyOrder.setPayAmount(collectionPeriod.getPrice().multiply(new BigDecimal(collectionBuyOrder.getQuantity())));
//        collectionBuyOrder.setCreateTime(new Date());
//
//        Long orderCode = IdGeneratorUtil.generator();
//        collectionBuyOrder.setPayOrderCode(orderCode.toString());
//
//        userService.checkTradePwd(operator.getId(), req.getPwd());
//        collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
//        collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
//        collectionBuyOrder.setPayDatetime(new Date());
//        collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
//        collectionBuyOrderMapper.insertSelective(collectionBuyOrder);
//
//        // Add by Robin
//        collectionPeriodOrderQueueMapper.updateOrderIdByUUIDList(uuidList, collectionBuyOrder.getId());
//        paySuccess(collectionBuyOrder, operator, ECollectionDetailBuyChannel.ZERO.getCode());
//        return new OrderPayRes(orderCode, EBoolean.YES.getCode());

//        if (EBigOrderPayType.ACCOUNT.getCode().equals(req.getPayType())) {
        //校验支付密码
//            userService.checkTradePwd(operator.getId(), req.getPwd());
//            collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
//            collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
//            collectionBuyOrder.setPayDatetime(new Date());
//            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
//            collectionBuyOrderMapper.insertSelective(collectionBuyOrder);

        // Add by Robin
        //collectionPeriodOrderQueueMapper.updateOrderIdByUUIDList(uuidList, collectionBuyOrder.getId());

//            paySuccess(collectionBuyOrder, operator, ECollectionDetailBuyChannel.ZERO.getCode());
//            return new OrderPayRes(orderCode, EBoolean.YES.getCode());
//        } else {
//            if (EBoolean.YES.getCode().equals(req.getIsBalanceDiscount())) {
//                //余额是否足够校验支付
//                Account buyUserAccount = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());
//                BigDecimal payAmount = collectionBuyOrder.getPayAmount().subtract(buyUserAccount.getAvailableAmount());
//                if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "你的余额已经足够支付，无需调用第三方支付");
//                } else {
//                    //冻结用户金额
//                    BigDecimal payBalanceAmount = buyUserAccount.getAvailableAmount();
//                    collectionBuyOrder.setPayBalanceAmount(payBalanceAmount);
//                    collectionBuyOrder.setPayCashAmount(payAmount);
//                    toPay(collectionBuyOrder);
//
//                    accountService.frozenAmount(buyUserAccount, payBalanceAmount,
//                            EJourBizTypeUser.Collection.Collection,
//                            EJourBizTypeUser.Collection.BuyCollection_Frozen,
//                            collectionBuyOrder.getId(),
//                            EJourBizTypeUser.Collection.BuyCollection_Frozen,
//                            collectionService.detailSimple(collectionBuyOrder.getCollectionId()).getName());
//                }
//
//                if (EBigOrderPayType.ALIPAY.getCode().equals(req.getPayType())) {
//                    String signOrder = null;
//                    if (EClient.H5.getCode().equals(client)) {
//                        signOrder = alipayService
//                                .getTradeWapPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        payAmount);
//                    } else {
//                        signOrder = alipayService
//                                .getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        payAmount);
//                    }
//                    return new OrderPayRes(orderCode, new AlipayPayOrderRes(signOrder));
//                } else if (EBigOrderPayType.WECHAT.getCode().equals(req.getPayType())) {
//                    WechatAppPayInfo wechatAppPayInfo = wechatService
//                            .getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                    EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                    payAmount, req.getWxAppId());
//                    return new OrderPayRes(orderCode, wechatAppPayInfo);
//                } else {
//                    throw new BizException(EErrorCode.E500003.getCode(), "不支持的支付方式!");
//                }
//            } else {
//                collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
//                collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getPayAmount());
//                if (EBigOrderPayType.ALIPAY.getCode().equals(req.getPayType())) {
//                    String signOrder = null;
//                    if (EClient.H5.getCode().equals(client)) {
//                        signOrder = alipayService
//                                .getTradeWapPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        collectionBuyOrder.getPayCashAmount());
//                    } else {
//                        signOrder = alipayService
//                                .getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        collectionBuyOrder.getPayCashAmount());
//                    }
//
//                    toPay(collectionBuyOrder);
//                    return new OrderPayRes(orderCode, new AlipayPayOrderRes(signOrder));
//                } else if (EBigOrderPayType.WECHAT.getCode().equals(req.getPayType())) {
//                    WechatAppPayInfo wechatAppPayInfo = wechatService
//                            .getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                    EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                    collectionBuyOrder.getPayCashAmount(), req.getWxAppId());
//                    toPay(collectionBuyOrder);
//                    return new OrderPayRes(orderCode, wechatAppPayInfo);
//                } else {
//                    throw new BizException(EErrorCode.E500003.getCode(), "不支持的支付方式!");
//                }
//            }
    }

    private void checkBuyCondition(CollectionBuyOrderCreateReq req, User operator, CollectionPeriod collectionPeriod) {
        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未上架");
        }
        if (ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getSoldStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数已售罄");
        }

        int advanceMilSecond = 0;
        if (null != operator) {
//            if (collectionPeriodPriorityBuyService.checkExist(req.getPeriodId(), operator)) {
//                advanceMilSecond = DateUtil.millisByMins(collectionPeriod.getAdvanceMins());
//            }
            advanceMilSecond = collectionPeriodPriorityBuyService.getMaxAdvanceMins(req.getPeriodId(), operator);
        }
        Date startDate = DateUtil.addMinutes(collectionPeriod.getStartSellDate(), -advanceMilSecond);
        long times = (startDate.getTime() - System.currentTimeMillis()) / 1000 * 1000;

        if (times > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数不在售卖中");
        }

//        Long remainQuantity = this.getCollectionPeriodOrderQueueSize(req.getCollectionId(), req.getPeriodId());
//        if (remainQuantity.intValue() < req.getQuantity()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
//        }
        if (collectionPeriod.getRemainQuantity() < req.getQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
        }

        // 获取该用户已购买该藏品的份数
        Integer count = getUserBuyCount(collectionPeriod.getId(), operator.getId());
        //判断该用户购买的份数是否已超过该藏品的单人购买上限
        if (count + req.getQuantity() > collectionPeriod.getBuyMax()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您购买的份数已超过该期藏品的单人购买上限" + collectionPeriod.getBuyMax() + "份");
        }

        if (EPayType.BALANCE.getId().equals(req.getPayType()) || EPayType.BANK_APPLET.getId().equals(req.getPayType())) {
            if (StringUtils.isBlank(req.getPwd())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付密码不能为空");
            }
            //验证交易密码
            userService.checkTradePwd(operator.getId(), req.getPwd());
        }

        //验证是否需要口令
        if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag())) {
            if (StringUtils.isBlank(req.getWord())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写口令");
            }
            //验证口令是否正确
            PeriodChannelWord periodChannelWord = periodChannelWordService.doGetUseWord(req.getPeriodId(), req.getWord());
            if (null == periodChannelWord) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令错误");
            }
        }
    }

    private OrderPayRes doHandleBuyBiz(CollectionBuyOrderCreateReq req, CollectionPeriod collectionPeriod, User operator,
            UserBindCard userBindCard) {
        // 更新期数剩余量
        collectionPeriod.setBuyQuantity(req.getQuantity());
        int effectCount = collectionPeriodMapper.updateRemainQuantity(collectionPeriod);
        if (effectCount <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数剩余数量不足");
        }

        OrderPayRes orderPayRes = new OrderPayRes();
        Collection collection = collectionService.detailSimple(req.getCollectionId());

        //包装购买订单
        CollectionBuyOrder collectionBuyOrder = createBuyOrder(req, operator, collectionPeriod, collection);

        //余额支付
        orderPayRes = payBalanceAmount(collection, collectionBuyOrder, operator);

        if (!EPayType.BALANCE.getId().equals(req.getPayType())) {
            // 三方支付
            orderPayRes = payCashAmount(req.getWxAppId(), userBindCard, collectionBuyOrder, operator,
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_8, req.getRedirectUrl());
        }

        // 更新期数作品剩余量
        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
                .detailRelation(req.getPeriodId(), req.getCollectionId());
        collectionPeriodRelation.setBuyQuantity(req.getQuantity());
        int effectCountRelation = collectionPeriodRelationMapper.updateRemainQuantity(collectionPeriodRelation);
        if (effectCountRelation <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数剩余数量不足");
        }
//        // 购买成功之后最后更新更新Redis队列
//        List<String> uuidList = new ArrayList<>();
//        String orderQueueKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_ORDER_QUEUE_KEY, req.getCollectionId(), req.getPeriodId());
//        for (int i = 0; i < req.getQuantity(); i++) {
//            String uuid = redisUtil.pop(orderQueueKey);
//            CollectionPeriodOrderQueue params = new CollectionPeriodOrderQueue();
//            params.setUuid(uuid);
//            params.setUserId(operator.getId());
//            params.setStatus(ECollectionPeriodOrderQueueStatus.COLLECTION_PERIOD_ORDER_QUEUE_STATUS_1.getCode());
//
//            params.setOrderTime(new Date());
//            collectionPeriodOrderQueueMapper.updateByUUIDSelective(params);
//            uuidList.add(uuid);
//        }
//        // Add by Robin
//        collectionPeriodOrderQueueMapper.updateOrderIdByUUIDList(uuidList, collectionBuyOrder.getId());

        return orderPayRes;
    }

    private OrderPayRes payCashAmount(String wxAppId, UserBindCard userBindCard, CollectionBuyOrder collectionBuyOrder,
            User operator, EPayRecordBizType recordBizType, String redirectUrl) {
        Long orderCode = Long.valueOf(collectionBuyOrder.getPayOrderCode());
        BigDecimal payAmount = collectionBuyOrder.getPayCashAmount();
        if (BigDecimal.ZERO.compareTo(payAmount) >= 0) {
            return null;
        }
        OrderPayRes res = new OrderPayRes();
        res.setOrderId(orderCode);
//        res.setBuySuccessFlag(EBoolean.YES.getCode());

        if (EPayType.WECHAT.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.WECHAT.getId()
                .equals(collectionBuyOrder.getPayType())) {
            WechatAppPayInfo result = wechatService.getAppPayInfo(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, wxAppId, collectionBuyOrder.getId());
            res.setWechatAppPayInfo(result);
        } else if (EPayType.ALIPAY.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.ALIPAY.getId()
                .equals(collectionBuyOrder.getPayType())) {
            String signOrder = alipayService.getTradeAppPaySignedOrder(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, collectionBuyOrder.getId());
            AlipayPayOrderRes result = new AlipayPayOrderRes(signOrder);
            res.setAlipayPayOrderRes(result);
        } else if (EPayType.ADAPAY.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.ADAPAY.getId()
                .equals(collectionBuyOrder.getPayType())) {
            AdaPayInfo appPayInfo = adapayService.getAppPayInfo(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, collectionBuyOrder.getId());
            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.WECHAT_APPLET.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.WECHAT_APPLET.getId()
                .equals(collectionBuyOrder.getPayType())) {
            BabyPayInfo appPayInfo = babyPayService
                    .getAppPayInfo(EPayType.WECHAT_APPLET.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode,
                            payAmount);
//            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.BANK_APPLET.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.BANK_APPLET.getId()
                .equals(collectionBuyOrder.getPayType())) {

            PayRecord payRecord = babyPayService
                    .getAppBankPayInfo(EPayType.BANK_APPLET.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode, userBindCard,
                            payAmount, collectionBuyOrder.getId());
            BabyBankPayInfo payInfo = new BabyBankPayInfo();
            payInfo.setBizCode(payRecord.getBizCode().toString());
            payInfo.setBizType(payRecord.getPayType());
            res.setBabyBankPayInfo(payInfo);
        } else if (EPayType.BANK_YEEPAY.getCode().equals(collectionBuyOrder.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(collectionBuyOrder.getPayType())) {

            YeeBankPayInfo payInfo = yeePayService
                    .getAppBankPayInfo(EPayType.BANK_YEEPAY.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode, userBindCard,
                            payAmount, collectionBuyOrder.getId(), collectionBuyOrder.getChannelId(), redirectUrl);
            res.setYeeBankPayInfo(payInfo);
        } else {
            throw new BizException(EErrorCode.CORE00000, "支付方式不支持");
        }

        return res;
    }

    OrderPayRes payBalanceAmount(Collection collection, CollectionBuyOrder data, User operator) {
        if (BigDecimal.ZERO.compareTo(data.getPayBalanceAmount()) >= 0) {
            return null;
        }
        Long refId = data.getId();

        String collectionName = collection.getName();
        //用户账户扣减金额
        Account buyAccount = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        if (EPayType.BALANCE.getId().equals(data.getPayType())) {
            //系统账户
            Account systemAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
            accountService.changeAmount(buyAccount, data.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    refId.toString(), refId,
                    EJourBizTypeUser.Collection.Collection,
                    EJourBizTypeUser.Collection.BuyCollection,
                    EJourBizTypeUser.Collection.BuyCollection,
                    collectionName);
            //平台账户收钱
            accountService.changeAmount(systemAccount, data.getPayBalanceAmount(),
                    EChannelType.INNER.getCode(),
                    refId.toString(), refId,
                    EJourBizTypeSystem.Collection.Collection,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodIncome,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodIncome,
                    collectionName);
        } else {
            accountService.frozenAmount(buyAccount, data.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.BuyCollection_Frozen,
                    refId, EJourBizTypeUser.Collection.BuyCollection_Frozen,
                    collectionName);
        }

        return new OrderPayRes(data.getId(), EBoolean.YES.getCode());
    }

    OrderPayRes payAuctionBalanceAmount(Collection collection, CollectionBuyOrder data, User operator) {
        if (BigDecimal.ZERO.compareTo(data.getPayBalanceAmount()) >= 0) {
            return null;
        }
        Long refId = data.getId();

        String collectionName = collection.getName();
        //用户账户扣减金额
        Account buyAccount = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        if (EPayType.BALANCE.getId().equals(data.getPayType())) {
            //系统账户
            Account systemAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
            accountService.changeAmount(buyAccount, data.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    refId.toString(), refId,
                    EJourBizTypeUser.AuctionProduct.AuctionProduct,
                    EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay,
                    EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay,
                    collectionName);
            //平台账户收钱
            accountService.changeAmount(systemAccount, data.getPayBalanceAmount(),
                    EChannelType.INNER.getCode(),
                    refId.toString(), refId,
                    EJourBizTypeSystem.Collection.Collection,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodAUctionIncome,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodAUctionIncome,
                    collectionName);

        } else {
            accountService.frozenAmount(buyAccount, data.getPayBalanceAmount(),
                    EJourBizTypeUser.AuctionProduct.AuctionProduct, EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay_Frozen,
                    refId, EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay_Frozen,
                    collectionName);
        }

        return new OrderPayRes(data.getId(), EBoolean.YES.getCode());
    }

    @NotNull
    private CollectionBuyOrder createBuyOrder(CollectionBuyOrderCreateReq req, User operator, CollectionPeriod period,
            Collection collection) {
        CollectionBuyOrder collectionBuyOrder = EntityUtils.copyData(req, CollectionBuyOrder.class);
        if (StringUtils.isNotBlank(req.getCid())) {
            collectionBuyOrder.setPromoteChannelId(new Long(req.getCid()));
        }

        if (StringUtils.isNotBlank(req.getBuySource())) {
            if (ECollectionBuyOrderSource.E_COLLECTION_BUY_ORDER_SOURCE_1.getCode().equals(req.getBuySource())) {
                if (!EBoolean.YES.getCode().equals(period.getMetaReleaseFlag())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未同步发售到元宇宙");
                }

                collectionBuyOrder.setBuySource(ECollectionBuyOrderSource.E_COLLECTION_BUY_ORDER_SOURCE_1.getCode());
                collectionBuyOrder.setPitId(period.getPitId());
            }
        }

        //获取折扣值(根据用户的藏品id，获取最低折扣比例)
        PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                .getLowestByUserId(period.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
        if (null != periodDiscountDetail) {
            period.setDiscountPrice(
                    (period.getPrice().multiply(periodDiscountDetail.getDiscountRate())).setScale(2, BigDecimal.ROUND_DOWN));
            period.setDiscountRate(periodDiscountDetail.getDiscountRate());
        } else {
            period.setDiscountPrice(period.getPrice());
            period.setDiscountRate(BigDecimal.ONE);
        }

        BigDecimal total = (period.getDiscountPrice().multiply(new BigDecimal(collectionBuyOrder.getQuantity())))
                .setScale(2, BigDecimal.ROUND_DOWN);
        collectionBuyOrder.setQuantity(req.getQuantity());
        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.PERIOD.getCode());
        collectionBuyOrder.setBizId(period.getId());
        collectionBuyOrder.setUserId(operator.getId());
        collectionBuyOrder.setCollectionId(req.getCollectionId());
        collectionBuyOrder.setAuthorId(collection.getAuthorId());
        collectionBuyOrder.setPrice(period.getPrice());
        collectionBuyOrder.setDiscountPrice(period.getDiscountPrice());
        collectionBuyOrder.setPayAmount(total);
        collectionBuyOrder.setCreateTime(new Date());
        collectionBuyOrder.setPayOrderCode(IdGeneratorUtil.generator().toString());
        collectionBuyOrder.setChannelId(period.getChannelId());

        if (EPayType.BALANCE.getId().equals(req.getPayType())) {
            collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
            collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode());

        }
//        else if (EBoolean.YES.getCode().equals(req.getIsBalanceDiscount())) {
//            Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());
//            if (account.getAvailableAmount().compareTo(total) >= 0) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "余额足够不需要抵扣");
//            }
//            collectionBuyOrder.setPayBalanceAmount(account.getAvailableAmount());
//            collectionBuyOrder.se
//            tPayCashAmount(total.subtract(account.getAvailableAmount()));
//            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode());
//
//        }
        else {
            collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
            collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getPayAmount());
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode());

        }

        collectionBuyOrder.setPayDatetime(new Date());
        collectionBuyOrder.setWord(req.getWord());
        collectionBuyOrderMapper.insertSelective(collectionBuyOrder);

        return collectionBuyOrder;
    }

    /**
     * front:一级市场购买盲盒
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes
    blindBoxCreate(CollectionBuyOrderCreateReq req, User operator, String client, Long channelId) {
        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        // 判断是否支持的交易方式
        businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(), req.getPayType());

        UserBindCard userBindCard = new UserBindCard();
        if (EPayType.BANK_APPLET.getId().equals(req.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(req.getPayType())) {
            if (null == req.getBindCardId()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
            }
            userBindCard = userBindCardService.detail(req.getBindCardId(), operator);
        }

        CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(req.getPeriodId());
        if (!collectionPeriod.getChannelId().equals(channelId)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道错误");
        }

        if (!ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的期数类型");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未上架");
        }
        if (ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getSoldStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数已售罄");
        }

        int advanceMilSecond = 0;
        if (null != operator) {
//            if (collectionPeriodPriorityBuyService.checkExist(req.getPeriodId(), operator)) {
//                advanceMilSecond = DateUtil.millisByMins(collectionPeriod.getAdvanceMins());
//            }
            advanceMilSecond = collectionPeriodPriorityBuyService.getMaxAdvanceMins(req.getPeriodId(), operator);
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先登录！");
        }

        Date startDate = DateUtil.addMinutes(collectionPeriod.getStartSellDate(), -advanceMilSecond);
        long times = (startDate.getTime() - System.currentTimeMillis()) / 1000 * 1000;

        if (times > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数不在售卖中");
        }

        req.setQuantity(1);
        if (collectionPeriod.getRemainQuantity() < req.getQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数剩余藏品数量不足，请稍后再试");
        }

        // 获取该用户已购买该藏品的份数
        Integer count = getUserBuyCount(collectionPeriod.getId(), operator.getId());
        //判断该用户购买的份数是否已超过该藏品的单人购买上限
        if (count + req.getQuantity() > collectionPeriod.getBuyMax()) {
//            String redisKey = String.format(RedisKeyList.MT_CONFIG_KEY, collectionPeriod.getId(), operator.getId());
//            redisUtil.set(redisKey, count, redisLockTime);
            int i = collectionPeriodExtraBuyChanceService.detailPeriodExtraChance(collectionPeriod.getId(), operator.getId());
            if (count + req.getQuantity() > collectionPeriod.getBuyMax() + i) {
                int maxCount = collectionPeriod.getBuyMax() + i;
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您购买的份数已超过该期藏品的单人购买上限" + maxCount + "份");
            }

        }
        if (EPayType.BALANCE.getId().equals(req.getPayType()) || EPayType.BANK_APPLET.getId().equals(req.getPayType())) {
            if (StringUtils.isBlank(req.getPwd())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付密码不能为空");
            }
            //验证交易密码
            userService.checkTradePwd(operator.getId(), req.getPwd());
        }

        CollectionPeriodRelation collectionPeriodRelation = getCollectionPeriodRelation(req, collectionPeriod, null);
//
//        List<Long> idList = new ArrayList<>();
//        // 检查用户持有该藏品数量是否上限,已上限就换一个
//        while (true) {
//            CollectionPeriodRelation relation = getCollectionPeriodRelation(req, collectionPeriod, idList);
//            Collection collection = collectionService.detailSimple(relation.getCollectionId());
//
//            boolean flag = collectionDetailService.checkCollectionCount(req.getCollectionId(), collection.getSingleMaxQuantity(),1, operator);
//            if (!flag) {
//                collectionPeriodRelation = relation;
//                break;
//            }
//            idList.add(relation.getCollectionId());
//        }

        req.setCollectionId(collectionPeriodRelation.getCollectionId());

        //验证是否需要口令
        if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag())) {
            if (StringUtils.isBlank(req.getWord())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写口令");
            }
            //验证口令是否正确
            PeriodChannelWord periodChannelWord = periodChannelWordService.doGetUseWord(req.getPeriodId(), req.getWord());
            if (null == periodChannelWord) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令错误");
            }
        }
        //更新剩余量，产生订单
        return doHandleBuyBiz(req, collectionPeriod, operator, userBindCard);
    }

    @NotNull
    private CollectionPeriodRelation getCollectionPeriodRelation(CollectionBuyOrderCreateReq req, CollectionPeriod collectionPeriod,
            List<Long> idList) {
        List<AwardEntity> awardEntityList = collectionPeriodRelationService
                .ListByPeriodId(collectionPeriod.getId(), configService.getStringValue("collection_period_blind_box_buy_rule"), idList);
        if (CollectionUtils.isEmpty(awardEntityList) && CollectionUtils.isEmpty(idList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数已售完");
        } else if (CollectionUtils.isEmpty(awardEntityList) && CollectionUtils.isNotEmpty(idList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数您已购买上限");
        }
        int prizeIndex = LuckyDrawUtil.getPrizeIndex(awardEntityList);
        //期数关联剩余数量变更
        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
                .detail(awardEntityList.get(prizeIndex).getId());
        if (collectionPeriodRelation.getRemainQuantity() < req.getQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
        }
        return collectionPeriodRelation;
    }

//    /**
//     * front:一级市场购买盲盒
//     */
//    @Override
//    public OrderPayRes blindBoxCreate(CollectionBuyOrderBlindBoxCreateReq req, User operator, String client) {
//        CollectionBuyOrder collectionBuyOrder = EntityUtils.copyData(req, CollectionBuyOrder.class);
//
//        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyForUpdate(req.getPeriodId());
//
//        if (!ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的期数类型");
//        }
//
//        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未上架");
//        }
//        if (ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getSoldStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数已售罄");
//        }
//
//        long advanceMilSecond = 0;
//        if (null != operator) {
//            if (collectionPeriodPriorityBuyService.checkExist(req.getPeriodId(), operator)) {
//                advanceMilSecond = DateUtil.millisByMins(collectionPeriod.getAdvanceMins());
//            }
//        } else {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先登录！");
//        }
//
//        long times = collectionPeriod.getStartSellDate().getTime() - System.currentTimeMillis() - advanceMilSecond;
//        if (times > 0) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数不在售卖中");
//        }
//
//        Integer quantity = 1;
//        collectionBuyOrder.setQuantity(quantity);
//
//        if (collectionPeriod.getRemainQuantity() < quantity) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数剩余藏品数量不足，请稍后再试");
//        }
//
//        // 获取该用户已购买该藏品的份数
//        Integer count = getUserBuyCount(collectionPeriod.getId(), operator.getId());
//        //判断该用户购买的份数是否已超过该藏品的单人购买上限
//        if (count + quantity > collectionPeriod.getBuyMax()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您购买的份数已超过该期藏品的单人购买上限" + collectionPeriod.getBuyMax() + "份");
//        }
//
//        //更新期数剩余数量
//        collectionPeriod.setRemainQuantity(collectionPeriod.getRemainQuantity() - quantity);
//        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);
//
//        List<AwardEntity> awardEntityList = collectionPeriodRelationService
//                .ListByPeriodId(collectionPeriod.getId(), configService.getStringValue("collection_period_blind_box_buy_rule"));
//        int prizeIndex = LuckyDrawUtil.getPrizeIndex(awardEntityList);
//        //期数关联剩余数量变更
//        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
//                .detail(awardEntityList.get(prizeIndex).getId());
//        if (collectionPeriodRelation.getRemainQuantity() < quantity) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
//        }
//        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getRemainQuantity() - quantity);
//        collectionPeriodRelationMapper.updateByPrimaryKeySelective(collectionPeriodRelation);
//
//        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.PERIOD.getCode());
//        collectionBuyOrder.setBizId(collectionPeriod.getId());
//        collectionBuyOrder.setUserId(operator.getId());
//        collectionBuyOrder.setCollectionId(collectionPeriodRelation.getCollectionId());
//        collectionBuyOrder.setPrice(collectionPeriod.getPrice());
//        collectionBuyOrder.setPayAmount(collectionPeriod.getPrice().multiply(new BigDecimal(collectionBuyOrder.getQuantity())));
//        collectionBuyOrder.setCreateTime(new Date());
//
//        Long orderCode = IdGeneratorUtil.generator();
//        collectionBuyOrder.setPayOrderCode(orderCode.toString());
//        if (EBigOrderPayType.ACCOUNT.getCode().equals(req.getPayType())) {
//            //校验支付密码
//            userService.checkTradePwd(operator.getId(), req.getPwd());
//            collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
//            collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
//            collectionBuyOrder.setPayDatetime(new Date());
//            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
//            collectionBuyOrderMapper.insertSelective(collectionBuyOrder);
//
//            paySuccess(collectionBuyOrder, operator, ECollectionDetailBuyChannel.ZERO.getCode());
//            return new OrderPayRes(orderCode, EBoolean.YES.getCode());
//        } else {
//            if (EBoolean.YES.getCode().equals(req.getIsBalanceDiscount())) {
//                //余额是否足够校验支付
//                Account buyUserAccount = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());
//                BigDecimal payAmount = collectionBuyOrder.getPayAmount().subtract(buyUserAccount.getAvailableAmount());
//                if (payAmount.compareTo(BigDecimal.ZERO) <= 0) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "你的余额已经足够支付，无需调用第三方支付");
//                } else {
//                    //冻结用户金额
//                    BigDecimal payBalanceAmount = buyUserAccount.getAvailableAmount();
//                    collectionBuyOrder.setPayBalanceAmount(payBalanceAmount);
//                    collectionBuyOrder.setPayCashAmount(payAmount);
//                    toPay(collectionBuyOrder);
//
//                    accountService.frozenAmount(buyUserAccount, payBalanceAmount,
//                            EJourBizTypeUser.Collection.Collection,
//                            EJourBizTypeUser.Collection.BuyCollection_Frozen,
//                            collectionBuyOrder.getId(),
//                            EJourBizTypeUser.Collection.BuyCollection_Frozen,
//                            collectionService.detailSimple(collectionBuyOrder.getCollectionId()).getName());
//                }
//
//                if (EBigOrderPayType.ALIPAY.getCode().equals(req.getPayType())) {
//                    String signOrder = null;
//                    if (EClient.H5.getCode().equals(client)) {
//                        signOrder = alipayService
//                                .getTradeWapPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        payAmount);
//                    } else {
//                        signOrder = alipayService
//                                .getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        payAmount);
//                    }
//                    return new OrderPayRes(orderCode, new AlipayPayOrderRes(signOrder));
//                } else if (EBigOrderPayType.WECHAT.getCode().equals(req.getPayType())) {
//                    WechatAppPayInfo wechatAppPayInfo = wechatService
//                            .getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                    EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                    payAmount, req.getWxAppId());
//                    return new OrderPayRes(orderCode, wechatAppPayInfo);
//                } else {
//                    throw new BizException(EErrorCode.E500003.getCode(), "不支持的支付方式!");
//                }
//            } else {
//                collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
//                collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getPayAmount());
//                if (EBigOrderPayType.ALIPAY.getCode().equals(req.getPayType())) {
//                    String signOrder = null;
//                    if (EClient.H5.getCode().equals(client)) {
//                        signOrder = alipayService
//                                .getTradeWapPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        collectionBuyOrder.getPayCashAmount());
//                    } else {
//                        signOrder = alipayService
//                                .getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                        EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                        collectionBuyOrder.getPayCashAmount());
//                    }
//
//                    toPay(collectionBuyOrder);
//                    return new OrderPayRes(orderCode, new AlipayPayOrderRes(signOrder));
//                } else if (EBigOrderPayType.WECHAT.getCode().equals(req.getPayType())) {
//                    WechatAppPayInfo wechatAppPayInfo = wechatService
//                            .getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
//                                    EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), orderCode,
//                                    collectionBuyOrder.getPayCashAmount(), req.getWxAppId());
//                    toPay(collectionBuyOrder);
//                    return new OrderPayRes(orderCode, wechatAppPayInfo);
//                } else {
//                    throw new BizException(EErrorCode.E500003.getCode(), "不支持的支付方式!");
//                }
//            }
//        }
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(CollectionBlindBoxBuyReq req, User operator) {
//        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先实名认证");
//        }
//
//        if (!EPayType.BALANCE.getId().equals(req.getPayType())) {
//            // 判断是否支持的交易方式
//            businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getCode(), req.getPayType());
//        }
//
//        UserBindCard userBindCard = new UserBindCard();
//        if (EPayType.BANK_APPLET.getId().equals(req.getPayType()) || EPayType.BANK_YEEPAY.getId()
//                .equals(req.getPayType())) {
//            if (null == req.getBindCardId()) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
//            }
//            userBindCard = userBindCardService.detail(req.getBindCardId(), operator);
//        }
//
//        // 判断该用户是否查询过盲盒
//        if (!redisUtil.hasKey("blindBox" + operator.getId())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请重新选择");
//        }
//        // 从缓存中随机获取一个盲盒
//
//        String st = (String) redisUtil.get("blindBox" + operator.getId());
//        JSONArray jsonArray = new JSONArray();
//        try {
//            jsonArray = JSONArray.parseArray(st);
//
//        } catch (Exception e) {
//            log.error("幸运抽奖购买出错" + st + ":" + e.getMessage());
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "幸运抽奖购买出错");
//        }
//        List<ChangeRecommend> recommendList = JSONObject.parseArray(jsonArray.toJSONString(), ChangeRecommend.class);
//
//        if (null == recommendList || CollectionUtils.isEmpty(recommendList)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请重新选择");
//        }
//
//        List<Long> list = recommendList.stream().map(x -> {
//            return x.getId();
//        }).collect(Collectors.toList());
//
//        List<ChangeRecommend> changeRecommendList = changeRecommendService
//                .detailByIdList(list, configService.getStringValue(SysConstants.OBTAIN_CONFIG));
//        int i = LuckyDrawUtil.getBlindBoxIndex(changeRecommendList);
//        ChangeRecommend recommend = changeRecommendList.get(i);
//
//        ChangeRecommendListRes res = new ChangeRecommendListRes();
//        BeanUtils.copyProperties(recommend, res);
//
//        ChangeRecommend changeRecommend = changeRecommendService.detailForUpdate(recommend.getId());
//        if (changeRecommend.getRemainQuantity() <= 0) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品已售罄，请重新换一批");
//        }
//        ChangeSeries changeSeries = changeSeriesService.detail(changeRecommend.getSeriesId());
//
//        if (!EChangeRecommendType.CHANGE_RECOMMEND_TYPE_1.getCode().equals(changeRecommend.getType())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该商品不是幸运抽奖");
//        }
//        if (EChangeRecommendStatus.CHANGE_RECOMMEND_STATUS_3.getCode()
//                .equals(changeRecommend.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "幸运抽奖已售完，请重新选择");
//        }
//        if (!EChangeRecommendStatus.CHANGE_RECOMMEND_STATUS_1.getCode().equals(changeRecommend.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该幸运抽奖未上架，请重新选择");
//        }
//
//        CollectionBuyOrder collectionBuyOrder = new CollectionBuyOrder();
//        collectionBuyOrder.setCollectionId(changeRecommend.getCollectionId());
//        collectionBuyOrder.setQuantity(1);
//        collectionBuyOrder.setPayType(EBigOrderPayType.ACCOUNT.getCode());
//
//        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.BLIND.getCode());
//        collectionBuyOrder.setBizId(changeRecommend.getId());
//        collectionBuyOrder.setUserId(operator.getId());
//        collectionBuyOrder.setPrice(changeSeries.getPrice());
//        collectionBuyOrder.setDiscountPrice(changeSeries.getPrice());
//        collectionBuyOrder.setPayAmount(changeSeries.getPrice());
//
//        collectionBuyOrder.setCreateTime(new Date());
//        Long orderCode = IdGeneratorUtil.generator();
//        collectionBuyOrder.setPayOrderCode(orderCode.toString());
//
//        //查看免费次数是否用完，未用完本次免费用
//        List<CollectionRight> collectionRightList = collectionRightService
//                .list(ECollectionRightDetailType.COLLECTION_RIGHT_DETAIL_TYPE_1.getCode(),
//                        changeRecommend.getSeriesId());
//
//        boolean freeFlag = false;
////        CollectionDetail bindboxCollectionDetail = null;
//        Long blindboxCollectionDetailId = null;
//        if (CollectionUtils.isNotEmpty(collectionRightList)) {
//            for (CollectionRight collectionRight : collectionRightList) {
//                List<CollectionDetail> myCollectionDetailList = collectionDetailService
//                        .listNoExport(collectionRight.getCollectionId(), operator.getId());
//                //当前藏品列表一个个判断使用是否超限
//                for (CollectionDetail collectionDetail : myCollectionDetailList) {
//                    List<Long> collectionDetailList = new ArrayList<>();
//                    collectionDetailList.add(collectionDetail.getId());
//
//                    Integer useTotalCount = selectBlindBoxFreeTotalCountByCurrentMonth(null, collectionDetailList);
//                    //如果有可使用次数，则赋值
//                    if (collectionRight.getCycleTime() > useTotalCount) {
//                        freeFlag = true;
//                        blindboxCollectionDetailId = collectionDetail.getId();
//                        break;
//                    }
//                }
//
//                if (freeFlag) {
//                    break;
//                }
//            }
//        }
//
//        if (!freeFlag) {
//            BlindboxUser blindboxUser = blindboxUserService.detailByUserId(operator.getId());
//            if (null != blindboxUser) {
//                List<Long> collectionDetailList = new ArrayList<>();
//                collectionDetailList.add(operator.getId());
//                Integer userUseTotalCount = selectBlindBoxFreeTotalCountByCurrentMonth(null, collectionDetailList);
//
//                Integer userRemainCount = blindboxUser.getQuantity() - userUseTotalCount;
//                if (userRemainCount > 0) {
//                    freeFlag = true;
//                    blindboxCollectionDetailId = operator.getId();
//                }
//            }
//        }
//
//        if (freeFlag) {
//            userService.checkTradePwd(operator.getId(), req.getPwd());
//            collectionBuyOrder.setDiscountPrice(BigDecimal.ZERO);
//            collectionBuyOrder.setPayAmount(BigDecimal.ZERO);
//            collectionBuyOrder.setPayType(EPayType.NO_TO_PAY.getId());
//            collectionBuyOrder.setParentBizId(changeSeries.getId());
//            collectionBuyOrder.setBlindboxCollectionDetailId(blindboxCollectionDetailId);
//            collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
//            collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
//            collectionBuyOrder.setPayDatetime(new Date());
//            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode());
//        } else {
//            if (EPayType.BALANCE.getId().equals(req.getPayType())) {
////                // 盲盒只能余额支付，校验支付密码
////                userService.checkTradePwd(operator.getId(), req.getPwd());
////                collectionBuyOrder.setPayType(EPayType.BALANCE.getId());
////                collectionBuyOrder.setParentBizId(changeSeries.getId());
////                collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
////                collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
////                collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode());
//
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择其他支付方式");
//            } else {
//
//                collectionBuyOrder.setPayType(req.getPayType());
//                collectionBuyOrder.setParentBizId(changeSeries.getId());
//                collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
//                collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getPayAmount());
//                collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode());
//
//                // 查询该系列对应的折扣商品
//                List<PeriodDiscountDetail> discountDetailList = periodDiscountDetailService
//                        .detailByRefId(EPeriodDiscountRefType.BLINDBOX.getCode(), recommend.getSeriesId());
//                if (CollectionUtils.isNotEmpty(discountDetailList)) {
//                    i:
//                    for (PeriodDiscountDetail discountDetail : discountDetailList) {
//
//                        CollectionDetail condition = new CollectionDetail();
//                        condition.setCollectionId(discountDetail.getCollectionId());
//                        condition.setOwnerType(EBoolean.NO.getCode());
//                        condition.setOwnerId(operator.getId());
//                        condition.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
//                        // 查询本人是否有折扣商品
//                        List<CollectionDetail> collectionDetailList = collectionDetailService.list(condition);
//
//                        for (CollectionDetail detail : collectionDetailList) {
//
//                            // 折扣次数为0这标识无限制折扣
//                            if (0 == discountDetail.getDiscountTime()) {
//                                collectionBuyOrder.setBlindboxCollectionDetailId(detail.getId());
//                                collectionBuyOrder.setDiscountPrice(changeSeries.getPrice().multiply(discountDetail.getDiscountRate())
//                                        .setScale(2, BigDecimal.ROUND_DOWN));
//                                collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getDiscountPrice());
//                            }
//
//                            CollectionBuyOrder orderCondition = new CollectionBuyOrder();
//                            orderCondition.setBlindboxCollectionDetailId(detail.getId());
//                            List<String> payStatusList = new ArrayList<>();
//                            payStatusList.add(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
//                            payStatusList.add(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_9.getCode());
//                            orderCondition.setPayStatusList(payStatusList);
//                            // 判断折扣次数是否够用，如果已使用完这向后顺眼
//                            int count = collectionBuyOrderMapper.selectCountByCondition(orderCondition);
//
//                            if (count < discountDetail.getDiscountTime()) {
//                                collectionBuyOrder.setBlindboxCollectionDetailId(detail.getId());
//                                collectionBuyOrder.setDiscountPrice(changeSeries.getPrice().multiply(discountDetail.getDiscountRate())
//                                        .setScale(2, BigDecimal.ROUND_DOWN));
//                                collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getDiscountPrice());
//
//                                break i;
//                            }
//                        }
//                    }
//                }
//            }
//
//            collectionBuyOrder.setPayDatetime(new Date());
//
//        }
//
//        Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());
//        collectionBuyOrder.setAuthorId(collection.getAuthorId());
//        collectionBuyOrder.setChannelId(1L);
//        collectionBuyOrderMapper.insertSelective(collectionBuyOrder);
//
//        OrderPayRes orderPayRes = new OrderPayRes();
//
//        if (EPayType.BALANCE.getId().equals(collectionBuyOrder.getPayType())) {
//            //余额支付
//            orderPayRes = payBlindBalanceAmount(collectionBuyOrder, operator, collection);
//        } else if (EPayType.NO_TO_PAY.getId().equals(collectionBuyOrder.getPayType())) {
//            orderPayRes.setRecommendListRes(res);
//        } else if (!EPayType.BALANCE.getId().equals(collectionBuyOrder.getPayType())) {
//            // 三方支付
//            orderPayRes = payCashAmount(req.getWxAppId(), userBindCard, collectionBuyOrder, operator,
//                    EPayRecordBizType.PAY_RECORD_BIZTYPE_14, req.getRedirectUrl());
//        }
//
//        int dealNum = changeRecommendService.blindBoxQuantity(changeRecommend.getId());
//
//        if (dealNum == 0) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该盲盒已售完");
//        }
//
//        // 刷新盲盒
//        ChangeRecommendListFrontReq request = new ChangeRecommendListFrontReq();
//        request.setSeriesId(recommend.getSeriesId());
//        changeRecommendService.listBlindboxFront(request, operator);
//
//        return orderPayRes;
        return null;
    }

    private OrderPayRes payBlindBalanceAmount(CollectionBuyOrder collectionBuyOrder, User operator, Collection collection) {
        //产生产品型号并跟token进行关联

        //消息推送
        String titleAction = "购买";
        if (EPayType.NO_TO_PAY.getId().equals(collectionBuyOrder.getPayType())) {
            titleAction = "免费选";
        }

        String title = titleAction + "幸运抽奖";
        String content = operator.getNickname() + titleAction + "【" + collection.getName() + "】幸运抽奖";
        smsService
                .sendBuyMsg(operator, title, content, ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue());

        Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        pay(collection, account, collectionBuyOrder,
                EJourBizTypeUser.Collection.CreateBlindBox,
                EJourBizTypeSystem.Collection.SELLBlindBoxIncome,
                EJourBizTypeUser.UserDiamondIncome.BUY_COLLECTION,
                EIncomeAmountType.TYPE_5,
                titleAction + "[" + collection.getName() + "]幸运抽奖");

        return new OrderPayRes(collectionBuyOrder.getId(), EBoolean.YES.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes auctionCreate(CollectionBuyOrderAuctionCreateReq request, User operator) {
        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先实名认证");
        }
        // 判断是否支持的交易方式
        businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(), request.getPayType());

        PeriodAuction periodAuction = periodAuctionService.check(request.getPeriodId(), operator);

        Integer callbackTime = configService.getIntegerValue("period_auction_pay_callback_time");
        Date date = DateUtils.addSeconds(periodAuction.getDelayedTime(), callbackTime);
        if (new Date().after(date)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已过支付截止日期");
        }
        if (request.getPayAmount().add(periodAuction.getAlreadyPayAmount()).compareTo(periodAuction.getCurrentPrice()) > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付金额过高");
        }

        UserBindCard userBindCard = new UserBindCard();
        if (EPayType.BANK_APPLET.getId().equals(request.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(request.getPayType())) {
            if (null == request.getBindCardId()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
            }
            userBindCard = userBindCardService.detail(request.getBindCardId(), operator);
        }

        CollectionPeriod collectionPeriod = checkAuctionCondition(request, operator);

        // 衍生区只有一个藏品,collectionId直接获取
        CollectionPeriodRelationListReq collectionPeriodRelationListReq = new CollectionPeriodRelationListReq();
        collectionPeriodRelationListReq.setPeriodId(request.getPeriodId());
        Long collectionId = collectionPeriodRelationService.list(collectionPeriodRelationListReq).get(0).getCollectionId();

        OrderPayRes orderPayRes = new OrderPayRes();

        Collection collection = collectionService.detailSimple(collectionId);
        //包装购买订单
        CollectionBuyOrder collectionBuyOrder = createBuyOrder(request, collectionId, operator, collectionPeriod, collection);

        //余额支付
        orderPayRes = payAuctionBalanceAmount(collection, collectionBuyOrder, operator);

        if (!EPayType.BALANCE.getId().equals(request.getPayType())) {
            // 三方支付
            orderPayRes = payCashAmount(request.getWxAppId(), userBindCard, collectionBuyOrder, operator,
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_13, request.getRedirectUrl());
        }

        periodAuction.setAlreadyPayAmount(periodAuction.getAlreadyPayAmount().add(request.getPayAmount()));
        periodAuctionService.modify(periodAuction);

        orderPayRes.setPayFlag(EBoolean.NO.getCode());
        if (periodAuction.getAlreadyPayAmount().compareTo(periodAuction.getCurrentPrice()) == 0) {
            orderPayRes.setPayFlag(EBoolean.YES.getCode());
        }

        if (EPayType.BALANCE.getId().equals(request.getPayType())) {
            auctionPaySuccess(collectionBuyOrder, periodAuction);
        }
        return orderPayRes;
    }

    @NotNull
    private CollectionBuyOrder createBuyOrder(CollectionBuyOrderAuctionCreateReq req, Long collectionId, User operator,
            CollectionPeriod collectionPeriod, Collection collection) {

        CollectionBuyOrder collectionBuyOrder = EntityUtils.copyData(req, CollectionBuyOrder.class);
        collectionBuyOrder.setQuantity(1);
        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.AUCTION.getCode());
        collectionBuyOrder.setBizId(collectionPeriod.getId());
        collectionBuyOrder.setUserId(operator.getId());
        collectionBuyOrder.setCollectionId(collectionId);
        collectionBuyOrder.setAuthorId(collection.getAuthorId());
        collectionBuyOrder.setPrice(req.getPayAmount());
        collectionBuyOrder.setPayAmount(req.getPayAmount());
        collectionBuyOrder.setCreateTime(new Date());
        collectionBuyOrder.setPayOrderCode(IdGeneratorUtil.generator().toString());
        collectionBuyOrder.setChannelId(collectionPeriod.getChannelId());
        if (EPayType.BALANCE.getId().equals(req.getPayType())) {
            collectionBuyOrder.setPayBalanceAmount(collectionBuyOrder.getPayAmount());
            collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
            collectionBuyOrder.setPayDatetime(new Date());
        } else {
            collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
            collectionBuyOrder.setPayCashAmount(collectionBuyOrder.getPayAmount());
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode());
        }

//        collectionBuyOrder.setPayDatetime(new Date());
        collectionBuyOrderMapper.insertSelective(collectionBuyOrder);

        return collectionBuyOrder;
    }

    private CollectionPeriod checkAuctionCondition(CollectionBuyOrderAuctionCreateReq request, User operator) {
        CollectionPeriod collectionPeriod = collectionPeriodService.detailsimple(request.getPeriodId());

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数未上架");
        }
        if (ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getSoldStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数已售罄");
        }
        if (collectionPeriod.getRemainQuantity() < 1) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
        }

        if (EPayType.BALANCE.getId().equals(request.getPayType()) || EPayType.BANK_APPLET.getId().equals(request.getPayType())) {
            if (StringUtils.isBlank(request.getPwd())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付密码不能为空");
            }
            //验证交易密码
            userService.checkTradePwd(operator.getId(), request.getPwd());
        }
        return collectionPeriod;
    }

    @Override
    public void check(CollectionBuyOrderCheckReq request, User operator) {
        String collectionPeriodCacheKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_KEY, request.getPeriodId().toString());
        CollectionPeriod collectionPeriod;
        if (redisUtil.hasKey(collectionPeriodCacheKey)) {
            collectionPeriod = JSON.parseObject(JSON.toJSONString(redisUtil.get(collectionPeriodCacheKey)), CollectionPeriod.class);
        } else {
            collectionPeriod = collectionPeriodMapper.selectByPrimaryKey(request.getPeriodId());
            if (null != collectionPeriod) {
                redisUtil.set(collectionPeriodCacheKey, collectionPeriod);
            }
        }
        if (null == collectionPeriod) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "藏品信息获取异常");
        }
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
            request.setQuantity(1);
        }

        if (collectionPeriod.getRemainQuantity() < request.getQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该藏品剩余数量不足，请稍后再试");
        }

        //校验用户权益
        //if (!memberConfigService.validMemberPrivilege(operator.getId(), EUserPrivilege.USER_PRIVILEGE_4.getCode())) {
        //throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您当前会员等级，不能购买藏品");
        //}

        // 获取该用户已购买该藏品的份数
        Integer count = getUserBuyCount(collectionPeriod.getId(), operator.getId());
        // 判断该用户购买的份数是否已超过该藏品的单人购买上限
        if (count + request.getQuantity() > collectionPeriod.getBuyMax()) {
//            String redisKey = String.format(RedisKeyList.MT_CONFIG_KEY, collectionPeriod.getId(), operator.getId());
//            redisUtil.set(redisKey, count, redisLockTime);
            int i = collectionPeriodExtraBuyChanceService.detailPeriodExtraChance(collectionPeriod.getId(), operator.getId());
            if (count + request.getQuantity() > collectionPeriod.getBuyMax() + i) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您购买的份数已超过该期藏品的单人购买上限" + collectionPeriod.getBuyMax() + "份");
            }
        }
        //验证是否需要口令
        if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag())) {
            if (StringUtils.isBlank(request.getWord())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前期数需要填写口令");
            }
        }
    }

    @Override
    public List<OrderClose> getFirstMarketTimeoutOrder(BigDecimal closeTime) {
        return collectionBuyOrderMapper.getFirstMarketTimeoutOrder(closeTime, new Date());
    }

    @Override
    public List<OrderClose> getOtherTimeoutOrder(BigDecimal closeTime) {
        return collectionBuyOrderMapper.getOtherTimeoutOrder(closeTime, new Date());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributionBlindBox(CollectionBuyOrder order) {
        try {
            lockService.create(ELockBizType.BLIND_BOX.getCode(), order.getId().toString());
        } catch (Exception e) {
            if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
            } else if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_9.getCode());
            }

            modify(order);
            return;
            //throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "重复插入");
        }

        order = collectionBuyOrderMapper.selectByPrimaryForUpdate(order.getId());
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus()) &&
                !ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不是待分配的无法处理");
        }

        User user = userService.detail(order.getUserId());
        Collection collection = collectionService.detailSimple(order.getCollectionId());

        List<CollectionDetail> collectionDetailList = collectionDetailService
                .doGenerateDetail(user, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_9
                                .getCode(), order.getId(), collection, order.getDiscountPrice(), 1, ECollectionDetailBuyChannel.THREE.getCode(),
                        collection.getLockTime(), collection.getTransformLimitTime(),
                        ECollectionDetailSource.CHANGE_TYPE_STATUS_5.getCode(), null);

        if (CollectionUtils.isEmpty(collectionDetailList)) {
            //已支付待分配的改成已支付
            if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_10.getCode());
            } else if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_10.getCode());
            }
        } else {

            //已支付待分配的改成已支付
            if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
            } else if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus())) {
                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_9.getCode());
            }
        }

        modify(order);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distributionFirstMarket(CollectionBuyOrder collectionBuyOrder) {
        lockService.create(ELockBizType.PERIOD_COLLECTION_DISTRIBUTION.getCode(), collectionBuyOrder.getId().toString());

        CollectionBuyOrder data = collectionBuyOrderMapper.selectByPrimaryForUpdate(collectionBuyOrder.getId());
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(data.getPayStatus())) {
            return;
        }
        CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(data.getBizId());

        ECollectionDetailSource source = null;
        ECollectionDetailBuyChannel buyChannel = ECollectionDetailBuyChannel.ZERO;
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_0;
        } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_1;
        } else if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_2;
            buyChannel = ECollectionDetailBuyChannel.PERIOD_BLIND_BOX;
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品分类不支持");
        }

        User operator = null;
        boolean companyFlag = false;

        BigDecimal acutalPayAmount = data.getPayBalanceAmount().add(data.getPayCashAmount());

        Collection collection = collectionService.detail(data.getCollectionId());

        // 判断是用户购买还是机构代购
        if (EPayType.TO_COMPANY_BUY.getId().equals(collectionBuyOrder.getPayType())) {
            Company company = companyService.detail(data.getUserId());
            operator = new User();
            operator.setId(company.getId());
            operator.setLoginName(company.getName());
            operator.setPhoto(company.getLogo());
            companyFlag = true;
            operator.setCompanyFlag(EBoolean.YES.getCode());
        } else {
            operator = userService.detailSimpleInfo(data.getUserId());
            operator.setCompanyFlag(EBoolean.NO.getCode());

            shellTaskConfigService.createPeriodBuy(operator, collectionBuyOrder.getId(), collectionBuyOrder.getPayAmount());
//            if (!EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(operator.getYaoFlag())) {
//                yaoTaskConfigService.createPeriodBuy(operator, collectionBuyOrder.getId());
//            } else {
//                // 奖励用户钻石
//                Account buyDiamondAccount = accountService.getAccount(data.getUserId(), ECurrency.DIAMOND.getCode());
//                BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);
//
//                // 用户购买“作品”金额 奖励钻石
//                accountService.changeAmount(buyDiamondAccount, buyDiamondRate.multiply(acutalPayAmount),
//                        EChannelType.INNER.getCode(),
//                        data.getId().toString(), data.getId(),
//                        EJourBizTypeUser.UserDiamondIncome.Present,
//                        EJourBizTypeUser.UserDiamondIncome.BUY_COLLECTION,
//                        EJourBizTypeUser.UserDiamondIncome.BUY_COLLECTION,
//                        collection.getName());
//
////                Account integral = accountService.getAccount(data.getUserId(), ECurrency.INTEGRAL.getCode());
////                // 用户购买“作品”金额 奖励元气值
////                accountService.changeAmount(integral, collection.getAccountIntegral(),
////                        EChannelType.INNER.getCode(),
////                        data.getId().toString(), data.getId(),
////                        EJourBizTypeUser.UserAccountIntegral.AccountIntegral,
////                        EJourBizTypeUser.UserAccountIntegral.BUY_COLLECTION,
////                        EJourBizTypeUser.UserAccountIntegral.BUY_COLLECTION,
////                        collection.getName());
//            }
        }

        BigDecimal commissionAmount = acutalPayAmount
                .multiply(collection.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));
        //记录平台的佣金收入，更新订单支付状态
        data.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());

        //产生产品型号并跟token进行关联
        Integer firstMarketLockHour = collection.getLockTime();
        List<CollectionDetail> collectionDetailList = collectionDetailService
                .doGenerateDetail(operator, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_3
                                .getCode(), data.getId(), collection, data.getDiscountPrice(), data.getQuantity(), buyChannel.getCode(),
                        firstMarketLockHour, collection.getTransformLimitTime(), source.getCode(), null);

        if (companyFlag) {
            data.setCommissionAmount(commissionAmount);
            collectionBuyOrderMapper.updateByPrimaryKeySelective(data);
            return;
        }

//        // 刷新用户KOL等级
//        memberConfigService.refreshMemberLevel(data.getUserId());
//
//        // 刷新节点等级
//        userNodeLevelService.doNodeLevelRefreshNew(data.getUserId(), acutalPayAmount);

        //消息推送
        String title = "购买藏品";
        String content = operator.getNickname() + "购买【" + collection.getName() + "】藏品";

        smsService
                .sendBuyMsg(operator, title, content, ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue());

        // 推荐收益(只给直推用户)
        incomeService.doInviteIncomeDirectly(data.getUserId(), commissionAmount, EIncomeAmountType.TYPE_4,
                data.getId(),
                content);
//
        // 推荐收益(只给直推用户)
//        incomeService.doInviteIncome(data.getUserId(), income, EIncomeAmountType.TYPE_4, data.getId(), content);
//
//        // 社区收益
//        incomeService.doNodeIncome(data.getUserId(), platIncome, EIncomeAmountType.TYPE_4, data.getId(),
//                content);

        //1、判断机构是否开启的分成模式
        //2、按机构分配的比例进行分配
        //3、yeepay产生分账记录
        BigDecimal companyAmount = BigDecimal.ZERO;
        BigDecimal platIncome = data.getPayCashAmount();
        if (EPayType.BANK_YEEPAY.getId().equals(data.getPayType()) || platIncome.compareTo(BigDecimal.ZERO) > 0) {
            Company company = companyService.detailSimple(collection.getAuthorId());
            String remark = "藏品出售" + collection.getName();

            if (ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus()) && EBoolean.YES.getCode()
                    .equals(company.getDivideFlag())) {

                platIncome = data.getPayCashAmount()
                        .multiply(collection.getPlatDivideRate().divide(new BigDecimal(100), 4, BigDecimal.ROUND_DOWN))
                        .setScale(2, BigDecimal.ROUND_UP);
                if (platIncome.compareTo(BigDecimal.ZERO) <= 0) {
                    platIncome = new BigDecimal("0.01");
                }
                companyAmount = data.getPayCashAmount().subtract(platIncome);
            }
            thirdDivideDetailService
                    .create(EThirdDivideDetailType.COMPANY.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
                            data.getId().toString(),
                            data.getPayOrderCode(), company.getMerchantNo(),
                            companyAmount, remark, company.getId());

            thirdDivideDetailService
                    .create(EThirdDivideDetailType.PLAT.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
                            data.getId().toString(),
                            data.getPayOrderCode(), company.getMerchantNo(),
                            platIncome, remark, company.getId());

            // 发行方账户加钱
            UserSettleAccount account = userSettleAccountService.getAccount(company.getId());
            String accountRemark = EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue();
            accountRemark = accountRemark + collection.getName();
            userSettleAccountService
                    .addToSettleAmount(account, companyAmount, data.getId().toString(), EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue(), accountRemark);
        }

        data.setCommissionAmount(platIncome);
        data.setCompanyAmount(companyAmount);
        collectionBuyOrderMapper.updateByPrimaryKeySelective(data);

        // 拉新活动处理
        // 处理拉新活动
        InvitationActivityUserRecord userRecord = invitationActivityUserRecordService
                .detailByUser(collectionBuyOrder.getUserId(), EInvitationActivityType.E_INVITATION_ACTIVITY_TYPE_1.getCode(),
                        data.getChannelId());
        if (null != userRecord) {
            userRecord = invitationActivityUserRecordService.detailForUpdate(userRecord.getId());
            if (EInvitationActivityUserRecordStatus.INVITATION_ACTIVITY_USER_RECORD_STATUS_0.getCode().equals(userRecord.getStatus())) {
                userRecord.setStatus(EInvitationActivityUserRecordStatus.INVITATION_ACTIVITY_USER_RECORD_STATUS_2.getCode());
                invitationActivityUserRecordService.modify(userRecord);
            }
        }

    }

    @Override
    public List<Long> selectMyLimitBuyBlindBoxCollectionId(Long userId, Long seriesId) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setUserId(userId);
        condition.setSeriesId(seriesId);

        return collectionBuyOrderMapper.selectMyLimitBuyBlindBoxCollectionId(condition);
    }

    @Override
    public Integer selectBlindBoxFreeTotalCountByCurrentMonth(Long parentBizId, List<Long> blindboxCollectionDetailIdList) {
        if (CollectionUtils.isEmpty(blindboxCollectionDetailIdList)) {
            return 0;
        }

        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setParentBizId(parentBizId);
        condition.setBlindboxCollectionDetailIdList(blindboxCollectionDetailIdList);
        condition.setPayType(ECollectionBuyOrderPayType.COLLECTION_BUY_ORDER_PAYTYPE_9.getCode());
        Date now = new Date();
        //2022年的特殊处理
        if ((DateUtil.dateToStr(now, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9)).substring(0, 4).equals("2022")) {
            condition.setPayDatetimeStart(DateUtil.strToDate("2022-11-01 00:00:00", com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
        } else {
            condition.setPayDatetimeStart(DateUtil.getCurrMonthFirstDay());
        }
        condition.setPayDatetimeEnd(DateUtil.getCurrMonthLastDay());

        return collectionBuyOrderMapper.selectTotalCount(condition);
    }

    @Override
    public Integer selectTotalTradedCount(String bizType, Date endDate) {
        CollectionBuyOrder condition = initSearchTradeCondition(bizType, endDate);

        return collectionBuyOrderMapper.selectTotalTradeCount(condition);
    }

    @Override
    public BigDecimal selectTotalTradedAmount(String bizType, Date endDate) {
        CollectionBuyOrder condition = initSearchTradeCondition(bizType, endDate);

        return collectionBuyOrderMapper.selectTotalTradeAmount(condition);
    }

    @Override
    public BigDecimal selectBlindTotalTradedAmount(Long userId, Date startDate, Date endDate) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setUserId(userId);
        condition.setBizType(ECollectionBuyOrderBizType.BLIND.getCode());
        condition.setPayDatetimeStart(startDate);
        condition.setPayDatetimeEnd(endDate);
        condition.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
        return collectionBuyOrderMapper.selectTotalTradeAmount(condition);
    }

    @Override
    public Integer listCount(CollectionBuyOrder buyOrder) {
        return collectionBuyOrderMapper.selectCount(buyOrder);

    }

    @Override
    public List<CompanySalesStatisticalRes> selectCompanyHomeSalesStatistical(Long companyId, String startDate, String endDate) {

        Date startDatetime = com.std.common.utils.DateUtil.strToDate(startDate, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
        Date endDatetime = com.std.common.utils.DateUtil.strToDate(endDate, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
        List<CompanySalesStatisticalRes> resList = collectionBuyOrderMapper
                .selectCompanyHomeSalesStatistical(companyId, startDatetime, endDatetime);

        for (CompanySalesStatisticalRes res : resList) {
            res.setDateStr(DateUtil.dateToStr(res.getDate(), "yyyy.MM.dd"));
        }

        Map<String, BigDecimal> dateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resList)) {
            for (CompanySalesStatisticalRes res : resList) {
                dateMap.put(res.getDateStr(), res.getAmount());
            }
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDatetime);

        List<CompanySalesStatisticalRes> companySalesStatisticalList = new ArrayList<>();
        Long id = 0L;
        while (true) {
            CompanySalesStatisticalRes res = new CompanySalesStatisticalRes();
            String dateToStr = DateUtil.dateToStr(calendar.getTime(), "yyyy.MM.dd");
            BigDecimal amount = dateMap.get(dateToStr);
            if (null == amount) {
                amount = BigDecimal.ZERO;
            }
            res.setId(id);
            res.setAmount(amount);
            res.setDate(calendar.getTime());
            res.setDateStr(dateToStr);
            companySalesStatisticalList.add(res);

            id++;
            calendar.add(Calendar.DAY_OF_MONTH, 1);

            if (calendar.getTime().after(endDatetime)) {
                break;
            }
        }

        return companySalesStatisticalList;
    }

    @Override
    public BigDecimal selectTotalSaleAmount(Long companyId) {
        return collectionBuyOrderMapper.selectTotalSaleAmount(companyId);
    }

    @Override
    public CollectionBuyOrderCollectionInfoRes detailByOrderCode(BaseIdReq req, User operator) {

        CollectionBuyOrder buyOrder = selectByOrderCode(req.getId().toString());
//        CollectionBuyOrder buyOrder = collectionBuyOrderMapper.selectByPrimaryKey(req.getId());
        if (!buyOrder.getUserId().equals(operator.getId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户不一致");
        }
        Collection collection = collectionService.detailSimple(buyOrder.getCollectionId());

        CollectionBuyOrderCollectionInfoRes res = new CollectionBuyOrderCollectionInfoRes();
        res.setCollectionName(collection.getName());
        res.setCollectionPic(collection.getCoverFileUrl());
        return res;
    }

    /**
     * 竞拍多次付款订单列表
     */
    @Override
    public CollectionBuyOrderAuctionPayRes periodAuctionPayRecord(BaseIdReq request, User operator) {
        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(request.getId());
        CollectionBuyOrderAuctionPayRes res = new CollectionBuyOrderAuctionPayRes();
        res.setTotalAmount(periodAuction.getCurrentPrice());
        res.setRemainAmount(periodAuction.getCurrentPrice().subtract(periodAuction.getAlreadyPayAmount()));

        Integer callbackTime = configService.getIntegerValue("period_auction_pay_callback_time");
        Date endTime = DateUtils.addSeconds(periodAuction.getDelayedTime(), callbackTime);
        // 截止日期
        res.setEndDatetime(endTime);

        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setBizId(request.getId());
        condition.setUserId(operator.getId());
        condition.setBizType(ECollectionBuyOrderBizType.AUCTION.getCode());
        condition.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());

        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByCondition(condition);

        List<CollectionBuyOrderAuctionPayRecordRes> payRecordResList = new ArrayList<>();
        for (CollectionBuyOrder buyOrder : collectionBuyOrderList) {
            CollectionBuyOrderAuctionPayRecordRes payRecordRes = new CollectionBuyOrderAuctionPayRecordRes();
            payRecordRes.setPayAmount(buyOrder.getPayAmount());
            payRecordRes.setPayDatetime(buyOrder.getPayDatetime());
            payRecordRes.setPayType(buyOrder.getPayType());

            if (EPayType.BANK_APPLET.getId().equals(buyOrder.getPayType()) || EPayType.BANK_YEEPAY.getId().equals(buyOrder.getPayType())) {
                PayRecord payRecord = payRecordService.detail(Long.valueOf(buyOrder.getPayOrderCode()));

                if (null != payRecord.getUserBindId()) {
                    UserBindCard userBindCard = userBindCardService.detail(payRecord.getUserBindId());
                    BankChannel bankChannel = bankChannelService.detail(userBindCard.getChannelBankId());
                    BankInfo bankInfo = bankInfoService.detail(bankChannel.getBankInfoId());

                    payRecordRes.setBankName(bankInfo.getBankName());
                    payRecordRes.setLogo(bankInfo.getLogo());
                    payRecordRes.setChannelType(bankChannel.getChannelType());
                    payRecordRes.setCardNo(
                            userBindCard.getCardNo().substring(userBindCard.getCardNo().length() - 4, userBindCard.getCardNo().length()));
                }
            }

            payRecordResList.add(payRecordRes);
        }

        res.setRecordResList(payRecordResList);
        return res;
    }


    @Override
    public void doPayback(Long aLong) {
        CollectionBuyOrder buyOrder = collectionBuyOrderMapper.selectByPrimaryKey(aLong);
        if (!ECollectionBuyOrderBizType.AUCTION.getCode().equals(buyOrder.getBizType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付记录不是中拍记录");
        }
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_4.getCode().equals(buyOrder.getPayStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付记录状态不是退款中");
        }
        buyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_5.getCode());
        collectionBuyOrderMapper.updateByPrimaryKeySelective(buyOrder);
    }

    private CollectionBuyOrder selectByOrderCode(String orderCode) {
        return collectionBuyOrderMapper.selectByOrderCode(orderCode);
    }

    private CollectionBuyOrder initSearchTradeCondition(String bizType, Date endDate) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setBizType(bizType);
        condition.setPayDatetimeEnd(endDate);
        condition.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());

        return condition;
    }

    /**
     * 获取用户购买该藏品的次数的
     */
    @Override
    public Integer getUserBuyCount(Long collectionPeriodId, Long userId) {
        CollectionBuyOrder order = new CollectionBuyOrder();
        order.setBizId(collectionPeriodId);
        order.setUserId(userId);
        return collectionBuyOrderMapper.getUserBuyCount(order);
    }

    /**
     * 支付金额，奖励钻石
     */
    private void pay(Collection collection, Account buyAccount,
            CollectionBuyOrder collectionBuyOrder,
            EJourCommon bizType,
            EJourCommon platBizType,
            EJourCommon diamondBizType,
            EIncomeAmountType incomeAmountType,
            String remark) {
        Long refId = collectionBuyOrder.getId();
        String collectionName = collection.getName();
        //默认余额支付
        Account systemAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
        //用户账户扣减金额
        accountService.changeAmount(buyAccount, collectionBuyOrder.getPayBalanceAmount().negate(),
                EChannelType.INNER.getCode(),
                refId.toString(), refId,
                EJourBizTypeUser.Collection.Collection,
                bizType,
                bizType,
                collectionName);
        //平台账户收钱
        accountService.changeAmount(systemAccount, collectionBuyOrder.getPayBalanceAmount(),
                EChannelType.INNER.getCode(),
                refId.toString(), refId,
                EJourBizTypeSystem.Collection.Collection,
                platBizType,
                platBizType,
                collectionName);

        BigDecimal acutalPayAmount = collectionBuyOrder.getPayBalanceAmount().add(collectionBuyOrder.getPayCashAmount());

        User user = userService.detailSimple(collectionBuyOrder.getUserId());
        if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(user.getYaoFlag())) {
            //奖励用户钻石
            Account buyDiamondAccount = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.DIAMOND.getCode());
            BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);

            //用户购买“作品”金额 奖励钻石
            accountService.changeAmount(buyDiamondAccount, buyDiamondRate.multiply(acutalPayAmount),
                    EChannelType.INNER.getCode(),
                    refId.toString(), refId,
                    EJourBizTypeUser.UserDiamondIncome.Present,
                    diamondBizType,
                    diamondBizType,
                    collectionName);
        }

//        //刷新用户KOL等级
//        memberConfigService.refreshMemberLevel(collectionBuyOrder.getUserId());
        Company company = companyService.detail(collection.getAuthorId());
        BigDecimal platIncome = acutalPayAmount.multiply(company.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));

        // 推荐收益(只给直推用户)
        incomeService.doInviteIncomeDirectly(collectionBuyOrder.getUserId(), platIncome, incomeAmountType,
                collectionBuyOrder.getId(),
                remark);

//        // 推荐收益
//        incomeService.doInviteIncome(collectionBuyOrder.getUserId(), platIncome, incomeAmountType,
//                collectionBuyOrder.getId(),
//                remark);
//
//        // 社区收益
//        incomeService.doNodeIncome(collectionBuyOrder.getUserId(), platIncome, incomeAmountType, collectionBuyOrder.getId(),
//                remark);

        //记录平台的佣金收入
        collectionBuyOrder.setCommissionAmount(platIncome);
        collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

//        // 刷新节点等级
//        userNodeLevelService.doNodeLevelRefreshNew(collectionBuyOrder.getUserId(), acutalPayAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long payOrderCancel(String orderCode) {
        CollectionBuyOrder collectionBuyOrder = getByBizCodeForUpdate(orderCode);
        if (!ECollectionPayStatus.COLLECTION_PAYSTATUS_0.getCode().equals(collectionBuyOrder.getPayStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买藏品订单不是待支付状态");
        }

        if (ECollectionBuyOrderBizType.AUCTION.getCode().equals(collectionBuyOrder.getBizType())) {
            if (EPayType.BANK_YEEPAY.getId().equals(collectionBuyOrder.getPayType())) {
                OrderQueryResponse query = yeePayService.yeepayOrderQuery(collectionBuyOrder.getPayOrderCode());
                if (!"OPR00000".equals(query.getResult().getCode())) {
                    log.error("一级市场中拍取消支付:" + orderCode + "易宝接口访问不成功，返回code:" + query.getResult().getCode() + "," + query.getResult()
                            .getMessage());
                }
                if ("OPR00000".equals(query.getResult().getCode()) && EYeepayStatus.SUCCESS.getCode()
                        .equals(query.getResult().getStatus())) {
                    return null;
                }
            }

            if (collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
                Account account = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.CNY.getCode());

                Collection collection = collectionService.detail(collectionBuyOrder.getCollectionId());
                accountService.unfrozenAmount(account, collectionBuyOrder.getPayBalanceAmount(),
                        EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.BuyCollection_UnFrozen,
                        collectionBuyOrder.getId(), EJourBizTypeUser.Collection.BuyCollection_UnFrozen, collection.getName());
            }
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_2.getCode());
            collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

            PeriodAuction periodAuction = periodAuctionService.detailByPeriod(collectionBuyOrder.getBizId());
            periodAuction.setAlreadyPayAmount(periodAuction.getAlreadyPayAmount().subtract(collectionBuyOrder.getPayAmount()));
            periodAuctionService.modify(periodAuction);
        } else if (ECollectionBuyOrderBizType.PERIOD.getCode().equals(collectionBuyOrder.getBizType())) {
//            if (EPayType.BANK_YEEPAY.getId().equals(collectionBuyOrder.getPayType())) {
//                OrderQueryResponse query = yeePayService.yeepayOrderQuery(collectionBuyOrder.getPayOrderCode());
//                if (!"OPR00000".equals(query.getResult().getCode())) {
//                    log.error("一级市场取消支付:" + orderCode + "易宝接口访问不成功，返回code:" + query.getResult().getCode() + "," + query.getResult()
//                            .getMessage());
//                }
//                if ("OPR00000".equals(query.getResult().getCode()) && EYeepayStatus.SUCCESS.getCode()
//                        .equals(query.getResult().getStatus())) {
//                    return;
//                }
//            }

            if (collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
                Account account = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.CNY.getCode());

                Collection collection = collectionService.detail(collectionBuyOrder.getCollectionId());
                accountService.unfrozenAmount(account, collectionBuyOrder.getPayBalanceAmount(),
                        EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.BuyCollection_UnFrozen,
                        collectionBuyOrder.getId(), EJourBizTypeUser.Collection.BuyCollection_UnFrozen, collection.getName());
            }
            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_2.getCode());
            collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

            //todo 数量加回去,redis处理，queue db 更新
            CollectionPeriod collectionPeriod = collectionPeriodService.selectForUpdate(collectionBuyOrder.getBizId());
            collectionPeriod.setRemainQuantity(collectionBuyOrder.getQuantity());
            collectionPeriod.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_0.getCode());
            //由于优先购买的取消，但开售时间没到,startStatus=0的情况，不用更改状态
            if (!ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStartStatus())) {
                collectionPeriod.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
            }
            collectionPeriodMapper.updateRemainPayOrderCancel(collectionPeriod);

            CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
                    .detailRelation(collectionBuyOrder.getBizId(), collectionBuyOrder.getCollectionId());
            collectionPeriodRelation.setRemainQuantity(collectionBuyOrder.getQuantity());
            collectionPeriodRelationMapper.updateRemainPayOrderCancel(collectionPeriodRelation);
        } else if (ECollectionBuyOrderBizType.BLIND.getCode().equals(collectionBuyOrder.getBizType())) {

            if (EPayType.BANK_YEEPAY.getId().equals(collectionBuyOrder.getPayType())) {
                OrderQueryResponse query = yeePayService.yeepayOrderQuery(collectionBuyOrder.getPayOrderCode());
                if (!"OPR00000".equals(query.getResult().getCode())) {
                    log.error("幸运抽奖取消支付:" + orderCode + "易宝接口访问不成功，返回code:" + query.getResult().getCode() + "," + query.getResult()
                            .getMessage());
                }
                if ("OPR00000".equals(query.getResult().getCode()) && EYeepayStatus.SUCCESS.getCode()
                        .equals(query.getResult().getStatus())) {
                    return null;
                }
            }

            collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_2.getCode());
            collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

//            ChangeRecommend changeRecommend = changeRecommendService.detailForUpdate(collectionBuyOrder.getBizId());
//            changeRecommendService.addBlindBoxQuantity(changeRecommend.getId());
        }

        return collectionBuyOrder.getChannelId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCallback(String orderCode, String payment) {
        CollectionBuyOrder collectionBuyOrder = getByBizCodeForUpdate(orderCode);
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode().equals(collectionBuyOrder.getPayStatus())) {
            log.info("购买一级市场藏品回调失败，订单状态为：" + collectionBuyOrder.getPayStatus());
            return;
        }

        collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode());
        collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

        if (ECollectionBuyOrderBizType.AUCTION.getCode().equals(collectionBuyOrder.getBizType())) {
            PeriodAuction periodAuction = periodAuctionService.detailByPeriod(collectionBuyOrder.getBizId());
            if (periodAuction.getAlreadyPayAmount().compareTo(periodAuction.getCurrentPrice()) == 0) {
                periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_4.getCode());
                periodAuctionService.modify(periodAuction);
            }
        }

        if (collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());
            String collectionName = collection.getName();
            Account account = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.CNY.getCode());

            accountService.unfrozenAmount(account, collectionBuyOrder.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.BuyCollection_UnFrozen,
                    collectionBuyOrder.getId(), EJourBizTypeUser.Collection.BuyCollection_UnFrozen, collectionName);

            //系统账户
            Account systemAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
            accountService.changeAmount(account, collectionBuyOrder.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    collectionBuyOrder.getId().toString(), collectionBuyOrder.getId(),
                    EJourBizTypeUser.Collection.Collection,
                    EJourBizTypeUser.Collection.BuyCollection,
                    EJourBizTypeUser.Collection.BuyCollection,
                    collectionName);
            //平台账户收钱
            accountService.changeAmount(systemAccount, collectionBuyOrder.getPayBalanceAmount(),
                    EChannelType.INNER.getCode(),
                    collectionBuyOrder.getId().toString(), collectionBuyOrder.getId(),
                    EJourBizTypeSystem.Collection.Collection,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodIncome,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodIncome,
                    collectionName);
        }

        //产生发票记录
        try {
            Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());
            String orderNote = "[" + collection.getName() + "]藏品购买";
            Company company = companyService.detailSimple(collectionBuyOrder.getAuthorId());
            User user = userService.detailSimple(collectionBuyOrder.getUserId());
            invoiceOrderService.create(EInvoiceOrderOrderType.INVOICE_ORDER_ORDERTYPE_0.getCode(),
                    collectionBuyOrder.getId(), orderNote, collectionBuyOrder.getPayAmount(), company, user);
        } catch (Exception e) {
            log.error("产生发票报错，原因:" + e.getMessage());
        }
    }

    /**
     * 一级市场竞拍中拍支付
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dAuctionoCallback(String orderCode, String payment) {
        CollectionBuyOrder collectionBuyOrder = getByBizCodeForUpdate(orderCode);
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode().equals(collectionBuyOrder.getPayStatus())) {
            log.info("一级市场竞拍中拍支付回调失败，订单状态为：" + collectionBuyOrder.getPayStatus());
            return;
        }
        collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
        collectionBuyOrder.setPayDatetime(new Date());
        collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

        Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());
        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(collectionBuyOrder.getBizId());
        if (collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {

            String collectionName = collection.getName();
            Account account = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.CNY.getCode());

            accountService.unfrozenAmount(account, collectionBuyOrder.getPayBalanceAmount(),
                    EJourBizTypeUser.AuctionProduct.AuctionProduct, EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay_UnFrozen,
                    collectionBuyOrder.getId(), EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay_UnFrozen, collectionName);

            //系统账户
            Account systemAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
            accountService.changeAmount(account, collectionBuyOrder.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    collectionBuyOrder.getId().toString(), collectionBuyOrder.getId(),
                    EJourBizTypeUser.AuctionProduct.AuctionProduct,
                    EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay,
                    EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Pay,
                    collectionName);
            //平台账户收钱
            accountService.changeAmount(systemAccount, collectionBuyOrder.getPayBalanceAmount(),
                    EChannelType.INNER.getCode(),
                    collectionBuyOrder.getId().toString(), collectionBuyOrder.getId(),
                    EJourBizTypeSystem.Collection.Collection,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodAUctionIncome,
                    EJourBizTypeSystem.Collection.SellCollectionPeriodAUctionIncome,
                    collectionName);
        }

        auctionPaySuccess(collectionBuyOrder, periodAuction);

        //产生发票记录
        try {

            String orderNote = "[" + collection.getName() + "]藏品竞拍成功支付";
            Company company = companyService.detailSimple(collectionBuyOrder.getAuthorId());
            User user = userService.detailSimple(collectionBuyOrder.getUserId());
            invoiceOrderService.create(EInvoiceOrderOrderType.INVOICE_ORDER_ORDERTYPE_0.getCode(),
                    collectionBuyOrder.getId(), orderNote, collectionBuyOrder.getPayAmount(), company, user);
        } catch (Exception e) {
            log.error("产生发票报错，原因:" + e.getMessage());
        }
//        if (CollectionUtils.isEmpty(collectionDetailList)) {
//            //已支付待分配的改成已支付
//            if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
//                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_10.getCode());
//            } else if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus())) {
//                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_10.getCode());
//            }
//        } else {
//
//            //已支付待分配的改成已支付
//            if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode().equals(order.getPayStatus())) {
//                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
//            } else if (ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_8.getCode().equals(order.getPayStatus())) {
//                order.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_9.getCode());
//            }
//        }
//        modify(order);

    }

    /**
     * 幸运抽奖回调
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doLuckyDrawCallback(String orderCode, PayRecord payRecord, String payment) {
        CollectionBuyOrder collectionBuyOrder = getByBizCodeForUpdate(orderCode);
        if (!ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_0.getCode().equals(collectionBuyOrder.getPayStatus())) {
            log.info("幸运抽奖支付回调失败，订单状态为：" + collectionBuyOrder.getPayStatus());
            return;
        }
        collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode());
        collectionBuyOrder.setPayDatetime(new Date());

        //奖励用户钻石
//        Account buyDiamondAccount = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.DIAMOND.getCode());
//        BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);

        BigDecimal acutalPayAmount = collectionBuyOrder.getPayBalanceAmount().add(collectionBuyOrder.getPayCashAmount());

        Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());

        User user = userService.detailSimple(collectionBuyOrder.getUserId());
        if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(user.getYaoFlag())) {

//            奖励用户钻石
            Account buyDiamondAccount = accountService.getAccount(collectionBuyOrder.getUserId(), ECurrency.DIAMOND.getCode());
            BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);

            //用户购买“作品”金额 奖励钻石
            accountService.changeAmount(buyDiamondAccount, buyDiamondRate.multiply(acutalPayAmount),
                    EChannelType.INNER.getCode(),
                    collectionBuyOrder.getId().toString(), collectionBuyOrder.getId(),
                    EJourBizTypeUser.UserDiamondIncome.Present,
                    EJourBizTypeUser.Collection.CreateBlindBox,
                    EJourBizTypeSystem.Collection.SELLBlindBoxIncome,
                    collection.getName());
        }

        // 易宝进行分账
        BigDecimal companyAmount = BigDecimal.ZERO;
        BigDecimal platIncome = collectionBuyOrder.getPayCashAmount();

        if (EPayType.BANK_YEEPAY.getId().equals(collectionBuyOrder.getPayType()) || platIncome.compareTo(BigDecimal.ZERO) > 0) {
            Company company = companyService.detailSimple(collection.getAuthorId());

            String remark = "幸运抽奖，藏品：" + collection.getName();

            if (ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus()) && EBoolean.YES.getCode()
                    .equals(company.getDivideFlag())) {
                platIncome = collectionBuyOrder.getPayCashAmount()
                        .multiply(collection.getPlatDivideRate().divide(new BigDecimal(100), 4, BigDecimal.ROUND_DOWN))
                        .setScale(2, BigDecimal.ROUND_DOWN);
                companyAmount = collectionBuyOrder.getPayCashAmount().subtract(platIncome);
            }
            thirdDivideDetailService
                    .create(EThirdDivideDetailType.COMPANY.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getCode(),
                            collectionBuyOrder.getId().toString(),
                            payRecord, company.getMerchantNo(),
                            companyAmount, remark, company.getId());

            thirdDivideDetailService
                    .create(EThirdDivideDetailType.PLAT.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getCode(),
                            collectionBuyOrder.getId().toString(),
                            payRecord, company.getMerchantNo(),
                            platIncome, remark, company.getId());

            // 发行方账户加钱
            UserSettleAccount account = userSettleAccountService.getAccount(company.getId());
            String accountRemark = EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getValue();
            remark = remark + collection.getName();
            userSettleAccountService
                    .addToSettleAmount(account, companyAmount, collectionBuyOrder.getId().toString(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getCode(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getValue(), accountRemark);
        }

        collectionBuyOrder.setCommissionAmount(platIncome);
        collectionBuyOrder.setCompanyAmount(companyAmount);
        collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);

        try {

            String orderNote = "幸运抽奖[" + collection.getName() + "]藏品成功支付";
            Company company = companyService.detailSimple(collectionBuyOrder.getAuthorId());
            invoiceOrderService.create(EInvoiceOrderOrderType.INVOICE_ORDER_ORDERTYPE_1.getCode(),
                    collectionBuyOrder.getId(), orderNote, collectionBuyOrder.getPayAmount(), company, user);
        } catch (Exception e) {
            log.error("产生发票报错，原因:" + e.getMessage());
        }

    }

    private void auctionPaySuccess(CollectionBuyOrder collectionBuyOrder, PeriodAuction periodAuction) {
        // 更新期数剩余量
        if (periodAuction.getAlreadyPayAmount().compareTo(periodAuction.getCurrentPrice()) == 0) {
            // 退还保证金
            User user = userService.detail(collectionBuyOrder.getUserId());

            PeriodAuctionBondRecord auctionBondRecord = periodAuctionBondRecordService.detailByUser(user, collectionBuyOrder.getBizId());
            lockService.create(ELockBizType.PERIOD_AUCTION_RETURN.getCode(), auctionBondRecord.getId().toString());

            CollectionPeriod collectionPeriod = collectionPeriodService.detailsimple(collectionBuyOrder.getBizId());
            collectionPeriod.setBuyQuantity(1);
            int effectCount = collectionPeriodMapper.updateRemainQuantity(collectionPeriod);
            if (effectCount <= 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "中拍支付中");
            }
            collectionPeriod.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
            collectionPeriodService.modify(collectionPeriod);

            // 更新期数作品剩余量
            CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService
                    .detailRelation(collectionPeriod.getId(), collectionBuyOrder.getCollectionId());
            collectionPeriodRelation.setBuyQuantity(1);
            int effectCountRelation = collectionPeriodRelationMapper.updateRemainQuantity(collectionPeriodRelation);
            if (effectCountRelation <= 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "中拍支付中");
            }

            periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_4.getCode());
            periodAuction.setUpdateTime(new Date());
            periodAuction.setIsDivides(EBoolean.YES.getCode());
            periodAuctionService.modify(periodAuction);
            Collection collection = collectionService.detailSimple(collectionBuyOrder.getCollectionId());

            List<CollectionDetail> collectionDetailList = collectionDetailService
                    .doGenerateDetail(user, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_13
                                    .getCode(), periodAuction.getId(), collection, periodAuction.getCurrentPrice(), 1,
                            ECollectionDetailBuyChannel.PERIOD_AUCTION.getCode(),
                            collection.getLockTime(), collection.getTransformLimitTime(),
                            ECollectionDetailSource.CHANGE_TYPE_STATUS_7.getCode(), null);

            auctionBondRecord.setUpdateDatetime(new Date());

            Company company = companyService.detail(new Long(collectionPeriod.getAuthorIds()));

            if (EPayType.BANK_YEEPAY.getId().equals(auctionBondRecord.getPayType())) {

                thirdPaybackDetailService.create(EPayBackOrderType.ORDER_TYPE_1.getCode(),
                        auctionBondRecord.getId().toString(), company.getId(), company.getMerchantNo(),
                        auctionBondRecord.getPayOrderCode(), auctionBondRecord.getPayCashAmount(),
                        collectionPeriod.getName() + "中拍支付，保证金退还");
                auctionBondRecord.setStatus(EPeriodAuctionBondRecordStatus.PERIOD_AUCTION_BOND_RECORD_STATUS_4.getCode());

            } else if (EPayType.BALANCE.getId().equals(auctionBondRecord.getPayType())) {
                //todo余额扣除
                Long refId = auctionBondRecord.getId();
                //用户账户扣减金额
                Account buyAccount = accountService.getAccount(auctionBondRecord.getUserId(), ECurrency.CNY.getCode());

                accountService.unfrozenAmount(buyAccount, auctionBondRecord.getPayBalanceAmount(),
                        EJourBizTypeUser.AuctionProduct.AuctionProduct, EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Bond_UnFrozen,
                        refId, EJourBizTypeUser.AuctionProduct.AuctionProduct_Period_Bond_UnFrozen,
                        collectionPeriod.getName());

                auctionBondRecord.setStatus(EPeriodAuctionBondRecordStatus.PERIOD_AUCTION_BOND_RECORD_STATUS_1.getCode());
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不支持的退款方式");
            }

            periodAuctionBondRecordService.modify(auctionBondRecord);

            BigDecimal commissionAmount = periodAuction.getCurrentPrice()
                    .multiply(company.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));

            if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(user.getYaoFlag())) {
//                中拍成功给钻石
//                        奖励用户钻石
                Account buyDiamondAccount = accountService.getAccount(periodAuction.getLastUserId(), ECurrency.DIAMOND.getCode());
                BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);

                BigDecimal acutalPayAmount = periodAuction.getCurrentPrice();
                // 用户购买“作品”金额 奖励钻石
                accountService.changeAmount(buyDiamondAccount, buyDiamondRate.multiply(acutalPayAmount),
                        EChannelType.INNER.getCode(),
                        periodAuction.getId().toString(), periodAuction.getId(),
                        EJourBizTypeUser.UserDiamondIncome.Present,
                        EJourBizTypeUser.UserDiamondIncome.PERIOD_COLLECTION_AUCTION,
                        EJourBizTypeUser.UserDiamondIncome.PERIOD_COLLECTION_AUCTION,
                        periodAuction.getName());
            }

//            // 用户购买“作品”金额 奖励元气值
//            Account integral = accountService.getAccount(periodAuction.getLastUserId(), ECurrency.INTEGRAL.getCode());
//
//            accountService.changeAmount(integral, collection.getAccountIntegral(),
//                    EChannelType.INNER.getCode(),
//                    periodAuction.getId().toString(), periodAuction.getId(),
//                    EJourBizTypeUser.UserAccountIntegral.AccountIntegral,
//                    EJourBizTypeUser.UserAccountIntegral.PERIOD_COLLECTION_AUCTION,
//                    EJourBizTypeUser.UserAccountIntegral.PERIOD_COLLECTION_AUCTION,
//                    periodAuction.getName());

            String content = "一级竞拍" + periodAuction.getName() + "成功";
            // 推荐收益(只给直推用户)
            incomeService.doInviteIncomeDirectly(periodAuction.getLastUserId(), commissionAmount, EIncomeAmountType.TYPE_6,
                    periodAuction.getId(),
                    content);
//            // 期数竞拍支付成功，进行分账
//            periodAuctionDivides(collectionBuyOrder, periodAuction, collectionPeriod, collection);

        }
    }

    /**
     * 竞拍成功支付分账
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodAuctionDivides(PeriodAuction periodAuction, CollectionPeriod collectionPeriod) {
        try {
            lockService.create(ELockBizType.PERIOD_AUCTION_DIVIDE.getCode(), periodAuction.getId().toString());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "竞拍记录分账重复，操作回滚。" + e.getMessage());
        }

        // 易宝支付的进行分账
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setBizType(ECollectionBuyOrderBizType.AUCTION.getCode());
        condition.setBizId(periodAuction.getPeriodId());
        condition.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());
        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByCondition(condition);

        Company company = companyService.detail(Long.valueOf(collectionPeriod.getAuthorIds()));

        for (CollectionBuyOrder order : collectionBuyOrderList) {

            //1、判断机构是否开启的分成模式
            //2、按机构分配的比例进行分配
            //3、yeepay产生分账记录
            BigDecimal platIncome = order.getPayCashAmount();
            BigDecimal companyAmount = BigDecimal.ZERO;
            if (EPayType.BANK_YEEPAY.getId().equals(order.getPayType())) {

                String remark = "期数竞拍中拍支付，藏品：" + periodAuction.getName();

                if (ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus()) && EBoolean.YES.getCode()
                        .equals(company.getDivideFlag())) {
                    platIncome = order.getPayCashAmount()
                            .multiply(company.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));
                    companyAmount = order.getPayCashAmount().subtract(platIncome);
                }
                thirdDivideDetailService
                        .create(EThirdDivideDetailType.COMPANY.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_13.getCode(),
                                order.getId().toString(),
                                order.getPayOrderCode(), company.getMerchantNo(),
                                companyAmount, remark, company.getId());

                thirdDivideDetailService
                        .create(EThirdDivideDetailType.PLAT.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_13.getCode(),
                                order.getId().toString(),
                                order.getPayOrderCode(), company.getMerchantNo(),
                                platIncome, remark, company.getId());
            }

            order.setCommissionAmount(platIncome);
            order.setCompanyAmount(companyAmount);
            collectionBuyOrderMapper.updateByPrimaryKeySelective(order);
        }

        periodAuction.setIsDivides(EBoolean.NO.getCode());
        periodAuctionService.modify(periodAuction);
    }

    @Override
    public CollectionBuyOrder getByBizCodeForUpdate(String payOrderCode) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setPayOrderCode(payOrderCode);
        List<CollectionBuyOrder> list = collectionBuyOrderMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return collectionBuyOrderMapper.selectByPrimaryForUpdate(list.get(0).getId());
        }

        return null;
    }

    @Override
    public CollectionBuyOrder getByBizCode(String payOrderCode) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setPayOrderCode(payOrderCode);
        List<CollectionBuyOrder> list = collectionBuyOrderMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return collectionBuyOrderMapper.selectByPrimaryForUpdate(list.get(0).getId());
        }

        return null;
    }

    /**
     * 删除数字藏品型号购买订单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionBuyOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改数字藏品型号购买订单
     *
     * @param req 修改数字藏品型号购买订单入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionBuyOrderModifyReq req, User operator) {
        CollectionBuyOrder collectionBuyOrder = EntityUtils.copyData(req, CollectionBuyOrder.class);
        collectionBuyOrderMapper.updateByPrimaryKeySelective(collectionBuyOrder);
    }

    @Override
    public void modify(CollectionBuyOrder req) {
        collectionBuyOrderMapper.updateByPrimaryKeySelective(req);
    }

    /**
     * 详情查询数字藏品型号购买订单
     *
     * @param id 主键ID
     * @return 数字藏品型号购买订单对象
     */
    @Override
    public CollectionBuyOrder detailSimple(Long id) {
        CollectionBuyOrder collectionBuyOrder = collectionBuyOrderMapper.selectByPrimaryKey(id);
        if (null == collectionBuyOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collectionBuyOrder;
    }

    /**
     * 详情查询数字藏品型号购买订单
     *
     * @param id 主键ID
     * @return 数字藏品型号购买订单对象
     */
    @Override
    public CollectionBuyOrder detail(Long id) {
        CollectionBuyOrder collectionBuyOrder = collectionBuyOrderMapper.selectByPrimaryKey(id);
        if (null == collectionBuyOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionBuyOrder.setUser(userService.selectSummaryInfo(collectionBuyOrder.getUserId()));

        if (StringUtils.isBlank(collectionBuyOrder.getUser().getLoginName())) {
            Company company = companyService.detail(collectionBuyOrder.getUserId());
            User user = new User();
            user.setId(company.getId());
            user.setLoginName(company.getName());
            user.setNickname(company.getName());
            user.setPhoto(company.getLogo());
            collectionBuyOrder.setUser(user);
        }

        Collection collection = collectionService.detail(collectionBuyOrder.getCollectionId());
        collectionBuyOrder.setCollectionName(collection.getName());

        if (null != collectionBuyOrder.getPayBalanceAmount() && collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            collectionBuyOrder.setIsDeduction(EBoolean.YES.getCode());
        } else {
            collectionBuyOrder.setIsDeduction(EBoolean.NO.getCode());
        }
        collectionBuyOrder.setChannelName(channelMerchantService.detail(collectionBuyOrder.getChannelId()).getName());
        return collectionBuyOrder;
    }

    @Override
    public CollectionBuyOrder detail(Long id, User operator) {
        CollectionBuyOrder collectionBuyOrder = collectionBuyOrderMapper.selectByPrimaryKey(id);
        if (null == collectionBuyOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionBuyOrder.setUser(userService.selectSummaryInfo(collectionBuyOrder.getUserId()));

        if (StringUtils.isBlank(collectionBuyOrder.getUser().getLoginName())) {
            Company company = companyService.detail(collectionBuyOrder.getUserId());
            User user = new User();
            user.setId(company.getId());
            user.setLoginName(company.getName());
            user.setNickname(company.getName());
            user.setPhoto(company.getLogo());
            collectionBuyOrder.setUser(user);
        }

        Collection collection = collectionService.detail(collectionBuyOrder.getCollectionId());
        collectionBuyOrder.setCollectionName(collection.getName());

        if (null != collectionBuyOrder.getPayBalanceAmount() && collectionBuyOrder.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            collectionBuyOrder.setIsDeduction(EBoolean.YES.getCode());
        } else {
            collectionBuyOrder.setIsDeduction(EBoolean.NO.getCode());
        }
        collectionBuyOrder.setChannelName(channelMerchantService.detail(collectionBuyOrder.getChannelId()).getName());
        return collectionBuyOrder;
    }

    @Override
    public boolean doCheckBuyOrder(String bizType, Long userId, Long collectionId) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setBizType(bizType);
        condition.setUserId(userId);
        condition.setCollectionId(collectionId);
        condition.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        List<CollectionBuyOrder> list = collectionBuyOrderMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }

        return false;
    }

    /**
     * 分页查询数字藏品型号购买订单
     *
     * @param req 分页查询数字藏品型号购买订单入参
     * @return 分页数字藏品型号购买订单对象
     */
    @Override
    public List<CollectionBuyOrder> page(CollectionBuyOrderPageReq req, User operator) {

        CollectionBuyOrder condition = EntityUtils.copyData(req, CollectionBuyOrder.class);
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorId(operator.getCompanyId());
        }

        if (StringUtils.isNotBlank(req.getIsDeduction()) && EBoolean.NO.getCode().equals(req.getIsDeduction())) {
            condition.setNoDeduction(EBoolean.YES.getCode());
        } else if (StringUtils.isNotBlank(req.getIsDeduction()) && EBoolean.YES.getCode().equals(req.getIsDeduction())) {
            condition.setDeduction(EBoolean.YES.getCode());
        }

        condition.setPayDatetimeStart(DateUtil.dateTime(req.getPayDatetimeStart(), DateUtil.YYYY_MM_DD));
        condition.setPayDatetimeEnd(DateUtil.dateTime(req.getPayDatetimeEnd(), DateUtil.YYYY_MM_DD));
        condition.setOrderBy("t.id desc");
        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByConditionOss(condition);
        // 转译UserId
        collectionBuyOrderList.forEach(item -> {
            User summaryInfo = userService.selectSummaryInfo(item.getUserId());

            if (StringUtils.isBlank(summaryInfo.getLoginName())) {
                Company company = companyService.detail(item.getUserId());
                User user = new User();
                user.setId(company.getId());
                user.setLoginName(company.getName());
                user.setNickname(company.getName());
                user.setPhoto(company.getLogo());
                summaryInfo = user;

            } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
                summaryInfo.setLoginName(getLoginName(summaryInfo.getLoginName()));
                summaryInfo.setMobile(getLoginName(summaryInfo.getMobile()));
                summaryInfo.setRealName(null);

            }
            item.setUser(summaryInfo);

            Collection collection = collectionService.detail(item.getCollectionId());
            item.setCollectionName(collection.getName());

            if (item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0 && !EBigOrderPayType.ACCOUNT.getCode()
                    .equals(item.getPayType())) {
                item.setIsDeduction(EBoolean.YES.getCode());
            } else {
                item.setIsDeduction(EBoolean.NO.getCode());
            }

            if (null != item.getChannelId()) {
                item.setChannelName(channelMerchantService.detail(item.getChannelId()).getName());
            }
            if (null != item.getPromoteChannelId()) {
                item.setPromoteChannelName(channelConfigService.detail(item.getPromoteChannelId()).getChannelName());
            }

//            if (ECollectionBuyOrderSource.E_COLLECTION_BUY_ORDER_SOURCE_1.getCode().equals(item.getBuySource())) {
//                item.setPitNumber(pitService.detailSimple(item.getPitId()).getPitNumber());
//            }
        });

        return collectionBuyOrderList;
    }

    private String getLoginName(String loginNameStr) {
        StringBuffer sb = new StringBuffer(loginNameStr);
        String loginName = sb.replace(3, 7, "****").toString();
        return loginName;
    }

    /**
     * 列表查询数字藏品型号购买订单
     *
     * @param req 列表查询数字藏品型号购买订单入参
     * @return 列表数字藏品型号购买订单对象
     */
    @Override
    public List<CollectionBuyOrder> list(CollectionBuyOrderListReq req) {
        CollectionBuyOrder condition = EntityUtils.copyData(req, CollectionBuyOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionBuyOrder.class));

        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByCondition(condition);
        // 转译UserId
        collectionBuyOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return collectionBuyOrderList;
    }

    @Override
    public List<CollectionBuyOrder> list(CollectionBuyOrder req) {
        return collectionBuyOrderMapper.selectByCondition(req);
    }

    @Override
    public List<CollectionBuyOrder> listSimple(CollectionBuyOrder req) {
        return collectionBuyOrderMapper.selectSimpleByCondition(req);
    }

    /**
     * 前端详情查询数字藏品型号购买订单
     *
     * @param id 主键ID
     * @return 数字藏品型号购买订单对象
     */
    @Override
    public CollectionBuyOrderDetailRes detailFront(Long id) {
        CollectionBuyOrderDetailRes res = new CollectionBuyOrderDetailRes();

        CollectionBuyOrder collectionBuyOrder = collectionBuyOrderMapper.selectByPrimaryKey(id);
        if (null == collectionBuyOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionBuyOrder.setUser(userService.selectSummaryInfo(collectionBuyOrder.getUserId()));

        BeanUtils.copyProperties(collectionBuyOrder, res);

        return res;
    }

    /**
     * 前端分页查询数字藏品型号购买订单
     *
     * @param req 前端分页查询数字藏品型号购买订单入参
     * @return 分页数字藏品型号购买订单对象
     */
    @Override
    public List<CollectionBuyOrderPageRes> pageFront(CollectionBuyOrderPageFrontReq req) {
        CollectionBuyOrder condition = EntityUtils.copyData(req, CollectionBuyOrder.class);
        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByCondition(condition);
        // 转译UserId
        collectionBuyOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<CollectionBuyOrderPageRes> resList = collectionBuyOrderList.stream().map((entity) -> {
            CollectionBuyOrderPageRes res = new CollectionBuyOrderPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionBuyOrderList, resList);
    }

    /**
     * 前端列表查询数字藏品型号购买订单
     *
     * @param req 前端列表查询数字藏品型号购买订单入参
     * @return 列表数字藏品型号购买订单对象
     */
    @Override
    public List<CollectionBuyOrderListRes> listFront(CollectionBuyOrderListFrontReq req) {
        CollectionBuyOrder condition = EntityUtils.copyData(req, CollectionBuyOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionBuyOrder.class));

        List<CollectionBuyOrder> collectionBuyOrderList = collectionBuyOrderMapper.selectByCondition(condition);
        // 转译UserId
        collectionBuyOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<CollectionBuyOrderListRes> resList = collectionBuyOrderList.stream().map((entity) -> {
            CollectionBuyOrderListRes res = new CollectionBuyOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public CollectionBuyOrder selectPayTotalCountAmount(Long userId) {
        CollectionBuyOrder condition = new CollectionBuyOrder();
        condition.setUserId(userId);
        condition.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_1.getCode());

        return collectionBuyOrderMapper.selectQuantityAmount(condition);
    }

    /**
     * 获取队列长度
     *
     * <AUTHOR>
     */
    private Long getCollectionPeriodOrderQueueSize(Long collectionId, Long periodId) {
        String redisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_ORDER_QUEUE_KEY, collectionId.toString(), periodId.toString());
        return redisUtil.lGetListSize(redisKey);
    }
}