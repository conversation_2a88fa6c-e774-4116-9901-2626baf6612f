package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChannelType;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailRefType;
import com.std.core.enums.ECollectionDetailSource;
import com.std.core.enums.ECollectionPayStatus;
import com.std.core.enums.ECollectionPeriodJoinRecordPayStatus;
import com.std.core.enums.ECollectionPeriodJoinRecordStatus;
import com.std.core.enums.ECollectionPeriodStatus;
import com.std.core.enums.ECompanyDivideStatus;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EIncomeAmountType;
import com.std.core.enums.EInvoiceOrderOrderType;
import com.std.core.enums.EJourBizTypeSystem;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.EJourCommon;
import com.std.core.enums.ELockBizType;
import com.std.core.enums.EPayBackOrderType;
import com.std.core.enums.EPayRecordBizType;
import com.std.core.enums.EPayType;
import com.std.core.enums.EPeriodDiscountRefType;
import com.std.core.enums.ESystemAccount;
import com.std.core.enums.EThirdDivideDetailType;
import com.std.core.enums.EYeepayStatus;
import com.std.core.mapper.CollectionPeriodJoinRecordMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.BabyBankPayInfo;
import com.std.core.pojo.domain.BabyPayInfo;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodJoinRecord;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.PeriodDiscountDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserBindCard;
import com.std.core.pojo.domain.YeeBankPayInfo;
import com.std.core.pojo.request.CollectionPeriodJoinRecordCancelRegistrationReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordCreateReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordListFrontReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordListReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordModifyReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordPageFrontReq;
import com.std.core.pojo.request.CollectionPeriodJoinRecordPageReq;
import com.std.core.pojo.response.AdaPayInfo;
import com.std.core.pojo.response.AlipayPayOrderRes;
import com.std.core.pojo.response.CollectionPeriodJoinMyRecordRes;
import com.std.core.pojo.response.CollectionPeriodJoinRecordDetailRes;
import com.std.core.pojo.response.CollectionPeriodJoinRecordListRes;
import com.std.core.pojo.response.CollectionPeriodJoinRecordPageRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.pojo.response.WechatAppPayInfo;
import com.std.core.service.IAccountService;
import com.std.core.service.IAdapayService;
import com.std.core.service.IAlipayService;
import com.std.core.service.IBabyPayService;
import com.std.core.service.IBusinessChannelService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionPeriodJoinRecordService;
import com.std.core.service.ICollectionPeriodPriorityBuyService;
import com.std.core.service.ICollectionPeriodRelationService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.service.ICollectionPeriodWhiteJoinService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IIncomeService;
import com.std.core.service.IInvoiceOrderService;
import com.std.core.service.ILockService;
import com.std.core.service.IPeriodDiscountDetailService;
import com.std.core.service.IThirdDivideDetailService;
import com.std.core.service.IThirdPaybackDetailService;
import com.std.core.service.IUserBindCardService;
import com.std.core.service.IUserService;
import com.std.core.service.IWechatService;
import com.std.core.service.IYeePayService;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.RedisUtil;
import com.yeepay.yop.sdk.service.trade.response.OrderQueryResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 期数抽签区报名记录ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-02-22 18:51
 */
@Service
@Slf4j
public class CollectionPeriodJoinRecordServiceImpl implements ICollectionPeriodJoinRecordService {

    @Resource
    private CollectionPeriodJoinRecordMapper collectionPeriodJoinRecordMapper;

    @Resource
    private IUserService userService;

    @Resource
    private ICollectionPeriodPriorityBuyService collectionPeriodPriorityBuyService;

    @Resource
    private IAccountService accountService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private ICollectionPeriodService collectionPeriodService;


    @Resource
    private ICollectionService collectionService;

    @Resource
    private ICollectionPeriodRelationService collectionPeriodRelationService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ILockService lockService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IUserBindCardService userBindCardService;

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private IAdapayService adapayService;

    @Resource
    private IBabyPayService babyPayService;

    @Resource
    private IYeePayService yeePayService;

    @Resource
    private IThirdPaybackDetailService thirdPaybackDetailService;

    @Resource
    private IThirdDivideDetailService thirdDivideDetailService;

    @Resource
    private IBusinessChannelService businessChannelService;

    @Resource
    private IIncomeService incomeService;

    @Resource
    private IPeriodDiscountDetailService periodDiscountDetailService;

    @Resource
    private IInvoiceOrderService invoiceOrderService;

    @Resource
    private ICollectionPeriodWhiteJoinService collectionPeriodWhiteJoinService;

    /**
     * 新增期数抽签区报名记录
     *
     * @param req 新增期数抽签区报名记录入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(CollectionPeriodJoinRecordCreateReq req, User operator) {
        if (StringUtils.isBlank(operator.getRealName()) || StringUtils.isBlank(operator.getIdNo())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先实名认证");
        }

        businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(), req.getPayType());

        UserBindCard userBindCard = new UserBindCard();
        if (EPayType.BANK_APPLET.getId().equals(req.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(req.getPayType())) {
            if (null == req.getBindCardId()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
            }
            userBindCard = userBindCardService.detail(req.getBindCardId(), operator);
        }

        CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(req.getPeriodId());
        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "抽签期数未上架");
        }
        Date date = new Date();
        if (collectionPeriod.getStartSellDate().compareTo(date) > 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "抽签报名还未开始");
        }

        if (collectionPeriod.getEndSellDate().compareTo(date) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "抽签报名已结束");
        }

        // 判断用户剩余可参与次数
        Integer priorityJoinCount = collectionPeriodPriorityBuyService
                .getOtherJoinCount(collectionPeriod.getId(), collectionPeriod.getPriorityAddQuantityFlag(), operator.getId());

        Integer alreadyJoinCount = getAlreadyJoinCount(collectionPeriod.getId(), operator.getId());
        if (collectionPeriod.getBuyMax() + priorityJoinCount - alreadyJoinCount <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您的报名次数已用完");
        }

        if (EBigOrderPayType.ACCOUNT.getCode().equals(req.getPayType())) {
            if (StringUtils.isBlank(req.getPwd())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付密码不能为空");
            }
            //校验支付密码
            userService.checkTradePwd(operator.getId(), req.getPwd());
        }

        CollectionPeriodJoinRecord collectionPeriodJoinRecord = createJoinByAccount(collectionPeriod, operator, alreadyJoinCount,
                priorityJoinCount, req);

        OrderPayRes orderPayRes = new OrderPayRes();

        //余额支付
        orderPayRes = payBalanceAmount(collectionPeriod, collectionPeriodJoinRecord, operator);

        if (!EPayType.BALANCE.getId().equals(req.getPayType())) {
            // 三方支付
            orderPayRes = payCashAmount(req, userBindCard, collectionPeriodJoinRecord, operator, collectionPeriod.getChannelId());
        }

        return orderPayRes;
    }

    /**
     * 取消报名
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelRegistration(CollectionPeriodJoinRecordCancelRegistrationReq request, User operator) {
        // 校验支付密码
        userService.checkTradePwd(operator.getId(), request.getPwd());

        CollectionPeriodJoinRecord joinRecord = collectionPeriodJoinRecordMapper.selectForUpdate(request.getId());
        if (!ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_0.getCode().equals(joinRecord.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前报名记录不能取消");
        }

        joinRecord.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_3.getCode());
        joinRecord.setUpdateDatetime(new Date());
        joinRecord.setIsDeal(EBoolean.NO.getCode());

        CollectionPeriod period = collectionPeriodService.detailsimple(joinRecord.getPeriodId());
        //取消订单，退款
        noPay(period, joinRecord, EJourBizTypeUser.Collection.JoinDrawCancelBack);
        int i = collectionPeriodJoinRecordMapper.updateCancel(joinRecord);

        if (i <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "取消失败请重试");
        }
    }

    private OrderPayRes payCashAmount(CollectionPeriodJoinRecordCreateReq req, UserBindCard userBindCard,
            CollectionPeriodJoinRecord collectionPeriodJoinRecord, User operator, Long channelId) {
        Long orderCode = Long.valueOf(collectionPeriodJoinRecord.getPayOrderCode());
        BigDecimal payAmount = collectionPeriodJoinRecord.getPayCashAmount();
        if (BigDecimal.ZERO.compareTo(payAmount) >= 0) {
            return null;
        }
        OrderPayRes res = new OrderPayRes();
        res.setOrderId(collectionPeriodJoinRecord.getId());
//        res.setBuySuccessFlag(EBoolean.YES.getCode());

        if (EPayType.WECHAT.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.WECHAT.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {
            WechatAppPayInfo result = wechatService.getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode,
                    payAmount, req.getWxAppId(), collectionPeriodJoinRecord.getId());
            res.setWechatAppPayInfo(result);
        } else if (EPayType.ALIPAY.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.ALIPAY.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {
            String signOrder = alipayService.getTradeAppPaySignedOrder(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode,
                    payAmount, collectionPeriodJoinRecord.getId());
            AlipayPayOrderRes result = new AlipayPayOrderRes(signOrder);
            res.setAlipayPayOrderRes(result);
        } else if (EPayType.ADAPAY.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.ADAPAY.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {
            AdaPayInfo appPayInfo = adapayService.getAppPayInfo(operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                    EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode,
                    payAmount, collectionPeriodJoinRecord.getId());
            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.WECHAT_APPLET.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.WECHAT_APPLET.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {
            BabyPayInfo appPayInfo = babyPayService
                    .getAppPayInfo(EPayType.WECHAT_APPLET.getCode(), operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode,
                            payAmount);
//            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.BANK_APPLET.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.BANK_APPLET.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {

            PayRecord payRecord = babyPayService
                    .getAppBankPayInfo(EPayType.BANK_APPLET.getCode(), operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode, userBindCard,
                            payAmount, collectionPeriodJoinRecord.getId());
            BabyBankPayInfo payInfo = new BabyBankPayInfo();
            payInfo.setBizCode(payRecord.getBizCode().toString());
            payInfo.setBizType(payRecord.getPayType());
            res.setBabyBankPayInfo(payInfo);
        } else if (EPayType.BANK_YEEPAY.getCode().equals(collectionPeriodJoinRecord.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(collectionPeriodJoinRecord.getPayType())) {

            YeeBankPayInfo payInfo = yeePayService
                    .getAppBankPayInfo(EPayType.BANK_YEEPAY.getCode(), operator.getId(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getValue(), orderCode, userBindCard,
                            payAmount, collectionPeriodJoinRecord.getId(), channelId, null);
            res.setYeeBankPayInfo(payInfo);
        } else {
            throw new BizException(EErrorCode.CORE00000, "支付方式不支持");
        }

        return res;
    }

    private OrderPayRes payBalanceAmount(CollectionPeriod collectionPeriod, CollectionPeriodJoinRecord data, User operator) {

        //用户账户扣减金额
        Account userAccount = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());

        if (EPayType.BALANCE.getId().equals(data.getPayType())) {
            accountService.changeAmount(userAccount, data.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    data.getId().toString(), data.getId(),
                    EJourBizTypeUser.Collection.Collection,
                    EJourBizTypeUser.Collection.JoinDrawStrawsBuy,
                    EJourBizTypeUser.Collection.JoinDrawStrawsBuy,
                    collectionPeriod.getName());
        } else {
            accountService.frozenAmount(userAccount, data.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.JoinDrawStraws_Pay_Frozen,
                    data.getId(), EJourBizTypeUser.Collection.JoinDrawStraws_Pay_Frozen, collectionPeriod.getName());
        }

        return new OrderPayRes(data.getId(), EBoolean.YES.getCode());

    }

    private CollectionPeriodJoinRecord createJoinByAccount(CollectionPeriod collectionPeriod, User operator,
            Integer alreadyJoinCount, Integer priorityJoinCount, CollectionPeriodJoinRecordCreateReq req) {
        // 生成报名订单
        CollectionPeriodJoinRecord data = new CollectionPeriodJoinRecord();
        data.setPeriodId(collectionPeriod.getId());
        data.setUserId(operator.getId());
        data.setPrice(collectionPeriod.getPrice());
        data.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_0.getCode());

        //获取折扣值(根据用户的藏品id，获取最低折扣比例)

        PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                .getLowestByUserId(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
        if (null != periodDiscountDetail) {
            collectionPeriod.setDiscountPrice(
                    (collectionPeriod.getPrice().multiply(periodDiscountDetail.getDiscountRate())).setScale(2, BigDecimal.ROUND_DOWN));
            collectionPeriod.setDiscountRate(periodDiscountDetail.getDiscountRate());
        } else {
            collectionPeriod.setDiscountPrice(collectionPeriod.getPrice());
            collectionPeriod.setDiscountRate(BigDecimal.ONE);
        }

        data.setDiscountPrice(collectionPeriod.getDiscountPrice());
        data.setPayAmount(collectionPeriod.getDiscountPrice());
        Long orderCode = IdGeneratorUtil.generator();
        data.setPayOrderCode(orderCode.toString());

        Date date = new Date();
        data.setCreateDatetime(date);
        data.setPayDatetime(date);
        // 优先购标识
        data.setPriorityFlag(EBoolean.YES.getCode());
        if (alreadyJoinCount >= priorityJoinCount) {
            data.setPriorityFlag(EBoolean.NO.getCode());
        }
        //判断本次是否在白名单范围内
        Integer totalNumber = collectionPeriodWhiteJoinService.selectTotalNumber(collectionPeriod.getId(), operator);
        if (totalNumber > alreadyJoinCount) {
            data.setWhiteFlag(EBoolean.YES.getCode());
        } else {
            data.setWhiteFlag(EBoolean.NO.getCode());
        }
        data.setJoinTime(date.getTime());

        data.setPayType(req.getPayType());
        if (EPayType.BALANCE.getId().equals(req.getPayType())) {
            data.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
            data.setPayBalanceAmount(collectionPeriod.getDiscountPrice());
            data.setPayCashAmount(BigDecimal.ZERO);
        }
//        else if (EBoolean.YES.getCode().equals(req.getIsBalanceDiscount())) {
//            Account account = accountService.getAccount(operator.getId(), ECurrency.CNY.getCode());
//            if (account.getAvailableAmount().compareTo(collectionPeriod.getPrice()) >= 0) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "余额足够不需要抵扣");
//            }
//            data.setPayBalanceAmount(account.getAvailableAmount());
//            data.setPayCashAmount(collectionPeriod.getPrice().subtract(account.getAvailableAmount()));
//            data.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_0.getCode());
//
//        }
        else {
            data.setPayBalanceAmount(BigDecimal.ZERO);
            data.setPayCashAmount(collectionPeriod.getDiscountPrice());
            data.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_0.getCode());
        }

        // 落地报名时间数字
        String s = com.std.common.utils.DateUtil.dateToStr(date, "mm:ss:SSS");
        String newString = s.replace(":", "");
        data.setJoinMillis(Long.valueOf(newString));
        data.setGroup(data.getJoinMillis() % collectionPeriod.getDrawParam());
        if (EBoolean.YES.getCode().equals(data.getPriorityFlag())) {
            data.setPriorityGroup(data.getJoinMillis() % collectionPeriod.getPriorityDrawParam());
        }
        collectionPeriodJoinRecordMapper.insertSelective(data);

        //消息推送
//        String title = "报名抽签活动";
//        String content = operator.getNickname() + "成功报名【" + collectionPeriod.getName() + "】抽签活动";
//        smsService
//                .sendBuyMsg(operator, title, content, ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue());

        return data;
    }

//    private void paySuccess(CollectionPeriodJoinRecord collectionPeriodJoinRecord, CollectionPeriod collectionPeriod, User operator) {
//        collectionPeriodJoinRecord.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
//        collectionPeriodJoinRecord.setPayDatetime(new Date());
//        Date date = new Date();
//        collectionPeriodJoinRecord.setJoinTime(date.getTime());
//
//        // 落地报名时间数字
//        String s = com.std.common.utils.DateUtil.dateToStr(date, "mm:ss:SSS");
//        String aa = "";
//        String newString = s.replace(":", aa);
//        collectionPeriodJoinRecord.setJoinMillis(Long.valueOf(newString));
//        collectionPeriodJoinRecordMapper.updateByPrimaryKeySelective(collectionPeriodJoinRecord);
//
//        Account account = accountService.getAccount(collectionPeriodJoinRecord.getUserId(), ECurrency.CNY.getCode());
//        if (EBigOrderPayType.ACCOUNT.getCode().equals(collectionPeriodJoinRecord.getPayType())) {
//
//            accountService.frozenAmount(account, collectionPeriodJoinRecord.getPayBalanceAmount(),
//                    EJourBizTypeUser.Collection.Collection,
//                    EJourBizTypeUser.Collection.JoinDrawStraws_Frozen,
//                    collectionPeriodJoinRecord.getId(), EJourBizTypeUser.Collection.JoinDrawStraws_Frozen,
//                    collectionPeriod.getName());
//        }
//
//        //消息推送
//        String title = "报名抽签活动";
//        String content = operator.getNickname() + "已成功报名【" + collectionPeriod.getName() + "】抽签活动";
//        smsService
//                .sendBuyMsg(operator, title, content, ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue());
//    }

    /**
     * 删除期数抽签区报名记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionPeriodJoinRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改期数抽签区报名记录
     *
     * @param req 修改期数抽签区报名记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionPeriodJoinRecordModifyReq req, User operator) {
        CollectionPeriodJoinRecord collectionPeriodJoinRecord = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
        collectionPeriodJoinRecordMapper.updateByPrimaryKeySelective(collectionPeriodJoinRecord);
    }

    /**
     * 详情查询期数抽签区报名记录
     *
     * @param id 主键ID
     * @return 期数抽签区报名记录对象
     */
    @Override
    public CollectionPeriodJoinRecord detail(Long id) {
        CollectionPeriodJoinRecord collectionPeriodJoinRecord = collectionPeriodJoinRecordMapper.selectByPrimaryKey(id);
        if (null == collectionPeriodJoinRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionPeriodJoinRecord.setUser(userService.selectSummaryInfo(collectionPeriodJoinRecord.getUserId()));
        collectionPeriodJoinRecord.setCollection(null == collectionPeriodJoinRecord.getCollectionId() ? null
                : collectionService.detailSimple(collectionPeriodJoinRecord.getCollectionId()));
        collectionPeriodJoinRecord.setCollectionPeriod(collectionPeriodService.detail(collectionPeriodJoinRecord.getPeriodId()));

        return collectionPeriodJoinRecord;
    }

    @Override
    public CollectionPeriodJoinRecord detailHistory(Long id) {
        CollectionPeriodJoinRecord collectionPeriodJoinRecord = collectionPeriodJoinRecordMapper.selectByPrimaryKeyHistory(id);
        if (null == collectionPeriodJoinRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionPeriodJoinRecord.setUser(userService.selectSummaryInfo(collectionPeriodJoinRecord.getUserId()));
        collectionPeriodJoinRecord.setCollection(null == collectionPeriodJoinRecord.getCollectionId() ? null
                : collectionService.detailSimple(collectionPeriodJoinRecord.getCollectionId()));
        collectionPeriodJoinRecord.setCollectionPeriod(collectionPeriodService.detail(collectionPeriodJoinRecord.getPeriodId()));

        return collectionPeriodJoinRecord;
    }

    /**
     * 分页查询期数抽签区报名记录
     *
     * @param req 分页查询期数抽签区报名记录入参
     * @return 分页期数抽签区报名记录对象
     */
    @Override
    public List<CollectionPeriodJoinRecord> page(CollectionPeriodJoinRecordPageReq req) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
        condition.setIsDeduction(null);
        if (StringUtils.isNotBlank(req.getIsDeduction())) {
            if (EBoolean.NO.getCode().equals(req.getIsDeduction())) {
                condition.setNoIsDeduction(EBoolean.YES.getCode());
            } else if (EBoolean.YES.getCode().equals(req.getIsDeduction())) {
                condition.setIsDeduction(EBoolean.YES.getCode());
            }
        }
        condition.setPayDatetimeStart(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeStart(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        condition.setPayDatetimeEnd(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeEnd(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        condition.setOrderBy("id desc");
        List<CollectionPeriodJoinRecord> collectionPeriodJoinRecordList = collectionPeriodJoinRecordMapper.selectByConditionOss(condition);

        collectionPeriodJoinRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            item.setCollection(null == item.getCollectionId() ? null : collectionService.detailSimple(item.getCollectionId()));
            item.setCollectionPeriod(collectionPeriodService.detail(item.getPeriodId()));
            item.setCreateDatetimeStr(DateUtil.dateToStr(new Date(item.getJoinTime()), "yyyy-MM-dd HH:mm:ss.SSS"));

            if (null != item.getPayBalanceAmount() && item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0 && !EBigOrderPayType.ACCOUNT
                    .getCode().equals(item.getPayType())) {
                item.setIsDeduction(EBoolean.YES.getCode());
            } else {
                item.setIsDeduction(EBoolean.NO.getCode());
            }
        });

        return collectionPeriodJoinRecordList;
    }

    @Override
    public List<CollectionPeriodJoinRecord> pageHistory(CollectionPeriodJoinRecordPageReq req) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
        condition.setIsDeduction(null);
        if (StringUtils.isNotBlank(req.getIsDeduction())) {
            if (EBoolean.NO.getCode().equals(req.getIsDeduction())) {
                condition.setNoIsDeduction(EBoolean.YES.getCode());
            } else if (EBoolean.YES.getCode().equals(req.getIsDeduction())) {
                condition.setIsDeduction(EBoolean.YES.getCode());
            }
        }
        condition.setPayDatetimeStart(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeStart(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        condition.setPayDatetimeEnd(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeEnd(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        condition.setOrderBy("id desc");
        List<CollectionPeriodJoinRecord> collectionPeriodJoinRecordList = collectionPeriodJoinRecordMapper
                .selectByConditionHistory(condition);

        collectionPeriodJoinRecordList.forEach(item -> {
                    item.setUser(userService.selectSummaryInfo(item.getUserId()));
                    item.setCollection(null == item.getCollectionId() ? null : collectionService.detailSimple(item.getCollectionId()));
                    item.setCollectionPeriod(collectionPeriodService.detail(item.getPeriodId()));
                    item.setCreateDatetimeStr(DateUtil.dateToStr(new Date(item.getJoinTime()), "yyyy-MM-dd HH:mm:ss.SSS"));

                    if (null != item.getPayBalanceAmount() && item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0 && !EBigOrderPayType.ACCOUNT
                            .getCode().equals(item.getPayType())) {
                        item.setIsDeduction(EBoolean.YES.getCode());
                    } else {
                        item.setIsDeduction(EBoolean.NO.getCode());
                    }
                }
        );

        return collectionPeriodJoinRecordList;
    }

    /**
     * 列表查询期数抽签区报名记录
     *
     * @param req 列表查询期数抽签区报名记录入参
     * @return 列表期数抽签区报名记录对象
     */
    @Override
    public List<CollectionPeriodJoinRecord> list(CollectionPeriodJoinRecordListReq req) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPeriodJoinRecord.class));

        List<CollectionPeriodJoinRecord> collectionPeriodJoinRecordList = collectionPeriodJoinRecordMapper.selectByCondition(condition);
        // 转译UserId
        collectionPeriodJoinRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return collectionPeriodJoinRecordList;
    }

    /**
     * 前端详情查询期数抽签区报名记录
     *
     * @param id 主键ID
     * @return 期数抽签区报名记录对象
     */
    @Override
    public CollectionPeriodJoinRecordDetailRes detailFront(Long id) {
        CollectionPeriodJoinRecordDetailRes res = new CollectionPeriodJoinRecordDetailRes();

        CollectionPeriodJoinRecord collectionPeriodJoinRecord = collectionPeriodJoinRecordMapper.selectByPrimaryKey(id);
        if (null == collectionPeriodJoinRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collectionPeriodJoinRecord.setUser(userService.selectSummaryInfo(collectionPeriodJoinRecord.getUserId()));

        BeanUtils.copyProperties(collectionPeriodJoinRecord, res);

        return res;
    }

    /**
     * 前端分页查询期数抽签区报名记录
     *
     * @param req 前端分页查询期数抽签区报名记录入参
     * @return 分页期数抽签区报名记录对象
     */
    @Override
    public List<CollectionPeriodJoinRecordPageRes> pageFront(CollectionPeriodJoinRecordPageFrontReq req) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
//        condition.setOrderBy("t.status,t.join_time desc");

        condition.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        condition.setNoStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_3.getCode());
        List<CollectionPeriodJoinRecordPageRes> resList = collectionPeriodJoinRecordMapper.selectByConditionFront(condition);

        for (CollectionPeriodJoinRecordPageRes res : resList) {
            res.setCreateDatetimeStr(DateUtil.dateToStr(new Date(res.getTime()), "yyyy-MM-dd HH:mm:ss.SSS"));
            String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, res.getUserId().toString());
            User user;
            if (redisUtil.hasKey(userCacheKey)) {
                user = JSON.parseObject(JSON.toJSONString(redisUtil.get(userCacheKey)), User.class);
            } else {
                user = userService.detail(res.getUserId());
                if (null != user) {
                    redisUtil.set(userCacheKey, user);
                }
            }
            res.setNickname(userService.dealNickName(user.getNickname()));
            res.setPhoto(user.getPhoto());
            res.setTime(null);
        }
        return resList;
    }

    /**
     * 前端列表查询期数抽签区报名记录
     *
     * @param req 前端列表查询期数抽签区报名记录入参
     * @return 列表期数抽签区报名记录对象
     */
    @Override
    public List<CollectionPeriodJoinRecordListRes> listFront(CollectionPeriodJoinRecordListFrontReq req) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(req, CollectionPeriodJoinRecord.class);
//        condition.setOrderBy("t.status,t.join_time desc");

        condition.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        condition.setNoStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_3.getCode());
        List<CollectionPeriodJoinRecordListRes> resList = collectionPeriodJoinRecordMapper.selectByConditionListFront(condition);

        for (CollectionPeriodJoinRecordListRes res : resList) {
            res.setCreateDatetimeStr(DateUtil.dateToStr(new Date(res.getTime()), "yyyy-MM-dd HH:mm:ss.SSS"));
            String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, res.getUserId().toString());
            User user;
            if (redisUtil.hasKey(userCacheKey)) {
                user = JSON.parseObject(JSON.toJSONString(redisUtil.get(userCacheKey)), User.class);
            } else {
                user = userService.detail(res.getUserId());
                if (null != user) {
                    redisUtil.set(userCacheKey, user);
                }
            }
            res.setNickname(userService.dealNickName(user.getNickname()));
            res.setPhoto(user.getPhoto());
            res.setTime(null);
        }
        return resList;
    }

    @Override
    public List<CollectionPeriodJoinMyRecordRes> myRecordList(CollectionPeriodJoinRecordListFrontReq request) {
        CollectionPeriodJoinRecord condition = EntityUtils.copyData(request, CollectionPeriodJoinRecord.class);
//        condition.setOrderBy("t.status,t.join_time desc");

        condition.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
        List<CollectionPeriodJoinMyRecordRes> resList = collectionPeriodJoinRecordMapper.selectMyRecordList(condition);

        CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(request.getPeriodId());

        for (CollectionPeriodJoinMyRecordRes res : resList) {
            res.setCreateDatetimeStr(DateUtil.dateToStr(new Date(res.getTime()), "yyyy-MM-dd HH:mm:ss.SSS"));
            res.setPeriodName(collectionPeriod.getName());
            res.setPeriodPic(collectionPeriod.getCoverFileUrl());
            res.setTime(null);
        }
        return resList;
    }

    /**
     * 查询参与总人数
     */
    @Override
    public Integer getJoinTotalNumber(Long periodId) {
        return collectionPeriodJoinRecordMapper.getJoinTotalNumber(periodId);
    }

    @Override
    public Integer getJoinTotalNumberHistory(Long periodId) {
        return collectionPeriodJoinRecordMapper.getJoinTotalNumberHistory(periodId);
    }


    /**
     * 查询已参与次数
     */
    @Override
    public Integer getAlreadyJoinCount(Long periodId, Long userId) {
        return collectionPeriodJoinRecordMapper.getAlreadyJoinCount(periodId, userId);
    }

    @Override
    public List<CollectionPeriodJoinRecord> selectPriorityListByPeriod(Long periodId) {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        condition.setPeriodId(periodId);
        condition.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_0.getCode());
        condition.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
        condition.setPriorityFlag(EBoolean.YES.getCode());
        condition.setOrderBy("join_time asc");
        return collectionPeriodJoinRecordMapper.selectByCondition(condition);
    }

    @Override
    public List<CollectionPeriodJoinRecord> selectListByPeriod(Long periodId, Set<Long> pickOnList, String priorityFlag) {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        condition.setPeriodId(periodId);
        condition.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_0.getCode());
//        condition.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
        condition.setPriorityFlag(priorityFlag);
        condition.setNoIdList(pickOnList);
        condition.setOrderBy("join_time asc");

        return collectionPeriodJoinRecordMapper.selectSimpleByCondition(condition);
    }

    /**
     * 公布处理数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void publishedJoinRecord(Set<Long> pickOnList, Set<Long> idList, Long collectionId) {
        List<CollectionPeriodJoinRecord> recordList = new ArrayList<>();
        for (Long id : pickOnList) {
            CollectionPeriodJoinRecord joinRecord = collectionPeriodJoinRecordMapper.selectForUpdate(id);
            joinRecord.setCollectionId(collectionId);
            joinRecord.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_1.getCode());
            joinRecord.setUpdateDatetime(new Date());
            joinRecord.setIsDeal(EBoolean.YES.getCode());
            recordList.add(joinRecord);
        }

        for (Long id : idList) {
            CollectionPeriodJoinRecord joinRecord = collectionPeriodJoinRecordMapper.selectForUpdate(id);
            joinRecord.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_2.getCode());
            joinRecord.setUpdateDatetime(new Date());
            joinRecord.setIsDeal(EBoolean.YES.getCode());
            recordList.add(joinRecord);
        }

        if (CollectionUtils.isNotEmpty(recordList)) {
            collectionPeriodJoinRecordMapper.batchUpdate(recordList);
        }
    }

    @Override
    public List<CollectionPeriodJoinRecord> detailNoDealJoinRecord() {
        return collectionPeriodJoinRecordMapper.detailNoDealJoinRecord();
    }

    @Override
    public Integer detailNoDealJoinRecord(Long periodId) {
        return collectionPeriodJoinRecordMapper.detailNoDealJoinRecordByPeriod(periodId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealDrawStrawsJoinRecord(CollectionPeriodJoinRecord joinRecord, Map<Long, CollectionPeriod> periodMap) {
        lockService.create(ELockBizType.DRAW_STRAWS.getCode(), joinRecord.getId().toString());

        joinRecord = collectionPeriodJoinRecordMapper.selectForUpdate(joinRecord.getId());
        //如果不是需要处理，则返回
        if (!EBoolean.YES.getCode().equals(joinRecord.getIsDeal())) {
            return;
        }

        CollectionPeriod period = periodMap.get(joinRecord.getPeriodId());
        if (null == period) {
            period = collectionPeriodService.detailsimple(joinRecord.getPeriodId());
            CollectionPeriodRelation condition = new CollectionPeriodRelation();
            condition.setPeriodId(period.getId());
            Long collectionId = collectionPeriodRelationService.list(condition).get(0).getCollectionId();

            Collection collection = collectionService.detailSimple(collectionId);
            period.setCollection(collection);
        }

        if (ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_1.getCode().equals(joinRecord.getStatus())) {
            pay(period, joinRecord);
        } else if (ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_2.getCode().equals(joinRecord.getStatus())) {
            noPay(period, joinRecord, EJourBizTypeUser.Collection.JoinDrawStrawsBack);
        }

        joinRecord.setIsDeal(EBoolean.NO.getCode());
        collectionPeriodJoinRecordMapper.updateData(joinRecord);
    }

    @Override
    public void doCallback(String bizCode, String code) {
        CollectionPeriodJoinRecord record = collectionPeriodJoinRecordMapper.selectByPayOrderCode(bizCode);
        if (record.getPayStatus().equals(EBoolean.YES.getCode())) {
            return;
        }
        record.setPayType(code);
        record.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
        collectionPeriodJoinRecordMapper.updateByPrimaryKeySelective(record);

        if (record.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(record.getPeriodId());

            Account account = accountService.getAccount(record.getUserId(), ECurrency.CNY.getCode());

            accountService.unfrozenAmount(account, record.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.JoinDrawStraws_NoPay_UnFrozen,
                    record.getId(), EJourBizTypeUser.Collection.JoinDrawStraws_NoPay_UnFrozen, collectionPeriod.getName());

            accountService.changeAmount(account, record.getPayBalanceAmount().negate(),
                    EChannelType.INNER.getCode(),
                    record.getId().toString(), record.getId(),
                    EJourBizTypeUser.Collection.Collection,
                    EJourBizTypeUser.Collection.JoinDrawStrawsBuy,
                    EJourBizTypeUser.Collection.JoinDrawStrawsBuy,
                    collectionPeriod.getName());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payOrderCancel(String orderCode) {
        CollectionPeriodJoinRecord joinRecord = collectionPeriodJoinRecordMapper.selectByPayOrderCode(orderCode);
        joinRecord = collectionPeriodJoinRecordMapper.selectForUpdate(joinRecord.getId());
        if (joinRecord.getPayStatus().equals(EBoolean.YES.getCode())) {
            return;
        }

        if (EPayType.BANK_YEEPAY.getId().equals(joinRecord.getPayType())) {
            OrderQueryResponse query = yeePayService.yeepayOrderQuery(joinRecord.getPayOrderCode());
            if (!"OPR00000".equals(query.getResult().getCode())) {
                log.error(
                        "期数抽签取消支付:" + orderCode + "易宝接口访问不成功，返回code:" + query.getResult().getCode() + "," + query.getResult().getMessage());
            }
            if ("OPR00000".equals(query.getResult().getCode()) && EYeepayStatus.SUCCESS.getCode().equals(query.getResult().getStatus())) {
                return;
            }
        }

        joinRecord.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_2.getCode());
        joinRecord.setPayDatetime(new Date());
        collectionPeriodJoinRecordMapper.updateByPrimaryKeySelective(joinRecord);

        if (joinRecord.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            CollectionPeriod collectionPeriod = collectionPeriodService.getCollectionPeriod(joinRecord.getPeriodId());

            Account account = accountService.getAccount(joinRecord.getUserId(), ECurrency.CNY.getCode());

            accountService.unfrozenAmount(account, joinRecord.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.JoinDrawStraws_NoPay_UnFrozen,
                    joinRecord.getId(), EJourBizTypeUser.Collection.JoinDrawStraws_NoPay_UnFrozen, collectionPeriod.getName());
        }

    }

    @Override
    public Integer getSuccessfulCount(Long periodId, Long operatorId) {
        return collectionPeriodJoinRecordMapper.getSuccessfulCount(periodId, operatorId);
    }

    @Override
    public Integer getJoinDrawStrawsNumber(Long periodId) {
        return collectionPeriodJoinRecordMapper.getJoinDrawStrawsNumber(periodId);
    }

    @Override
    public Integer getJoinDrawStrawsNumberHistory(Long periodId) {
        return collectionPeriodJoinRecordMapper.getJoinDrawStrawsNumberHistory(periodId);
    }

    @Override
    public void doPeriodDrawStrawsEnd(Long periodId, Integer param, Integer Number, Set<Long> pickOnList, Set<Long> noPickOnList,
            String priorityFlag) {
        // 获取记录
        List<CollectionPeriodJoinRecord> recordList = selectListByPeriod(periodId, pickOnList, priorityFlag);
        Map<Long, Long> recordMaps = new LinkedHashMap<>();

        for (CollectionPeriodJoinRecord record : recordList) {
            recordMaps.put(record.getId(), record.getJoinMillis());
        }

        // 筛选记录
        extractPickOnRecord(recordMaps, pickOnList, param, Number);

        Set<Long> keySet = recordMaps.keySet();
        noPickOnList.clear();
        noPickOnList.addAll(keySet);

    }

    @Override
    public int doPeriodDrawStrawsEndWhite(CollectionPeriod period, Long collectionId) {
        CollectionPeriodJoinRecord joinRecord = new CollectionPeriodJoinRecord();

        joinRecord.setPeriodId(period.getId());
        joinRecord.setCollectionId(collectionId);
        joinRecord.setUpdateDatetime(new Date());
        return collectionPeriodJoinRecordMapper.doPeriodDrawStrawsEndWhite(joinRecord);
    }

    @Override
    public int doPeriodDrawStrawsEnd(Integer limit, CollectionPeriod period, Long collectionId, String priorityGroup) {
        CollectionPeriodJoinRecord joinRecord = new CollectionPeriodJoinRecord();

        joinRecord.setPeriodId(period.getId());
        joinRecord.setCollectionId(collectionId);
        joinRecord.setUpdateDatetime(new Date());
        joinRecord.setLimit(limit);
        joinRecord.setPriorityFlag(priorityGroup);
        return collectionPeriodJoinRecordMapper.doPeriodDrawStrawsEnd(joinRecord);
    }

    @Override
    public void doPeriodDrawStrawsMiss(Long periodId) {
        collectionPeriodJoinRecordMapper.doPeriodDrawStrawsMiss(periodId, new Date());
    }

    @Override
    public void doPeriodJoinRecordMigration() {
        collectionPeriodJoinRecordMapper.joinRecordMigration();
        collectionPeriodJoinRecordMapper.joinRecordRemove();
    }

    @Override
    public BigDecimal selectTotalAmount() {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        condition.setPayStatus(ECollectionPeriodJoinRecordPayStatus.COLLECTION_PERIOD_JOIN_RECORD_PAYSTATUS_1.getCode());
        condition.setNoStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_3.getCode());
        BigDecimal totalAmount = collectionPeriodJoinRecordMapper.selectTotalAmount(condition);
        BigDecimal historyTotalAmount = collectionPeriodJoinRecordMapper.selectHistoryTotalAmount(condition);

        return totalAmount.add(historyTotalAmount);
    }

    @Override
    public BigDecimal selectSuccessTotalAmount() {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        //已中签的报名记录
        condition.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_1.getCode());
        BigDecimal totalAmount = collectionPeriodJoinRecordMapper.selectTotalAmount(condition);
        BigDecimal historyTotalAmount = collectionPeriodJoinRecordMapper.selectHistoryTotalAmount(condition);

        return totalAmount.add(historyTotalAmount);
    }

    @Override
    public BigDecimal selectSuccessTotalAmount(Long userId, Date payDatetimeStart, Date payDatetimeEnd) {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        condition.setUserId(userId);
        condition.setPayDatetimeStart(payDatetimeStart);
        condition.setPayDatetimeEnd(payDatetimeEnd);
        //已中签的报名记录
        condition.setStatus(ECollectionPeriodJoinRecordStatus.COLLECTION_PERIOD_JOIN_RECORD_STATUS_1.getCode());
        BigDecimal totalAmount = collectionPeriodJoinRecordMapper.selectTotalAmount(condition);
        BigDecimal historyTotalAmount = collectionPeriodJoinRecordMapper.selectHistoryTotalAmount(condition);

        return totalAmount.add(historyTotalAmount);
    }

    @Override
    public Integer listCount(CollectionPeriodJoinRecord joinRecord) {
        return collectionPeriodJoinRecordMapper.selectCount(joinRecord);
    }

    @Override
    public Integer selectTotalCount(Long periodId, String priorityFlag, String whiteFlag) {
        CollectionPeriodJoinRecord condition = new CollectionPeriodJoinRecord();
        condition.setPeriodId(periodId);
        condition.setPriorityFlag(priorityFlag);
        condition.setWhiteFlag(whiteFlag);

        return collectionPeriodJoinRecordMapper.selectTotalCount(condition);
    }

    private void extractPickOnRecord(Map<Long, Long> recordMaps, Set<Long> pickOnList, Integer drawParam, Integer number) {
        if (number <= 0) {
            return;
        }
        Map<Long, List<Long>> groupMap = new TreeMap<>();
        for (Long id : recordMaps.keySet()) {
            if (null == recordMaps.get(id)) {
                continue;
            }
            long l = recordMaps.get(id) % drawParam;
            List<Long> groupList = groupMap.get(l);
            if (null == groupList || CollectionUtils.isEmpty(groupList)) {
                groupList = new ArrayList<>();
            }
            groupList.add(id);
            groupMap.put(l, groupList);
        }

        flag:
        for (Long num : groupMap.keySet()) {
            List<Long> recordList = groupMap.get(num);
            for (Long id : recordList) {
                pickOnList.add(id);
                recordMaps.remove(id);
                number--;
                if (number <= 0) {
                    break flag;
                }
            }
        }
    }

    private void noPay(CollectionPeriod collectionPeriod, CollectionPeriodJoinRecord collectionPeriodJoinRecord, EJourCommon bizCategory) {
        //易宝支付原路退还
        if (EPayType.BANK_YEEPAY.getId().equals(collectionPeriodJoinRecord.getPayType())) {
            Company company = companyService.detail(new Long(collectionPeriod.getAuthorIds()));
            thirdPaybackDetailService.create(EPayBackOrderType.ORDER_TYPE_3.getCode(),
                    collectionPeriodJoinRecord.getId().toString(), company.getId(), company.getMerchantNo(),
                    collectionPeriodJoinRecord.getPayOrderCode(), collectionPeriodJoinRecord.getPayCashAmount(),
                    collectionPeriod.getName() + EPayBackOrderType.ORDER_TYPE_3.getValue());
        } else {
            Account userAccount = accountService.getAccount(collectionPeriodJoinRecord.getUserId(), ECurrency.CNY.getCode());
            accountService.changeAmount(userAccount, collectionPeriodJoinRecord.getPayBalanceAmount(),
                    EChannelType.INNER.getCode(),
                    collectionPeriodJoinRecord.getId().toString(), collectionPeriodJoinRecord.getId(),
                    EJourBizTypeUser.Collection.Collection,
                    bizCategory,
                    bizCategory,
                    collectionPeriod.getName());
        }

        //记录平台的佣金收入
        collectionPeriodJoinRecord.setCommissionAmount(BigDecimal.ZERO);
    }

    private void pay(CollectionPeriod collectionPeriod, CollectionPeriodJoinRecord collectionPeriodJoinRecord) {
        //产生产品型号并跟token进行关联
        Collection collection = collectionPeriod.getCollection();
        Integer firstMarketLockHour = collection.getLockTime();
        User operator = new User();
        operator.setId(collectionPeriodJoinRecord.getUserId());
        collectionDetailService.doGenerateDetail(operator, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_3
                        .getCode(), collectionPeriodJoinRecord.getId(), collection, collectionPeriodJoinRecord.getDiscountPrice(), 1,
                ECollectionDetailBuyChannel.DRAW_STRAWS.getCode(),
                firstMarketLockHour, collection.getTransformLimitTime(), ECollectionDetailSource.CHANGE_TYPE_STATUS_3.getCode(), null);

        Long buyUserId = collectionPeriodJoinRecord.getUserId();
        Long refId = collectionPeriodJoinRecord.getId();
        String periodName = collectionPeriod.getName();

        //奖励用户钻石
//        Account buyDiamondAccount = accountService.getAccount(buyUserId, ECurrency.DIAMOND.getCode());
//        BigDecimal buyDiamondRate = configService.getBigDecimalValue(SysConstantsCache.DIAMOND_USER_BUY_COLLECTION);

        BigDecimal acutalPayAmount = collectionPeriodJoinRecord.getPayBalanceAmount().add(collectionPeriodJoinRecord.getPayCashAmount());

        //平台账户中签加钱
        Account incomeAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
        accountService.changeAmount(incomeAccount, collectionPeriodJoinRecord.getPayBalanceAmount(),
                EChannelType.INNER.getCode(),
                collectionPeriodJoinRecord.getId().toString(), collectionPeriodJoinRecord.getId(),
                EJourBizTypeSystem.Collection.Collection,
                EJourBizTypeSystem.Collection.JoinDrawStrawsBuyIncome,
                EJourBizTypeSystem.Collection.JoinDrawStrawsBuyIncome,
                collectionPeriod.getName());

//        //用户报名抽签活动 奖励钻石
//        accountService.changeAmount(buyDiamondAccount, buyDiamondRate.multiply(acutalPayAmount),
//                EChannelType.INNER.getCode(),
//                refId.toString(), refId,
//                EJourBizTypeUser.UserDiamondIncome.Present,
//                EJourBizTypeUser.UserDiamondIncome.JOIN_DRAW_STRAWS,
//                EJourBizTypeUser.UserDiamondIncome.JOIN_DRAW_STRAWS,
//                periodName);

        // 用户报名抽签活动 奖励元气值
//        Account integral = accountService.getAccount(buyUserId, ECurrency.INTEGRAL.getCode());

//        accountService.changeAmount(integral, collection.getAccountIntegral(),
//                EChannelType.INNER.getCode(),
//                refId.toString(), refId,
//                EJourBizTypeUser.UserAccountIntegral.AccountIntegral,
//                EJourBizTypeUser.UserAccountIntegral.JOIN_DRAW_STRAWS,
//                EJourBizTypeUser.UserAccountIntegral.JOIN_DRAW_STRAWS,
//                periodName);
//        //刷新用户KOL等级
//        memberConfigService.refreshMemberLevel(buyUserId);
//
//        // 刷新节点等级
//        userNodeLevelService.doNodeLevelRefreshNew(buyUserId, acutalPayAmount);

        Company company = companyService.detail(collectionPeriod.getAuthorIds());

        BigDecimal commissionAmount = acutalPayAmount
                .multiply(collection.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));

//        // 推荐收益
        incomeService.doInviteIncome(buyUserId, commissionAmount, EIncomeAmountType.TYPE_4, collectionPeriodJoinRecord.getId(),
                "参与抽奖区[" + collectionPeriod.getName() + "]");
//
//        // 社区收益
//        incomeService.doNodeIncome(buyUserId, commissionAmount, EIncomeAmountType.TYPE_4, collectionPeriodJoinRecord.getId(),
//                "参与抽奖区[" + collectionPeriod.getName() + "]活动");

        //1、判断机构是否开启的分成模式
        //2、按机构分配的比例进行分配
        //3、yeepay产生分账记录
        BigDecimal platIncome = collectionPeriodJoinRecord.getPayCashAmount();
        BigDecimal companyAmount = BigDecimal.ZERO;
        if (EPayType.BANK_YEEPAY.getId().equals(collectionPeriodJoinRecord.getPayType())) {

            String remark = "抽签报名" + collection.getName();

            if (ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus()) && EBoolean.YES.getCode()
                    .equals(company.getDivideFlag())) {
                platIncome = collectionPeriodJoinRecord.getPayCashAmount()
                        .multiply(collection.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN));
                companyAmount = collectionPeriodJoinRecord.getPayCashAmount().subtract(platIncome);
            }
            thirdDivideDetailService
                    .create(EThirdDivideDetailType.COMPANY.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                            collectionPeriodJoinRecord.getId().toString(),
                            collectionPeriodJoinRecord.getPayOrderCode(), company.getMerchantNo(),
                            companyAmount,
                            remark, company.getId());

            thirdDivideDetailService
                    .create(EThirdDivideDetailType.PLAT.getCode(), EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode(),
                            collectionPeriodJoinRecord.getId().toString(),
                            collectionPeriodJoinRecord.getPayOrderCode(), company.getMerchantNo(),
                            platIncome, remark, company.getId());
        }

        //记录平台的佣金收入
        collectionPeriodJoinRecord.setCommissionAmount(platIncome);

        try {
            String orderNote = "抽签购买[" + collection.getName() + "]成功";
            User user = userService.detailSimple(collectionPeriodJoinRecord.getUserId());
            invoiceOrderService.create(EInvoiceOrderOrderType.INVOICE_ORDER_ORDERTYPE_0.getCode(),
                    collectionPeriodJoinRecord.getId(), orderNote, collectionPeriodJoinRecord.getPayCashAmount(), company, user);
        } catch (Exception e) {
            log.error("产生发票报错，原因:" + e.getMessage());
        }
    }
}