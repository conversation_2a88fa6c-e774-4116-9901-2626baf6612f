package com.std.core.service.impl;

import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.*;
import com.std.core.mapper.UserSettleAccountMapper;
import com.std.core.pojo.domain.UserPaymentAccountInfo;
import com.std.core.pojo.domain.UserSettleAccount;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserSettleAccountCreateReq;
import com.std.core.pojo.request.UserSettleAccountListReq;
import com.std.core.pojo.request.UserSettleAccountListFrontReq;
import com.std.core.pojo.request.UserSettleAccountModifyReq;
import com.std.core.pojo.request.UserSettleAccountPageReq;
import com.std.core.pojo.request.UserSettleAccountPageFrontReq;
import com.std.core.pojo.response.UserSettleAccountDetailRes;
import com.std.core.pojo.response.UserSettleAccountListRes;
import com.std.core.pojo.response.UserSettleAccountPageRes;
import com.std.core.service.ICompanyService;
import com.std.core.service.ISettleAccountJourService;
import com.std.core.service.IUserSettleAccountService;
import com.std.core.service.IUserService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 用户结算账户ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 15:49
 */
@Service
public class UserSettleAccountServiceImpl implements IUserSettleAccountService {

    @Resource
    private UserSettleAccountMapper userSettleAccountMapper;

    @Resource
    private IUserService userService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private ISettleAccountJourService settleAccountJourService;

    /**
     * 新增用户结算账户
     *
     * @param req      新增用户结算账户入参
     * @param operator 操作人
     */
    @Override
    public void create(UserSettleAccountCreateReq req, User operator) {
        UserSettleAccount userSettleAccount = EntityUtils.copyData(req, UserSettleAccount.class);
        userSettleAccountMapper.insertSelective(userSettleAccount);
    }

    private UserSettleAccount create(Long userId) {
        UserSettleAccount userSettleAccount = new UserSettleAccount();
        userSettleAccount.setType(ESettleAccountType.C.getCode());
        userSettleAccount.setUserId(userId);
        userSettleAccount.setSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setOnSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setToSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setCreateDatetime(new Date());
        userSettleAccountMapper.insertSelective(userSettleAccount);

        return userSettleAccount;
    }

    private UserSettleAccount createCompnay(Long companyId) {
        UserSettleAccount userSettleAccount = new UserSettleAccount();
        userSettleAccount.setType(ESettleAccountType.BP.getCode());
        userSettleAccount.setUserId(companyId);
        userSettleAccount.setSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setOnSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setToSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setCreateDatetime(new Date());
        userSettleAccountMapper.insertSelective(userSettleAccount);

        return userSettleAccount;
    }

    private UserSettleAccount createPlat() {
        UserSettleAccount userSettleAccount = new UserSettleAccount();
        userSettleAccount.setType(ESettleAccountType.P.getCode());
        userSettleAccount.setUserId(100000000000000000L);
        userSettleAccount.setSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setOnSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setToSettleAmount(BigDecimal.ZERO);
        userSettleAccount.setCreateDatetime(new Date());
        userSettleAccountMapper.insertSelective(userSettleAccount);

        return userSettleAccount;
    }
    /**
     * 删除用户结算账户
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        userSettleAccountMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改用户结算账户
     *
     * @param req      修改用户结算账户入参
     * @param operator 操作人
     */
    @Override
    public void modify(UserSettleAccountModifyReq req, User operator) {
        UserSettleAccount userSettleAccount = EntityUtils.copyData(req, UserSettleAccount.class);
        userSettleAccountMapper.updateByPrimaryKeySelective(userSettleAccount);
    }

    /**
     * 详情查询用户结算账户
     *
     * @param id 主键ID
     * @return 用户结算账户对象
     */
    @Override
    public UserSettleAccount detail(Long id) {
        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectByPrimaryKey(id);
        if (null == userSettleAccount) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userSettleAccount.setUser(userService.selectSummaryInfo(userSettleAccount.getUserId()));

        return userSettleAccount;
    }

    @Override
    public UserSettleAccount detailForUpdate(Long id) {
        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectForUpdate(id);
        if (null == userSettleAccount) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return userSettleAccount;
    }

    /**
     * 分页查询用户结算账户
     *
     * @param req 分页查询用户结算账户入参
     * @return 分页用户结算账户对象
     */
    @Override
    public List<UserSettleAccount> page(UserSettleAccountPageReq req) {
        UserSettleAccount condition = EntityUtils.copyData(req, UserSettleAccount.class);
        condition.setType(ESettleAccountType.C.getCode());
        condition.setOrderBy("t.id desc");
        List<UserSettleAccount> userSettleAccountList = userSettleAccountMapper.selectByCondition(condition);
        // 转译UserId
        userSettleAccountList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userSettleAccountList;
    }

    @Override
    public List<UserSettleAccount> pageCompany(UserSettleAccountPageReq request) {
        UserSettleAccount condition = EntityUtils.copyData(request, UserSettleAccount.class);
        condition.setType(ESettleAccountType.BP.getCode());
        condition.setOrderBy("t.id desc");
        List<UserSettleAccount> userSettleAccountList = userSettleAccountMapper.selectByCondition(condition);
        // 转译UserId
        userSettleAccountList.forEach(item -> {
            item.setCompany(companyService.detailSimple(item.getUserId()));
        });

        return userSettleAccountList;
    }

    /**
     * 列表查询用户结算账户
     *
     * @param req 列表查询用户结算账户入参
     * @return 列表用户结算账户对象
     */
    @Override
    public List<UserSettleAccount> list(UserSettleAccountListReq req) {
        UserSettleAccount condition = EntityUtils.copyData(req, UserSettleAccount.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserSettleAccount.class));

        List<UserSettleAccount> userSettleAccountList = userSettleAccountMapper.selectByCondition(condition);
        // 转译UserId
        userSettleAccountList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        return userSettleAccountList;
    }

    /**
     * 前端详情查询用户结算账户
     *
     * @param id 主键ID
     * @return 用户结算账户对象
     */
    @Override
    public UserSettleAccountDetailRes detailFront(Long id) {
        UserSettleAccountDetailRes res = new UserSettleAccountDetailRes();

        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectByPrimaryKey(id);
        if (null == userSettleAccount) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        userSettleAccount.setUser(userService.selectSummaryInfo(userSettleAccount.getUserId()));

        BeanUtils.copyProperties(userSettleAccount, res);

        return res;
    }

    /**
     * 前端分页查询用户结算账户
     *
     * @param req 前端分页查询用户结算账户入参
     * @return 分页用户结算账户对象
     */
    @Override
    public List<UserSettleAccountPageRes> pageFront(UserSettleAccountPageFrontReq req) {
        UserSettleAccount condition = EntityUtils.copyData(req, UserSettleAccount.class);
        List<UserSettleAccount> userSettleAccountList = userSettleAccountMapper.selectByCondition(condition);
        // 转译UserId
        userSettleAccountList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<UserSettleAccountPageRes> resList = userSettleAccountList.stream().map((entity) -> {
            UserSettleAccountPageRes res = new UserSettleAccountPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(userSettleAccountList, resList);
    }

    /**
     * 前端列表查询用户结算账户
     *
     * @param req 前端列表查询用户结算账户入参
     * @return 列表用户结算账户对象
     */
    @Override
    public List<UserSettleAccountListRes> listFront(UserSettleAccountListFrontReq req) {
        UserSettleAccount condition = EntityUtils.copyData(req, UserSettleAccount.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), UserSettleAccount.class));

        List<UserSettleAccount> userSettleAccountList = userSettleAccountMapper.selectByCondition(condition);
        // 转译UserId
        userSettleAccountList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<UserSettleAccountListRes> resList = userSettleAccountList.stream().map((entity) -> {
            UserSettleAccountListRes res = new UserSettleAccountListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 增加用户结算账户代结算金额
     *
     * @param
     * @param amount
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        settleAccountJourService.create(settleAccount, EBoolean.NO.getCode(), settleAccount.getToSettleAmount(), amount, refId, bizType, bizNote, remark);
        settleAccount.setToSettleAmount(amount);
        userSettleAccountMapper.addToSettleAmount(settleAccount);


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        settleAccount.setToSettleAmount(amount);
        userSettleAccountMapper.addToSettleAmount(settleAccount);

    }

    @Override
    public void addOnSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }

        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_0.getCode(), settleAccount.getToSettleAmount(), amount.negate(), refId, bizType, bizNote, remark);
        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_2.getCode(), settleAccount.getOnSettleAmount(), amount, refId, bizType, bizNote, remark);
        settleAccount.setOnSettleAmount(amount);
        userSettleAccountMapper.addOnSettleAmount(settleAccount);
    }

    @Override
    public void subtractOnSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark) {

        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_2.getCode(), settleAccount.getOnSettleAmount(), amount.negate(), refId, bizType, bizNote, remark);
        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_1.getCode(), settleAccount.getSettleAmount(), amount, refId, bizType, bizNote, remark);

        settleAccount.setOnSettleAmount(amount);
        userSettleAccountMapper.subtractOnSettleAmount(settleAccount);
    }

    @Override
    public void subtractOnToSettleAmount(UserSettleAccount settleAccount, BigDecimal amount, String refId, String bizType, String bizNote, String remark) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return;
        }
        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_0.getCode(), settleAccount.getToSettleAmount(), amount.negate(), refId, bizType, bizNote, remark);
        settleAccountJourService.create(settleAccount, ESettleAccountJourType.E_SETTLE_ACCOUNT_JOUR_TYPE_1.getCode(), settleAccount.getSettleAmount(), amount, refId, bizType, bizNote, remark);

        settleAccount.setOnSettleAmount(amount);
        userSettleAccountMapper.subtractOnToSettleAmount(settleAccount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized UserSettleAccount getAccount(UserPaymentAccountInfo userInfo) {
        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectByuserId(userInfo.getUserId());

        if (null == userSettleAccount) {
            userSettleAccount = create(userInfo.getUserId());
        }
        return userSettleAccount;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public synchronized UserSettleAccount getAccount(Long companyId) {
        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectByCompanyId(companyId);

        if (null == userSettleAccount) {
            userSettleAccount = createCompnay(companyId);
        }

        UserSettleAccount settleAccount = detailForUpdate(userSettleAccount.getId());
        return settleAccount;
    }

    @Override
    public void addSettleAmount(UserSettleAccount settleAccount, BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        settleAccountJourService.create(settleAccount, EBoolean.YES.getCode(), settleAccount.getToSettleAmount(), amount.negate(), null, "-1", "每日结算", DateUtil.dateToStr(new Date(), DateUtil.DATA_TIME_PATTERN_9) + "结算");

        settleAccount.setSettleAmount(amount);
        userSettleAccountMapper.addSettleAmount(settleAccount);
    }

    @Override
    public UserSettleAccount getPlatAccount() {
        UserSettleAccount userSettleAccount = userSettleAccountMapper.selectPlatAccount();

        if (null == userSettleAccount) {
            userSettleAccount = createPlat();
        }
        return userSettleAccount;
    }




}