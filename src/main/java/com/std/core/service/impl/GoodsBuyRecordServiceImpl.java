package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EChannelType;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EExchangeCodeStyle;
import com.std.core.enums.EGoodsAdditionalInformationStatus;
import com.std.core.enums.EGoodsBuyRecordGoodsType;
import com.std.core.enums.EGoodsBuyRecordStatus;
import com.std.core.enums.EGoodsIntegralGoodsType;
import com.std.core.enums.EJourBizTypeSystem;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.ELockBizType;
import com.std.core.enums.ESystemAccount;
import com.std.core.mapper.GoodsBuyRecordMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.Express;
import com.std.core.pojo.domain.GoodsAdditionalInformation;
import com.std.core.pojo.domain.GoodsBuyRecord;
import com.std.core.pojo.domain.GoodsIntegral;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ExchangeCodeExchangeReq;
import com.std.core.pojo.request.GoodsBuyRecordBatchDeliveryReq;
import com.std.core.pojo.request.GoodsBuyRecordCancelDeliveryReq;
import com.std.core.pojo.request.GoodsBuyRecordDeliveryReq;
import com.std.core.pojo.request.GoodsBuyRecordListFrontReq;
import com.std.core.pojo.request.GoodsBuyRecordListReq;
import com.std.core.pojo.request.GoodsBuyRecordModifyReq;
import com.std.core.pojo.request.GoodsBuyRecordPageFrontReq;
import com.std.core.pojo.request.GoodsBuyRecordPageReq;
import com.std.core.pojo.request.GoodsBuyRecordSumReq;
import com.std.core.pojo.response.GoodsBuyRecordDelivery;
import com.std.core.pojo.response.GoodsBuyRecordDetailRes;
import com.std.core.pojo.response.GoodsBuyRecordListRes;
import com.std.core.pojo.response.GoodsBuyRecordPageRes;
import com.std.core.pojo.response.GoodsBuyRecordSumRes;
import com.std.core.service.IAccountService;
import com.std.core.service.IExchangeCodeService;
import com.std.core.service.IExpressService;
import com.std.core.service.IGoodsAdditionalInformationService;
import com.std.core.service.IGoodsBuyRecordService;
import com.std.core.service.IGoodsIntegralService;
import com.std.core.service.IGoodsSupplierService;
import com.std.core.service.ILockService;
import com.std.core.service.IUserService;
import com.std.core.util.SysConstants;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 兑现记录ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 01:43
 */
@Service
public class GoodsBuyRecordServiceImpl implements IGoodsBuyRecordService {

    @Resource
    private GoodsBuyRecordMapper goodsBuyRecordMapper;

    @Resource
    private IUserService userService;

    @Resource
    private IGoodsAdditionalInformationService goodsAdditionalInformationService;

    @Resource
    private ILockService lockService;

    @Resource
    private IExpressService expressService;

    @Resource
    private IAccountService accountService;

    @Resource
    private IGoodsIntegralService goodsIntegralService;

    @Resource
    private IGoodsSupplierService goodsSupplierService;

    @Resource
    private IExchangeCodeService exchangeCodeService;

    /**
     * 新增兑现记录
     */
    @Override
    public void create(GoodsBuyRecord req) {
        goodsBuyRecordMapper.insertSelective(req);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delivery(GoodsBuyRecordDeliveryReq request, User operator) {
        GoodsBuyRecord goodsBuyRecord = detailSimple(request.getId());
        if (!EGoodsBuyRecordGoodsType.GOODS_BUY_RECORD_GOODSTYPE_0.getCode().equals(goodsBuyRecord.getGoodsType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "实物商品才能发货");
        }
        if (!EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_0.getCode().equals(goodsBuyRecord.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无需发货");
        }
        Express express = expressService.detailByKdnCode(request.getLogisticsCompany());

        if (null == express) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
        }

        goodsBuyRecord.setLogisticsCompany(request.getLogisticsCompany());
        goodsBuyRecord.setLogisticsCode(request.getLogisticsCode());
        goodsBuyRecord.setStatus(EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_2.getCode());
        goodsBuyRecord.setDeliverer(request.getDeliverer());
        goodsBuyRecord.setDeliveryDatetime(new Date());
        goodsBuyRecord.setUpdater(operator.getId());
        goodsBuyRecord.setUpdaterName(operator.getLoginName());
        goodsBuyRecord.setUpdateDatetime(new Date());
        goodsBuyRecordMapper.updateByPrimaryKeySelective(goodsBuyRecord);
    }

    /**
     * 批量发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<GoodsBuyRecordDelivery> batchDelivery(GoodsBuyRecordBatchDeliveryReq request, User operator) {
        List<GoodsBuyRecordDelivery> resList = new ArrayList<>();
        for (GoodsBuyRecordDeliveryReq req : request.getReqList()) {
            GoodsBuyRecord goodsBuyRecord = detailSimple(req.getId());
            GoodsBuyRecordDelivery res = new GoodsBuyRecordDelivery();
            if (!EGoodsBuyRecordGoodsType.GOODS_BUY_RECORD_GOODSTYPE_0.getCode().equals(goodsBuyRecord.getGoodsType())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "实物商品才能发货");
                BeanUtils.copyProperties(req, res);
                res.setRemark("实物商品才能发货");
                resList.add(res);
                continue;
            }
            if (!EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_0.getCode().equals(goodsBuyRecord.getStatus())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无需发货");
                BeanUtils.copyProperties(req, res);
                res.setRemark("无需发货");
                resList.add(res);
                continue;
            }

            Express express = new Express();
            try {
                express = expressService.detailByKdnCode(req.getLogisticsCompany());
            } catch (Exception e) {
                BeanUtils.copyProperties(req, res);
                res.setRemark("快递物流错误");
                resList.add(res);
                continue;
            }

            if (null == express) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
                BeanUtils.copyProperties(req, res);
                res.setRemark("快递物流错误");
                resList.add(res);
                continue;
            }

            goodsBuyRecord.setLogisticsCompany(req.getLogisticsCompany());
            goodsBuyRecord.setLogisticsCode(req.getLogisticsCode());
            goodsBuyRecord.setStatus(EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_2.getCode());
            goodsBuyRecord.setDeliverer(req.getDeliverer());
            goodsBuyRecord.setDeliveryDatetime(new Date());
            goodsBuyRecord.setUpdater(operator.getId());
            goodsBuyRecord.setUpdaterName(operator.getLoginName());
            goodsBuyRecord.setUpdateDatetime(new Date());
            goodsBuyRecordMapper.updateByPrimaryKeySelective(goodsBuyRecord);
        }

        return resList;
    }

    /**
     * 取消发货
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelDelivery(GoodsBuyRecordCancelDeliveryReq request, User operator) {
        GoodsBuyRecord buyRecord = detailForUpdate(request.getId());
        if (!EGoodsBuyRecordGoodsType.GOODS_BUY_RECORD_GOODSTYPE_0.getCode().equals(buyRecord.getGoodsType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "实物商品才能取消发货");
        }

        if (EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_3.getCode().equals(buyRecord.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已取消切勿重复操作");
        }

        buyRecord.setStatus(EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_3.getCode());
        buyRecord.setUpdater(operator.getId());
        buyRecord.setUpdaterName(operator.getLoginName());
        buyRecord.setUpdateDatetime(new Date());
        buyRecord.setRemark(request.getRemark());

        GoodsIntegral goodsIntegral = goodsIntegralService.detailForUpdate(buyRecord.getGoodsId());

        goodsBuyRecordMapper.updateByPrimaryKeySelective(buyRecord);

        // 取消发货，元气值还给用户
        Account account = accountService.getAccount(buyRecord.getUserId(), ECurrency.INTEGRAL.getCode());
        accountService.changeAmount(account, buyRecord.getPayPrice(), EChannelType.INNER.getCode(),
                buyRecord.getId().toString(), buyRecord.getId(),
                EJourBizTypeUser.Integral.Integral,
                EJourBizTypeUser.Integral.Integral_Cancel,
                EJourBizTypeUser.Integral.Integral_Cancel, goodsIntegral.getName());

        //系统积分账户减钱
        Account systemAccount = accountService.getAccountForUpdate(ESystemAccount.BIZ.INCOME_INTEGRAL.getAccountNumber());
        accountService.changeAmount(systemAccount, goodsIntegral.getIntegralPrice(),
                EChannelType.INNER.getCode(),
                buyRecord.getId().toString(), buyRecord.getId(),
                EJourBizTypeSystem.Integral.Integral,
                EJourBizTypeSystem.Integral.Integral_Cancel,
                EJourBizTypeSystem.Integral.Integral_Cancel, goodsIntegral.getName());

        // 库存加回去
        int i = goodsIntegralService.modifyRemainQuantityAdd(goodsIntegral.getId(), buyRecord.getQuantity());

        if (i == 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "取消发货失败");
        }
    }

    /**
     * 删除兑现记录
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        goodsBuyRecordMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改兑现记录
     *
     * @param req 修改兑现记录入参
     * @param operator 操作人
     */
    @Override
    public void modify(GoodsBuyRecordModifyReq req, User operator) {
        GoodsBuyRecord goodsBuyRecord = EntityUtils.copyData(req, GoodsBuyRecord.class);
        goodsBuyRecordMapper.updateByPrimaryKeySelective(goodsBuyRecord);
    }

    /**
     * 详情查询兑现记录
     *
     * @param id 主键ID
     * @return 兑现记录对象
     */
    @Override
    public GoodsBuyRecord detail(Long id) {
        GoodsBuyRecord goodsBuyRecord = goodsBuyRecordMapper.selectByPrimaryKey(id);
        if (null == goodsBuyRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        goodsBuyRecord.setUser(userService.selectSummaryInfo(goodsBuyRecord.getUserId()));
        if (StringUtils.isNotBlank(goodsBuyRecord.getLogisticsCompany())) {
            Express express = expressService.detailByKdnCode(goodsBuyRecord.getLogisticsCompany());

            if (null == express) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
            }
            goodsBuyRecord.setLogisticsCompanyName(express.getName());
        }

        goodsBuyRecord.setSupplierName(goodsSupplierService.detail(goodsBuyRecord.getSupplierId()).getName());
        return goodsBuyRecord;
    }

    public GoodsBuyRecord detailSimple(Long id) {
        GoodsBuyRecord goodsBuyRecord = goodsBuyRecordMapper.selectByPrimaryKey(id);
        if (null == goodsBuyRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsBuyRecord;
    }

    public GoodsBuyRecord detailForUpdate(Long id) {
        GoodsBuyRecord goodsBuyRecord = goodsBuyRecordMapper.selectForUpdate(id);
        if (null == goodsBuyRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return goodsBuyRecord;
    }


    /**
     * 分页查询兑现记录
     *
     * @param req 分页查询兑现记录入参
     * @return 分页兑现记录对象
     */
    @Override
    public List<GoodsBuyRecord> page(GoodsBuyRecordPageReq req) {
        GoodsBuyRecord condition = EntityUtils.copyData(req, GoodsBuyRecord.class);
        if (StringUtils.isNotBlank(req.getLogisticsCode())) {
            condition.setLogisticsCodeStr(req.getLogisticsCode());
            condition.setLogisticsCode(null);
        }
        condition.setOrderBy("t.id desc");
        List<GoodsBuyRecord> goodsBuyRecordList = goodsBuyRecordMapper.selectByCondition(condition);
        // 转译UserId
        goodsBuyRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            if (StringUtils.isNotBlank(item.getLogisticsCompany())) {
                Express express = expressService.detailByKdnCode(item.getLogisticsCompany());

                if (null == express) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
                }
                item.setLogisticsCompanyName(express.getName());
            }
            item.setSupplierName(goodsSupplierService.detail(item.getSupplierId()).getName());
        });

        return goodsBuyRecordList;
    }

    /**
     * 列表查询兑现记录
     *
     * @param req 列表查询兑现记录入参
     * @return 列表兑现记录对象
     */
    @Override
    public List<GoodsBuyRecord> list(GoodsBuyRecordListReq req) {
        GoodsBuyRecord condition = EntityUtils.copyData(req, GoodsBuyRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsBuyRecord.class));

        List<GoodsBuyRecord> goodsBuyRecordList = goodsBuyRecordMapper.selectByCondition(condition);
        // 转译UserId
        goodsBuyRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            if (StringUtils.isNotBlank(item.getLogisticsCompany())) {
                Express express = expressService.detailByKdnCode(item.getLogisticsCompany());

                if (null == express) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
                }
                item.setLogisticsCompanyName(express.getName());
            }
        });

        return goodsBuyRecordList;
    }

    @Override
    public List<GoodsBuyRecord> list(GoodsBuyRecord req) {
        return goodsBuyRecordMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询兑现记录
     *
     * @param id 主键ID
     * @return 兑现记录对象
     */
    @Override
    public GoodsBuyRecordDetailRes detailFront(Long id) {
        GoodsBuyRecordDetailRes res = new GoodsBuyRecordDetailRes();

        GoodsBuyRecord goodsBuyRecord = goodsBuyRecordMapper.selectByPrimaryKey(id);
        if (null == goodsBuyRecord) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(goodsBuyRecord, res);

        if (EGoodsBuyRecordGoodsType.GOODS_BUY_RECORD_GOODSTYPE_1.getCode().equals(goodsBuyRecord.getGoodsType())
                && EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_2.getCode().equals(goodsBuyRecord.getStatus())) {
            GoodsAdditionalInformation information = goodsAdditionalInformationService.detail(goodsBuyRecord.getInformationId());
            res.setInformation(information.getContent());
            GoodsIntegral goodsIntegral = goodsIntegralService.detail(goodsBuyRecord.getGoodsId());
            if (SysConstants.COLLECTION_CATEGORY.equals(goodsIntegral.getCategoryId().toString())) {
                res.setGoodsType(EGoodsBuyRecordGoodsType.GOODS_BUY_RECORD_GOODSTYPE_2.getCode());
            }
        }

        if (StringUtils.isNotBlank(goodsBuyRecord.getLogisticsCompany())) {
            Express express = expressService.detailByKdnCode(goodsBuyRecord.getLogisticsCompany());

            if (null == express) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
            }
            res.setLogisticsCompany(express.getName());
        }

        return res;
    }

    /**
     * 前端分页查询兑现记录
     *
     * @param req 前端分页查询兑现记录入参
     * @return 分页兑现记录对象
     */
    @Override
    public List<GoodsBuyRecordPageRes> pageFront(GoodsBuyRecordPageFrontReq req, User operator) {
        GoodsBuyRecord condition = EntityUtils.copyData(req, GoodsBuyRecord.class);
        condition.setOrderBy("t.id desc");
        condition.setUserId(operator.getId());
        List<GoodsBuyRecord> goodsBuyRecordList = goodsBuyRecordMapper.selectByCondition(condition);

        List<GoodsBuyRecordPageRes> resList = goodsBuyRecordList.stream().map((entity) -> {
            GoodsBuyRecordPageRes res = new GoodsBuyRecordPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(goodsBuyRecordList, resList);
    }

    /**
     * 前端列表查询兑现记录
     *
     * @param req 前端列表查询兑现记录入参
     * @return 列表兑现记录对象
     */
    @Override
    public List<GoodsBuyRecordListRes> listFront(GoodsBuyRecordListFrontReq req) {
        GoodsBuyRecord condition = EntityUtils.copyData(req, GoodsBuyRecord.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), GoodsBuyRecord.class));

        List<GoodsBuyRecord> goodsBuyRecordList = goodsBuyRecordMapper.selectByCondition(condition);
        // 转译UserId
        goodsBuyRecordList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
        });

        List<GoodsBuyRecordListRes> resList = goodsBuyRecordList.stream().map((entity) -> {
            GoodsBuyRecordListRes res = new GoodsBuyRecordListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 虚拟商品分配附加信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void distribution(GoodsBuyRecord buyRecord) {
        try {
            lockService.create(ELockBizType.INTEGRAL_GOODS_DISTRIBUTION_1.getCode(), buyRecord.getId().toString());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分配附加信息重复");
        }

        if (!EGoodsIntegralGoodsType.GOODS_INTEGRAL_GOODSTYPE_1.getCode().equals(buyRecord.getGoodsType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有虚拟商城才能分配附加信息");
        }

        buyRecord = goodsBuyRecordMapper.selectForUpdate(buyRecord.getId());

        if (!EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_0.getCode().equals(buyRecord.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该信息已被使用" + buyRecord.getId());
        }

        GoodsAdditionalInformation information = goodsAdditionalInformationService.detailByGoodsId(buyRecord.getGoodsId());

        if (null == information) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "附加信息已用完");
        }

        try {
            lockService.create(ELockBizType.INTEGRAL_ADDITIONAL_INFORMATION_1.getCode(), information.getId().toString());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分配附加信息重复");
        }

        buyRecord.setInformationId(information.getId());
        buyRecord.setStatus(EGoodsBuyRecordStatus.GOODS_BUY_RECORD_STATUS_2.getCode());
        buyRecord.setUpdateDatetime(new Date());
        goodsBuyRecordMapper.updateByPrimaryKeySelective(buyRecord);

        information = goodsAdditionalInformationService.detailForUpdate(information.getId());

        if (!EGoodsAdditionalInformationStatus.GOODS_ADDITIONAL_INFORMATION_STATUS_0.getCode().equals(information.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分配错误，附加信息已被修改");
        }

        information.setRecordId(buyRecord.getId());
        information.setStatus(EGoodsAdditionalInformationStatus.GOODS_ADDITIONAL_INFORMATION_STATUS_1.getCode());
        information.setUpdateDatetime(new Date());
        goodsAdditionalInformationService.modify(information);

        ExchangeCodeExchangeReq exchangeCodeExchangeReq = new ExchangeCodeExchangeReq();
        exchangeCodeExchangeReq.setExchangeNo(information.getContent());
        User user = userService.detail(buyRecord.getUserId());
        exchangeCodeService.exchange(exchangeCodeExchangeReq, user, null, EExchangeCodeStyle.ARTIVITY);
    }

    @Override
    public List<GoodsBuyRecordSumRes> detailSum(GoodsBuyRecordSumReq request) {
        GoodsBuyRecord condition = EntityUtils.copyData(request, GoodsBuyRecord.class);
        GoodsBuyRecordSumRes goodsBuyRecordSumRes = goodsBuyRecordMapper.selectSum(condition);
        Integer totalUserCount = goodsBuyRecordMapper.selectSumUser(condition);
        goodsBuyRecordSumRes.setTotalUserCount(totalUserCount);

        List<GoodsBuyRecordSumRes> resList = new ArrayList<>();
        resList.add(goodsBuyRecordSumRes);
        return resList;
    }


}