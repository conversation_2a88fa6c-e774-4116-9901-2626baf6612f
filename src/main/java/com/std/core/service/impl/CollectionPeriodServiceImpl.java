package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.*;
import com.std.core.mapper.CategoryMapper;
import com.std.core.mapper.CollectionBuyOrderMapper;
import com.std.core.mapper.CollectionPeriodMapper;
import com.std.core.mapper.CollectionPeriodRelationMapper;
import com.std.core.pojo.domain.ApproveRecord;
import com.std.core.pojo.domain.Category;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionBuyOrder;
import com.std.core.pojo.domain.CollectionCreateCheck;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodInfo;
import com.std.core.pojo.domain.CollectionPeriodPriorityBuy;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.CollectionPeriodSend;
import com.std.core.pojo.domain.CollectionPeriodWhiteJoin;
import com.std.core.pojo.domain.CollectionRightCompany;
import com.std.core.pojo.domain.CollectionRightRecord;
import com.std.core.pojo.domain.CollectionRightsDetail;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.CompanyInfo;
import com.std.core.pojo.domain.Contract;
import com.std.core.pojo.domain.JpushRecord;
import com.std.core.pojo.domain.PeriodAuction;
import com.std.core.pojo.domain.PeriodChannelWord;
import com.std.core.pojo.domain.PeriodDiscountDetail;
import com.std.core.pojo.domain.Produced;
import com.std.core.pojo.domain.SubscriptionSend;
import com.std.core.pojo.domain.UploadFile;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CategoryListRes;
import com.std.core.pojo.response.CollectionContractTokenRes;
import com.std.core.pojo.response.CollectionPeriodAndCollectionDetailRes;
import com.std.core.pojo.response.CollectionPeriodAuditCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodAuditRes;
import com.std.core.pojo.response.CollectionPeriodByPlateRes;
import com.std.core.pojo.response.CollectionPeriodCategoryRes;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodCollectionPageResRelation;
import com.std.core.pojo.response.CollectionPeriodCollectionRes;
import com.std.core.pojo.response.CollectionPeriodDetailFrontRes;
import com.std.core.pojo.response.CollectionPeriodDropRes;
import com.std.core.pojo.response.CollectionPeriodListRes;
import com.std.core.pojo.response.CollectionPeriodPageFrontRes;
import com.std.core.pojo.response.CollectionPeriodPlateListRes;
import com.std.core.pojo.response.CollectionRightsDetailListRes;
import com.std.core.pojo.response.TreasurePlanCompanyDetailRes;
import com.std.core.pojo.response.TreasurePlanCompanyListRes;
import com.std.core.pojo.response.TreasurePlanDetailListRes;
import com.std.core.pojo.response.TreasurePlanHomeDetailRes;
import com.std.core.pojo.response.TreasurePlanHomeListRes;
import com.std.core.pojo.response.TreasurePlanListRes;
import com.std.core.service.IApproveRecordService;
import com.std.core.service.ICategoryService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICnavigateService;
import com.std.core.service.ICollectionBuyOrderService;
import com.std.core.service.ICollectionPeriodExtraBuyChanceService;
import com.std.core.service.ICollectionPeriodJoinRecordService;
import com.std.core.service.ICollectionPeriodPriorityBuyService;
import com.std.core.service.ICollectionPeriodRelationService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.service.ICollectionPeriodWhiteJoinService;
import com.std.core.service.ICollectionRightCompanyService;
import com.std.core.service.ICollectionRightRecordService;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyChannelService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IConfigService;
import com.std.core.service.IContractService;
import com.std.core.service.IContractTokenService;
import com.std.core.service.IJpushRecordService;
import com.std.core.service.ILockService;
import com.std.core.service.IPeriodAuctionBondRecordService;
import com.std.core.service.IPeriodAuctionService;
import com.std.core.service.IPeriodChannelWordService;
import com.std.core.service.IPeriodDiscountDetailService;
import com.std.core.service.IProducedService;
import com.std.core.service.ISmsService;
import com.std.core.service.ISubscriptionSendService;
import com.std.core.service.IUserService;
import com.std.core.util.DateUtil;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstants;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作品期数ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Service
public class CollectionPeriodServiceImpl implements ICollectionPeriodService {

    @Resource
    private CollectionPeriodMapper collectionPeriodMapper;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private IConfigService configService;

    @Resource
    private CollectionPeriodRelationMapper collectionPeriodRelationMapper;

    @Resource
    private ICollectionPeriodRelationService collectionPeriodRelationService;

    @Resource
    private ICollectionPeriodPriorityBuyService collectionPeriodPriorityBuyService;

    @Resource
    private IContractTokenService contractTokenService;

    @Resource
    private ICompanyService companyService;

    private static Integer COPYRIGHT_QUANTITY = 1;

    @Resource
    private ICnavigateService cnavigateService;

    @Resource
    private IJpushRecordService jpushRecordService;

    @Resource
    private ICollectionPeriodJoinRecordService collectionPeriodJoinRecordService;

    @Resource
    private RedisUtil redisUtil;

    private static Long redisLockTime = 600L;

    @Resource
    private IContractService contractService;

    @Resource
    private ILockService lockService;

    @Resource
    private IPeriodAuctionService periodAuctionService;

    @Resource
    private IPeriodAuctionBondRecordService periodAuctionBondRecordService;

    @Resource
    private IUserService userService;

    @Resource
    private IPeriodDiscountDetailService periodDiscountDetailService;

    @Resource
    private CollectionBuyOrderMapper collectionBuyOrderMapper;

    @Resource
    private IPeriodChannelWordService periodChannelWordService;

    @Resource
    private ICompanyChannelService companyChannelService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private IApproveRecordService approveRecordService;

    @Resource
    private ICategoryService categoryService;

    @Resource
    private ICollectionRightsDetailService collectionRightsDetailService;

    @Resource
    private CategoryMapper categoryMapper;

    @Resource
    private ISmsService smsService;

    @Resource
    private ISmsOutService smsOutService;

    @Resource
    private ICollectionBuyOrderService collectionBuyOrderService;

    @Resource
    private ICollectionPeriodExtraBuyChanceService collectionPeriodExtraBuyChanceService;

    @Resource
    private IProducedService producedService;

    @Resource
    private ICollectionRightCompanyService collectionRightCompanyService;

    @Resource
    private ICollectionRightRecordService collectionRightRecordService;

    @Resource
    private ICollectionPeriodWhiteJoinService collectionPeriodWhiteJoinService;

    @Resource
    private ISubscriptionSendService subscriptionSendService;

//    @Resource
//    private IPitService pitService;

    /**
     * 新增作品期数
     *
     * @param req      新增作品期数入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodCreateCopyRightReq req, User operator) {
        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }

        collectionPeriod.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
//        if (!ECollectionRightType.COLLECTION_PAYTYPE_2.getCode().equals(collectionPeriod.getRightType()) && StringUtils
//                .isBlank(collectionPeriod.getRightContent())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益内容");
//        }

        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_0.getCode());
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
            collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        collectionPeriod.setCategory(ECollectionPeriodCategory.COPYRIGHT.getCode());
//        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(req.getPlateCategory());
        int totalQuantity = 0;
        int remainQuantity = 0;
        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            totalQuantity = req.getSellCollectionList().size() + req.getShowCollectionList().size();
        } else {
            totalQuantity = req.getSellCollectionList().size();
        }
        remainQuantity = req.getSellCollectionList().size();

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(remainQuantity);
        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        if (StringUtils.isBlank(req.getWordFlag())) {
            collectionPeriod.setWordFlag(EBoolean.NO.getCode());
        }

        collectionPeriodMapper.insertSelective(collectionPeriod);

        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getSellCollectionList()) {
                // 校验锁仓时间
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }
                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }

                // 判断口令购买
                if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                        !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
                } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                        !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
                }

                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);

                collectionPeriodRelation.setRemainQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getShowCollectionList()) {
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }

                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SHOW.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelation.setRemainQuantity(0);

                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        companyService.checkCompany(companyId);
        collectionPeriod.setAuthorIds(companyId);

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);
    }

    /**
     * 新增版权区产品期数（发行方端）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodCreateCopyRightCompanyReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        //更新优先权数据
        if (null != req.getAdvanceMins() && CollectionUtils.isEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购作品不能为空");
        } else if (null == req.getAdvanceMins() && CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无优先抢购分钟数，不能新增优先购作品");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.COPYRIGHT.getCode());
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());

//
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }

        int totalQuantity = 0;
        int remainQuantity = 0;
        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            totalQuantity = req.getSellCollectionList().size() + req.getShowCollectionList().size();
        } else {
            totalQuantity = req.getSellCollectionList().size();
        }
        remainQuantity = req.getSellCollectionList().size();

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(remainQuantity);
        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        collectionPeriodMapper.insertSelective(collectionPeriod);

        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        String buyType = null;
        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getSellCollectionList()) {
                // 校验锁仓时间
                Collection collection = collectionService.detailSimple(child.getId());

                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }

                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }
                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }

                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                if (null != buyType && !buyType.equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
                }

                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();
                buyType = collection.getBuyType();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);

                collectionPeriodRelation.setRemainQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getShowCollectionList()) {
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }

                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }

                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                if (null != buyType && !buyType.equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();
                buyType = collection.getBuyType();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SHOW.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelation.setRemainQuantity(0);

                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }
        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(buyType) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(buyType) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        }
        companyService.checkCompany(companyId);
        collectionPeriod.setAuthorIds(companyId);

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setPlateCategory(plateCategory);
        collectionPeriod.setWordFlag(buyType);
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    private void checkCollectionPermissions(User operator, Collection collection) {
        collectionService.checkOperation(collection);
        // 校验是否有权限使用该作品
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (!operator.getCompanyId().equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "无权限使用");
            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }
        if ((!ECollectionType.COLLECTION_TYPE_3.getCode().equals(collection.getType()) && !ECollectionType.COLLECTION_TYPE_1.getCode()
                .equals(collection.getType())) || !ECollectionStatus.COLLECTION_STATUS_1.getCode()
                .equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选藏品状态暂不能发售");
        }
    }

    private void checkCollectionPermissionsSaas(User operator, Collection collection) {
        collectionService.checkOperation(collection);
        // 校验是否有权限使用该作品
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (!operator.getCompanyId().equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "无权限使用");
            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }
        if ((!ECollectionType.COLLECTION_TYPE_3.getCode().equals(collection.getType()) && !ECollectionType.COLLECTION_TYPE_1.getCode()
                .equals(collection.getType()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选藏品状态暂不能发售");
        }
    }


    private void checkCollection(Collection collection) {
        if ((!ECollectionType.COLLECTION_TYPE_3.getCode().equals(collection.getType()) && !ECollectionType.COLLECTION_TYPE_1.getCode()
                .equals(collection.getType())) || !ECollectionStatus.COLLECTION_STATUS_1.getCode()
                .equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "所选藏品状态暂不能发售");
        }
    }

    /**
     * 新增衍生区作品期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodCreateDeriveReq req, User operator) {

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setWordFlag(EBoolean.NO.getCode());
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_0.getCode());
        collectionPeriod.setChannelId(1L);
        collectionPeriod.setCategory(ECollectionPeriodCategory.DERIVATIVE.getCode());
        Collection collection = collectionService.detail(req.getCollectionId());

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
        } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
        }

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        init(collection, collectionPeriod);
        // 检查发行方
        companyService.checkCompany(collectionPeriod.getAuthorIds());

        if (collection.getRemainQuantity() < req.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }

        collectionPeriod.setTotalQuantity(req.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }

        collectionPeriodMapper.insertSelective(collectionPeriod);

        //新增藏品关系
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        List<CollectionPeriodPriorityBuyCompanyCreateReq> collectionPeriodPriorityBuyList = req.getCollectionPeriodPriorityBuyList();
        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
            collectionPeriodPriorityBuyList.forEach(item -> {
                item.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
            });
        }
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        List<PeriodDiscountDetailCompanyCreateReq> discountCollectionList = req.getDiscountCollectionList();
        if (CollectionUtils.isNotEmpty(discountCollectionList)) {
            discountCollectionList.forEach(item -> {
                item.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
            });
        }
        periodDiscountDetailService.create(collectionPeriod, discountCollectionList, operator);

        //更新空投
        dropCreate(collectionPeriod, req.getCollectionPeriodDropList());
    }

    /**
     * 新增衍生区产品期数（发行方端）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodCreateDeriveCompanyReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.DERIVATIVE.getCode());
        Collection collection = collectionService.detail(req.getCollectionId());
        collectionPeriod.setPrice(collection.getPrice());

        // 检查板块是否一致
        if (!collectionPeriod.getPlateCategory().equals(collection.getPlateCategory())) {
            ECollectionPeriodPlateCategory plateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collectionPeriod.getPlateCategory());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不属于" + plateCategory.getValue() + "板块");
        }

        // 判断口令购买
        if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType()) && CollectionUtils
                .isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不能使用口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())
                && CollectionUtils
                .isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "未设置口令");
        }
        collectionPeriod.setWordFlag(collection.getBuyType());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        init(collection, collectionPeriod);

        if (collection.getRemainQuantity() < req.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }

        collectionPeriod.setTotalQuantity(req.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        try {
            Date date = DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2);
            if (null == date) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能为空");
            }
            collectionPeriod.setStartSellDate(date);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间错误");
        }
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        collectionPeriodMapper.insertSelective(collectionPeriod);

        //新增藏品关系
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        if (null != req.getAdvanceMins() && CollectionUtils.isEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购作品不能为空");
        } else if (null == req.getAdvanceMins() && CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无优先抢购分钟数，不能新增优先购作品");
        }

        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService
                .create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    /**
     * 新增盲盒区产品期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBlindBoxPeriod(CollectionPeriodCreateBlindBoxReq req, User operator) {

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        // 判断是否拥有该渠道的发布权限
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_0.getCode());
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
            collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }
        collectionPeriod.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
//        if (!ECollectionRightType.COLLECTION_PAYTYPE_2.getCode().equals(collectionPeriod.getRightType()) && StringUtils
//                .isBlank(collectionPeriod.getRightContent())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益内容");
//        }

        collectionPeriod.setCategory(ECollectionPeriodCategory.BLINDBOX.getCode());

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {
            // 校验锁仓时间
            Collection collection = collectionService.detailSimple(child.getId());

            if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
            }
            if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
            }
            if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
            }
            if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
            }

            // 判断口令购买
            // 判断口令购买
            if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                    !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
            } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                    !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
            }

            // 校验是否有权限使用该作品
            checkCollectionPermissions(operator, collection);

            if (collection.getRemainQuantity() < child.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "剩余数量不足");
            }
            companyId = collection.getAuthorId();
            lockTime = collection.getLockTime();
            transformLimitTime = collection.getTransformLimitTime();
            plateCategory = collection.getPlateCategory();

            totalQuantity = totalQuantity + child.getQuantity();
        }

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setAuthorIds(companyId);
        // 检查发行方
        companyService.checkCompany(companyId);
        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(totalQuantity);

        collectionPeriodMapper.insertSelective(collectionPeriod);

        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(child.getQuantity());

                collectionPeriodRelation.setRemainQuantity(child.getQuantity());
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);
    }

    /**
     * 新增盲盒区产品期数(发行方端)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createBlindBoxPeriod(CollectionPeriodCreateBlindBoxCompanyReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        //更新优先权数据
        if (null != req.getAdvanceMins() && CollectionUtils.isEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购作品不能为空");
        } else if (null == req.getAdvanceMins() && CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无优先抢购分钟数，不能新增优先购作品");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.BLINDBOX.getCode());
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        String buyType = null;
        for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {
            // 校验锁仓时间
            Collection collection = collectionService.detailSimple(child.getId());

            if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
            }
            if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
            }
            if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
            }
            if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
            }

            if (null != buyType && !buyType.equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
            }
            // 校验是否有权限使用该作品
            checkCollectionPermissions(operator, collection);

            if (collection.getRemainQuantity() < child.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "剩余数量不足");
            }
            companyId = collection.getAuthorId();
            lockTime = collection.getLockTime();
            transformLimitTime = collection.getTransformLimitTime();
            plateCategory = collection.getPlateCategory();
            buyType = collection.getBuyType();

            totalQuantity = totalQuantity + child.getQuantity();
        }

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(buyType) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(buyType) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        }

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setAuthorIds(companyId);
        collectionPeriod.setPlateCategory(plateCategory);
        collectionPeriod.setWordFlag(buyType);
        // 检查发行方
        companyService.checkCompany(companyId);
        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(totalQuantity);

        collectionPeriodMapper.insertSelective(collectionPeriod);

        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(child.getQuantity());

                collectionPeriodRelation.setRemainQuantity(child.getQuantity());
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    /**
     * 新增抽签区期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createDrawStrawsPeriod(CollectionPeriodCreateDrawStrawsReq req, User operator) {

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        // 判断是否拥有该渠道的发布权限
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_0.getCode());
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
            collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        collectionPeriod.setCategory(ECollectionPeriodCategory.DrawStraws.getCode());
        Collection collection = collectionService.detail(req.getCollectionId());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        if (CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList()) && (null == req.getPriorityDrawParam() || null == req
                .getPriorityNumber())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购参数不能为空");
        }

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }

        if (collectionPeriod.getEndSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "结束时间不能小于当前时间");
        }
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
        collectionPeriod.setEndSellDate(DateUtil.strToDate(req.getEndSellDate(), DateUtil.DATA_TIME_PATTERN_3));
        if (collectionPeriod.getStartSellDate().after(collectionPeriod.getEndSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能晚于结束时间");
        }

        if (collection.getRemainQuantity() < req.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }
        init(collection, collectionPeriod);
        // 检查发行方
        companyService.checkCompany(collectionPeriod.getAuthorIds());
        collectionPeriod.setPriorityAddQuantityFlag(EBoolean.NO.getCode());
        collectionPeriod.setTotalQuantity(req.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriodMapper.insertSelective(collectionPeriod);

        //新增藏品关系
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        collectionPeriodPriorityBuyService.createDrawStraws(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        //更新白名单用户
        collectionPeriodWhiteJoinService.create(collectionPeriod, req.getWhiteJoinList(), operator);
    }

    @Override
    public void init(Collection collection, CollectionPeriod collectionPeriod) {
        //拷贝藏品的信息
        collectionPeriod.setName(collection.getName());
        collectionPeriod.setCoverFileUrl(collection.getCoverFileUrl());
        collectionPeriod.setPlateCategory(collection.getPlateCategory());
        collectionPeriod.setFileType(collection.getFileType());
        collectionPeriod.setTags(collection.getTags());
        collectionPeriod.setContent(collection.getContent());
        collectionPeriod.setAuthorIds(collection.getAuthorId());
        collectionPeriod.setRightType(collection.getRightType());
        collectionPeriod.setRightContent(collection.getRightContent());
        collectionPeriod.setLockTime(collection.getLockTime());
        collectionPeriod.setTransformLimitTime(collection.getTransformLimitTime());

    }

    /**
     * 修改版权藏品期数
     *
     * @param req      修改作品期数入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodModifyCopyRightReq req, User operator) {
        CollectionPeriod period = detail(req.getId());

        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数已上架，不能修改");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
//        if (!ECollectionRightType.COLLECTION_PAYTYPE_2.getCode().equals(collectionPeriod.getRightType()) && StringUtils
//                .isBlank(collectionPeriod.getRightContent())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益内容");
//        }

        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        int totalQuantity = 0;
        int remainQuantity = 0;
        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            totalQuantity = req.getSellCollectionList().size() + req.getShowCollectionList().size();
        } else {
            totalQuantity = req.getSellCollectionList().size();
        }
        remainQuantity = req.getSellCollectionList().size();

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(remainQuantity);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());

        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getSellCollectionList()) {

                // 校验锁仓时间
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }

                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }
                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }
                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                // 判断口令购买
                if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                        !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
                } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                        !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);

                collectionPeriodRelation.setRemainQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getShowCollectionList()) {
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }

                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }
                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SHOW.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelation.setRemainQuantity(0);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        companyService.detail(companyId);
        collectionPeriod.setAuthorIds(companyId);
        // 检查发行方
        companyService.checkCompany(companyId);
        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);
    }

    /**
     * 修改版权区产品期数（发行方端）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodModifyCopyRightCompanyReq req, User operator) {
        CollectionPeriod period = detail(req.getId());

        if (!period.getAuthorIds().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数已上架，不能修改");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }

        int totalQuantity = 0;
        int remainQuantity = 0;
        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            totalQuantity = req.getSellCollectionList().size() + req.getShowCollectionList().size();
        } else {
            totalQuantity = req.getSellCollectionList().size();
        }
        remainQuantity = req.getSellCollectionList().size();

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(remainQuantity);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());

        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        String buyType = null;
        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getSellCollectionList()) {

                // 校验锁仓时间
                Collection collection = collectionService.detailSimple(child.getId());

                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }

                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }

                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }
                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }

                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                if (null != buyType && !buyType.equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();
                buyType = collection.getBuyType();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);

                collectionPeriodRelation.setRemainQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        if (CollectionUtils.isNotEmpty(req.getShowCollectionList())) {
            for (CollectionPeriodCreateCopyRightCReq child : req.getShowCollectionList()) {
                Collection collection = collectionService.detailSimple(child.getId());
                if (collection.getRemainQuantity() < 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
                }
                if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
                }
                if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
                }

                if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
                }
                if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
                }

                if (null != buyType && !buyType.equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
                }

                // 校验是否有权限使用该作品
                checkCollectionPermissions(operator, collection);

                companyId = collection.getAuthorId();
                lockTime = collection.getLockTime();
                transformLimitTime = collection.getTransformLimitTime();
                plateCategory = collection.getPlateCategory();
                buyType = collection.getBuyType();

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SHOW.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(COPYRIGHT_QUANTITY);
                collectionPeriodRelation.setRemainQuantity(0);
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }
        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(buyType) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(buyType) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        }
        // 检查发行方
        companyService.checkCompany(companyId);
        collectionPeriod.setAuthorIds(companyId);

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setPlateCategory(plateCategory);
        collectionPeriod.setWordFlag(buyType);
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    /**
     * 修改衍生区作品期数
     *
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodModifyDeriveReq req, User operator) {
        CollectionPeriod period = detail(req.getId());

        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        Collection collection = collectionService.detail(req.getCollectionId());

        // 判断口令购买
        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
        } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
        }

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        init(collection, collectionPeriod);
        // 检查发行方
        companyService.checkCompany(collectionPeriod.getAuthorIds());
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }

        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());

        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);


        //更新优先权数据
        List<CollectionPeriodPriorityBuyCompanyCreateReq> collectionPeriodPriorityBuyList = req.getCollectionPeriodPriorityBuyList();
        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
            collectionPeriodPriorityBuyList.forEach(item -> {
                item.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
            });
        }
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        List<PeriodDiscountDetailCompanyCreateReq> discountCollectionList = req.getDiscountCollectionList();
        if (CollectionUtils.isNotEmpty(discountCollectionList)) {
            discountCollectionList.forEach(item -> {
                item.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
            });
        }
        periodDiscountDetailService.create(collectionPeriod, discountCollectionList, operator);

        //更新空投
        dropCreate(collectionPeriod, req.getCollectionPeriodDropList());
    }

    /**
     * 修改衍生区产品期数（发行方端）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodModifyDeriveCompanyReq req, User operator) {

        CollectionPeriod period = detailsimple(req.getId());

        if (!ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数不属于衍生区");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        Collection collection = collectionService.detail(req.getCollectionId());

        // 检查板块是否一致
        if (!collectionPeriod.getPlateCategory().equals(collection.getPlateCategory())) {
            ECollectionPeriodPlateCategory plateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collectionPeriod.getPlateCategory());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不属于" + plateCategory.getValue() + "板块");
        }

        // 判断口令购买
        if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType()) && CollectionUtils
                .isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不能使用口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())
                && CollectionUtils
                .isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "未设置口令");
        }

        collectionPeriod.setWordFlag(collection.getBuyType());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        init(collection, collectionPeriod);

        if (collection.getRemainQuantity() < req.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }

        collectionPeriod.setTotalQuantity(req.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        // 发布时间需提前
//        Integer earlyHour = configService.getIntegerValue(SysConstants.PUSH_PERIOD_NEED_EARLY_HOUR);
//        Calendar calendar = Calendar.getInstance();
//        calendar.add(Calendar.HOUR_OF_DAY, earlyHour);
        collectionPeriod.setStartSellDate(DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
//        if (calendar.getTime().before(collectionPeriod.getStartSellDate())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发布期数必须提前" + earlyHour + "小时");
//        }
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间早于当前时间");
        }
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //新增藏品关系
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        if (null != req.getAdvanceMins() && 0 < req.getAdvanceMins() && CollectionUtils.isEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购作品不能为空");
        } else if (null == req.getAdvanceMins() && CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无优先抢购分钟数，不能新增优先购作品");
        }

        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService
                .create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    /**
     * 发行方提交期数发布申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodSubmitApplyFor(BaseIdReq request, User operator) {
        CollectionPeriod collectionPeriod = detailsimple(request.getId());

        Company company = companyService.detailSimple(collectionPeriod.getAuthorIds());
        if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "申请期数状态不正确");
        }

        Integer daySize = configService.getIntegerValue(SysConstants.TREASURE_PLAN_DAY_SIZE);

        Integer count = collectionPeriodMapper.selectApplyForAndToApplyFor(collectionPeriod);
        if (count >= daySize) {
            String dateToStr = DateUtil.dateToStr(collectionPeriod.getStartSellDate(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), dateToStr + "当天发售安排已满，请重新选择其他日前");
        }

//         发布时间需提前
        Integer earlyHour = configService.getIntegerValue(SysConstants.PUSH_PERIOD_NEED_EARLY_HOUR);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, earlyHour);

        if (calendar.getTime().after(collectionPeriod.getStartSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发布期数必须提前" + earlyHour + "小时");
        }

        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode());
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodSubmitApplyForCompany(BaseIdReq request, User operator) {
        CollectionPeriod collectionPeriod = detailForUpdate(request.getId());

        if (!collectionPeriod.getAuthorIds().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能对应发行方才能发起");
        }

        Company company = companyService.detailSimple(collectionPeriod.getAuthorIds());
        if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "申请期数状态不正确");
        }

        Integer daySize = configService.getIntegerValue(SysConstants.TREASURE_PLAN_DAY_SIZE);

        Integer count = collectionPeriodMapper.selectApplyForAndToApplyFor(collectionPeriod);
        if (count >= daySize) {
            String dateToStr = DateUtil.dateToStr(collectionPeriod.getStartSellDate(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_9);
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), dateToStr + "当天发售安排已满，请重新选择其他日前");
        }

//         发布时间需提前
        Integer earlyHour = configService.getIntegerValue(SysConstants.PUSH_PERIOD_NEED_EARLY_HOUR);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, earlyHour);

        if (calendar.getTime().after(collectionPeriod.getStartSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发布期数必须提前" + earlyHour + "小时");
        }

        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode());
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        // 作品提交审核
        CollectionPeriodRelation relation = new CollectionPeriodRelation();
        relation.setPeriodId(collectionPeriod.getId());
        List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

        for (CollectionPeriodRelation periodRelation : list) {
            Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
            if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(collection.getStatus())
                    && !ECollectionStatus.COLLECTION_STATUS_9.getCode().equals(collection.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数藏品" + collection.getName() + "状态不正确");
            }

            Collection collectionModify = new Collection();
            collectionModify.setId(collection.getId());
            collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_8.getCode());
            collectionModify.setUpdater(operator.getUpdater());
            collectionModify.setUpdaterName(operator.getUpdaterName());
            collectionModify.setUpdateDatetime(new Date());
            collectionService.modify(collectionModify);
        }

        // 权益次数减少
        CollectionRightRecord rightRecord = new CollectionRightRecord();
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> rightRecordList = collectionRightRecordService.list(rightRecord);
        for (CollectionRightRecord record : rightRecordList) {
            CollectionRightCompany rightCompany = collectionRightCompanyService.detailForUpdate(record.getRightCompanyId());
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detailForUpdate(record.getRightId());

            if (!ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode().equals(rightCompany.getStatus())) {
                Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                        collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                .getValue() + "权益未开启");
            }

            if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(rightsDetail.getNumberFlag())) {
                if (1 > rightsDetail.getRemainNumber()) {
                    Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                            collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                    .getValue() + "权益已用完");
                }
                rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() - 1);
            }

            rightCompany.setNumber(rightCompany.getNumber() + 1);

            collectionRightCompanyService.modify(rightCompany);
            collectionRightsDetailService.modify(rightsDetail);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodUndoApplyForCompany(BaseIdReq request, User operator) {
        CollectionPeriod collectionPeriod = detailForUpdate(request.getId());

        if (!collectionPeriod.getAuthorIds().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只能对应发行方才能发起");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不在审核中");
        }

        CollectionPeriod period = new CollectionPeriod();
        period.setId(collectionPeriod.getId());
        period.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        period.setUpdater(operator.getId());
        period.setUpdaterName(operator.getLoginName());
        period.setUpdateDatetime(new Date());
        collectionPeriodMapper.updateByPrimaryKeySelective(period);

        // 作品审核失败
        CollectionPeriodRelation relation = new CollectionPeriodRelation();
        relation.setPeriodId(collectionPeriod.getId());
        List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

        for (CollectionPeriodRelation periodRelation : list) {
            Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
            if (!ECollectionStatus.COLLECTION_STATUS_8.getCode().equals(collection.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数藏品" + collection.getName() + "不是待审核状态");
            }

            Collection collectionModify = new Collection();
            collectionModify.setId(collection.getId());
            collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
            collectionModify.setUpdater(operator.getUpdater());
            collectionModify.setUpdaterName(operator.getUpdaterName());
            collectionModify.setUpdateDatetime(new Date());
            collectionService.modify(collectionModify);
        }

        // 权益次数回收
        rightAdd(collectionPeriod);
    }

    /**
     * 期数审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodAudit(CollectionPeriodAuditReq request, User operator) {

        List<CollectionPeriod> periodList = new ArrayList<>();
        for (Long id : request.getIdList()) {
            CollectionPeriod collectionPeriod = detailsimple(id);
            periodList.add(collectionPeriod);
            if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode().equals(collectionPeriod.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionPeriod.getName() + "申请期数状态不正确");
            }

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
                // 检查发行方
                companyService.checkCompany(collectionPeriod.getAuthorIds());
                Company company = companyService.detailSimple(collectionPeriod.getAuthorIds());
                if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
                }
//                CollectionPeriod period =new CollectionPeriod();
//                period.setId(collectionPeriod.getId());
//                period.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
//                period.setUpdater(operator.getId());
//                period.setUpdaterName(operator.getLoginName());
//                period.setUpdateDatetime(new Date());
//                collectionPeriodMapper.updateByPrimaryKeySelective(period);
            } else if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(request.getStatus())) {

                ApproveRecord approveRecord = new ApproveRecord();
                approveRecord.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_4.getCode());
                approveRecord.setRefId(id);
                approveRecord.setCreater(operator.getId());
                approveRecord.setCreaterName(operator.getLoginName());
                approveRecord.setCreateDatetime(new Date());

                if (StringUtils.isBlank(request.getOpinion())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
                }
                approveRecord.setOpinion(request.getOpinion());
                approveRecord.setHistoryData(JSON.toJSONString(collectionPeriod));
                approveRecordService.create(approveRecord);

                CollectionPeriod period = new CollectionPeriod();
                period.setId(collectionPeriod.getId());
                period.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode());
                period.setUpdater(operator.getId());
                period.setUpdaterName(operator.getLoginName());
                period.setUpdateDatetime(new Date());
                collectionPeriodMapper.updateByPrimaryKeySelective(period);

                // 审核不通过发消息给发行方
                if (ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode().equals(collectionPeriod.getCreateType())) {
                    String title = "期数审核未通过";
                    String content = "您发起的" + collectionPeriod.getName() + "期数新增申请已被打回,原因：" + request.getOpinion();

                    smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), collectionPeriod.getAuthorIds(), title, content,
                            ESmsRefType.COMPANY_SMS.getCode(),
                            ESmsRefType.COMPANY_SMS.getValue());
                }
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核状态入参错误");
            }
        }

        // 审核通过直接上架
        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
            CollectionPeriodBatchUpAndDownReq req = new CollectionPeriodBatchUpAndDownReq();
            req.setIdList(request.getIdList());
            req.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());

            upAndDown(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode(), operator, periodList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodAuditCompany(CollectionPeriodAuditCompanyReq request, User operator) {

        CollectionPeriod collectionPeriod = detailsimple(request.getId());
        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionPeriod.getName() + "申请期数状态不正确");
        }

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
            // 检查发行方
            companyService.checkCompany(collectionPeriod.getAuthorIds());
            Company company = companyService.detailSimple(collectionPeriod.getAuthorIds());
            if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
            }
        } else if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(request.getStatus())) {

            ApproveRecord approveRecord = new ApproveRecord();
            approveRecord.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_4.getCode());
            approveRecord.setRefId(request.getId());
            approveRecord.setCreater(operator.getId());
            approveRecord.setCreaterName(operator.getLoginName());
            approveRecord.setCreateDatetime(new Date());

            if (StringUtils.isBlank(request.getOpinion())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
            }
            approveRecord.setOpinion(request.getOpinion());
            approveRecord.setHistoryData(JSON.toJSONString(collectionPeriod));
            approveRecordService.create(approveRecord);

            CollectionPeriod period = new CollectionPeriod();
            period.setId(collectionPeriod.getId());
            period.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode());
            period.setUpdater(operator.getId());
            period.setUpdaterName(operator.getLoginName());
            period.setUpdateDatetime(new Date());
            collectionPeriodMapper.updateByPrimaryKeySelective(period);

            // 作品审核失败
            CollectionPeriodRelation relation = new CollectionPeriodRelation();
            relation.setPeriodId(collectionPeriod.getId());
            List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

            for (CollectionPeriodRelation periodRelation : list) {
                Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
                if (!ECollectionStatus.COLLECTION_STATUS_8.getCode().equals(collection.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数藏品" + collection.getName() + "不是待审核状态");
                }

                Collection collectionModify = new Collection();
                collectionModify.setId(collection.getId());
                collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_9.getCode());
                collectionModify.setUpdater(operator.getUpdater());
                collectionModify.setUpdaterName(operator.getUpdaterName());
                collectionModify.setUpdateDatetime(new Date());
                collectionService.modify(collectionModify);

                ApproveRecord approve = new ApproveRecord();
                approve.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_1.getCode());
                approve.setRefId(collection.getId());
                approve.setCreater(operator.getId());
                approve.setCreaterName(operator.getLoginName());
                approve.setCreateDatetime(new Date());

                if (StringUtils.isBlank(request.getOpinion())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
                }
                approve.setOpinion(request.getOpinion());
                approve.setHistoryData(collection.toString());
                approveRecordService.create(approve);
            }

            // 权益次数增加
            rightAdd(collectionPeriod);

        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核状态入参错误");
        }

        // 审核通过直接上架
        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {

            // 先上架作品
            // 作品提交审核
            CollectionPeriodRelation relation = new CollectionPeriodRelation();
            relation.setPeriodId(collectionPeriod.getId());
            List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

            for (CollectionPeriodRelation periodRelation : list) {
                Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
                CollectionOssAuditReq req = new CollectionOssAuditReq();
//                req.setContractId(request.getContractId());
//                req.setPlatDivideRate(request.getPlatDivideRate());
//                    req.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
//                req.setDivideAuthorId(collection.getDivideAuthorId());

                // 更新权益(元宇宙入场券)
                CollectionRightsDetail rightsDetail = new CollectionRightsDetail();
                rightsDetail.setCollectionId(collection.getId());
                rightsDetail.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
                List<CollectionRightsDetail> rightsDetails = collectionRightsDetailService.list(rightsDetail);
                if (CollectionUtils.isNotEmpty(rightsDetails)) {
                    collection.setU3dFlag(EBoolean.YES.getCode());
                }
                collection.setUseFlag(EBoolean.YES.getCode());
                collectionService.auditOn(req, operator, collection);

                // 空投
                CollectionRightRecord rightRecord = new CollectionRightRecord();
                rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
                rightRecord.setRefId(collectionPeriod.getId());
                List<CollectionRightRecord> rightRecords = collectionRightRecordService.list(rightRecord);
                for (CollectionRightRecord record : rightRecords) {
                    CollectionDropUserCollectionReq dropReq = new CollectionDropUserCollectionReq();
                    dropReq.setCollectionId(collection.getId());
                    dropReq.setNeedCollectionId(record.getCollectionId());
                    dropReq.setQuantity(1);
                    User user = userService.detailBrief(collection.getAuthorId());
                    collectionService
                            .dropByUserCollection(dropReq, user, ECollectionRightDropType.E_COLLECTION_RIGHT_DROP_TYPE_0.getCode());
                }

            }

//            CollectionPeriodBatchUpAndDownReq req = new CollectionPeriodBatchUpAndDownReq();
//            req.setIdList(request.getIdList());
//            req.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
            List<CollectionPeriod> periodList = new ArrayList<>();
            periodList.add(collectionPeriod);

            upAndDown(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode(), operator, periodList);
        }

        // 审核不通过发消息给发行方
        if (ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode().equals(collectionPeriod.getCreateType())) {
            String title = null;
            String content = null;

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
                title = "期数审核通过";
                content = "您发起的" + collectionPeriod.getName() + "期数新增申请已通过";
            } else {
                title = "期数审核未通过";
                content = "您发起的" + collectionPeriod.getName() + "期数新增申请已被打回,原因：" + request.getOpinion();
            }
            smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), collectionPeriod.getAuthorIds(), title, content,
                    ESmsRefType.COMPANY_SMS.getCode(),
                    ESmsRefType.COMPANY_SMS.getValue());

            User user = userService.detailBrief(collectionPeriod.getAuthorIds());
            smsOutService.sendSmsOut(user.getMobile(), content, null);
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void periodAuditCompanyOss(CollectionPeriodAuditCompanyOssReq request, User operator) {

        CollectionPeriod collectionPeriod = detailForUpdate(request.getId());

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionPeriod.getName() + "申请期数状态不正确");
        }

        if (!ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory()) && CollectionUtils
                .isNotEmpty(request.getShowModifyCollectionInfoList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有版权区才需要隐藏款藏品");
        }

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
            // 检查发行方
            companyService.checkCompany(collectionPeriod.getAuthorIds());
            Company company = companyService.detailSimple(collectionPeriod.getAuthorIds());
            if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
            }
        } else if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(request.getStatus())) {

            ApproveRecord approveRecord = new ApproveRecord();
            approveRecord.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_4.getCode());
            approveRecord.setRefId(request.getId());
            approveRecord.setCreater(operator.getId());
            approveRecord.setCreaterName(operator.getLoginName());
            approveRecord.setCreateDatetime(new Date());

            if (StringUtils.isBlank(request.getOpinion())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
            }
            approveRecord.setOpinion(request.getOpinion());
            approveRecord.setHistoryData(JSON.toJSONString(collectionPeriod));
            approveRecordService.create(approveRecord);

            CollectionPeriod period = new CollectionPeriod();
            period.setId(collectionPeriod.getId());
            period.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode());
            period.setUpdater(operator.getId());
            period.setUpdaterName(operator.getLoginName());
            period.setUpdateDatetime(new Date());
            collectionPeriodMapper.updateByPrimaryKeySelective(period);

            // 作品审核失败
            CollectionPeriodRelation relation = new CollectionPeriodRelation();
            relation.setPeriodId(collectionPeriod.getId());
            List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

            for (CollectionPeriodRelation periodRelation : list) {
                Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
                if (!ECollectionStatus.COLLECTION_STATUS_8.getCode().equals(collection.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数藏品" + collection.getName() + "不是待审核状态");
                }

                Collection collectionModify = new Collection();
                collectionModify.setId(collection.getId());
                collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_9.getCode());
                collectionModify.setUpdater(operator.getUpdater());
                collectionModify.setUpdaterName(operator.getUpdaterName());
                collectionModify.setUpdateDatetime(new Date());
                collectionService.modify(collectionModify);

                ApproveRecord approve = new ApproveRecord();
                approve.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_1.getCode());
                approve.setRefId(collection.getId());
                approve.setCreater(operator.getId());
                approve.setCreaterName(operator.getLoginName());
                approve.setCreateDatetime(new Date());

                if (StringUtils.isBlank(request.getOpinion())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
                }
                approve.setOpinion(request.getOpinion());
                approve.setHistoryData(collection.toString());
                approveRecordService.create(approve);

            }

        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核状态入参错误");
        }

        // 权益次数回收
        rightAdd(collectionPeriod);

        CollectionPeriod period = EntityUtils.copyData(request, CollectionPeriod.class);
        period.setId(collectionPeriod.getId());
        period.setPrice(request.getPrice());
        period.setPlateCategory(collectionPeriod.getPlateCategory());
        period.setCategory(collectionPeriod.getCategory());
        period.setChannelId(collectionPeriod.getChannelId());
        period.setAuthorIds(collectionPeriod.getAuthorIds());
        // 审核通过直接上架
        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {

            period.setMetaReleaseFlag(EBoolean.NO.getCode());
//            if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())
//                    && StringUtils.isNotBlank(request.getMetaReleaseFlag())
//                    && EBoolean.YES.getCode().equals(request.getMetaReleaseFlag())) {
//                Pit pit = pitService.detailForUpdate(request.getPitId());
//                if (!EPitType.E_PIT_TYPE_1.getCode().equals(pit.getType())) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择正确的元宇宙坑位");
//                }
//
//                if (!EPitStatus.PIT_STATUS_0.getCode().equals(pit.getStatus())) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择未使用的元宇宙坑位");
//                }
//                pit.setPeriodId(collectionPeriod.getId());
//                pit.setStatus(EPitStatus.PIT_STATUS_1.getCode());
//                pitService.modify(pit);
//                period.setMetaReleaseFlag(request.getMetaReleaseFlag());
//                period.setPitId(request.getPitId());
//            }

            // 修改作品信息
            // 先将老的对应的作品状态改为待发布
            // 删除老的对应关系
            CollectionPeriodRelation relation = new CollectionPeriodRelation();
            relation.setPeriodId(collectionPeriod.getId());
            List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);
            for (CollectionPeriodRelation periodRelation : list) {
                Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
                Collection collectionModify = new Collection();
                collectionModify.setId(collection.getId());
                collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
                collectionService.modify(collectionModify);
            }
            //删除之前的藏品关系，再新增
            collectionPeriodRelationMapper.deleteByPeriod(period.getId());

            List<Collection> collectionList = new ArrayList<>();
            Integer totalQuantity = 0;
            Integer remainQuantity = 0;

            // 校验输入的作品的锁仓时间是否都一致
            CollectionCreateCheck collectionCreateCheck = new CollectionCreateCheck();
            collectionCreateCheck.setLockTime(0);
            collectionCreateCheck.setTransformLimitTime(0);

            for (CollectionModifyPlatformAuditReq platformReq : request.getSellModifyCollectionList()) {
                platformReq.setCategory(ECollectionCategory.DERIVATIVE.getCode());
                if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
                    platformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
                }
                // 生成作品
                Integer quantity = getQuantity(operator, period, collectionList, collectionCreateCheck, platformReq,
                        request.getPlatDivideRate(), request.getContractId(), ECollectionPeriodRelationCategory.SELL.getCode());
                totalQuantity = quantity + totalQuantity;
                remainQuantity = quantity + remainQuantity;
            }
            period.setCollectionNumber(request.getSellModifyCollectionList().size());
            if (null != request.getShowModifyCollectionInfoList()) {
                for (CollectionModifyPlatformAuditReq platformReq : request.getShowModifyCollectionInfoList()) {
                    platformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
                    // 生成作品
                    Integer quantity = getQuantity(operator, period, collectionList, collectionCreateCheck, platformReq,
                            request.getPlatDivideRate(), request.getContractId(), ECollectionPeriodRelationCategory.SHOW.getCode());
                    totalQuantity = quantity + totalQuantity;
                }
                period.setCollectionNumber(period.getCollectionNumber() + request.getShowModifyCollectionInfoList().size());
            }

            ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(collectionCreateCheck.getPlateCategory());
            companyService.checkCompany(collectionCreateCheck.getCompanyId());
            if (!collectionCreateCheck.getCompanyId().equals(collectionPeriod.getAuthorIds())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数和作品发行方不匹配");
            }

            // 判断口令购买
            if (EBoolean.NO.getCode().equals(collectionCreateCheck.getBuyType()) &&
                    CollectionUtils.isNotEmpty(request.getPeriodChannelWordList())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
            } else if (EBoolean.YES.getCode().equals(collectionCreateCheck.getBuyType()) &&
                    CollectionUtils.isEmpty(request.getPeriodChannelWordList())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
            }

            period.setTotalQuantity(totalQuantity);
            period.setRemainQuantity(remainQuantity);
            period.setLockTime(collectionCreateCheck.getLockTime());
            period.setTransformLimitTime(collectionCreateCheck.getTransformLimitTime());
            period.setPlateCategory(collectionCreateCheck.getPlateCategory());
            period.setWordFlag(collectionCreateCheck.getBuyType());

            period.setUpdater(operator.getId());
            period.setUpdaterName(operator.getLoginName());
            period.setUpdateDatetime(new Date());

            //初始化售卖时间
            initSellDate(request.getStartSellDate(), request.getEndSellDate(), period);

            //更新优先权数据
            collectionPeriodPriorityBuyService.create(period, request.getCollectionPeriodPriorityBuyList(), operator);

            //更新折扣
            periodDiscountDetailService.create(period, request.getDiscountCollectionList(), operator);

            // 口令
            periodChannelWordService.create(period.getId(), request.getPeriodChannelWordList(), operator);

            if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
                //更新空投
                dropCreate(period, request.getCollectionPeriodDropList());
            }

            if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())) {
                Collection collection = collectionList.get(0);
                init(collection, period);
                // 空投
                CollectionRightRecord rightRecord = new CollectionRightRecord();
                rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
                rightRecord.setRefId(collectionPeriod.getId());
                List<CollectionRightRecord> rightRecords = collectionRightRecordService.list(rightRecord);
                for (CollectionRightRecord record : rightRecords) {
                    CollectionDropUserCollectionReq dropReq = new CollectionDropUserCollectionReq();
                    dropReq.setCollectionId(collection.getId());
                    dropReq.setNeedCollectionId(record.getCollectionId());
                    dropReq.setQuantity(1);
                    User user = userService.detailBrief(collection.getAuthorId());
                    collectionService
                            .dropByUserCollection(dropReq, user, ECollectionRightDropType.E_COLLECTION_RIGHT_DROP_TYPE_0.getCode());
                }
            } else if (CollectionUtils.isNotEmpty(request.getTagList())) {
                String newStr = request.getTagList().stream().collect(Collectors.joining(","));
                period.setTags(newStr);
            }

            collectionPeriodMapper.updateByPrimaryKeySelective(period);

            List<CollectionPeriod> periodList = new ArrayList<>();
            periodList.add(period);

            upAndDown(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode(), operator, periodList);

            // 权益次数减少
            rightSubtract(collectionPeriod);
        }

        // 审核完发消息给发行方
        if (ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode().equals(collectionPeriod.getCreateType())) {
            String title = null;
            String content = null;

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())) {
                title = "期数审核通过";
                content = "您发起的" + period.getName() + "期数新增申请已通过";
            } else {
                title = "期数审核未通过";
                content = "您发起的" + collectionPeriod.getName() + "期数新增申请已被打回,原因：" + request.getOpinion();
            }
            smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), collectionPeriod.getAuthorIds(), title, content,
                    ESmsRefType.COMPANY_SMS.getCode(),
                    ESmsRefType.COMPANY_SMS.getValue());

            User user = userService.detailBrief(collectionPeriod.getAuthorIds());
            smsOutService.sendSmsOut(user.getMobile(), content, null);
        }


    }

    private void rightSubtract(CollectionPeriod collectionPeriod) {
        // 权益次数减少
        CollectionRightRecord rightRecord = new CollectionRightRecord();
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> rightRecordList = collectionRightRecordService.list(rightRecord);
        for (CollectionRightRecord record : rightRecordList) {
            CollectionRightCompany rightCompany = collectionRightCompanyService.detailForUpdate(record.getRightCompanyId());
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detailForUpdate(record.getRightId());

            if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(rightsDetail.getNumberFlag())) {
                rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() - 1);
            }

            rightCompany.setNumber(rightCompany.getNumber() + 1);

            collectionRightCompanyService.modify(rightCompany);
            collectionRightsDetailService.modify(rightsDetail);
        }
    }

    private void rightAdd(CollectionPeriod collectionPeriod) {
        // 权益次数增加
        CollectionRightRecord rightRecord = new CollectionRightRecord();
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> rightRecordList = collectionRightRecordService.list(rightRecord);
        for (CollectionRightRecord record : rightRecordList) {
            CollectionRightCompany rightCompany = collectionRightCompanyService.detailForUpdate(record.getRightCompanyId());
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detailForUpdate(record.getRightId());

            if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(rightsDetail.getNumberFlag())) {
                rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() + 1);
            }

            rightCompany.setNumber(rightCompany.getNumber() - 1);

            collectionRightCompanyService.modify(rightCompany);
            collectionRightsDetailService.modify(rightsDetail);
        }
    }

    private Integer getQuantity(User operator, CollectionPeriod collectionPeriod, List<Collection> collectionList,
                                CollectionCreateCheck collectionCreateCheck, CollectionModifyPlatformAuditReq platformReq, BigDecimal platDivideRate,
                                Long contractId, String periodRelationCategory) {
        // 分账不能都给发行方需要留点给平台用于支付手续费
        BigDecimal divideMinRate = configService.getBigDecimalValue(SysConstants.YEEPAY_DIVIDE_MIN_RATE);
        if (platDivideRate.compareTo(divideMinRate) < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "平台分成比例不能小于" + divideMinRate + "，用于支付手续费");
        }

        // 生成作品
        Collection collection = collectionService
                .modifyCollectionPlatPlatform(platformReq, platDivideRate, contractId, operator, collectionPeriod.getCategory());
        if (null == platformReq.getQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写发行数量");
        }
        Integer quantity = platformReq.getQuantity();

        // 校验锁仓时间、转增限制时间等以及生成关联记录
        checkCollection(operator, collectionPeriod, collectionCreateCheck, platformReq.getOrderNo(), collection,
                periodRelationCategory, platformReq.getQuantity());

        if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "非藏品作品不能发售");
        }

        if (collection.getRemainQuantity() < quantity) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "发售数量大于总数量");
        }
        collectionList.add(collection);
        return quantity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodOrderNoReq req, User operator) {
        CollectionPeriod period = detail(req.getId());

        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, period.getChannelId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        CollectionPeriod collectionPeriod = new CollectionPeriod();
        collectionPeriod.setId(req.getId());
        collectionPeriod.setOrderNo(req.getOrderNo());

        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        // 删除前端列表查缓存
        String redisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_LIST_KEY_CHANNEL, period.getChannelId().toString());
        redisUtil.del(redisKey);
    }

    /**
     * 修改盲盒区产品期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyBlindBoxPeriod(CollectionPeriodModifyBlindBoxReq req, User operator) {
        CollectionPeriod period = detail(req.getId());
        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {
            // 校验锁仓时间
            Collection collection = collectionService.detailSimple(child.getId());

            if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
            }
            if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
            }
            if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
            }

            // 判断口令购买
            if (EBoolean.NO.getCode().equals(collectionPeriod.getWordFlag()) &&
                    !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode().equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是普通购买");
            } else if (EBoolean.YES.getCode().equals(collectionPeriod.getWordFlag()) &&
                    !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode().equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品购买方式不是口令购买");
            }

            // 校验是否有权限使用该作品
            checkCollectionPermissions(operator, collection);

            if (collection.getRemainQuantity() < child.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "剩余数量不足");
            }
            companyId = collection.getAuthorId();
            lockTime = collection.getLockTime();
            transformLimitTime = collection.getTransformLimitTime();

            totalQuantity = totalQuantity + child.getQuantity();
        }

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setAuthorIds(companyId);
        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(totalQuantity);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());

        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(child.getQuantity());

                collectionPeriodRelation.setRemainQuantity(child.getQuantity());
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);
    }

    /**
     * 修改盲盒区产品期数(发行方端)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyBlindBoxPeriod(CollectionPeriodModifyBlindBoxCompanyReq req, User operator) {
        CollectionPeriod period = detail(req.getId());
        if (!period.getAuthorIds().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        //更新优先权数据
        if (null != req.getAdvanceMins() && 0 < req.getAdvanceMins() && CollectionUtils.isEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购作品不能为空");
        } else if (null == req.getAdvanceMins() && CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无优先抢购分钟数，不能新增优先购作品");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        // 校验输入的作品的锁仓时间是否都一致
        Integer lockTime = 0;
        Integer transformLimitTime = 0;
        Long companyId = null;
        String plateCategory = null;
        String buyType = null;
        for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {
            // 校验锁仓时间
            Collection collection = collectionService.detailSimple(child.getId());

            if (lockTime != 0 && !lockTime.equals(collection.getLockTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
            }
            if (transformLimitTime != 0 && !transformLimitTime.equals(collection.getTransformLimitTime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
            }
            if (companyId != null && !companyId.equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
            }
            if (null != plateCategory && !plateCategory.equals(collection.getPlateCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
            }

            if (null != buyType && !buyType.equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
            }

            // 校验是否有权限使用该作品
            checkCollectionPermissions(operator, collection);

            if (collection.getRemainQuantity() < child.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "剩余数量不足");
            }
            companyId = collection.getAuthorId();
            lockTime = collection.getLockTime();
            transformLimitTime = collection.getTransformLimitTime();
            plateCategory = collection.getPlateCategory();
            buyType = collection.getBuyType();

            totalQuantity = totalQuantity + child.getQuantity();
        }

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(buyType) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(buyType) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        }

        collectionPeriod.setLockTime(lockTime);
        collectionPeriod.setTransformLimitTime(transformLimitTime);
        collectionPeriod.setAuthorIds(companyId);
        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(totalQuantity);
        collectionPeriod.setWordFlag(buyType);
        collectionPeriod.setPlateCategory(plateCategory);

        // 检查发行方
        companyService.checkCompany(companyId);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());

        //插入关联关系
        if (CollectionUtils.isNotEmpty(req.getSellCollectionList())) {
            for (CollectionPeriodCreateBlindBoxDetailCReq child : req.getSellCollectionList()) {

                CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
                collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
                collectionPeriodRelation.setCollectionId(child.getId());
                collectionPeriodRelation.setOrderNo(child.getOrderNo());
                collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
                collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
                collectionPeriodRelation.setTotalQuantity(child.getQuantity());

                collectionPeriodRelation.setRemainQuantity(child.getQuantity());
                collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
            }
        }

        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService
                .create(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), collectionPeriod.getAdvanceMins());

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);
        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    /**
     * 修改抽签区期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyDrawStrawsPeriod(CollectionPeriodModifyDrawStrawsReq req, User operator) {
        CollectionPeriod period = detail(req.getId());
        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        Collection collection = collectionService.detail(req.getCollectionId());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        if (CollectionUtils.isNotEmpty(req.getCollectionPeriodPriorityBuyList()) && (null == req.getPriorityDrawParam() || null == req
                .getPriorityNumber())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "优先购参数不能为空");
        }

        if (collection.getRemainQuantity() < req.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }
        init(collection, collectionPeriod);

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
        if (collectionPeriod.getEndSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "结束时间不能小于当前时间");
        }
        collectionPeriod.setStartSellDate(DateUtil.strToDate(req.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
        collectionPeriod.setEndSellDate(DateUtil.strToDate(req.getEndSellDate(), DateUtil.DATA_TIME_PATTERN_3));
        if (collectionPeriod.getStartSellDate().after(collectionPeriod.getEndSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能晚于结束时间");
        }

        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(req.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(req.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        collectionPeriodPriorityBuyService.createDrawStraws(collectionPeriod.getId(), req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService
                .create(collectionPeriod.getId(), EPeriodDiscountRefType.PERIOD.getCode(), req.getDiscountCollectionList(), operator);

        //更新白名单用户
        collectionPeriodWhiteJoinService.create(collectionPeriod, req.getWhiteJoinList(), operator);
    }

    /**
     * 新增竞拍区
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createPeriodAuctionPeriod(CollectionPeriodCreateAuctionReq request, User operator) {

        CollectionPeriod collectionPeriod = EntityUtils.copyData(request, CollectionPeriod.class);
        // 判断是否拥有该渠道的发布权限
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_0.getCode());
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, request.getChannelId());
            collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        collectionPeriod.setCategory(ECollectionPeriodCategory.PeriodAuction.getCode());

        Collection collection = collectionService.detailSimple(request.getCollectionId());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        if (collection.getRemainQuantity() < request.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }
        init(collection, collectionPeriod);
        collectionPeriod.setTotalQuantity(request.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        // 竞拍每人最多参与一次
        collectionPeriod.setBuyMax(1);

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(request.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
        collectionPeriod.setEndSellDate(
                DateUtil.strToDate(request.getEndSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
        if (collectionPeriod.getEndSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
        if (collectionPeriod.getStartSellDate().after(collectionPeriod.getEndSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能晚于结束时间");
        }

        collectionPeriodMapper.insertSelective(collectionPeriod);

        //新增藏品关系
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(request.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(request.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        List<CollectionPeriodDrawStrawsCreateReq> createReqs = new ArrayList<>();
        CollectionPeriodDrawStrawsCreateReq createReq = new CollectionPeriodDrawStrawsCreateReq();
        createReq.setCollectionId(request.getNeedCollectionId());
        createReq.setQuantity(1);
        createReqs.add(createReq);
        collectionPeriodPriorityBuyService.createDrawStraws(collectionPeriod.getId(), createReqs, operator);

        PeriodAuction periodAuction = EntityUtils.copyData(request, PeriodAuction.class);
        periodAuction.setPeriodId(collectionPeriod.getId());
        periodAuction.setStartPrice(collectionPeriod.getPrice());
        periodAuction.setCurrentPrice(collectionPeriod.getPrice());
        periodAuction.setStartTime(collectionPeriod.getStartSellDate());
        periodAuction.setEndTime(collectionPeriod.getEndSellDate());
        periodAuction.setTimeLimit((int) DateUtil.minuteBetween(collectionPeriod.getStartSellDate(), collectionPeriod.getEndSellDate()));
        periodAuction.setCreateTime(new Date());
        periodAuction.setDelayedSecond(request.getDelayedMinutes() * 60);
        periodAuction.setDelayedTime(periodAuction.getEndTime());
        periodAuction.setName(collectionPeriod.getName());
        periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode());
        // 生产竞拍标的
        periodAuctionService.create(periodAuction);
    }

    /**
     * 修改竞拍期数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyPeriodAuctionPeriod(CollectionPeriodModifyAuctionReq request, User operator) {
        CollectionPeriod period = detail(request.getId());
        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, request.getChannelId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }
        CollectionPeriod collectionPeriod = EntityUtils.copyData(request, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.PeriodAuction.getCode());

        Collection collection = collectionService.detailSimple(request.getCollectionId());

        // 校验是否有权限使用该作品
        checkCollectionPermissions(operator, collection);

        if (collection.getRemainQuantity() < request.getTotalQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }
        init(collection, collectionPeriod);
        collectionPeriod.setTotalQuantity(request.getTotalQuantity());
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        // 竞拍每人最多参与一次
        collectionPeriod.setBuyMax(1);

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        collectionPeriod.setStartSellDate(
                DateUtil.strToDate(request.getStartSellDate(), DateUtil.DATA_TIME_PATTERN_2));
        collectionPeriod.setEndSellDate(
                DateUtil.strToDate(request.getEndSellDate(), DateUtil.DATA_TIME_PATTERN_2));

        if (collectionPeriod.getStartSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
        if (collectionPeriod.getEndSellDate().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能小于当前时间");
        }
        if (collectionPeriod.getStartSellDate().after(collectionPeriod.getEndSellDate())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始时间不能晚于结束时间");
        }
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //新增藏品关系
        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(request.getCollectionId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(request.getTotalQuantity());
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        List<CollectionPeriodDrawStrawsCreateReq> createReqs = new ArrayList<>();
        CollectionPeriodDrawStrawsCreateReq createReq = new CollectionPeriodDrawStrawsCreateReq();
        createReq.setCollectionId(request.getNeedCollectionId());
        createReq.setQuantity(1);
        createReqs.add(createReq);
        collectionPeriodPriorityBuyService.createDrawStraws(collectionPeriod.getId(), createReqs, operator);

        PeriodAuction periodAuction = EntityUtils.copyData(request, PeriodAuction.class);
        periodAuction.setPeriodId(collectionPeriod.getId());
        periodAuction.setStartPrice(collectionPeriod.getPrice());
        periodAuction.setCurrentPrice(collectionPeriod.getPrice());
        periodAuction.setStartTime(collectionPeriod.getStartSellDate());
        periodAuction.setEndTime(collectionPeriod.getEndSellDate());
        periodAuction.setTimeLimit((int) DateUtil.minuteBetween(collectionPeriod.getStartSellDate(), collectionPeriod.getEndSellDate()));
        periodAuction.setCreateTime(new Date());
        periodAuction.setDelayedSecond(request.getDelayedMinutes() * 60);
        periodAuction.setDelayedTime(periodAuction.getEndTime());
        periodAuction.setName(collectionPeriod.getName());
        periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode());
        // 生产竞拍标的
        periodAuctionService.create(periodAuction);
    }

    /**
     * 获取期数详细信息
     *
     * <AUTHOR>
     */
    @Override
    public CollectionPeriod getCollectionPeriod(Long periodId) {
        String collectionPeriodKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_KEY, periodId);
        CollectionPeriod collectionPeriod;
        if (redisUtil.hasKey(collectionPeriodKey)) {
            collectionPeriod = JSON.parseObject(JSON.toJSONString(redisUtil.get(collectionPeriodKey)), CollectionPeriod.class);
        } else {
            collectionPeriod = collectionPeriodMapper.selectByPrimaryKey(periodId);
            if (null != collectionPeriod) {
                redisUtil.set(collectionPeriodKey, collectionPeriod);
            }
        }
        return collectionPeriod;
    }

    /**
     * 详情查询作品期数
     *
     * @param id 主键ID
     * @return 作品期数对象
     */
    @Override
    public CollectionPeriod detail(Long id) {
        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyOss(id);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        Company company = companyService.detail(collectionPeriod.getAuthorIds());
        collectionPeriod.setAuthorId(company.getId());

        collectionPeriod.setAuthor(company.getShortName());
        collectionPeriod.setAuthorPic(company.getLogo());

        if (StringUtils.isNotBlank(collectionPeriod.getTags())) {
            List<String> result = Arrays.asList(collectionPeriod.getTags().split(","));
            collectionPeriod.setTagList(result);
        }

        //藏品列表
        List<CollectionPeriodCollectionPageRes> collectionList = collectionPeriodRelationMapper.selectListByPeriodId(id, null);

        List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
        List<CollectionPeriodCollectionPageRes> showCollectionList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(collectionList)) {

            ECollectionPeriodCollectionStatus eCollectionPeriodCollectionStatus = null;
            if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_0;
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_1;
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
            }

            int i = 0;
            BigDecimal rate = BigDecimal.ZERO;
            for (CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes : collectionList) {
                collectionPeriodCollectionPageRes.setQuantity(collectionPeriodCollectionPageRes.getTotalQuantity());
                collectionPeriodCollectionPageRes.setPrice(collectionPeriod.getPrice());
                if (collectionPeriodCollectionPageRes.getRemainQuantity() <= 0) {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
                }
                collectionPeriodCollectionPageRes.setStatus(eCollectionPeriodCollectionStatus.getCode());

                if (StringUtils.isNotBlank(collectionPeriodCollectionPageRes.getFileUrl())) {
                    List<UploadFile> uploadFiles = JSONArray.parseArray(collectionPeriodCollectionPageRes.getFileUrl(),
                            UploadFile.class);
                    collectionPeriodCollectionPageRes.setFileList(uploadFiles);
                }

                if (ECollectionPeriodRelationCategory.SELL.getCode().equals(collectionPeriodCollectionPageRes.getCategory())) {
                    // 是盲盒的话展示下占比
                    if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
//                        if (i == collectionList.size() - 1) {
//                            collectionPeriodCollectionPageRes.setRate(new BigDecimal(100).subtract(rate).stripTrailingZeros());
//                        } else {
//                            BigDecimal rateDetail = new BigDecimal(collectionPeriodCollectionPageRes.getTotalQuantity())
//                                    .divide(new BigDecimal(collectionPeriod.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN)
//                                    .multiply(new BigDecimal(100));
//                            collectionPeriodCollectionPageRes.setRate(rateDetail.stripTrailingZeros());
//                            rate = rate.add(rateDetail);
//                        }
                        BigDecimal rateDetail = new BigDecimal(collectionPeriodCollectionPageRes.getTotalQuantity())
                                .divide(new BigDecimal(collectionPeriod.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN)
                                .multiply(new BigDecimal(100));
                        collectionPeriodCollectionPageRes.setRate(rateDetail.stripTrailingZeros());
                    } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())
                            && ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(collectionPeriod.getRightType())) {
                        //获取权益列表
                        CollectionRightsDetailListReq collectionRightsDetailListReq = new CollectionRightsDetailListReq();
                        collectionRightsDetailListReq.setCollectionId(collectionPeriodCollectionPageRes.getId());
                        collectionPeriod.setRightsDetailList(collectionRightsDetailService.list(collectionRightsDetailListReq));
                    }

                    sellCollectionList.add(collectionPeriodCollectionPageRes);
                } else {
                    showCollectionList.add(collectionPeriodCollectionPageRes);
                }

                i++;
            }
        }
        collectionPeriod.setCollectionList(collectionList);
        collectionPeriod.setSellCollectionList(sellCollectionList);
        collectionPeriod.setShowCollectionList(showCollectionList);

//        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityFinalBuyList = new ArrayList<>();
//        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityBuyList = collectionPeriodPriorityBuyService
//                .list(collectionPeriod.getId());
//        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
//            for (CollectionPeriodPriorityBuy collectionPeriodPriorityBuy : collectionPeriodPriorityBuyList) {
//                Collection collection = collectionService.detailSimple(collectionPeriodPriorityBuy.getCollectionId());
//                collectionPeriodPriorityBuy.setCollectionName(collection.getName());
//                collectionPeriodPriorityBuy.setCoverFileUrl(collection.getCoverFileUrl());
//                collectionPeriodPriorityBuy.setCollection(collection);
//                collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
//            }
//        }
//
//        collectionPeriod.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);

        // 获取优先购
        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityFinalBuyList = new ArrayList<>();
        List<PeriodDiscountDetail> discountCollectionList = new ArrayList<>();
        if (!ECollectionPeriodCategory.DrawStraws.getCode().equals(collectionPeriod.getCategory())) {
            CollectionRightRecord rightRecord = new CollectionRightRecord();
            rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode());
            rightRecord.setRefId(collectionPeriod.getId());
            List<CollectionRightRecord> collectionPeriodPriorityBuyList = collectionRightRecordService.list(rightRecord);
            if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
                for (CollectionRightRecord record : collectionPeriodPriorityBuyList) {
                    CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
                    Collection collection = collectionService.detailSimple(record.getCollectionId());
                    CollectionPeriodPriorityBuy collectionPeriodPriorityBuy = new CollectionPeriodPriorityBuy();
                    collectionPeriodPriorityBuy.setCollectionName(collection.getName());
                    collectionPeriodPriorityBuy.setCoverFileUrl(collection.getCoverFileUrl());
                    collectionPeriodPriorityBuy.setCollection(collection);
                    collectionPeriodPriorityBuy.setRightId(record.getRightCompanyId());
                    collectionPeriodPriorityBuy.setPeriodId(record.getRefId());
                    collectionPeriodPriorityBuy.setCollectionId(collection.getId());
                    collectionPeriodPriorityBuy.setQuantity(1);
                    collectionPeriodPriorityBuy.setAdvanceMins(record.getAdvanceMins());
                    collectionPeriodPriorityBuy.setNumberFlag(rightsDetail.getNumberFlag());
                    collectionPeriodPriorityBuy.setRemainNumber(rightsDetail.getRemainNumber());

                    collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
                }
            }
            // 获取折扣藏品
            rightRecord = new CollectionRightRecord();
            rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode());
            rightRecord.setRefId(collectionPeriod.getId());
            List<CollectionRightRecord> collectiondiscountCollectionList = collectionRightRecordService.list(rightRecord);
            for (CollectionRightRecord record : collectiondiscountCollectionList) {
                CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
                PeriodDiscountDetail discountDetail = new PeriodDiscountDetail();
                Collection collection = collectionService.detailSimple(record.getCollectionId());
                discountDetail.setRightId(record.getRightCompanyId());
                discountDetail.setRefType(EPeriodDiscountRefType.PERIOD.getCode());
                discountDetail.setRefId(collectionPeriod.getId());
                discountDetail.setCollectionId(record.getCollectionId());
                discountDetail.setCollection(collection);
                discountDetail.setDiscountRate(record.getDiscountRate());
                discountDetail.setCollectionName(collection.getName());
                discountDetail.setCoverFileUrl(collection.getCoverFileUrl());
                discountDetail.setNumberFlag(rightsDetail.getNumberFlag());
                discountDetail.setRemainNumber(rightsDetail.getRemainNumber());
                discountCollectionList.add(discountDetail);
            }

            collectionPeriod.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);
            collectionPeriod.setDiscountCollectionList(discountCollectionList);

            // 获取空投藏品
            rightRecord = new CollectionRightRecord();
            rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
            rightRecord.setRefId(collectionPeriod.getId());
            List<CollectionRightRecord> list = collectionRightRecordService.list(rightRecord);
            List<CollectionPeriodDropRes> dropResList = new ArrayList<>();
            for (CollectionRightRecord record : list) {
                CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
                CollectionPeriodDropRes dropRes = new CollectionPeriodDropRes();
                dropRes.setRightId(record.getRightCompanyId());
                dropRes.setCollectionId(record.getCollectionId());
                Collection collection = collectionService.detailSimple(record.getCollectionId());
                dropRes.setCollectionName(collection.getName());
                dropRes.setCoverFileUrl(collection.getCoverFileUrl());
                dropRes.setNumberFlag(rightsDetail.getNumberFlag());
                dropRes.setRemainNumber(rightsDetail.getRemainNumber());
                dropResList.add(dropRes);
            }
            collectionPeriod.setCollectionPeriodDropList(dropResList);

        } else {
            List<CollectionPeriodPriorityBuy> collectionPeriodPriorityBuyList = collectionPeriodPriorityBuyService
                    .list(collectionPeriod.getId());
            if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
                for (CollectionPeriodPriorityBuy collectionPeriodPriorityBuy : collectionPeriodPriorityBuyList) {
                    Collection collection = collectionService.detailSimple(collectionPeriodPriorityBuy.getCollectionId());
                    collectionPeriodPriorityBuy.setCollectionName(collection.getName());
                    collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
                }
            }
            collectionPeriod.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);
            collectionPeriod.setDiscountCollectionList(
                    periodDiscountDetailService.list(EPeriodDiscountRefType.PERIOD.getCode(), collectionPeriod.getId()));
        }

        collectionPeriod.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));

        //获取口令列表
        collectionPeriod.setPeriodChannelWordList(periodChannelWordService.selectListByPeriodId(collectionPeriod.getId()));
        collectionPeriod.setChannelName(channelMerchantService.detail(collectionPeriod.getChannelId()).getName());

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            collectionPeriod.setApproveOpinion(
                    approveRecordService.detail(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_4.getCode(), collectionPeriod.getId()));
        }

//        if (EBoolean.YES.getCode().equals(collectionPeriod.getMetaReleaseFlag())) {
//            Pit pit = pitService.detailSimple(collectionPeriod.getPitId());
//            collectionPeriod.setPitNumber(pit.getPitNumber());
//        }
        //获取白名单列表
        collectionPeriod.setWhiteJoinList(collectionPeriodWhiteJoinService.list(collectionPeriod.getId()));

        return collectionPeriod;
    }

    @Override
    public CollectionPeriodAuditRes detailAudit(Long id) {
        CollectionPeriod period = collectionPeriodMapper.selectByPrimaryKeyOss(id);
        CollectionPeriodAuditRes collectionPeriod = new CollectionPeriodAuditRes();
        BeanUtils.copyProperties(period, collectionPeriod);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        Company company = companyService.detail(collectionPeriod.getAuthorIds());
        collectionPeriod.setAuthorId(company.getId());

        collectionPeriod.setAuthor(company.getShortName());
        collectionPeriod.setAuthorPic(company.getLogo());

        if (StringUtils.isNotBlank(collectionPeriod.getTags())) {
            List<String> result = Arrays.asList(collectionPeriod.getTags().split(","));
            collectionPeriod.setTagList(result);
        }

        //藏品列表
        List<CollectionPeriodCollectionPageRes> collectionList = collectionPeriodRelationMapper.selectListByPeriodId(id, null);

        List<CollectionPeriodAuditCollectionPageRes> sellCollectionList = new ArrayList<>();
        List<CollectionPeriodAuditCollectionPageRes> showCollectionList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(collectionList)) {

            Long contractId = null;
            for (CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes : collectionList) {
                CollectionPeriodAuditCollectionPageRes auditCollectionPageRes = new CollectionPeriodAuditCollectionPageRes();
                auditCollectionPageRes.setPrice(collectionPeriodCollectionPageRes.getPrice());
                auditCollectionPageRes.setOrderNo(collectionPeriodCollectionPageRes.getOrderNo());
                auditCollectionPageRes.setQuantity(collectionPeriodCollectionPageRes.getTotalQuantity());

                Collection collection = collectionService.detail(collectionPeriodCollectionPageRes.getId());
                contractId = collection.getContractId();
                BeanUtils.copyProperties(collection, auditCollectionPageRes);

                auditCollectionPageRes.getCollectionSaleDemand().setRemainQuantity(collection.getRemainQuantity());
                auditCollectionPageRes.setPrice(collectionPeriod.getPrice());

                if (StringUtils.isNotBlank(auditCollectionPageRes.getFileUrl())) {
                    List<UploadFile> uploadFiles = JSONArray.parseArray(auditCollectionPageRes.getFileUrl(),
                            UploadFile.class);
                    auditCollectionPageRes.setFileList(uploadFiles);
                }

                if (ECollectionPeriodRelationCategory.SELL.getCode().equals(collectionPeriodCollectionPageRes.getCategory())) {
                    // 是盲盒的话展示下占比
                    if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {

                        BigDecimal rateDetail = new BigDecimal(auditCollectionPageRes.getTotalQuantity())
                                .divide(new BigDecimal(collectionPeriod.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN)
                                .multiply(new BigDecimal(100));
                        auditCollectionPageRes.setRate(rateDetail.stripTrailingZeros());
                    }
                    sellCollectionList.add(auditCollectionPageRes);
                } else {
                    showCollectionList.add(auditCollectionPageRes);
                }
            }
            collectionPeriod.setContractId(contractId);
        }
        collectionPeriod.setSellCollectionList(sellCollectionList);
        collectionPeriod.setShowCollectionList(showCollectionList);

        // 获取优先购
        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityFinalBuyList = new ArrayList<>();
        CollectionRightRecord rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> collectionPeriodPriorityBuyList = collectionRightRecordService.list(rightRecord);
        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
            for (CollectionRightRecord record : collectionPeriodPriorityBuyList) {
                CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
                Collection collection = collectionService.detailSimple(record.getCollectionId());
                CollectionPeriodPriorityBuy collectionPeriodPriorityBuy = new CollectionPeriodPriorityBuy();
                collectionPeriodPriorityBuy.setCollectionName(collection.getName());
                collectionPeriodPriorityBuy.setCoverFileUrl(collection.getCoverFileUrl());
                collectionPeriodPriorityBuy.setCollection(collection);
                collectionPeriodPriorityBuy.setRightId(record.getRightCompanyId());
                collectionPeriodPriorityBuy.setCreateType(rightsDetail.getCreateType());
                collectionPeriodPriorityBuy.setPeriodId(record.getRefId());
                collectionPeriodPriorityBuy.setCollectionId(collection.getId());
                collectionPeriodPriorityBuy.setQuantity(1);
                collectionPeriodPriorityBuy.setAdvanceMins(record.getAdvanceMins());
                collectionPeriodPriorityBuy.setNumberFlag(rightsDetail.getNumberFlag());
                collectionPeriodPriorityBuy.setRemainNumber(rightsDetail.getRemainNumber());

                collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
            }
        }
        collectionPeriod.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);
        collectionPeriod.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));

        // 获取折扣藏品
        List<PeriodDiscountDetail> discountCollectionList = new ArrayList<>();
        rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> collectiondiscountCollectionList = collectionRightRecordService.list(rightRecord);
        for (CollectionRightRecord record : collectiondiscountCollectionList) {
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
            PeriodDiscountDetail discountDetail = new PeriodDiscountDetail();
            Collection collection = collectionService.detailSimple(record.getCollectionId());
            discountDetail.setRightId(record.getRightCompanyId());
            discountDetail.setCreateType(rightsDetail.getCreateType());
            discountDetail.setRefType(EPeriodDiscountRefType.PERIOD.getCode());
            discountDetail.setRefId(collectionPeriod.getId());
            discountDetail.setCollectionId(record.getCollectionId());
            discountDetail.setCollection(collection);
            discountDetail.setDiscountRate(record.getDiscountRate());
            discountDetail.setCollectionName(collection.getName());
            discountDetail.setCoverFileUrl(collection.getCoverFileUrl());
            discountDetail.setNumberFlag(rightsDetail.getNumberFlag());
            discountDetail.setRemainNumber(rightsDetail.getRemainNumber());
            discountCollectionList.add(discountDetail);
        }
        collectionPeriod.setDiscountCollectionList(discountCollectionList);

        // 获取空投藏品
        rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> list = collectionRightRecordService.list(rightRecord);
        List<CollectionPeriodDropRes> dropResList = new ArrayList<>();
        for (CollectionRightRecord record : list) {
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
            CollectionPeriodDropRes dropRes = new CollectionPeriodDropRes();
            dropRes.setRightId(record.getRightCompanyId());
            dropRes.setCollectionId(record.getCollectionId());
            Collection collection = collectionService.detailSimple(record.getCollectionId());
            dropRes.setCollectionName(collection.getName());
            dropRes.setCoverFileUrl(collection.getCoverFileUrl());
            dropRes.setNumberFlag(rightsDetail.getNumberFlag());
            dropRes.setRemainNumber(rightsDetail.getRemainNumber());
            dropResList.add(dropRes);
        }
        collectionPeriod.setCollectionPeriodDropList(dropResList);
        //获取口令列表
        collectionPeriod.setPeriodChannelWordList(periodChannelWordService.selectListByPeriodId(collectionPeriod.getId()));
        collectionPeriod.setChannelName(channelMerchantService.detail(collectionPeriod.getChannelId()).getName());

        return collectionPeriod;
    }

    @Override
    public CollectionPeriodInfo auctionDetail(Long id) {
        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyOss(id);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        CollectionPeriodInfo res = new CollectionPeriodInfo();
        BeanUtils.copyProperties(collectionPeriod, res);

        Company company = companyService.detail(collectionPeriod.getAuthorIds());
        res.setAuthorId(company.getId());

        res.setAuthor(company.getShortName());
        res.setAuthorPic(company.getLogo());

        if (StringUtils.isNotBlank(res.getTags())) {
            List<String> result = Arrays.asList(res.getTags().split(","));
            res.setTagList(result);
        }

        //藏品列表
        List<CollectionPeriodCollectionPageRes> collectionList = collectionPeriodRelationMapper.selectListByPeriodId(id, null);

        List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
        List<CollectionPeriodCollectionPageRes> showCollectionList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(collectionList)) {

            ECollectionPeriodCollectionStatus eCollectionPeriodCollectionStatus = null;
            if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_0;
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_1;
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(collectionPeriod.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
            }

            int i = 0;
            BigDecimal rate = BigDecimal.ZERO;
            for (CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes : collectionList) {
                collectionPeriodCollectionPageRes.setQuantity(collectionPeriodCollectionPageRes.getTotalQuantity());
                collectionPeriodCollectionPageRes.setPrice(res.getPrice());
                if (collectionPeriodCollectionPageRes.getRemainQuantity() <= 0) {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
                }
                collectionPeriodCollectionPageRes.setStatus(eCollectionPeriodCollectionStatus.getCode());

                if (StringUtils.isNotBlank(collectionPeriodCollectionPageRes.getFileUrl())) {
                    List<UploadFile> uploadFiles = JSONArray.parseArray(collectionPeriodCollectionPageRes.getFileUrl(),
                            UploadFile.class);
                    collectionPeriodCollectionPageRes.setFileList(uploadFiles);
                }
                showCollectionList.add(collectionPeriodCollectionPageRes);
                i++;
            }
        }
        res.setCollectionList(collectionList);
        res.setSellCollectionList(sellCollectionList);
        res.setShowCollectionList(showCollectionList);

        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityFinalBuyList = new ArrayList<>();
        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityBuyList = collectionPeriodPriorityBuyService
                .list(collectionPeriod.getId());
        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
            for (CollectionPeriodPriorityBuy collectionPeriodPriorityBuy : collectionPeriodPriorityBuyList) {
                Collection collection = collectionService.detailSimple(collectionPeriodPriorityBuy.getCollectionId());
                collectionPeriodPriorityBuy.setCollectionName(collection.getName());
                collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
            }
        }

        res.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);
        res.setLockTime(collectionPeriodRelationService.getPeriodLockTime(res.getId()));

        // 竞拍信息赋值
        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(res.getId());
        BeanUtils.copyProperties(periodAuction, res, "id", "name", "status", "authorId", "author", "authorPic");
        res.setDelayedMinutes(periodAuction.getDelayedSecond() / 60);

        if (null != res.getLastUserId()) {
            res.setLastUser(userService.detailSimple(res.getLastUserId()));
        }
        res.setAuctionStatus(periodAuction.getStatus());

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                || ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStartStatus())) {
            res.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS.getCode());
        }
        return res;
    }


    /**
     * 分页查询作品期数
     *
     * @param req 分页查询作品期数入参
     * @return 分页作品期数对象
     */
    @Override
    public List<CollectionPeriod> page(CollectionPeriodPageReq req, User operator) {
        CollectionPeriod condition = EntityUtils.copyData(req, CollectionPeriod.class);
        condition.setAuthorIds(req.getAuthorId());
        condition.setSellDateStart(DateUtil.dateTime(req.getSellDateStart(), DateUtil.DATA_TIME_PATTERN_9));
        condition.setSellDateEnd(DateUtil.dateTime(req.getSellDateEnd(), DateUtil.DATA_TIME_PATTERN_9));

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorIds(operator.getCompanyId());
        }
        List<CollectionPeriod> collectionPeriodList = collectionPeriodMapper.selectByConditionOss(condition);
        if (CollectionUtils.isNotEmpty(collectionPeriodList)) {
            for (CollectionPeriod collectionPeriod : collectionPeriodList) {

                Company company = companyService.detail(collectionPeriod.getAuthorIds());
                collectionPeriod.setAuthorId(company.getId());

                collectionPeriod.setAuthor(company.getShortName());
                collectionPeriod.setAuthorPic(company.getLogo());

                if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(collectionPeriod.getCategory())) {
                    PeriodAuction periodAuction = periodAuctionService.detailByPeriod(collectionPeriod.getId());
                    collectionPeriod.setBond(periodAuction.getBond());
                    collectionPeriod.setPriceAuction(periodAuction.getPriceAuction());
                    collectionPeriod.setDelayedMinutes(periodAuction.getDelayedSecond() / 60);

                }
                collectionPeriod.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));
                collectionPeriod.setChannelName(channelMerchantService.detail(collectionPeriod.getChannelId()).getName());

//                if (EBoolean.YES.getCode().equals(collectionPeriod.getMetaReleaseFlag())) {
//                    Pit pit = pitService.detailSimple(collectionPeriod.getPitId());
//                    collectionPeriod.setPitNumber(pit.getPitNumber());
//                }
            }
        }

        return collectionPeriodList;
    }


    /**
     * 竞拍期数管理端分页查
     */
    @Override
    public List<CollectionPeriodInfo> auctionPage(CollectionPeriodAuctionPageReq req) {
        req.setAuthorIds(null != req.getAuthorIds() ? req.getAuthorIds() : req.getAuthorId());

        List<CollectionPeriodInfo> resList = new ArrayList<>();
        List<CollectionPeriod> collectionPeriodList = collectionPeriodMapper.selectAuctionOss(req);
        if (CollectionUtils.isNotEmpty(collectionPeriodList)) {
            for (CollectionPeriod collectionPeriod : collectionPeriodList) {
                CollectionPeriodInfo res = new CollectionPeriodInfo();
                BeanUtils.copyProperties(collectionPeriod, res);
                if (StringUtils.isNotBlank(res.getAuthorIds())) {
                    List<String> list = Arrays.asList(res.getAuthorIds().split(","));
                    Company company = companyService.detail(Long.valueOf(list.get(0)));
                    res.setAuthorId(company.getId());

                    res.setAuthor(company.getShortName());
                    res.setAuthorPic(company.getLogo());
                }

                if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(res.getCategory())) {
                    PeriodAuction periodAuction = periodAuctionService.detailByPeriod(res.getId());
                    BeanUtils.copyProperties(periodAuction, res, "id", "name", "status", "authorId", "author", "authorPic");
                    res.setDelayedMinutes(periodAuction.getDelayedSecond() / 60);

                    if (null != res.getLastUserId()) {
                        res.setLastUser(userService.detailSimple(res.getLastUserId()));
                    }
                    res.setAuctionStatus(periodAuction.getStatus());
                }
                res.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));

                if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                        || ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStartStatus())) {
                    res.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS.getCode());
                }
                resList.add(res);
            }
        }

        return PageInfoUtil.listToPage(collectionPeriodList, resList);
    }

    /**
     * 列表查询作品期数
     *
     * @param req 列表查询作品期数入参
     * @return 列表作品期数对象
     */
    @Override
    public List<CollectionPeriod> list(CollectionPeriodListReq req, User operator) {
        CollectionPeriod condition = EntityUtils.copyData(req, CollectionPeriod.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionPeriod.class));
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorIds(operator.getCompanyId());
        }
        List<CollectionPeriod> collectionPeriodList = collectionPeriodMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(collectionPeriodList)) {
            for (CollectionPeriod collectionPeriod : collectionPeriodList) {

                Company company = companyService.detail(collectionPeriod.getAuthorIds());
                collectionPeriod.setAuthorId(company.getId());

                collectionPeriod.setAuthor(company.getShortName());
                collectionPeriod.setAuthorPic(company.getLogo());

                if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(collectionPeriod.getCategory())) {
                    PeriodAuction periodAuction = periodAuctionService.detailByPeriod(collectionPeriod.getId());
                    collectionPeriod.setBond(periodAuction.getBond());
                    collectionPeriod.setPriceAuction(periodAuction.getPriceAuction());
                    collectionPeriod.setDelayedMinutes(periodAuction.getDelayedSecond() / 60);

                }
                collectionPeriod.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));
                collectionPeriod.setChannelName(channelMerchantService.detail(collectionPeriod.getChannelId()).getName());
            }
        }
        return collectionPeriodList;
    }

    @Override
    public List<CollectionPeriod> list(CollectionPeriod condition) {
        return collectionPeriodMapper.selectByCondition(condition);
    }

    /**
     * 前端分页查询作品期数
     *
     * @param req 前端分页查询作品期数入参
     * @return 分页作品期数对象
     */
    @Override
    public List<CollectionPeriodPageFrontRes> pageFront(CollectionPeriodPageFrontReq req, User operator, Long channelId) {
        CollectionPeriod condition = EntityUtils.copyData(req, CollectionPeriod.class);
        condition.setAuthorIds(req.getAuthorId());
        condition.setChannelId(channelId);
        condition.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());

        if (StringUtils.isNotBlank(req.getKeywords()) && 1 == req.getKeywords().length()) {
            String keywords = req.getKeywords();
            String replace = keywords.replace(",", "");
            condition.setKeywords(replace);
        }

        if (StringUtils.isNotBlank(req.getKeywords()) && StringUtils.isBlank(condition.getKeywords())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "搜索内容不能为空或只有单个符号");
        }

        if (StringUtils.isNotBlank(req.getCategoryId())) {
            if (ECollectionPeriodStatusCategory.ZERO.getCode().equals(req.getCategoryId())) {
                condition.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                List<String> startStatusList = new ArrayList<>();
                startStatusList.add(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode());
                startStatusList.add(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                startStatusList.add(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_4.getCode());
                condition.setStartStatusList(startStatusList);
            } else if (ECollectionPeriodStatusCategory.ONE.getCode().equals(req.getCategoryId())) {
                //历史藏品放出已下架的
                condition.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                condition.setStatus(null);
                List<String> statusList = new ArrayList<>();
                statusList.add(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                statusList.add(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                condition.setStatusList(statusList);
            }
        }
        condition.setSoldFlag(EBoolean.YES.getCode());
        condition.setOrderBy("t.start_status = '1' desc,t.start_status ASC, t.order_no desc");

        List<CollectionPeriodPageFrontRes> resList = collectionPeriodMapper.selectByConditionFront(condition);

        if (CollectionUtils.isNotEmpty(resList)) {
            List<Long> periodIds = resList.stream().map(CollectionPeriodPageFrontRes::getId).collect(Collectors.toList());
            List<CollectionPeriodCollectionPageResRelation> collectionRelationList = getPeriodCollectionRelation(periodIds);

            resList.forEach(period -> {
                //不是待开始状态，前端不用显示提前分钟数
                if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
                    checkAdvanceMins(operator, period);
                } else {
                    period.setAdvanceMins(0);
                }

                if (StringUtils.isNotBlank(period.getTags())) {
                    List<String> result = new ArrayList<>();
                    if (EBoolean.YES.getCode().equals(period.getWordFlag())) {
                        result.add(ECollectionPeriodWordFlag.WORD.getValue());
                    }
                    result.addAll(Arrays.asList(period.getTags().split(",")));
                    period.setTagList(result);
                }

                // 生成机构
                createAuthor(period);
                // 获取期数作品
                List<CollectionPeriodCollectionPageResRelation> relationList = collectionRelationList.stream()
                        .filter(item -> item.getPeriodId().equals(period.getId())).collect(Collectors.toList());

                period.setLockTime(relationList.get(0).getLockTime());
                // 版权区的期数
                if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.COPYRIGHT.getUnit());
                    //售卖中的
                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
                        // 最终根据剩余量来判断状态
                        int remainQuantity = period.getRemainQuantity();

                        if (remainQuantity <= 0) {
                            period.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                        }
                    }
                } else if (ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
                    Integer joinTotalNumber = collectionPeriodJoinRecordService.getJoinTotalNumber(period.getId());
                    if (null == joinTotalNumber || joinTotalNumber == 0) {
                        joinTotalNumber = collectionPeriodJoinRecordService.getJoinTotalNumberHistory(period.getId());
                    }
                    period.setTotalDrawStrawsCount(joinTotalNumber);

                    // 如果时间到了就修改状态
                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus()) && new Date()
                            .after(period.getEndSellDate())) {
                        period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                    }

                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(period.getStartStatus())) {
                        // todo 中签人数加入缓存
                        String redisId = String.format(RedisKeyList.MT_PERIOD_DRAW_STRAWS_COUNT_KEY, period.getId().toString());
                        if (redisUtil.hasKey(redisId)) {
                            Integer joinNumber = Integer.valueOf(redisUtil.get(redisId).toString());
                            period.setDrawStrawsCount(joinNumber);
                        } else {
                            Integer joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumber(period.getId());
                            if (null == joinNumber || joinNumber == 0) {
                                joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumberHistory(period.getId());
                            }
                            period.setDrawStrawsCount(joinNumber);
                            redisUtil.set(redisId, joinNumber, redisLockTime);
                        }
                    }

                } else if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
                    if (!ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {

                        // todo 加缓存
                        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(period.getId());
                        long times = (periodAuction.getDelayedTime().getTime() - System.currentTimeMillis()) / 1000;
                        period.setEndTime(times);
                        // 当前价格
                        period.setPrice(periodAuction.getCurrentPrice());
                    }

                } else {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
                }

                period.setEndTime(0L);
                if (!ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
                    if (null != period.getEndSellDate()) {
                        long times = (period.getEndSellDate().getTime() - System.currentTimeMillis()) / 1000 * 1000;
                        period.setEndTime(times);
                        if (times <= 0) {
                            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                        }
                    }
                }

                period.setDiscountPrice(period.getPrice());
                period.setDiscountRate(BigDecimal.ONE);
                if (null != operator) {
                    //获取折扣值(根据用户的藏品id，获取最低折扣比例)
                    PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                            .getLowestByUserId(period.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
                    if (null != periodDiscountDetail) {
                        period.setDiscountPrice(
                                (period.getPrice().multiply(periodDiscountDetail.getDiscountRate())).setScale(2, BigDecimal.ROUND_DOWN));
                        period.setDiscountRate(periodDiscountDetail.getDiscountRate());
                    } else {
                        period.setDiscountPrice(period.getPrice());
                        period.setDiscountRate(BigDecimal.ONE);
                    }
                }
            });
        }

        return resList;
    }

    /**
     * 前端详情查询作品期数
     *
     * @param id 主键ID
     * @return 作品期数对象
     */
    @Override
    public CollectionPeriodDetailFrontRes detailFront(Long id, User operator) {
//        CollectionPeriodDetailFrontRes period = new CollectionPeriodDetailFrontRes();
//        // todo 前端详情查加缓存 结束要清
//        String frontPeriodDetail = String.format(RedisKeyList.MT_PERIOD_FRONT_DETAIL_KEY, id.toString());
//        if (redisUtil.hasKey(frontPeriodDetail)) {
//            period = JSON.parseObject(JSON.toJSONString(redisUtil.get(frontPeriodDetail)), CollectionPeriodDetailFrontRes.class);
//        } else {
//            period = collectionPeriodMapper.selectByPrimaryKeyFront(id);
//            redisUtil.set(frontPeriodDetail, period, redisLockTime);
//        }
        CollectionPeriodDetailFrontRes period = collectionPeriodMapper.selectByPrimaryKeyFront(id);
        period.setAuctionFlag(EAuctionFlag.EAUCTION_FLAG_0.getCode());

        if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
            checkAdvanceMins(operator, period);
            //long realTimes = (period.getStartSellDate().getTime() - System.currentTimeMillis()) / 1000 * 1000;
//            //实际倒计时到，更改期数状态
//            if (realTimes <= 0) {
//                CollectionPeriod collectionPeriod = new CollectionPeriod();
//                collectionPeriod.setId(id);
//                collectionPeriod.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
//                collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);
//            }
        } else {
            Long times = (period.getStartSellDate().getTime() - System.currentTimeMillis()) / 1000 * 1000;
            period.setStartTime(times);

            if (null != operator && ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())
                    && collectionPeriodPriorityBuyService.checkExist(period.getId(), operator)) {
                period.setAuctionFlag(EAuctionFlag.EAUCTION_FLAG_1.getCode());
            }
        }

        // 生成机构
        createAuthor(period);

        // todo 期数作品加入缓存
        // 获取期数作品
        List<CollectionPeriodCollectionPageRes> collectionList = getPeriodCollection(period.getId(), period.getCategory());

        // 判断期数藏品是否展示完全
        String periodCollectionShowFlag = EBoolean.YES.getCode();
        if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(period.getCategory()) && period.getCollectionNumber() > collectionList
                .size()) {
            periodCollectionShowFlag = EBoolean.NO.getCode();
        }
        period.setPeriodCollectionShowFlag(periodCollectionShowFlag);

        if (StringUtils.isNotBlank(period.getTags())) {
            List<String> result = new ArrayList<>();
            if (EBoolean.YES.getCode().equals(period.getWordFlag())) {
                result.add(ECollectionPeriodWordFlag.WORD.getValue());
            }
            result.addAll(Arrays.asList(period.getTags().split(",")));
            period.setTagList(result);
        }

        List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
        List<CollectionPeriodCollectionPageRes> showCollectionList = new ArrayList<>();

        //假设已售罄，下面是否是已售罄
        boolean copyRightSelledFlag = true;
        // 已算出的百分比，用来计算剩余百分比
        BigDecimal rate = BigDecimal.ZERO;
        int i = 0;
        List<Long> contractIdList = new ArrayList<>();
        List<CollectionContractTokenRes> contractTokenList = new ArrayList<>();

        for (CollectionPeriodCollectionPageRes collectionRes : collectionList) {
            // 组装期数作品的价格、状态、文件
            assignPeriodCollection(period, collectionRes);
            collectionRes.setWordFlag(period.getWordFlag());

            //

            // 期数的上链logo
            Collection collection = collectionService.detailSimple(collectionRes.getId());
            if (!contractIdList.contains(collection.getContractId())) {
                CollectionContractTokenRes tokenRes = new CollectionContractTokenRes();
                //出品方名称显示
                if (null != collection.getProducedId()) {
                    period.setProducedId(collection.getProducedId());
                    Produced produced = producedService.detail(collection.getProducedId());
                    period.setProducedName(produced.getName());
                    period.setProducedPic(produced.getPic());
                }

                Contract contract = contractService.detail(collection.getContractId());
                tokenRes.setContractLogo(contract.getLogo());
                period.setContractToken(tokenRes);
                contractTokenList.add(tokenRes);
                contractIdList.add(collection.getContractId());
            }

            if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(period.getCategory())) {
                // 版权判断是否为隐藏，vip制定购处理，判断是否售完
                copyRightSelledFlag = copyrightPeriodCollection(collectionRes, sellCollectionList, showCollectionList, operator,
                        copyRightSelledFlag);

                //获取折扣值(根据用户的藏品id，获取最低折扣比例)
                PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                        .getLowestByUserId(period.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
                if (null != periodDiscountDetail) {
                    collectionRes.setDiscountPrice(
                            (period.getPrice().multiply(periodDiscountDetail.getDiscountRate())).setScale(2, BigDecimal.ROUND_DOWN));
                    collectionRes.setDiscountRate(periodDiscountDetail.getDiscountRate());
                } else {
                    collectionRes.setDiscountPrice(period.getPrice());
                    collectionRes.setDiscountRate(BigDecimal.ONE);
                }

            } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
                // 衍生没有隐藏，只需要加售卖列表
                sellCollectionList.add(collectionRes);
                period.setLevelType(collectionRes.getLevelType());

            } else if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(period.getCategory())) {
                // 盲盒区计算每个作品的占比，且没有隐藏，只需要加售卖列表
//                if (i == collectionList.size() - 1) {
//                    collectionRes.setRate(new BigDecimal(100).subtract(rate).stripTrailingZeros());
//                } else {
//                    BigDecimal rateDetail = new BigDecimal(collectionRes.getTotalQuantity())
//                            .divide(new BigDecimal(period.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100));
//                    collectionRes.setRate(rateDetail.stripTrailingZeros());
//                    rate = rate.add(rateDetail);
//                }

                BigDecimal rateDetail = new BigDecimal(collectionRes.getTotalQuantity())
                        .divide(new BigDecimal(period.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100));
                collectionRes.setRate(rateDetail.stripTrailingZeros());
//                rate = rate.add(rateDetail);
                sellCollectionList.add(collectionRes);
            } else if (ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
                // 抽签没有隐藏，只需要加售卖列表
                sellCollectionList.add(collectionRes);
            } else if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
                // 竞拍没有隐藏，只需要加售卖列表
                sellCollectionList.add(collectionRes);
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数类型错误");
            }
            i++;
        }

        period.setSellCollectionList(sellCollectionList);
        period.setShowCollectionList(showCollectionList);
        // 版权区内容类型一定是图片
        period.setContentType(collectionList.get(0).getContentType());
        //抽签区的白名单默认值
        period.setWhiteFlag(EBoolean.NO.getCode());
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(period.getCategory())) {
            period.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());

            period.setTotalQuantityUnit(ECollectionPeriodCategory.COPYRIGHT.getUnit());
            // todo 购买须知加入缓存
            period.setBuyNotice(
                    configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.COLLECTION_COPYRIGHT_PERIOD_BUY_NOTE));

            if (copyRightSelledFlag) {
                period.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
            }
        } else if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
            period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
            // todo 购买须知加入缓存
            period.setBuyNotice(configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.COLLECTION_PERIOD_BUY_NOTE));
        } else if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(period.getCategory())) {
            period.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());

            period.setTotalQuantityUnit(ECollectionPeriodCategory.BLINDBOX.getUnit());
            // todo 购买须知加入缓存
            period.setBuyNotice(
                    configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.COLLECTION_PERIOD_BLIND_BOX_BUY_NOTE));

            Integer buyCount = period.getBuyMax();
            if (null != operator) {
                Integer count = collectionBuyOrderService.getUserBuyCount(period.getId(), operator.getId());
                int extraCount = collectionPeriodExtraBuyChanceService.detailPeriodExtraChance(period.getId(), operator.getId());
                buyCount = buyCount - count + extraCount;
            }

            period.setBuyCount(buyCount);
        } else if (ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
            drawStrawsDeal(operator, period);
            // 报名须知
            // todo 报名须知加入缓存
            String joinNote = configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.PERIOD_DRAW_STRAWS_JOIN_NOTE);
            period.setJoinNote(joinNote);
            period.setTotalQuantityUnit(ECollectionPeriodCategory.DrawStraws.getUnit());
            // todo 购买须知加入缓存
            period.setBuyNotice(
                    configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.COLLECTION_PERIOD_DRAW_STRAWS_BUY_NOTE));

            List<CollectionPeriodWhiteJoin> whiteJoinList = collectionPeriodWhiteJoinService.list(period.getId());
            if (CollectionUtils.isNotEmpty(whiteJoinList)) {
                period.setWhiteFlag(EBoolean.YES.getCode());
            } else {
                period.setWhiteFlag(EBoolean.NO.getCode());
            }

        } else if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
            auctionDeal(operator, period);
            // 报名须知
            // todo 参拍须知加入缓存
            String auctionNote = configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.PERIOD_AUCTION_JOIN_NOTE);
            period.setAuctionNote(auctionNote);
            period.setTotalQuantityUnit(ECollectionPeriodCategory.PeriodAuction.getUnit());
            // todo 购买须知加入缓存
            period.setBuyNotice(
                    configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.COLLECTION_PERIOD_AUCTION_BUY_NOTE));
            period.setAuctionBondNote(
                    configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.PERIOD_AUCTION_AUCTION_BOND_NOTE));
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "未知类型");
        }

        //分享介绍
        // todo 分享介绍加入缓存
        period.setIntroduce(configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.SHARE_NOTE));
        //作废
//        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
//            period.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
//        }

        // todo 期数锁仓时间加缓存
//        period.setLockTime(collectionPeriodRelationService.getPeriodLockTime(period.getId()));
//        if (-1 != period.getLockTime()) {
//            period.setLockDay(period.getLockTime() / 24L);
//            period.setLockHour(period.getLockTime() % 24L);
//        }
        period.setTransformLimitTime(collectionPeriodRelationService.getPeriodTransformLimitTime(period.getId()));
        period.setLockTime(period.getTransformLimitTime());
        if (-1 != period.getTransformLimitTime()) {
            period.setLockDay(period.getTransformLimitTime() / 24L);
            period.setLockHour(period.getTransformLimitTime() % 24L);
        }

        //获取折扣值(根据用户的藏品id，获取最低折扣比例)
        PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                .getLowestByUserId(period.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
        if (null != periodDiscountDetail) {
            period.setDiscountPrice(
                    (period.getPrice().multiply(periodDiscountDetail.getDiscountRate())).setScale(2, BigDecimal.ROUND_DOWN));
            period.setDiscountRate(periodDiscountDetail.getDiscountRate());
        } else {
            period.setDiscountPrice(period.getPrice());
            period.setDiscountRate(BigDecimal.ONE);
        }
        //期数是衍生区，且rightType=3时，查询权益列表
        if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory()) ||
                ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
            if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(period.getRightType())) {
                //获取权限列表
                CollectionRightsDetailListFrontReq rightsDetailListFrontReq = new CollectionRightsDetailListFrontReq();
                rightsDetailListFrontReq.setCollectionId(collectionList.get(0).getId());
                period.setRightList(collectionRightsDetailService.listFrontNotRemain(rightsDetailListFrontReq, null));
            }
        } else if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(period.getCategory()) && (1 == collectionList.size())) {

            //获取权限列表
            CollectionRightsDetailListFrontReq rightsDetailListFrontReq = new CollectionRightsDetailListFrontReq();
            rightsDetailListFrontReq.setCollectionId(collectionList.get(0).getId());
            List<CollectionRightsDetailListRes> rightsDetailListRes = collectionRightsDetailService
                    .listFrontNotRemain(rightsDetailListFrontReq, null);
            if (CollectionUtils.isNotEmpty(rightsDetailListRes)) {
                period.setRightList(rightsDetailListRes);
                period.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
            }
        }

        period.setContractTokenList(contractTokenList);

        period.setSubscriptionFlag(EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_0.getCode());
        if (null != operator && ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(period.getStartSellDate());
            calendar.add(Calendar.MINUTE, -5);
            SubscriptionSend condition = new SubscriptionSend();
            condition.setRefType(ESubscriptionSendRefType.SUBSCRIPTION_SEND_REFTYPE_0.getCode());
            condition.setRefId(period.getId());
            condition.setUserId(operator.getId());

            List<SubscriptionSend> list = subscriptionSendService.list(condition);
            if (CollectionUtils.isNotEmpty(list)) {
                SubscriptionSend subscriptionSend = list.get(0);
                if (ESubscriptionSendStatus.SUBSCRIPTION_SEND_STATUS_2.getCode().equals(subscriptionSend.getStatus())) {
                    period.setSubscriptionFlag(EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_0.getCode());
                } else {
                    period.setSubscriptionFlag(EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_1.getCode());
                }
            } else {
                period.setSubscriptionFlag(EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_0.getCode());
            }

            if (EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_0.getCode().equals(period.getSubscriptionFlag())
                    && !new Date().before(calendar.getTime())) {
                period.setSubscriptionFlag(EPeriodSubscriptionSendStatus.E_PERIOD_SUBSCRIPTION_SEND_STATUS_2.getCode());
            }
        }

        // 设置截止时间戳
        period.setEndTime(0L);
        if (!ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
            if (null != period.getEndSellDate()) {
                long times = (period.getEndSellDate().getTime() - System.currentTimeMillis()) / 1000 * 1000;
                period.setEndTime(times);
                if (times <= 0) {
                    period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                }
            }
        }
        return period;
    }

    @Override
    public List<CollectionPeriodCollectionPageRes> periodCollectionPage(CollectionPeriodCollectionPageReq request) {
        CollectionPeriod period = detailsimple(request.getPeriodId());

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数还未上架");
        }

        if (!ECollectionPeriodCategory.BLINDBOX.getCode().equals(period.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数错误");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        List<CollectionPeriodCollectionPageRes> pageResList = collectionPeriodRelationService.detailListByPeriodId(period.getId(), null);
        List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();

        for (CollectionPeriodCollectionPageRes collectionRes : pageResList) {
            // 组装期数作品的价格、状态、文件

            collectionRes.setPrice(period.getPrice());
            ECollectionPeriodCollectionStatus eCollectionPeriodCollectionStatus = null;
            if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_0;
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
                if (collectionRes.getRemainQuantity() <= 0) {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
                } else {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_1;
                }
            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(period.getStartStatus())) {
                //区分已售罄或已售完
                if (collectionRes.getRemainQuantity() <= 0) {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
                } else {
                    eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_3;
                }

            } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStartStatus())) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
            }

            collectionRes.setStatus(eCollectionPeriodCollectionStatus.getCode());

            if (StringUtils.isNotBlank(collectionRes.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(collectionRes.getFileUrl(),
                        UploadFile.class);
                collectionRes.setFileList(uploadFiles);
            }
            collectionRes.setWordFlag(period.getWordFlag());

            BigDecimal rateDetail = new BigDecimal(collectionRes.getTotalQuantity())
                    .divide(new BigDecimal(period.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN).multiply(new BigDecimal(100));
            collectionRes.setRate(rateDetail.stripTrailingZeros());
            sellCollectionList.add(collectionRes);

        }

        return sellCollectionList;
    }


    private void auctionDeal(User operator, CollectionPeriodDetailFrontRes period) {
        String endSellDateNote = "竞拍成功即可体验藏品";
        period.setEndSellDateNote(endSellDateNote);

        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(period.getId());

        period.setBond(periodAuction.getBond());
        period.setStartPrice(periodAuction.getStartPrice());
        period.setPriceAuction(periodAuction.getPriceAuction());
        period.setAuctionTimes(periodAuction.getAuctionTimes());
        period.setLastUserId(periodAuction.getLastUserId());
        period.setCurrentPrice(periodAuction.getCurrentPrice());

        period.setDelayedMinutes(periodAuction.getDelayedSecond() / 60);

        if (null != operator) {
            List<Long> userIdList = periodAuctionBondRecordService.detailJoinData(period.getId());
            if (userIdList.contains(operator.getId())) {
                period.setAuctionFlag(EAuctionFlag.EAUCTION_FLAG_2.getCode());
            }
            if (EAuctionFlag.EAUCTION_FLAG_1.getCode().equals(period.getAuctionFlag())) {
                period.setAuctionOfferFlag(EAuctionOfferFlag.E_AUCTION_OFFER_FLAG_0.getCode());
            }
            if (periodAuction.getLastUserId().equals(operator.getId())) {
                period.setAuctionOfferFlag(EAuctionOfferFlag.E_AUCTION_OFFER_FLAG_1.getCode());
            }
        } else {
            period.setAuctionFlag(EAuctionFlag.EAUCTION_FLAG_0.getCode());
        }

        if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
            long times = (periodAuction.getDelayedTime().getTime() - System.currentTimeMillis()) / 1000 * 1000;
            period.setEndTime(times);
            if (times <= 0) {
                period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode().equals(periodAuction.getStatus())
                        && periodAuction.getAuctionTimes() == 0) {
                    periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_2.getCode());
                } else if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode().equals(periodAuction.getStatus())) {
                    periodAuction.setStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode());
                }
            }
        }

        period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS.getCode());
        if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
            period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode());
        } else if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_2.getCode().equals(periodAuction.getStatus())) {
            period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_2.getCode());
        } else if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_3.getCode().equals(periodAuction.getStatus())) {
            period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode());
            if (null != periodAuction.getLastUserId() && periodAuction.getLastUserId() > 0 && null != operator && periodAuction
                    .getLastUserId().equals(operator.getId())) {
                period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_3.getCode());
            }
        } else if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode().equals(periodAuction.getStatus())) {
            period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode());
            if (null != periodAuction.getLastUserId() && periodAuction.getLastUserId() > 0 && null != operator && periodAuction
                    .getLastUserId().equals(operator.getId())) {
                period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_5.getCode());
                if (periodAuction.getAlreadyPayAmount().compareTo(BigDecimal.ZERO) > 0
                        && periodAuction.getAlreadyPayAmount().compareTo(periodAuction.getCurrentPrice()) < 0) {
                    period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_6.getCode());
                }
            }
        } else if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_4.getCode().equals(periodAuction.getStatus())) {
            period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode());
            // 中拍人
            if (null != periodAuction.getLastUserId() && periodAuction.getLastUserId() > 0 && null != operator && periodAuction
                    .getLastUserId().equals(operator.getId())) {
                period.setAuctionStatus(EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_4.getCode());

            }
        }

        period.setNextPrice(periodAuction.getCurrentPrice().add(periodAuction.getPriceAuction()));
        if (periodAuction.getAuctionTimes() == 0) {
            period.setNextPrice(periodAuction.getStartPrice());
        }
    }

    private void drawStrawsDeal(User operator, CollectionPeriodDetailFrontRes period) {
        String endSellDateNote =
                com.std.common.utils.DateUtil.dateToStr(period.getEndSellDate(), "yyyy年MM月dd日 ahh点") + "结束报名";

        Integer otherJoinCount = 0;
        if (!ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
            period.setJoinTotalNumber(collectionPeriodJoinRecordService.getJoinTotalNumber(period.getId()));
            if (null != operator) {
                // 获取用户优先购可抽签次数
                otherJoinCount = collectionPeriodPriorityBuyService
                        .getOtherJoinCount(period.getId(), period.getPriorityAddQuantityFlag(), operator.getId());
                // 获取用户优先购已抽签次数
                Integer alreadyJoinCount = collectionPeriodJoinRecordService
                        .getAlreadyJoinCount(period.getId(), operator.getId());
                otherJoinCount = otherJoinCount - alreadyJoinCount;
            }
        }
        if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(period.getStartStatus())) {
            period.setDrawStrawsFlage(ECollectionPeriodDrawStrawsFlag.TWO.getCode());

            if (null != operator) {
                String drawStrawsFlag = ECollectionPeriodDrawStrawsFlag.TWO.getCode();
                String flagRedis = String.format(RedisKeyList.MT_PERIOD_DRAW_STRAWS_FLAG_KEY, period.getId().toString(), operator.getId());
                // todo 中签标识信息加入缓存
                if (redisUtil.hasKey(flagRedis)) {
                    drawStrawsFlag = redisUtil.get(flagRedis).toString();
                } else {
                    Integer alreadyJoinCount = collectionPeriodJoinRecordService
                            .getAlreadyJoinCount(period.getId(), operator.getId());
                    if (alreadyJoinCount > 0) {
                        // 判断用户是否中签
                        Integer successfulCount = collectionPeriodJoinRecordService
                                .getSuccessfulCount(period.getId(), operator.getId());
//                                    res.setDrawStrawsFlage(ECollectionPeriodDrawStrawsFlag.ZERO.getCode());
                        drawStrawsFlag = ECollectionPeriodDrawStrawsFlag.ZERO.getCode();
                        if (successfulCount > 0) {
//                                        res.setDrawStrawsFlage(ECollectionPeriodDrawStrawsFlag.ONE.getCode());
                            drawStrawsFlag = ECollectionPeriodDrawStrawsFlag.ONE.getCode();
                        }
                    }
                    redisUtil.set(flagRedis, drawStrawsFlag, redisLockTime);
                }

                period.setDrawStrawsFlage(drawStrawsFlag);
            }

            endSellDateNote = "中签名单已公布";
        }
        period.setEndSellDateNote(endSellDateNote);
        period.setBuyCount((period.getBuyMax() + otherJoinCount) < 0 ? 0 : (period.getBuyMax() + otherJoinCount));

        period.setPlayNote(period.getPlayNote());
    }

    private boolean copyrightPeriodCollection(CollectionPeriodCollectionPageRes res,
                                              List<CollectionPeriodCollectionPageRes> sellCollectionList,
                                              List<CollectionPeriodCollectionPageRes> showCollectionList,
                                              User operator,
                                              boolean copyRightSelledFlag) {
        if (ECollectionPeriodRelationCategory.SELL.getCode().equals(res.getCategory())) {
            sellCollectionList.add(res);

            //有售卖中的，这期时刻就不是已售罄;或者待开售
            if ((copyRightSelledFlag && ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_1.getCode()
                    .equals(res.getStatus()))
                    || ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_0.getCode()
                    .equals(res.getStatus())) {
                copyRightSelledFlag = false;
            }
        } else {
            res.setShowName("隐藏");
            showCollectionList.add(res);
        }

        return copyRightSelledFlag;
    }

    @NotNull
    private void assignPeriodCollection(CollectionPeriodDetailFrontRes period,
                                        CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes) {
        collectionPeriodCollectionPageRes.setPrice(period.getPrice());
        ECollectionPeriodCollectionStatus eCollectionPeriodCollectionStatus = null;
        if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
            eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_0;
        } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
            if (collectionPeriodCollectionPageRes.getRemainQuantity() <= 0) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
            } else {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_1;
            }
        } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(period.getStartStatus())) {
            //区分已售罄或已售完
            if (collectionPeriodCollectionPageRes.getRemainQuantity() <= 0) {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
            } else {
                eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_3;
            }

        } else if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStartStatus())) {
            eCollectionPeriodCollectionStatus = ECollectionPeriodCollectionStatus.COLLECTION_PERIOD_COLLECTION_STATUS_2;
        }

        collectionPeriodCollectionPageRes.setStatus(eCollectionPeriodCollectionStatus.getCode());

        if (StringUtils.isNotBlank(collectionPeriodCollectionPageRes.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collectionPeriodCollectionPageRes.getFileUrl(),
                    UploadFile.class);
            collectionPeriodCollectionPageRes.setFileList(uploadFiles);
        }
    }

    @Nullable
    private List<CollectionPeriodCollectionPageRes> getPeriodCollection(Long periodId, String category) {
        List<CollectionPeriodCollectionPageRes> collectionList = new ArrayList<>();

        if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(category)) {
            String redisId = String.format(RedisKeyList.MT_PERIOD_RELATION_LIST_KEY, periodId.toString());
            if (redisUtil.hasKey(redisId)) {
                JSONArray tableData = JSONArray.parseArray(JSON.toJSONString(redisUtil.get(redisId)));
                collectionList = JSONObject.parseArray(tableData.toJSONString(), CollectionPeriodCollectionPageRes.class);

            } else {
                collectionList = collectionPeriodRelationMapper.selectListByPeriodId(periodId, null);
                redisUtil.set(redisId, collectionList, redisLockTime);
            }
        }
        if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(category)) {
            Integer collectionNumber = configService.getIntegerValue(SysConstants.PERIOD_BLIND_SHOW_MAX_NUMBER);
            collectionList = collectionPeriodRelationMapper.selectListByPeriodId(periodId, collectionNumber);
        } else {
            collectionList = collectionPeriodRelationMapper.selectListByPeriodId(periodId, null);
        }

        return collectionList;
    }

    private List<CollectionPeriodCollectionPageResRelation> getPeriodCollectionRelation(List<Long> periodIds) {
        List<CollectionPeriodCollectionPageResRelation> collectionRelationList = collectionPeriodRelationMapper
                .selectListByPeriodIds(periodIds);
        return collectionRelationList;
    }

    private void createAuthor(CollectionPeriodDetailFrontRes period) {

        List<CompanyInfo> authorList = new ArrayList<>();
        String companyIdCacheKey = String.format(RedisKeyList.MT_COMPANY_KEY, period.getAuthorIds());
        Company company;
        if (redisUtil.hasKey(companyIdCacheKey)) {
            company = JSON.parseObject(JSON.toJSONString(redisUtil.get(companyIdCacheKey)), Company.class);
        } else {
            company = companyService.detail(period.getAuthorIds());
            redisUtil.set(companyIdCacheKey, company);
        }
        CompanyInfo companyInfo = new CompanyInfo();
        BeanUtils.copyProperties(company, companyInfo);
        companyInfo.setName(company.getShortName());
        authorList.add(companyInfo);

        period.setAuthorId(company.getId());
        period.setAuthor(company.getShortName());
        period.setAuthorPic(company.getLogo());

        period.setAuthorList(authorList);

    }

    private void createAuthor(CollectionPeriodPageFrontRes period) {
        if (StringUtils.isNotBlank(period.getAuthorIds())) {
            List<String> list = Arrays.asList(period.getAuthorIds().split(","));
            List<CompanyInfo> authorList = new ArrayList<>();
            for (String authorId : list) {
                String companyIdCacheKey = String.format(RedisKeyList.MT_COMPANY_KEY, authorId);
                Company company;
                if (redisUtil.hasKey(companyIdCacheKey)) {
                    company = JSON.parseObject(JSON.toJSONString(redisUtil.get(companyIdCacheKey)), Company.class);
                } else {
                    company = companyService.detail(Long.valueOf(authorId));
                    redisUtil.set(companyIdCacheKey, company);
                }
                CompanyInfo companyInfo = new CompanyInfo();
                BeanUtils.copyProperties(company, companyInfo);
                companyInfo.setName(company.getShortName());
                authorList.add(companyInfo);

                period.setAuthorId(company.getId());
                period.setAuthor(company.getShortName());
                period.setAuthorPic(company.getLogo());
            }
            period.setAuthorList(authorList);
        }
    }

    private void createAuthor(CollectionPeriodListRes period) {
        if (StringUtils.isNotBlank(period.getAuthorIds())) {
            List<String> list = Arrays.asList(period.getAuthorIds().split(","));
            List<CompanyInfo> authorList = new ArrayList<>();
            for (String authorId : list) {
                String companyIdCacheKey = String.format(RedisKeyList.MT_COMPANY_KEY, authorId);
                Company company;
                if (redisUtil.hasKey(companyIdCacheKey)) {
                    company = JSON.parseObject(JSON.toJSONString(redisUtil.get(companyIdCacheKey)), Company.class);
                } else {
                    company = companyService.detail(Long.valueOf(authorId));
                    redisUtil.set(companyIdCacheKey, company);
                }
                CompanyInfo companyInfo = new CompanyInfo();
                BeanUtils.copyProperties(company, companyInfo);
                companyInfo.setName(company.getShortName());
                authorList.add(companyInfo);
                period.setAuthorId(company.getId());
                period.setAuthor(company.getShortName());
                period.setAuthorPic(company.getLogo());
            }
            period.setAuthorList(authorList);
        }
    }

    private void checkAdvanceMins(User operator, CollectionPeriodDetailFrontRes period) {
        int advanceMins = 0;

        if (null != operator && !ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
            advanceMins = collectionPeriodPriorityBuyService.getMaxAdvanceMins(period.getId(), operator);

            if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
                if (collectionPeriodPriorityBuyService.checkExist(period.getId(), operator)) {
                    advanceMins = 0;
                    period.setAuctionFlag(EAuctionFlag.EAUCTION_FLAG_1.getCode());
                }
            }
        }

        Date startDate = DateUtil.addMinutes(period.getStartSellDate(), -advanceMins);
        long times = (startDate.getTime() - System.currentTimeMillis()) / 1000 * 1000;
        period.setStartTime(times);
        period.setAdvanceMins(advanceMins);

        //正常时间到了，且状态是待开始的；变更状态为售卖中，其他情况不处理
        if (times <= 0) {
            //回写发售状态
            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
        }
    }

    private void checkAdvanceMins(User operator, CollectionPeriodPageFrontRes period) {
        int advanceMins = 0;
        if (null != operator) {
//            if (collectionPeriodPriorityBuyService.checkExist(period.getId(), operator)) {
//                advanceMins = period.getAdvanceMins();
//            }
            advanceMins = collectionPeriodPriorityBuyService.getMaxAdvanceMins(period.getId(), operator);
        }

        Date startDate = DateUtil.addMinutes(period.getStartSellDate(), -advanceMins);
        long times = (startDate.getTime() - System.currentTimeMillis()) / 1000 * 1000;
        period.setStartTime(times);
        period.setAdvanceMins(advanceMins);

        //正常时间到了，且状态是待开始的；变更状态为售卖中，其他情况不处理
        if (times <= 0) {
            //回写发售状态
            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
        }
    }

    private void checkAdvanceMins(User operator, CollectionPeriodListRes period) {
        int advanceMins = 0;
        if (null != operator && !ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())
                && !ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
            advanceMins = collectionPeriodPriorityBuyService.getMaxAdvanceMins(period.getId(), operator);
//            if (collectionPeriodPriorityBuyService.checkExist(period.getId(), operator)) {
//
//                advanceMins = period.getAdvanceMins();
//            }
        }

        Date startDate = DateUtil.addMinutes(period.getStartSellDate(), -advanceMins);
        long times = (startDate.getTime() - System.currentTimeMillis()) / 1000 * 1000;
        period.setStartTime(times);
        period.setAdvanceMins(advanceMins);

        //正常时间到了，且状态是待开始的；变更状态为售卖中，其他情况不处理
        if (times <= 0) {
            //回写发售状态
            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
        }
    }

    /**
     * 前端列表查询作品期数
     *
     * @param req 前端列表查询作品期数入参
     * @return 列表作品期数对象
     */
    @Override
    public List<CollectionPeriodListRes> listFront(CollectionPeriodListFrontReq req, User operator, Long channelId) {
        String countRedisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_LIST_COUNT_KEY, channelId);
        Integer integer = null;
        if (redisUtil.hasKey(countRedisKey)) {
            integer = Integer.valueOf(redisUtil.get(countRedisKey).toString());
        }

        CollectionPeriod condition = EntityUtils.copyData(req, CollectionPeriod.class);
        condition.setChannelId(channelId);
        condition.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
        condition.setKeywords(req.getKeywords());
        if (StringUtils.isNotBlank(req.getCategoryId())) {
            if (ECollectionPeriodStatusCategory.ZERO.getCode().equals(req.getCategoryId())) {
                condition.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_0.getCode());
            } else if (ECollectionPeriodStatusCategory.ONE.getCode().equals(req.getCategoryId())) {
                condition.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
            }
        }
        condition.setOrderBy("t.start_status = '1' desc,t.start_status ASC, t.order_no desc");

        List<CollectionPeriodListRes> resList;
        //无分类缓存
        String redisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_LIST_KEY_CHANNEL, channelId.toString());
        if (StringUtils.isBlank(req.getPlateCategory())) {
            if (redisUtil.hasKey(redisKey)) {
                resList = JSON.parseArray(JSON.toJSONString(redisUtil.get(redisKey)), CollectionPeriodListRes.class);
                if (null != integer && integer != resList.size()) {
                    resList = collectionPeriodMapper.selectFrontList(condition);
                    redisUtil.set(redisKey, JSON.toJSON(resList));
                }
            } else {
                resList = collectionPeriodMapper.selectFrontList(condition);
                redisUtil.set(redisKey, JSON.toJSON(resList));
            }
        } else {
            //有分类不缓存
            resList = collectionPeriodMapper.selectFrontList(condition);
        }

        if (CollectionUtils.isNotEmpty(resList)) {
            List<Long> periodIds = resList.stream().map(CollectionPeriodListRes::getId).collect(Collectors.toList());
            List<CollectionPeriodCollectionPageResRelation> collectionRelationList = getPeriodCollectionRelation(periodIds);

            resList.forEach(period -> {
                //不是待开始状态，前端不用显示提前分钟数
                if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {
                    checkAdvanceMins(operator, period);
                } else {
                    period.setAdvanceMins(0);
                }

                if (StringUtils.isNotBlank(period.getTags())) {
                    List<String> result = new ArrayList<>();
                    if (EBoolean.YES.getCode().equals(period.getWordFlag())) {
                        result.add(ECollectionPeriodWordFlag.WORD.getValue());
                    }
                    result.addAll(Arrays.asList(period.getTags().split(",")));
                    period.setTagList(result);
                }

                // 生成机构
                createAuthor(period);
                // 获取期数作品
                List<CollectionPeriodCollectionPageResRelation> relationList = collectionRelationList.stream()
                        .filter(item -> item.getPeriodId().equals(period.getId())).collect(Collectors.toList());

                period.setLockTime(relationList.get(0).getLockTime());
                // 版权区的期数
                if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.COPYRIGHT.getUnit());
                    //售卖中的
                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
                        // todo 期数作品加入缓存
//                    List<CollectionPeriodCollectionPageRes> collectionPeriodCollectionPageResList = collectionPeriodRelationMapper.selectListByPeriodId(period.getId());
                        // 获取期数作品
//                    List<CollectionPeriodCollectionPageRes> collectionList = getPeriodCollection(period.getId());

                        // 拉取列表，根据每个藏品判断是否是vip指定的；有vip指定的进内部判断，如无则按正常逻辑
                        // 有vip指定且还未被购买的逻辑：用户如果未登录，剩余量减1；如果已登录且不是指定用户，剩余量减1；
                        // 最终根据剩余量来判断状态
                        int remainQuantity = period.getRemainQuantity();

                        if (remainQuantity <= 0) {
                            period.setSoldStatus(ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode());
                            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                        }
                    }
                } else if (ECollectionPeriodCategory.DrawStraws.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
                    Integer joinTotalNumber = collectionPeriodJoinRecordService.getJoinTotalNumber(period.getId());
                    if (null == joinTotalNumber || joinTotalNumber == 0) {
                        joinTotalNumber = collectionPeriodJoinRecordService.getJoinTotalNumberHistory(period.getId());
                    }
                    period.setTotalDrawStrawsCount(joinTotalNumber);

                    // 如果时间到了就修改状态
                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus()) && new Date()
                            .after(period.getEndSellDate())) {
                        period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                    }

                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(period.getStartStatus())
                            || ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStartStatus())) {
//                        // todo 中签人数加入缓存
//                        String redisId = String.format(RedisKeyList.MT_PERIOD_DRAW_STRAWS_COUNT_KEY, period.getId().toString());
//                        if (redisUtil.hasKey(redisId)) {
//                            Integer joinNumber = Integer.valueOf(redisUtil.get(redisId).toString());
//                            if (joinNumber <= 0) {
//                                joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumber(period.getId());
//                                redisUtil.set(redisId, joinNumber, redisLockTime);
//                            }
//                            period.setDrawStrawsCount(joinNumber);
//
//                        } else {
//                            Integer joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumber(period.getId());
//                            period.setDrawStrawsCount(joinNumber);
//                            redisUtil.set(redisId, joinNumber, redisLockTime);
//                        }

                        // todo 中签人数加入缓存
                        String redisId = String.format(RedisKeyList.MT_PERIOD_DRAW_STRAWS_COUNT_KEY, period.getId().toString());
                        if (redisUtil.hasKey(redisId)) {
                            Integer joinNumber = Integer.valueOf(redisUtil.get(redisId).toString());
                            if (joinNumber <= 0) {
                                joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumber(period.getId());
                                if (null == joinNumber || joinNumber == 0) {
                                    joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumberHistory(period.getId());
                                }
                                redisUtil.set(redisId, joinNumber, redisLockTime);
                            }
                            period.setDrawStrawsCount(joinNumber);
                        } else {
                            Integer joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumber(period.getId());
                            if (null == joinNumber || joinNumber == 0) {
                                joinNumber = collectionPeriodJoinRecordService.getJoinDrawStrawsNumberHistory(period.getId());
                            }
                            period.setDrawStrawsCount(joinNumber);
                            redisUtil.set(redisId, joinNumber, redisLockTime);
                        }
                    }

                } else if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
                    if (!ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStartStatus())) {

                        // todo 加缓存
                        PeriodAuction periodAuction = periodAuctionService.detailByPeriod(period.getId());
                        long times = (periodAuction.getDelayedTime().getTime() - System.currentTimeMillis()) / 1000 * 1000;
                        period.setEndTime(times);
                        if (times < 0) {
                            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());

                            if (!EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_0.getCode().equals(periodAuction.getStatus())) {
                                CollectionPeriod collectionPeriod = new CollectionPeriod();
                                collectionPeriod.setId(period.getId());
                                collectionPeriod.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                                collectionPeriod.setUpdateDatetime(new Date());
                                modify(collectionPeriod);

                                redisUtil.del(redisKey);
                            }

                        }
                        // 当前价格
                        period.setPrice(periodAuction.getCurrentPrice());

                        if (null != operator && EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode().equals(periodAuction.getStatus())
                                && operator.getId().equals(periodAuction.getLastUserId())) {
                            period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_3.getCode());
                        }
                    }

                } else {
                    period.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());

                }

                initEndTime(period);

                period.setDiscountPrice(period.getPrice());
                period.setDiscountRate(BigDecimal.ONE);
                if (null != operator) {
                    //获取折扣值(根据用户的藏品id，获取最低折扣比例)
                    PeriodDiscountDetail periodDiscountDetail = periodDiscountDetailService
                            .getLowestByUserId(period.getId(), EPeriodDiscountRefType.PERIOD.getCode(), operator);
                    if (null != periodDiscountDetail) {
                        period.setDiscountPrice(
                                (period.getPrice().multiply(periodDiscountDetail.getDiscountRate()))
                                        .setScale(2, BigDecimal.ROUND_DOWN));
                        period.setDiscountRate(periodDiscountDetail.getDiscountRate());
                    } else {
                        period.setDiscountPrice(period.getPrice());
                        period.setDiscountRate(BigDecimal.ONE);
                    }
                }


            });
        }

        return resList;
    }

    private void initEndTime(CollectionPeriodListRes period) {
        period.setEndTime(0L);
        if (!ECollectionPeriodCategory.PeriodAuction.getCode().equals(period.getCategory())) {
            if (null != period.getEndSellDate()) {
                long times = (period.getEndSellDate().getTime() - System.currentTimeMillis()) / 1000 * 1000;
                period.setEndTime(times);
                if (times <= 0) {
                    period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                }
            }
        }
    }

    @Override
    public List<CollectionPeriod> selectPeriodDrawStrawsEnd() {
        return collectionPeriodMapper.selectPeriodDrawStrawsEnd(new Date());
    }

    /**
     * 处理一级市场抽签区结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPeriodDrawStrawsEndTrigger(CollectionPeriod period) {
        lockService.create(ELockBizType.DRAW_STRAWS_END.getCode(), period.getId().toString());

        period = collectionPeriodMapper.selectByPrimaryKeyForUpdate(period.getId());
        // 不是待上架且不是售卖中的期数不做处理
        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())
                && !ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
            return;
        }

        CollectionPeriodRelation condition = new CollectionPeriodRelation();
        condition.setPeriodId(period.getId());
        CollectionPeriodRelation periodRelation = collectionPeriodRelationService.list(condition).get(0);
        // 结算白名单的份数
        int whiteNumber = collectionPeriodJoinRecordService.doPeriodDrawStrawsEndWhite(period, periodRelation.getCollectionId());

        // 结算优先购的份数
        int priorityWhiteNumber = collectionPeriodJoinRecordService
                .selectTotalCount(period.getId(), EBoolean.YES.getCode(), EBoolean.YES.getCode());
        int needPriorityNumber = period.getPriorityNumber() - priorityWhiteNumber;
        if (needPriorityNumber < 0) {
            needPriorityNumber = 0;
        }
        int priorityNumber = collectionPeriodJoinRecordService
                .doPeriodDrawStrawsEnd(needPriorityNumber, period, periodRelation.getCollectionId(), EBoolean.YES.getCode());
        // 结算剩余份数
        int remainNumber = period.getTotalQuantity() - whiteNumber - priorityNumber;
        if (remainNumber < 0) {
            remainNumber = 0;
        }

        int number = collectionPeriodJoinRecordService
                .doPeriodDrawStrawsEnd(remainNumber, period, periodRelation.getCollectionId(),
                        null);
        // 处理剩余没中签的
        collectionPeriodJoinRecordService.doPeriodDrawStrawsMiss(period.getId());

        // 期数关联作品数量修改
        period.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_4.getCode());
        period.setRemainQuantity(period.getRemainQuantity() - (whiteNumber + priorityNumber + number));
        period.setSoldStatus(period.getRemainQuantity() == 0 ? ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode() : null);
        period.setUpdateDatetime(new Date());
        collectionPeriodMapper.updateByPrimaryKeySelective(period);

        periodRelation.setBuyQuantity((whiteNumber + priorityNumber + number));
        int effectCountRelation = collectionPeriodRelationMapper.updateRemainQuantity(periodRelation);
        if (effectCountRelation <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数藏品剩余数量不足");
        }
    }

    @Override
    public CollectionPeriod detailsimple(Long id) {
        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyOss(id);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return collectionPeriod;
    }

    @Override
    public CollectionPeriod detailForUpdate(Long id) {
        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyForUpdate(id);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return collectionPeriod;
    }

    @Override
    public List<Long> selectPeriodStart(CollectionPeriod condition) {
        return collectionPeriodMapper.selectPeriodStart(condition);
    }

    @Override
    public void modify(CollectionPeriod detailsimple) {
        collectionPeriodMapper.updateByPrimaryKeySelective(detailsimple);
    }

    @Override
    public Integer doPeriodDrawStrawsEndModifyStatus(CollectionPeriod period) {
        Integer count = collectionPeriodJoinRecordService.detailNoDealJoinRecord(period.getId());

        if (count == 0) {
            CollectionPeriod collectionPeriod = selectForUpdate(period.getId());
            collectionPeriod.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
            collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);
            return 1;
        }
        return 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doBuyByCompany(CollectionPeriodOssBuyReq request, User operator) {
        CollectionPeriod period = collectionPeriodMapper.selectByPrimaryKeyForUpdate(request.getId());
        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), period.getName() + "期数不是已上架");
        }
//        if (!ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStartStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), period.getName() + "期数不是售卖中");
//        }
        if (!ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), period.getName() + "期数不是衍生期期数");
        }
        CollectionPeriodRelation collectionPeriodRelation = collectionPeriodRelationService.list(period.getId()).get(0);

        //产生订单
        CollectionBuyOrder collectionBuyOrder = new CollectionBuyOrder();
        BigDecimal total = (period.getPrice().multiply(new BigDecimal(request.getQuantity())))
                .setScale(2, BigDecimal.ROUND_DOWN);
        collectionBuyOrder.setBizType(ECollectionBuyOrderBizType.PERIOD.getCode());
        collectionBuyOrder.setBizId(period.getId());
        collectionBuyOrder.setUserId(new Long(period.getAuthorIds()));
        collectionBuyOrder.setCollectionId(collectionPeriodRelation.getCollectionId());
        collectionBuyOrder.setQuantity(request.getQuantity());
        collectionBuyOrder.setPrice(period.getPrice());
        collectionBuyOrder.setDiscountPrice(period.getPrice());
        collectionBuyOrder.setPayAmount(total);
        collectionBuyOrder.setCreateTime(new Date());
        collectionBuyOrder.setPayType(EPayType.TO_COMPANY_BUY.getId());
        collectionBuyOrder.setPayOrderCode(IdGeneratorUtil.generator().toString());

        collectionBuyOrder.setPayBalanceAmount(BigDecimal.ZERO);
        collectionBuyOrder.setPayCashAmount(BigDecimal.ZERO);
        collectionBuyOrder.setPayStatus(ECollectionBuyOrderPayStatus.COLLECTION_BUY_ORDER_PAYSTATUS_3.getCode());
        collectionBuyOrder.setPayDatetime(new Date());
        collectionBuyOrder.setChannelId(period.getChannelId());
        collectionBuyOrderMapper.insertSelective(collectionBuyOrder);

        // 更新期数剩余量
        period.setBuyQuantity(request.getQuantity());
        int effectCount = collectionPeriodMapper.updateRemainQuantity(period);
        if (effectCount <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数剩余数量不足");
        }

        // 更新期数作品剩余量
        collectionPeriodRelation.setBuyQuantity(request.getQuantity());
        int effectCountRelation = collectionPeriodRelationMapper.updateRemainQuantity(collectionPeriodRelation);
        if (effectCountRelation <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数剩余数量不足");
        }
    }

    @Override
    public List<ChannelMerchant> listChannelMerchant(User operator) {
        List<Long> channelIdList = collectionPeriodMapper.selectChannelMerchant(operator.getCompanyId());

        if (CollectionUtils.isEmpty(channelIdList)) {
            channelIdList = companyChannelService.listByCompany(operator.getCompanyId());
        } else {
            List<Long> channelIdListNew = companyChannelService.listByCompany(operator.getCompanyId());
            channelIdList.addAll(channelIdListNew);
        }

        ChannelMerchant condition = new ChannelMerchant();
        condition.setIdList(channelIdList);

        return channelMerchantService.list(condition);
    }

    @Override
    public List<CollectionPeriodPlateListRes> listByPlate(Long channelId, String categoryType) {
        //获取8大板块
        Category condition = new Category();
        condition.setType(categoryType);
        //isall 有值不展示全部
        condition.setIsAll(EBoolean.YES.getCode());
        condition.setOrderBy("order_no desc");
        List<CollectionPeriodCategoryRes> list = list(condition);

        List<CollectionPeriodPlateListRes> resultList = new ArrayList<>();

        List<CollectionPeriodPlateListRes> sellingPlateList = new ArrayList<>();
        List<CollectionPeriodPlateListRes> soldPlateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (CollectionPeriodCategoryRes collectionPeriodCategoryRes : list) {
                //获取即将开始/正在进行中的上架期数
                List<CollectionPeriodByPlateRes> periodList = selectList(channelId, collectionPeriodCategoryRes.getId());
                if (CollectionUtils.isNotEmpty(periodList)) {
                    CollectionPeriodByPlateRes collectionPeriodByPlateRes = periodList.get(0);
                    CollectionPeriodPlateListRes result = new CollectionPeriodPlateListRes(collectionPeriodCategoryRes.getId(),
                            collectionPeriodCategoryRes.getName(), periodList, periodList.size(),
                            collectionPeriodByPlateRes.getStartSellDate());
                    if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode()
                            .equals(collectionPeriodByPlateRes.getStartStatus())) {
                        sellingPlateList.add(result);
                    } else {
                        soldPlateList.add(result);
                    }
                }
            }
        }

        sellingPlateList.sort(Comparator
                .comparing(CollectionPeriodPlateListRes::getLastStartSellDate, Comparator.nullsFirst(Comparator.reverseOrder())));

        resultList.addAll(sellingPlateList);
        resultList.addAll(soldPlateList);

        return resultList;
    }

    @Override
    public List<CategoryListRes> listCompanyCategoryFront(Long authorId) {
        CategoryListFrontReq request = new CategoryListFrontReq();
        request.setType(ECategoryType.TYPE_9.getCode());
        List<CategoryListRes> list = categoryService.listFront(request);
        if (CollectionUtils.isNotEmpty(list)) {
            for (CategoryListRes categoryListRes : list) {
                //售卖中
                if (EBoolean.NO.getCode().equals(categoryListRes.getId())) {
                    Integer count = collectionPeriodMapper.selectIngCountByAuthorIds(authorId);
                    if (count == 0) {
                        list.remove(categoryListRes);
                        break;
                    }
                }
            }
        }

        return list;
    }

    @Override
    public List<CollectionPeriodCategoryRes> list(Category req) {
        List<Category> categoryList = categoryMapper.selectByCondition(req);

        List<CollectionPeriodCategoryRes> resultList = new ArrayList<>();
        for (Category category : categoryList) {
            Integer count = collectionPeriodMapper.selectOnlineCountByPlateCategory(category.getNameKey());
            if (count > 0) {
                CollectionPeriodCategoryRes res = new CollectionPeriodCategoryRes();
                res.setId(category.getNameKey());
                res.setName(category.getName());
                res.setBizType(category.getBizType());
                resultList.add(res);
            }
        }

        return resultList;
    }

    /**
     * 修改期数板块
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyPlateCategory(Long collectionId, String plateCategory) {
        CollectionPeriodRelation condition = new CollectionPeriodRelation();
//        condition.setCategory(ECollectionPeriodCategory.DERIVATIVE.getCode());
        condition.setCollectionId(collectionId);
        List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(condition);

        for (CollectionPeriodRelation relation : list) {
            CollectionPeriod collectionPeriod = detailsimple(relation.getPeriodId());

            if (ECollectionPeriodCategory.DERIVATIVE.getCode().equals(collectionPeriod.getCategory())) {
                CollectionPeriod period = new CollectionPeriod();
                period.setId(relation.getPeriodId());
                period.setPlateCategory(plateCategory);
                collectionPeriodMapper.updateByPrimaryKeySelective(period);
            }
        }
    }

    /**
     * 新增衍生区并生成作品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodAndCollectionCreateDeriveReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        // 生成作品
        Collection collection = new Collection();
        Integer quantity = 0;
        if (null != req.getCollectionCreatePlatformReq()) {
            req.getCollectionCreatePlatformReq().setCategory(ECollectionCategory.DERIVATIVE.getCode());
            collection = collectionService.createCollectionCompanyPlatform(req.getCollectionCreatePlatformReq(), operator,
                    ECollectionPeriodCategory.DERIVATIVE.getCode());
            if (null == req.getCollectionCreatePlatformReq().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写发行数量");
            }
            quantity = req.getCollectionCreatePlatformReq().getQuantity();
        } else if (null != req.getCollectionInfo()) {
            collection = collectionService
                    .modifyCollectionCompanyPlatform(req.getCollectionInfo(), operator, ECollectionPeriodCategory.DERIVATIVE.getCode());

            if (null == req.getCollectionInfo().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写发行数量");
            }
            quantity = req.getCollectionInfo().getQuantity();
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择作品");
        }

        if (!ECollectionCategory.DERIVATIVE.getCode().equals(collection.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是衍生区作品");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "非藏品作品不能发售");
        }

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.DERIVATIVE.getCode());
        collectionPeriod.setPrice(req.getPrice());

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collection.getBuyType()) && CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collection.getBuyType()) && CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "未设置口令");
        }

        init(collection, collectionPeriod);

        collectionPeriod.setWordFlag(collection.getBuyType());
        if (collection.getRemainQuantity() < quantity) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "发售数量大于总数量");
        }

        collectionPeriod.setTotalQuantity(quantity);
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());

        //设置发售时间
        initSellDate(req.getStartSellDate(), req.getEndSellDate(), collectionPeriod);
        collectionPeriod.setCollectionNumber(1);

        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());
        collectionPeriodMapper.insertSelective(collectionPeriod);

        //新增藏品关系
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(collection.getId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(quantity);
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(collectionPeriod, req.getDiscountCollectionList(), operator);

        //更新空投
        dropCreate(collectionPeriod, req.getCollectionPeriodDropList());

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    private void initSellDate(String startSellDateStr, String endSellDateStr, CollectionPeriod collectionPeriod) {
        Date date = new Date();

        //开始时间设置
        Date startSellDate = DateUtil.strToDate(startSellDateStr, DateUtil.DATA_TIME_PATTERN_2);
        if (null == startSellDate) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始发售时间不能为空");
        }
        collectionPeriod.setStartSellDate(startSellDate);
        if (collectionPeriod.getStartSellDate().before(date)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "开始发售时间早于当前时间");
        }

        //结束时间设置
        Date endSellDate = DateUtil.strToDate(endSellDateStr, DateUtil.DATA_TIME_PATTERN_2);
        if (null == endSellDate) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "截止发售时间不能为空");
        }
        collectionPeriod.setEndSellDate(endSellDate);
        int maxSellDay = configService.getIntegerValue(SysConstants.COLLECTION_PERIOD_SELL_MAX_DAY);

        // 期数发布后在线销售最大天数
        Date endMaxSellDate = DateUtil.getRelativeDateOfDays(startSellDate, maxSellDay);
        if (endMaxSellDate.getTime() < endSellDate.getTime()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "截止发售时间最晚" + DateUtil.dateToStr(endMaxSellDate, com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2));
        }

    }

    private void dropCreate(CollectionPeriod collectionPeriod, List<CollectionPeriodDropReq> collectionPeriodDropReqList) {
        collectionRightRecordService
                .removeDropByrRefId(collectionPeriod.getId(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode(),
                        ECollectionRightDropType.E_COLLECTION_RIGHT_DROP_TYPE_0.getCode());

        if (null == collectionPeriodDropReqList) {
            return;
        }
        for (CollectionPeriodDropReq collectionReq : collectionPeriodDropReqList) {
            CollectionRightCompany rightCompany = collectionRightCompanyService.detail(collectionReq.getRightId());
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(rightCompany.getRefId());
            Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
            if (!ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode().equals(rightCompany.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                        collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                .getValue() + "权益未开启");
            }
            if (!ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(rightsDetail.getType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "权益错误");
            }
            if ((0L != rightCompany.getCompanyId() && !rightCompany.getCompanyId().equals(collectionPeriod.getAuthorIds()))
                    || (!EBoolean.NO.getCode().equals(rightCompany.getPlateCategory()) && !rightCompany.getPlateCategory()
                    .equals(collectionPeriod.getPlateCategory()))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                        collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                .getValue() + "权益不可用");
            }
            if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                    .equals(rightsDetail.getNumberFlag())
                    && 1 > rightsDetail.getRemainNumber()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                        collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                .getValue() + "权益已用完");
            }

            // 生成权益使用记录
            CollectionRightRecord rightRecord = new CollectionRightRecord();
            rightRecord.setRightId(rightsDetail.getId());
            rightRecord.setRightCompanyId(rightCompany.getId());
            rightRecord.setCollectionId(rightsDetail.getCollectionId());
            rightRecord.setRefType(rightsDetail.getType());
            rightRecord.setRefId(collectionPeriod.getId());
            rightRecord.setDropNumber(rightsDetail.getDropNumber());
            rightRecord.setCreateDatetime(new Date());
            collectionRightRecordService.create(rightRecord);
        }
    }

    /**
     * 修改衍生区并生成作品
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodAndCollectionModifyDeriveReq req, User operator) {

        CollectionPeriod period = detailsimple(req.getId());

        if (!ECollectionPeriodCategory.DERIVATIVE.getCode().equals(period.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数不属于衍生区");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        if (!period.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(period.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(period.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }
        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 生成作品
        Collection collection = new Collection();
        Integer quantity = 0;
        if (null != req.getCollectionCreatePlatformReq()) {
            req.getCollectionCreatePlatformReq().setCategory(ECollectionCategory.DERIVATIVE.getCode());
            collection = collectionService.createCollectionCompanyPlatform(req.getCollectionCreatePlatformReq(), operator,
                    ECollectionPeriodCategory.DERIVATIVE.getCode());
            if (null == req.getCollectionCreatePlatformReq().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写发行数量");
            }
            quantity = req.getCollectionCreatePlatformReq().getQuantity();
        } else if (null != req.getCollectionInfo()) {
            collection = collectionService
                    .modifyCollectionCompanyPlatform(req.getCollectionInfo(), operator, ECollectionPeriodCategory.DERIVATIVE.getCode());
            if (null == req.getCollectionInfo().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写发行数量");
            }
            quantity = req.getCollectionInfo().getQuantity();
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择作品");
        }

        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(collection.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9
                .getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不可使用");
        }
        if (!ECollectionCategory.DERIVATIVE.getCode().equals(collection.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是衍生区作品");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "非藏品作品不能发售");
        }
        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setPrice(req.getPrice());

        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collection.getBuyType()) && CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collection.getBuyType()) && CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "未设置口令");
        }

        init(collection, collectionPeriod);

        if (collection.getRemainQuantity() < quantity) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "发售数量大于总数量");
        }

        collectionPeriod.setTotalQuantity(quantity);
        collectionPeriod.setRemainQuantity(collectionPeriod.getTotalQuantity());

        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        collectionPeriod.setCollectionNumber(1);

        //设置发售时间
        initSellDate(req.getStartSellDate(), req.getEndSellDate(), collectionPeriod);

        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //新增藏品关系
        collectionPeriodRelationMapper.deleteByPeriod(collectionPeriod.getId());
        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(ECollectionPeriodRelationCategory.SELL.getCode());
        collectionPeriodRelation.setCollectionId(collection.getId());
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(quantity);
        collectionPeriodRelation.setRemainQuantity(collectionPeriodRelation.getTotalQuantity());
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(collectionPeriod, req.getDiscountCollectionList(), operator);

        //更新空投
        dropCreate(collectionPeriod, req.getCollectionPeriodDropList());

        // 口令
        periodChannelWordService
                .create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    @Override
    public CollectionPeriodAndCollectionDetailRes detailPeriodAndCollection(Long id) {
        CollectionPeriodAndCollectionDetailRes res = new CollectionPeriodAndCollectionDetailRes();
        CollectionPeriod collectionPeriod = collectionPeriodMapper.selectByPrimaryKeyOss(id);
        if (null == collectionPeriod) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(collectionPeriod, res);
        Company company = companyService.detail(collectionPeriod.getAuthorIds());
        res.setAuthorId(company.getId());

        res.setAuthor(company.getShortName());
        res.setAuthorPic(company.getLogo());

        if (StringUtils.isNotBlank(collectionPeriod.getTags())) {
            List<String> result = Arrays.asList(collectionPeriod.getTags().split(","));
            res.setTagList(result);
            res.setTags(collectionPeriod.getTags());
        }

        //藏品列表
        List<CollectionPeriodRelation> collectionList = collectionPeriodRelationMapper.selectCollectionRelationListByPeriodId(id);

        List<CollectionPeriodCollectionRes> sellCollectionList = new ArrayList<>();
        List<CollectionPeriodCollectionRes> showCollectionList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(collectionList)) {

            for (CollectionPeriodRelation collectionPeriodCollectionPageRes : collectionList) {
                CollectionPeriodCollectionRes collectionRes = new CollectionPeriodCollectionRes();
                Collection collection = collectionService.detail(collectionPeriodCollectionPageRes.getCollectionId());
                BeanUtils.copyProperties(collection, collectionRes);
                collectionRes.setQuantity(collectionPeriodCollectionPageRes.getTotalQuantity());
                collectionRes.setPrice(collectionPeriod.getPrice());
                collectionRes.setOrderNo(collectionPeriodCollectionPageRes.getOrderNo());
//                //获取权益列表
//                CollectionRightsDetailListReq collectionRightsDetailListReq = new CollectionRightsDetailListReq();
//                collectionRightsDetailListReq.setCollectionId(collection.getId());
//                collectionRes.setRightsDetailList(collectionRightsDetailService.list(collectionRightsDetailListReq));
//
//                CollectionSaleDemand saleDemand = collectionSaleDemandService.detailByCollectionId(collection.getId());
//                collectionRes.setCollectionSaleDemand(saleDemand);
//
//                if (StringUtils.isNotBlank(collection.getFileUrl())) {
//                    List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
//                            UploadFile.class);
//                    collectionRes.setFileList(uploadFiles);
//                }
//
//                if (StringUtils.isNotBlank(collection.getTags())) {
//                    List<String> result = Arrays.asList(collection.getTags().split(","));
//                    collectionRes.setTagList(result);
//                }

                if (ECollectionPeriodRelationCategory.SELL.getCode().equals(collectionPeriodCollectionPageRes.getCategory())) {
                    // 是盲盒的话展示下占比
                    if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
                        BigDecimal rateDetail = new BigDecimal(collectionPeriodCollectionPageRes.getTotalQuantity())
                                .divide(new BigDecimal(collectionPeriod.getTotalQuantity()), 4, BigDecimal.ROUND_DOWN)
                                .multiply(new BigDecimal(100));
                        collectionRes.setRate(rateDetail.stripTrailingZeros());
                    }

                    sellCollectionList.add(collectionRes);
                } else {
                    showCollectionList.add(collectionRes);
                }
            }
        }
        res.setCollectionList(collectionList);
        res.setSellCollectionList(sellCollectionList);
        res.setShowCollectionList(showCollectionList);

        // 获取优先购
        List<CollectionPeriodPriorityBuy> collectionPeriodPriorityFinalBuyList = new ArrayList<>();
        CollectionRightRecord rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> collectionPeriodPriorityBuyList = collectionRightRecordService.list(rightRecord);
        if (CollectionUtils.isNotEmpty(collectionPeriodPriorityBuyList)) {
            for (CollectionRightRecord record : collectionPeriodPriorityBuyList) {
                CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
                Collection collection = collectionService.detailSimple(record.getCollectionId());
                CollectionPeriodPriorityBuy collectionPeriodPriorityBuy = new CollectionPeriodPriorityBuy();
                collectionPeriodPriorityBuy.setCreateType(rightsDetail.getCreateType());
                collectionPeriodPriorityBuy.setCollectionName(collection.getName());
                collectionPeriodPriorityBuy.setCoverFileUrl(collection.getCoverFileUrl());
                collectionPeriodPriorityBuy.setCollection(collection);
                collectionPeriodPriorityBuy.setRightId(record.getRightCompanyId());
                collectionPeriodPriorityBuy.setPeriodId(record.getRefId());
                collectionPeriodPriorityBuy.setCollectionId(collection.getId());
                collectionPeriodPriorityBuy.setQuantity(1);
                collectionPeriodPriorityBuy.setAdvanceMins(record.getAdvanceMins());
                collectionPeriodPriorityBuy.setRemainNumber(rightsDetail.getRemainNumber());
                collectionPeriodPriorityBuy.setNumberFlag(rightsDetail.getNumberFlag());

                collectionPeriodPriorityFinalBuyList.add(collectionPeriodPriorityBuy);
            }
        }

        res.setCollectionPeriodPriorityBuyList(collectionPeriodPriorityFinalBuyList);
        res.setLockTime(collectionPeriodRelationService.getPeriodLockTime(collectionPeriod.getId()));

        // 获取折扣藏品
        List<PeriodDiscountDetail> discountCollectionList = new ArrayList<>();
        rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> collectiondiscountCollectionList = collectionRightRecordService.list(rightRecord);
        for (CollectionRightRecord record : collectiondiscountCollectionList) {
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
            PeriodDiscountDetail discountDetail = new PeriodDiscountDetail();
            Collection collection = collectionService.detailSimple(record.getCollectionId());
            discountDetail.setCreateType(rightsDetail.getCreateType());
            discountDetail.setRightId(record.getRightCompanyId());
            discountDetail.setRefType(EPeriodDiscountRefType.PERIOD.getCode());
            discountDetail.setRefId(collectionPeriod.getId());
            discountDetail.setCollectionId(record.getCollectionId());
            discountDetail.setCollection(collection);
            discountDetail.setDiscountRate(record.getDiscountRate());
            discountDetail.setCollectionName(collection.getName());
            discountDetail.setCoverFileUrl(collection.getCoverFileUrl());
            discountDetail.setRemainNumber(rightsDetail.getRemainNumber());
            discountDetail.setNumberFlag(rightsDetail.getNumberFlag());
            discountCollectionList.add(discountDetail);
        }

        res.setDiscountCollectionList(discountCollectionList);

        // 获取空投藏品
        rightRecord = new CollectionRightRecord();
        rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
        rightRecord.setRefId(collectionPeriod.getId());
        List<CollectionRightRecord> list = collectionRightRecordService.list(rightRecord);
        List<CollectionPeriodDropRes> dropResList = new ArrayList<>();
        for (CollectionRightRecord record : list) {
            CollectionRightsDetail rightsDetail = collectionRightsDetailService.detail(record.getRightId());
            CollectionPeriodDropRes dropRes = new CollectionPeriodDropRes();
            dropRes.setRightId(record.getRightCompanyId());
            dropRes.setCollectionId(record.getCollectionId());
            Collection collection = collectionService.detailSimple(record.getCollectionId());
            dropRes.setCollectionName(collection.getName());
            dropRes.setCoverFileUrl(collection.getCoverFileUrl());
            dropRes.setRemainNumber(rightsDetail.getRemainNumber());
            dropRes.setNumberFlag(rightsDetail.getNumberFlag());
            dropResList.add(dropRes);
        }
        res.setCollectionPeriodDropList(dropResList);

        //获取口令列表
        res.setPeriodChannelWordList(periodChannelWordService.selectListByPeriodId(collectionPeriod.getId()));
        res.setChannelName(channelMerchantService.detail(collectionPeriod.getChannelId()).getName());

        if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            res.setApproveOpinion(
                    approveRecordService.detail(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_4.getCode(), collectionPeriod.getId()));
        }
        return res;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodAndCollectionCreateCopyRightReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());
        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        // 生成期数
        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.COPYRIGHT.getCode());
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());

        if ((null == req.getSellCollectionList() || CollectionUtils.isEmpty(req.getSellCollectionList())) &&
                (null == req.getSellModifyCollectionList() || CollectionUtils.isEmpty(req.getSellModifyCollectionList()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择售卖作品");
        }

        int totalQuantity = 0;
        int remainQuantity = 0;
        if (null != req.getSellModifyCollectionList()) {
            totalQuantity = totalQuantity + req.getSellModifyCollectionList().size();
            remainQuantity = remainQuantity + req.getSellModifyCollectionList().size();
        }
        if (null != req.getSellCollectionList()) {
            totalQuantity = totalQuantity + req.getSellCollectionList().size();
            remainQuantity = remainQuantity + req.getSellCollectionList().size();
        }
        if (null != req.getShowModifyCollectionInfoList()) {
            totalQuantity = totalQuantity + req.getShowModifyCollectionInfoList().size();
        }
        if (null != req.getShowCollectionList()) {
            totalQuantity = totalQuantity + req.getShowCollectionList().size();
        }

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(remainQuantity);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }
        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        initSellDate(req.getStartSellDate(), req.getEndSellDate(), collectionPeriod);
        collectionPeriod.setCollectionNumber(totalQuantity);
        collectionPeriodMapper.insertSelective(collectionPeriod);

        // 校验输入的作品的锁仓时间是否都一致
        CollectionCreateCheck collectionCreateCheck = new CollectionCreateCheck();
        collectionCreateCheck.setLockTime(0);
        collectionCreateCheck.setTransformLimitTime(0);
        if (null != req.getSellCollectionList()) {
            for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
                createPlatformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
                Collection collection = collectionService
                        .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());

                checkCollection(operator, collectionPeriod, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SELL.getCode(), COPYRIGHT_QUANTITY);
            }
        }

        if (null != req.getSellModifyCollectionList()) {
            for (CollectionModifyPlatformReq copyRightCReq : req.getSellModifyCollectionList()) {
                Collection collection = collectionService
                        .modifyCollectionCompanyPlatform(copyRightCReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
                if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是版权区作品");
                } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "非藏品作品不能发售");
                }
                checkCollection(operator, collectionPeriod, collectionCreateCheck, copyRightCReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SELL.getCode(), COPYRIGHT_QUANTITY);
            }
        }

        if (null != req.getShowCollectionList()) {
            for (CollectionCreatePlatformReq createPlatformReq : req.getShowCollectionList()) {
                createPlatformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
                Collection collection = collectionService
                        .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
                checkCollection(operator, collectionPeriod, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SHOW.getCode(), COPYRIGHT_QUANTITY);
            }
        }

        if (null != req.getShowModifyCollectionInfoList()) {
            for (CollectionModifyPlatformReq copyRightCReq : req.getShowModifyCollectionInfoList()) {
                Collection collection = collectionService
                        .modifyCollectionCompanyPlatform(copyRightCReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
                if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是版权区作品");
                } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "非藏品作品不能发售");
                }
                checkCollection(operator, collectionPeriod, collectionCreateCheck, copyRightCReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SHOW.getCode(), COPYRIGHT_QUANTITY);
            }
        }

        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(collectionCreateCheck.getPlateCategory());
        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collectionCreateCheck.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非售卖藏品不能发售");
        }
        companyService.checkCompany(collectionCreateCheck.getCompanyId());
        collectionPeriod.setAuthorIds(collectionCreateCheck.getCompanyId());

        collectionPeriod.setLockTime(collectionCreateCheck.getLockTime());
        collectionPeriod.setTransformLimitTime(collectionCreateCheck.getTransformLimitTime());
        collectionPeriod.setPlateCategory(collectionCreateCheck.getPlateCategory());
        collectionPeriod.setWordFlag(collectionCreateCheck.getBuyType());
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(collectionPeriod, req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodAndCollectionModifyCopyRightReq req, User operator) {
        CollectionPeriod collectionPeriod = detailsimple(req.getId());
        if (!ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collectionPeriod.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数不属于版权区");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        if (!collectionPeriod.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        CollectionPeriod period = EntityUtils.copyData(req, CollectionPeriod.class);
        if ((null == req.getSellCollectionList() || CollectionUtils.isEmpty(req.getSellCollectionList())) &&
                (null == req.getSellModifyCollectionList() || CollectionUtils.isEmpty(req.getSellModifyCollectionList()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择售卖作品");
        }

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(period.getId());

        int totalQuantity = 0;
        int remainQuantity = 0;
        if (null != req.getSellModifyCollectionList()) {
            totalQuantity = totalQuantity + req.getSellModifyCollectionList().size();
            remainQuantity = remainQuantity + req.getSellModifyCollectionList().size();
        }

        if (null != req.getSellCollectionList()) {
            totalQuantity = totalQuantity + req.getSellCollectionList().size();
            remainQuantity = remainQuantity + req.getSellCollectionList().size();
        }
        if (null != req.getShowModifyCollectionInfoList()) {
            totalQuantity = totalQuantity + req.getShowModifyCollectionInfoList().size();
        }

        if (null != req.getShowCollectionList()) {
            totalQuantity = totalQuantity + req.getShowCollectionList().size();
        }

        period.setTotalQuantity(totalQuantity);
        period.setRemainQuantity(remainQuantity);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            period.setTags(newStr);
        }

        period.setUpdater(operator.getId());
        period.setUpdaterName(operator.getLoginName());
        period.setUpdateDatetime(new Date());
        initSellDate(req.getStartSellDate(), req.getEndSellDate(), period);

        // 校验输入的作品的锁仓时间是否都一致
        CollectionCreateCheck collectionCreateCheck = new CollectionCreateCheck();
        collectionCreateCheck.setLockTime(0);
        collectionCreateCheck.setTransformLimitTime(0);
        for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
            createPlatformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
            Collection collection = collectionService
                    .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SELL.getCode(), COPYRIGHT_QUANTITY);
        }

        for (CollectionCreatePlatformReq createPlatformReq : req.getShowCollectionList()) {
            createPlatformReq.setCategory(ECollectionCategory.COPYRIGHT.getCode());
            Collection collection = collectionService
                    .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SHOW.getCode(), COPYRIGHT_QUANTITY);
        }

        for (CollectionModifyPlatformReq createPlatformReq : req.getSellModifyCollectionList()) {
            Collection collection = collectionService
                    .modifyCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
            if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是版权区作品");
            }
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SELL.getCode(), COPYRIGHT_QUANTITY);
        }

        for (CollectionModifyPlatformReq createPlatformReq : req.getShowModifyCollectionInfoList()) {
            Collection collection = collectionService
                    .modifyCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.COPYRIGHT.getCode());
            if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是版权区作品");
            }
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SHOW.getCode(), COPYRIGHT_QUANTITY);
        }

        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(collectionCreateCheck.getPlateCategory());
        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collectionCreateCheck.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非售卖藏品不能发售");
        }
        companyService.checkCompany(collectionCreateCheck.getCompanyId());
        period.setAuthorIds(collectionCreateCheck.getCompanyId());

        period.setLockTime(collectionCreateCheck.getLockTime());
        period.setTransformLimitTime(collectionCreateCheck.getTransformLimitTime());
        period.setPlateCategory(collectionCreateCheck.getPlateCategory());
        period.setWordFlag(collectionCreateCheck.getBuyType());

        period.setCollectionNumber(totalQuantity);
        collectionPeriodMapper.updateByPrimaryKeySelective(period);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(period, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(period, req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(period.getId(), req.getPeriodChannelWordList(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionPeriodBlindBoxAndCollectionCreateReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());
        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());

        CollectionPeriod collectionPeriod = EntityUtils.copyData(req, CollectionPeriod.class);
        collectionPeriod.setCategory(ECollectionPeriodCategory.BLINDBOX.getCode());
        collectionPeriod.setCreateType(ECollectionPeriodCreateType.E_COLLECTION_PERIOD_CREATE_TYPE_1.getCode());

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collectionPeriod.setTags(newStr);
        }

        collectionPeriod.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode());
        collectionPeriod.setUpdater(operator.getId());
        collectionPeriod.setUpdaterName(operator.getLoginName());
        collectionPeriod.setUpdateDatetime(new Date());
        initSellDate(req.getStartSellDate(), req.getEndSellDate(), collectionPeriod);

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        for (CollectionModifyPlatformReq child : req.getSellModifyCollectionList()) {
            if (null == child.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), child.getName() + "使用数量不能为空");
            }
            totalQuantity = totalQuantity + child.getQuantity();
        }
        for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
            if (null == createPlatformReq.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), createPlatformReq.getName() + "使用数量不能为空");
            }
            totalQuantity = totalQuantity + createPlatformReq.getQuantity();
        }

        collectionPeriod.setTotalQuantity(totalQuantity);
        collectionPeriod.setRemainQuantity(totalQuantity);
        collectionPeriod.setCollectionNumber(req.getSellModifyCollectionList().size() + req.getSellCollectionList().size());
        collectionPeriodMapper.insertSelective(collectionPeriod);
        // 校验输入的作品的锁仓时间是否都一致
        CollectionCreateCheck collectionCreateCheck = new CollectionCreateCheck();
        collectionCreateCheck.setLockTime(0);
        collectionCreateCheck.setTransformLimitTime(0);

        Integer collectionSize = 0;

        if (null != req.getSellCollectionList()) {
            collectionSize = collectionSize + req.getSellCollectionList().size();
            for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
                createPlatformReq.setCategory(ECollectionCategory.DERIVATIVE.getCode());
                Collection collection = collectionService
                        .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.BLINDBOX.getCode());
                checkCollection(operator, collectionPeriod, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SELL.getCode(), createPlatformReq.getQuantity());
            }
        }

        if (null != req.getSellModifyCollectionList()) {
            collectionSize = collectionSize + req.getSellModifyCollectionList().size();
            for (CollectionModifyPlatformReq createPlatformReq : req.getSellModifyCollectionList()) {
                Collection collection = collectionService
                        .modifyCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.BLINDBOX.getCode());
                if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory()) && !ECollectionCategory.DERIVATIVE.getCode()
                        .equals(collection.getCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是可用作品");
                }
                checkCollection(operator, collectionPeriod, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                        ECollectionPeriodRelationCategory.SELL.getCode(), createPlatformReq.getQuantity());
            }
        }

        if (collectionSize < 2) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "盲盒区发售作品不能少于两个");
        }

        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(collectionCreateCheck.getPlateCategory());
        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collectionCreateCheck.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非售卖藏品不能发售");
        }
        companyService.checkCompany(collectionCreateCheck.getCompanyId());
        collectionPeriod.setAuthorIds(collectionCreateCheck.getCompanyId());

        collectionPeriod.setLockTime(collectionCreateCheck.getLockTime());
        collectionPeriod.setTransformLimitTime(collectionCreateCheck.getTransformLimitTime());
        collectionPeriod.setPlateCategory(collectionCreateCheck.getPlateCategory());
        collectionPeriod.setWordFlag(collectionCreateCheck.getBuyType());
        collectionPeriodMapper.updateByPrimaryKeySelective(collectionPeriod);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(collectionPeriod, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(collectionPeriod, req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(collectionPeriod.getId(), req.getPeriodChannelWordList(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionPeriodBlindBoxAndCollectionModifyReq req, User operator) {
        CollectionPeriod collectionPeriod = detailsimple(req.getId());
        if (!ECollectionPeriodCategory.BLINDBOX.getCode().equals(collectionPeriod.getCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数不属于盲盒区");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        if (!collectionPeriod.getAuthorIds().equals(operator.getCompanyId()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        }

        if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(collectionPeriod.getStatus())
                && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(collectionPeriod.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数不是待上架状态，不能修改");
        }

        // 判断是否拥有该渠道的发布权限
        companyChannelService.checkPermission(operator, req.getChannelId());

        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        CollectionPeriod period = EntityUtils.copyData(req, CollectionPeriod.class);

        if ((null == req.getSellModifyCollectionList() || CollectionUtils.isEmpty(req.getSellModifyCollectionList())) &&
                (null == req.getSellCollectionList() || CollectionUtils.isEmpty(req.getSellCollectionList()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择售卖作品");
        }

        //删除之前的藏品关系，再新增
        collectionPeriodRelationMapper.deleteByPeriod(period.getId());
        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            period.setTags(newStr);
        }

        period.setUpdater(operator.getId());
        period.setUpdaterName(operator.getLoginName());
        period.setUpdateDatetime(new Date());

        initSellDate(req.getStartSellDate(), req.getEndSellDate(), period);

        // 统计售卖盲盒的总数
        int totalQuantity = 0;
        Integer collectionSize = 0;

        if (null != req.getSellCollectionList()) {
            collectionSize = collectionSize + req.getSellCollectionList().size();
            for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
                if (null == createPlatformReq.getQuantity()) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), createPlatformReq.getName() + "使用数量不能为空");
                }
                totalQuantity = totalQuantity + createPlatformReq.getQuantity();
            }
        }

        if (null != req.getSellModifyCollectionList()) {
            collectionSize = collectionSize + req.getSellModifyCollectionList().size();
            for (CollectionModifyPlatformReq createPlatformReq : req.getSellModifyCollectionList()) {
                if (null == createPlatformReq.getQuantity()) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), createPlatformReq.getName() + "使用数量不能为空");
                }
                totalQuantity = totalQuantity + createPlatformReq.getQuantity();
            }
        }

        if (collectionSize < 2) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "盲盒作品数量不能少于2个");
        }

        period.setTotalQuantity(totalQuantity);
        period.setRemainQuantity(totalQuantity);

        // 校验输入的作品的锁仓时间是否都一致
        CollectionCreateCheck collectionCreateCheck = new CollectionCreateCheck();
        collectionCreateCheck.setLockTime(0);
        collectionCreateCheck.setTransformLimitTime(0);

        for (CollectionCreatePlatformReq createPlatformReq : req.getSellCollectionList()) {
            createPlatformReq.setCategory(ECollectionCategory.DERIVATIVE.getCode());
            Collection collection = collectionService
                    .createCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.BLINDBOX.getCode());
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SELL.getCode(), createPlatformReq.getQuantity());
        }

        for (CollectionModifyPlatformReq createPlatformReq : req.getSellModifyCollectionList()) {
            Collection collection = collectionService
                    .modifyCollectionCompanyPlatform(createPlatformReq, operator, ECollectionPeriodCategory.BLINDBOX.getCode());
            if (!ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory()) && !ECollectionCategory.DERIVATIVE.getCode()
                    .equals(collection.getCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是可用作品");
            }
            checkCollection(operator, period, collectionCreateCheck, createPlatformReq.getOrderNo(), collection,
                    ECollectionPeriodRelationCategory.SELL.getCode(), createPlatformReq.getQuantity());
        }

        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(collectionCreateCheck.getPlateCategory());
        // 判断口令购买
        if (EBoolean.NO.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isNotEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "普通购买的期数不能使用口令");
        } else if (EBoolean.YES.getCode().equals(collectionCreateCheck.getBuyType()) &&
                CollectionUtils.isEmpty(req.getPeriodChannelWordList())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令购买的期数必须新增口令");
        } else if (ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collectionCreateCheck.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非售卖藏品不能发售");
        }
        companyService.checkCompany(collectionCreateCheck.getCompanyId());
        period.setAuthorIds(collectionCreateCheck.getCompanyId());

        period.setLockTime(collectionCreateCheck.getLockTime());
        period.setTransformLimitTime(collectionCreateCheck.getTransformLimitTime());
        period.setPlateCategory(collectionCreateCheck.getPlateCategory());
        period.setWordFlag(collectionCreateCheck.getBuyType());
        period.setCollectionNumber(req.getSellModifyCollectionList().size() + req.getSellCollectionList().size());
        collectionPeriodMapper.updateByPrimaryKeySelective(period);

        //更新优先权数据
        collectionPeriodPriorityBuyService.create(period, req.getCollectionPeriodPriorityBuyList(), operator);

        //更新折扣
        periodDiscountDetailService.create(period, req.getDiscountCollectionList(), operator);

        // 口令
        periodChannelWordService.create(period.getId(), req.getPeriodChannelWordList(), operator);
    }

    @Override
    public void checkCollection(User operator, CollectionPeriod collectionPeriod, CollectionCreateCheck collectionCreateCheck,
                                Integer orderNo, Collection collection, String type, Integer totalQuantity) {
        if (collection.getRemainQuantity() < 1) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "藏品剩余数量不足");
        }

        if (collectionCreateCheck.getLockTime() != 0 && !collectionCreateCheck.getLockTime().equals(collection.getLockTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品锁仓时间不一致");
        }

        if (collectionCreateCheck.getTransformLimitTime() != 0 && !collectionCreateCheck.getTransformLimitTime()
                .equals(collection.getTransformLimitTime())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品转赠限制时间不一致");
        }
        if (collectionCreateCheck.getCompanyId() != null && !collectionCreateCheck.getCompanyId().equals(collection.getAuthorId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品发行方不一致");
        }
        if (null != collectionCreateCheck.getContractId() && !collectionCreateCheck.getContractId()
                .equals(collection.getContractId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "售卖的作品上链合约不一致");
        }

        if (null != collectionCreateCheck.getPlateCategory() && !collectionCreateCheck.getPlateCategory()
                .equals(collection.getPlateCategory())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品板块不一致");
        }

        if (null != collectionCreateCheck.getBuyType() && !collectionCreateCheck.getBuyType().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买方式不一致");
        }
        checkCollectionPermissionsSaas(operator, collection);

        collectionCreateCheck.setCompanyId(collection.getAuthorId());
        collectionCreateCheck.setLockTime(collection.getLockTime());
        collectionCreateCheck.setTransformLimitTime(collection.getTransformLimitTime());
        collectionCreateCheck.setPlateCategory(collection.getPlateCategory());
        collectionCreateCheck.setBuyType(collection.getBuyType());
        collectionCreateCheck.setContractId(collection.getContractId());

        CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
        collectionPeriodRelation.setCategory(type);
        collectionPeriodRelation.setCollectionId(collection.getId());
        collectionPeriodRelation.setOrderNo(orderNo);
        collectionPeriodRelation.setPeriodId(collectionPeriod.getId());
        collectionPeriodRelation.setPrice(collectionPeriod.getPrice());
        collectionPeriodRelation.setTotalQuantity(totalQuantity);

        collectionPeriodRelation.setRemainQuantity(totalQuantity);
        collectionPeriodRelationMapper.insertSelective(collectionPeriodRelation);
    }

    /**
     * 获取即将开始/正在进行中的上架期数
     */
    private List<CollectionPeriodByPlateRes> selectList(Long channelId, String plateCategory) {
        CollectionPeriod condition = new CollectionPeriod();
        condition.setChannelId(channelId);
        condition.setPlateCategory(plateCategory);
        condition.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());
//        List<String> startStatusList = new ArrayList<>();
//        startStatusList.add(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode());
//        startStatusList.add(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode());
//        condition.setStartStatusList(startStatusList);
        condition.setOrderBy("t.start_status = '1' desc,t.start_status ASC, t.order_no desc");

        return collectionPeriodMapper.selectSimpleFrontList(condition);
    }

    /**
     * 批量上下架
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpAndDown(CollectionPeriodBatchUpAndDownReq request, User operator) {
        List<CollectionPeriod> periodList = new ArrayList<>();

        for (Long id : request.getIdList()) {
            CollectionPeriod detail = detail(id);
            periodList.add(detail);

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus()) && !detail.getStatus()
                    .equals(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "待上架的期数才能上架");
            }

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(request.getStatus()) && detail.getStatus()
                    .equals(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "待上架的期数不能下架");
            }

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(request.getStatus())
                    && ECollectionPeriodSoldStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(detail.getSoldStatus())
                    && ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(detail.getStartStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该期数已售罄或已结束");
            }
            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(request.getStatus())
                    && ECollectionPeriodCategory.PeriodAuction.getCode().equals(detail.getCategory())) {
                PeriodAuction periodAuction = periodAuctionService.detailByPeriod(detail.getId());
                if (EPeriodAuctionStatus.E_PERIOD_AUCTION_STATUS_1.getCode().equals(periodAuction.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "期数竞拍等待中拍用户支付，不能下架");
                }
            }

            //已上架的期数下架开始的状态改成已结束
            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(request.getStatus())) {
                if (ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(detail.getStartStatus())
                        || ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(detail.getStartStatus())) {
                    detail.setStartStatus(ECollectionPeriodStartStatus.COLLECTION_PERIOD_STATUS_2.getCode());
                }
            }
        }


        upAndDown(request.getStatus(), operator, periodList);
    }

    private void upAndDown(String status, User operator, List<CollectionPeriod> periodList) {
        Map<Long, Integer> channelIdList = new HashMap<>();

        Integer orderNo = collectionPeriodMapper.selectMaxOrderNo();
        for (CollectionPeriod detail : periodList) {
            // 查询当前已上架条数
            Integer integer = channelIdList.get(detail.getChannelId());
            if (null == integer) {
                integer = collectionPeriodMapper.selectAddedCount(detail.getChannelId());
            }

            detail.setStatus(status);
            detail.setUpdater(operator.getId());
            detail.setUpdaterName(operator.getLoginName());
            detail.setUpdateDatetime(new Date());
            if (null == detail.getOrderNo()) {
                orderNo++;
                detail.setOrderNo(orderNo);
            }

            CollectionPeriodRelation collectionPeriodRelation = new CollectionPeriodRelation();
            collectionPeriodRelation.setPeriodId(detail.getId());
            List<CollectionPeriodRelation> collectionPeriodRelationList = collectionPeriodRelationMapper
                    .selectByCondition(collectionPeriodRelation);
            // 上架藏品减去剩余数量
            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(detail.getStatus())) {
                ECollectionDetailRefType refType = ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_3;

                if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(detail.getCategory())) {
                    refType = ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_13;
                }

                for (CollectionPeriodRelation periodRelation : collectionPeriodRelationList) {
                    Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());
                    if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "还未审核通过");
                    }
                    if (collection.getRemainQuantity().intValue() >= periodRelation.getTotalQuantity()) {
                        collection.setRemainQuantity(collection.getRemainQuantity() - periodRelation.getTotalQuantity());
                        // 期数上架给作品分配tokenId
                        contractTokenService.distributionToken(collection, periodRelation.getTotalQuantity(),
                                refType.getCode(), periodRelation.getId(),
                                EContractTokenStatus.CONTRACT_TOKEN_STATUS_3, null);
                    } else {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "剩余数量不足");
                    }
                    collectionService.modify(collection);
                }

                // 上架的期数数量加一
                integer = integer + 1;
                channelIdList.put(detail.getChannelId(), integer);

                if (EBoolean.YES.getCode().equals(detail.getWordFlag())) {
                    List<PeriodChannelWord> list = periodChannelWordService.selectListByPeriodId(detail.getId());
                    if (CollectionUtils.isEmpty(list)) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "口令期数未设置口令");
                    }
                }

                // 审核通过直接上架


                // 先上架作品
                // 作品提交审核
                CollectionPeriodRelation relation = new CollectionPeriodRelation();
                relation.setPeriodId(detail.getId());
                List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);

                for (CollectionPeriodRelation periodRelation : list) {
                    Collection collection = collectionService.detailForUpdate(periodRelation.getCollectionId());

                    // 空投
                    CollectionRightRecord rightRecord = new CollectionRightRecord();
                    rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
                    rightRecord.setRefId(detail.getId());
                    List<CollectionRightRecord> rightRecords = collectionRightRecordService.list(rightRecord);
                    for (CollectionRightRecord record : rightRecords) {
                        CollectionDropUserCollectionReq dropReq = new CollectionDropUserCollectionReq();
                        dropReq.setCollectionId(collection.getId());
                        dropReq.setNeedCollectionId(record.getCollectionId());
                        dropReq.setQuantity(1);
                        User user = userService.detailBrief(collection.getAuthorId());
                        collectionService
                                .dropByUserCollection(dropReq, user, ECollectionRightDropType.E_COLLECTION_RIGHT_DROP_TYPE_0.getCode());
                    }
                }

                // 权益次数减少
                CollectionRightRecord rightRecord = new CollectionRightRecord();
                rightRecord.setRefId(detail.getId());
                List<CollectionRightRecord> rightRecordList = collectionRightRecordService.list(rightRecord);
                for (CollectionRightRecord record : rightRecordList) {
                    CollectionRightCompany rightCompany = collectionRightCompanyService.detailForUpdate(record.getRightCompanyId());
                    CollectionRightsDetail rightsDetail = collectionRightsDetailService.detailForUpdate(record.getRightId());

                    if (!ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode().equals(rightCompany.getStatus())) {
                        Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                        .getValue() + "权益未开启");
                    }

                    if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(rightsDetail.getNumberFlag())) {
                        if (1 > rightsDetail.getRemainNumber()) {
                            Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                    collection.getName() + ECollectionRightSpecificType.getCollectionRightSpecificTypeType(rightsDetail.getType())
                                            .getValue() + "权益已用完");
                        }
                        rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() - 1);
                    }

                    rightCompany.setNumber(rightCompany.getNumber() + 1);

                    collectionRightCompanyService.modify(rightCompany);
                    collectionRightsDetailService.modify(rightsDetail);
                }
            } else {
                downHandler(channelIdList, detail, integer, collectionPeriodRelationList);
            }

            // 删除缓存
            String collectionPeriodCacheKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_KEY, detail.getId());
            if (redisUtil.hasKey(collectionPeriodCacheKey)) {
                redisUtil.del(collectionPeriodCacheKey);
            }
        }

        if (CollectionUtils.isNotEmpty(periodList)) {
            collectionPeriodMapper.batchUpdate(periodList);

            for (CollectionPeriod period : periodList) {
                ChannelMerchant channelMerchant = channelMerchantService.detail(period.getChannelId());
                //主站推送
                if (EChannelMerchantType.E_CHANNEL_MERCHANT_TYPE_0.getCode().equals(channelMerchant.getType())) {
                    if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode().equals(period.getStatus())) {
                        CollectionPeriodSend periodSend = new CollectionPeriodSend();
                        periodSend.setPeriodId(period.getId());
                        periodSend.setCreateDatetime(new Date());
                        collectionPeriodMapper.creatPeriodSend(periodSend);
                    }
                }
            }
        }

        // 抽签区数据迁移
        collectionPeriodJoinRecordService.doPeriodJoinRecordMigration();

        for (Long channelId : channelIdList.keySet()) {

            String redisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_LIST_KEY_CHANNEL, channelId.toString());
            // 删除前端列表查缓存
            redisUtil.del(redisKey);
            Integer integer = channelIdList.get(channelId);
            // 数据条数加入缓存
            String countRedisKey = String.format(RedisKeyList.MT_COLLECTION_PERIOD_LIST_COUNT_KEY, channelId);
            redisUtil.set(countRedisKey, integer);

            // 删除宝藏计划缓存
            String planRedisKey = String.format(RedisKeyList.MT_TREASURE_PLAN_LIST_KEY, channelId);
            redisUtil.del(planRedisKey);
            // 宝藏计划加入缓存
            List<TreasurePlanHomeListRes> resList = new ArrayList<>();
            resList = collectionPeriodMapper.treasurePlanListHome(new Date(), channelId, null);
            redisUtil.set(planRedisKey, resList);
        }
    }

    private void downHandler(Map<Long, Integer> channelIdList, CollectionPeriod detail, Integer integer,
                             List<CollectionPeriodRelation> collectionPeriodRelationList) {
        // 下架收回藏品
        if (detail.getRemainQuantity().intValue() > 0) {
            for (CollectionPeriodRelation periodRelation : collectionPeriodRelationList) {
                Collection collection = collectionService.detailSimple(periodRelation.getCollectionId());
                if (periodRelation.getRemainQuantity().intValue() > 0) {
                    collection.setRemainQuantity(collection.getRemainQuantity() + periodRelation.getRemainQuantity());
                    collectionService.modify(collection);
                    Contract contract = contractService.detail(collection.getContractId());
                    if (EContractProtocol.ERC721.getCode().equals(contract.getProtocol())) {
                        ECollectionDetailRefType refType = ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_3;

                        if (ECollectionPeriodCategory.PeriodAuction.getCode().equals(detail.getCategory())) {
                            refType = ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_13;
                        }
                        // 期数下架回收tokenId
                        contractTokenService
                                .periodRecycleToken(collection.getId(), periodRelation.getRemainQuantity(), refType);
                    }

                }
            }
        }

        // 期数下架对应bannner下架
        String url = ECollectionPeriodCategory.COPYRIGHT.getCode().equals(detail.getCategory())
                ? EBannerUrl.HOME_COLLECTION_PERIOD_COPYRIGHT_DETAIL.getCode()
                : EBannerUrl.HOME_COLLECTION_PERIOD_DERIVE_DETAIL.getCode();
        cnavigateService.baechDown(detail.getId(), url);

        // 下架删除前端的期数详情缓存
        String frontPeriodDetail = String.format(RedisKeyList.MT_PERIOD_FRONT_DETAIL_KEY, detail.toString());
        redisUtil.del(frontPeriodDetail);
        // 下架删除期数作品缓存
        String periodRelationList = String.format(RedisKeyList.MT_PERIOD_RELATION_LIST_KEY, detail.toString());
        redisUtil.del(periodRelationList);
        // 下架删除期数token缓存
        String redisTokenId = String.format(RedisKeyList.MT_PERIOD_TOKEN_KEY, detail.toString());
        redisUtil.del(redisTokenId);

        // 上架的期数数量加一
        integer = integer - 1;
        channelIdList.put(detail.getChannelId(), integer);
    }

    @Override
    public int updatePeriodStart(List<Long> idList) {
        CollectionPeriod collectionPeriod = new CollectionPeriod();
        collectionPeriod.setIdList(idList);
        return collectionPeriodMapper.updatePeriodStart(collectionPeriod);
    }

    @Override
    public List<Long> selectPeriodEnd() {
        return collectionPeriodMapper.selectPeriodEnd();
    }

    @Override
    public void updatePeriodEnd(List<Long> idList) {
        CollectionPeriod collectionPeriod = new CollectionPeriod();
        collectionPeriod.setIdList(idList);
        collectionPeriodMapper.updatePeriodEnd(collectionPeriod);
    }

    @Override
    public void updatePeriodDown() {
        collectionPeriodMapper.updatePeriodDown();
    }

    /**
     * 购买期数
     */
    @Override
    public void buy(CollectionPeriodBuyReq request, User operator) {
        CollectionPeriod period = detail(request.getId());

        if (period.getRemainQuantity() < request.getNumber()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "份数不足");
        }


    }

    @Override
    public void detailByPlanId(TreasurePlanListRes res, String plateCategory) {
        CollectionPeriod condition = new CollectionPeriod();
        condition.setPlateCategory(plateCategory);
        condition.setStartSellDateStartPlan(DateUtil.getCurrDayFirstTime());
        condition.setStatus(ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_1.getCode());

        condition.setOrderBy("t.start_sell_date");
        PageHelper.startPage(1, 50);

        List<CollectionPeriod> periodList = collectionPeriodMapper.selectByCondition(condition);

        Date startDate = null;
        Date endDate = null;

        List<TreasurePlanDetailListRes> listResList = new ArrayList<>();
        for (CollectionPeriod period : periodList) {

            if (null == startDate || period.getStartSellDate().before(startDate)) {
                startDate = period.getStartSellDate();
            }

            if (null == endDate || period.getStartSellDate().after(endDate)) {
                endDate = period.getStartSellDate();
            }

            TreasurePlanDetailListRes listRes = new TreasurePlanDetailListRes();
            listRes.setId(period.getId());
            listRes.setCategory(period.getCategory());
            listRes.setTitle(period.getName());
            listRes.setSubtitle(period.getIntroduce());
            listRes.setPic(period.getPlanPic());
            listRes.setFileType(period.getFileType());

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(period.getStartSellDate());
            listRes.setStartDateStr(calendar.get(Calendar.MONTH) + 1 + "." + calendar.get(Calendar.DAY_OF_MONTH));

            listResList.add(listRes);
        }

        if (null != startDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            res.setStartDate(calendar.get(Calendar.MONTH) + 1 + "." + calendar.get(Calendar.DAY_OF_MONTH));
        }

        if (null != endDate) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            res.setEndDate(calendar.get(Calendar.MONTH) + 1 + "." + calendar.get(Calendar.DAY_OF_MONTH));
        }

        res.setResList(listResList);

    }

    /**
     * 宝藏计划查询
     */
    @Override
    public List<TreasurePlanListRes> treasurePlanList(TreasurePlanListFrontReq request) {
        List<TreasurePlanListRes> resList = new ArrayList<>();

        TreasurePlanListRes res = new TreasurePlanListRes();
        detailByPlanId(res, request.getPlateCategory());
        if (CollectionUtils.isNotEmpty(res.getResList())) {
            resList.add(res);
        }

        return resList;
    }

    @Override
    public List<TreasurePlanHomeListRes> treasurePlanListHome(Long channelId, String plateCategory) {
        List<TreasurePlanHomeListRes> resList = new ArrayList<>();
//        String redisId = String.format(RedisKeyList.MT_TREASURE_PLAN_LIST_KEY, channelId);

//        if (StringUtils.isBlank(plateCategory)) {
//            if (redisUtil.hasKey(redisId)) {
//                JSONArray tableData = JSONArray.parseArray(JSON.toJSONString(redisUtil.get(redisId)));
//                resList = JSONObject.parseArray(tableData.toJSONString(), TreasurePlanHomeListRes.class);
//            } else {
//                resList = collectionPeriodMapper.treasurePlanListHome(new Date(), channelId, plateCategory);
//                redisUtil.set(redisId, resList, redisLockTime);
//            }
//        } else {
        resList = collectionPeriodMapper.treasurePlanListHome(new Date(), channelId, plateCategory);
//            redisUtil.set(redisId, resList, redisLockTime);
//        }

        for (TreasurePlanHomeListRes res : resList) {
            for (TreasurePlanHomeDetailRes detailRes : res.getResList()) {
                detailRes.setStartDateStr(DateUtil.dateToStr(detailRes.getStartSellDate(), "HH:mm"));
            }
        }
        return resList;
    }

    @Override
    public List<TreasurePlanHomeListRes> treasurePlanListHome(Date date) {
        List<TreasurePlanHomeListRes> resList = new ArrayList<>();

        resList = collectionPeriodMapper.treasurePlanListHome(date, null, null);

        return resList;
    }

    @Override
    public List<TreasurePlanCompanyListRes> treasurePlanCompanyListHome(TreasurePlanCompanyListReq request, User operator) {
        List<TreasurePlanCompanyListRes> resList = new ArrayList<>();
        ChannelMerchant channelMerchant = channelMerchantService.detailByFront(request.getChannelId());
        request.setChannelId(channelMerchant.getId());
        resList = collectionPeriodMapper.treasurePlanCompanyListHome(request);

        Integer daySize = configService.getIntegerValue(SysConstants.TREASURE_PLAN_DAY_SIZE);
        for (TreasurePlanCompanyListRes res : resList) {
            res.setNumber(res.getResList().size());
            res.setFinishFlag(EBoolean.NO.getCode());
            if (daySize <= res.getNumber()) {
                res.setFinishFlag(EBoolean.YES.getCode());
            }

            int auditNumber = 0;
            for (TreasurePlanCompanyDetailRes detailRes : res.getResList()) {
                detailRes.setStartDateStr(DateUtil.dateToStr(detailRes.getStartSellDate(), "HH:mm"));

                detailRes.setMyPeriodFlag(EBoolean.NO.getCode());
                if (detailRes.getAuthorIds().equals(operator.getCompanyId())) {
                    detailRes.setMyPeriodFlag(EBoolean.YES.getCode());
                }

                if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_3.getCode().equals(detailRes.getStatus())) {
                    if (!detailRes.getAuthorIds().equals(operator.getCompanyId())) {
                        detailRes.setPic(null);
                        detailRes.setPrice(null);
                    }
                    auditNumber++;
                }
                detailRes.setAuthorIds(null);
                if (StringUtils.isNotBlank(detailRes.getTags())) {
                    List<String> result = new ArrayList<>();
                    result.addAll(Arrays.asList(detailRes.getTags().split(",")));
                    detailRes.setTagList(result);
                }
            }

            res.setAuditNumber(auditNumber);
            res.setAddedNumber(res.getNumber() - auditNumber);
        }
        return resList;
    }

    @Override
    public CollectionPeriod selectForUpdate(Long id) {
        return collectionPeriodMapper.selectByPrimaryKeyForUpdate(id);
    }

    @Override
    public List<CollectionPeriodSend> getQuickStartPeriod() {
        CollectionPeriodSend collectionPeriod = new CollectionPeriodSend();
        collectionPeriod.setSendDatetime(new Date());
        collectionPeriod.setSendHour(configService.getIntegerValue("period_start_send_hour"));
        return collectionPeriodMapper.getQuickStartPeriod(collectionPeriod);
    }

    @Override
    public void sendTrailerPeriod(CollectionPeriodSend period) {
        Integer sendHour = configService.getIntegerValue("period_start_send_hour");

        long minuteBetween = com.std.common.utils.DateUtil.minuteBetween(new Date(), period.getStartSellDate());
        if (EBoolean.YES.getCode().equals(period.getSendFlag()) && minuteBetween > 0) {
            // 上架发布推送
            JpushRecord jpushRecord = new JpushRecord();
            jpushRecord.setTitle("期数发售马上开始");

            String time = sendHour + "小时！";

            if (minuteBetween < 50) {
                time = minuteBetween + "分钟！";
            }

            jpushRecord.setNoticeInfo(
                    ECollectionPeriodCategory.getCollectionPeriodCategory(period.getCategory()).getValue() + period.getName()
                            + "距离抢购开始还剩"
                            + time);
            jpushRecord.setRefId(period.getId());
            jpushRecordService.create(jpushRecord);

            period.setSendDatetime(new Date());
        }
        period.setSendFlag(EBoolean.NO.getCode());
        collectionPeriodMapper.updatePeriodSend(period);
    }

}