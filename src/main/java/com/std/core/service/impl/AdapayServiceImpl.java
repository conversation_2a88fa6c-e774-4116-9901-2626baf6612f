package com.std.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alipay.api.internal.util.WebUtils;
import com.huifu.adapay.core.exception.BaseAdaPayException;
import com.huifu.adapay.model.AdapayCommon;
import com.huifu.adapay.model.Payment;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.core.config.AdapayConfig;
import com.std.core.config.WechatConfig;
import com.std.core.enums.EAdapayStatus;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.EPayMethod;
import com.std.core.enums.EPayRecordBizType;
import com.std.core.enums.EPayRecordStatus;
import com.std.core.enums.EPayType;
import com.std.core.mapper.PayRecordMapper;
import com.std.core.pojo.domain.AdapayCallbackData;
import com.std.core.pojo.domain.AdapayCallbackInfo;
import com.std.core.pojo.domain.AdapayExpend;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.response.AdaPayInfo;
import com.std.core.pojo.response.AdaPayStatus;
import com.std.core.service.*;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.RedisUtil;
import com.std.core.util.wechat.MD5;
import com.std.core.util.wechat.MD5Util;
import com.std.core.util.wechat.WXRefund;

import java.beans.BeanInfo;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.SortedMap;
import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> silver
 * @since : 2020-04-11 12:01
 */
@Slf4j
@Service
public class AdapayServiceImpl implements IAdapayService {

    @Resource
    private ICollectionDetailTransferRecordService collectionDetailTransferRecordService;

    @Resource
    private IChargeService chargeService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private AdapayConfig adapayConfig;


    @Resource
    private WechatConfig wechatConfig;

    private static final String IP = "***********";

    @Resource
    private IPayRecordService payRecordService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private IContractTokenExportService contractTokenExportService;

    @Resource
    private PayRecordMapper payRecordMapper;

    @Resource
    private ICollectionBuyOrderService collectionBuyOrderService;

    @Resource
    private ICollectionPeriodJoinRecordService collectionPeriodJoinRecordService;

    @Resource
    private IPeriodAuctionBondRecordService periodAuctionBondRecordService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doCallback(AdapayCallbackInfo info) {
        try {
            AdapayCallbackData callbackData = info.getData();
            String infoId = info.getId();
            if (!EAdapayStatus.SUCCEEDED.getCode().equals(callbackData.getStatus())) {
                log.error("汇付记录支付失败，状态:{}", callbackData.getStatus());
                return;
            }

            // 查询支付记录
            PayRecord payRecord = payRecordService.detailForUpdate(Long.valueOf(callbackData.getOrder_no()));
            if (!EPayRecordStatus.PAY_RECORD_STATUS_0.getCode().equals(payRecord.getStatus())) {
                log.error("支付记录不处于待回调状态:{}", callbackData.getOrder_no());
                return;
            }

            // 此处调用订单查询接口验证是否交易成功
            boolean isSucc = reqOrderquery(callbackData.getId(), callbackData.getOrder_no());

            if (isSucc) {
                payRecord.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_1.getCode());
                payRecord.setCallbackTime(new Date());
                if (EPayRecordBizType.PAY_RECORD_BIZTYPE_0.getCode().equals(payRecord.getBizType())) {
                    chargeService.doChargeCallback(isSucc, infoId, payRecord, EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_2.getCode().equals(payRecord.getBizType())) {
//                    buyoutProductsService.doCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_3.getCode().equals(payRecord.getBizType())) {
//                    auctionRecordService.doCallback(payRecord.getBizCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_4.getCode().equals(payRecord.getBizType())) {
//                    auctionProductsService.doCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
//                    //退回保证金
//                    AuctionProducts auctionProducts = auctionProductsService.getAuctionProductsByBizCode(payRecord.getBizCode().toString());
//                    AuctionBond auctionBond = auctionBondService.detail(auctionProducts.getId(), auctionProducts.getLastUserId());
//                    Collection collection = collectionService.detailSimple(auctionProducts.getCollectionId());

//                    auctionRecordService.auctionBondRefund(auctionBond, auctionProducts, collection);
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_6.getCode().equals(payRecord.getBizType())) {
                    contractTokenExportService.doCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_7.getCode().equals(payRecord.getBizType())) {
                    collectionService.doCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode(),
                            EJourBizTypeUser.Collection.CreateAssembleCollection_UnFrozen,
                            EPayRecordBizType.PAY_RECORD_BIZTYPE_7.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode().equals(payRecord.getBizType())) {
                    collectionBuyOrderService.doCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_9.getCode().equals(payRecord.getBizType())) {
                    collectionDetailTransferRecordService
                            .doCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_10.getCode().equals(payRecord.getBizType())) {
                    collectionPeriodJoinRecordService.doCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_12.getCode().equals(payRecord.getBizType())) {
                    periodAuctionBondRecordService.doCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_13.getCode().equals(payRecord.getBizType())) {
                    collectionBuyOrderService.dAuctionoCallback(payRecord.getBizCode().toString(), EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_14.getCode().equals(payRecord.getBizType())) {
                    collectionBuyOrderService
                            .doLuckyDrawCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_15.getCode().equals(payRecord.getBizType())) {
//                    degressionAuctionBondService
//                            .doCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
                } else if (EPayRecordBizType.PAY_RECORD_BIZTYPE_16.getCode().equals(payRecord.getBizType())) {
//                    degressionAuctionOrderService
//                            .doCallback(payRecord.getBizCode().toString(), payRecord, EBigOrderPayType.ADAPAY.getCode());
                }
            } else {
                payRecord.setStatus(EPayRecordStatus.PAY_RECORD_STATUS_2.getCode());
            }
            payRecord.setResponse(info.toString());
            payRecordMapper.updateByPrimaryKeySelective(payRecord);

        } catch (Exception e) {
            throw new BizException(EErrorCode.CORE00000.getCode(), "汇付回调错误{}", e.getMessage());
        }
    }


    @Override
    public <T> AdaPayInfo getAppPayInfo(Long userId, String bizType, String bizNote, Long bizCode, BigDecimal amount, Long refId
            , Object... args) {

        if (BigDecimal.ZERO.compareTo(amount) >= 0) {
            throw new BizException(EErrorCode.CORE00019);
        }

        PayRecord payRecord = payRecordService.detail(bizCode);
        if (payRecord != null && !payRecord.getPayType().equals(EPayType.ADAPAY.getCode())) {
            payRecordService.remove(bizCode);
            // 落地支付记录
            payRecord = payRecordService
                    .create(userId, EPayType.ADAPAY.getCode(), EPayMethod.APP.getCode(), amount, bizType, bizCode, refId,
                            args.length == 0 ? null : args[0]);
        } else if (payRecord == null) {
            // 落地支付记录
            payRecord = payRecordService
                    .create(userId, EPayType.ADAPAY.getCode(), EPayMethod.APP.getCode(), amount, bizType, bizCode, refId,
                            args.length == 0 ? null : args[0]);
        }

        Map<String, Object> payment = new HashMap<>();
        try {
//            stringObjectMap = executePayment(adapayConfig.getAppid(), bizCode.toString(), amount, bizNote);
            payment = executePayment(bizCode.toString(), EPayType.ALIPAY.getCode(), amount, bizNote);
            payRecord.setResponse(payment.toString());
            payRecordService.modify(payRecord);
        } catch (Exception e) {
            log.error("汇付支付失败，原因：" + e.getMessage());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "支付失败");
        }

        if (!EAdapayStatus.SUCCEEDED.getCode().equals(payment.get("status"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "访问太频繁了，请稍后再试");
        }
        AdaPayInfo adaPayInfo = new AdaPayInfo();

        AdapayExpend expend = JSON.parseObject(JSON.toJSONString(payment.get("expend")), AdapayExpend.class);
        adaPayInfo.setPayInfo(expend.getPay_info());
        adaPayInfo.setId((String) payment.get("id"));
        return adaPayInfo;
    }

    /**
     * 执行一个支付交易
     *
     * @return 创建的支付对象
     * @throws Exception 异常
     */
    @Test
    public Map<String, Object> executePayment(String orderNo, BigDecimal amount, String bizNote) throws Exception {
        System.out.println("=======execute payment begin=======");
        //创建支付对象的参数，全部参数请参考 https://docs.adapay.tech/api/index.html
        Map<String, Object> paymentParams = new HashMap<>(10);
        paymentParams.put("order_no", orderNo);
        paymentParams.put("adapay_func_code", "qrPrePay.qrPreOrder");
        paymentParams.put("pay_amt", amount);
        paymentParams.put("app_id", adapayConfig.getAppid());
        paymentParams.put("currency", "cny");
        paymentParams.put("goods_title", "支付下单");
        paymentParams.put("goods_desc", bizNote);

//        Map<String, Object> deviceInfo = new HashMap<>(2);
//
//        deviceInfo.put("device_ip", "127.0.0.1");
//        deviceInfo.put("device_mac", "交易设备 MAC");
//        deviceInfo.put("device_type", "1");
//        deviceInfo.put("device_imei", "交易设备 IMEI");
//        deviceInfo.put("device_imsi", "交易设备 IMSI");
//        deviceInfo.put("device_iccId", "ICCID");
//        deviceInfo.put("device_wifi_mac", "WIFIMAC");

//        paymentParams.put("device_info", deviceInfo);

//        Map<String, Object> expendParams = new HashMap<>(2);
//        String openId = "";//微信授权获取
//        expendParams.put("open_id", openId);
//        expendParams.put("is_raw", "1");
//        expendParams.put("callback_url", "绝对路径");
//        expendParams.put("limit_pay", "1");
//
//        paymentParams.put("expend", expendParams);
        Map<String, Object> response = AdapayCommon.requestAdapayUits(paymentParams);

        System.out.println(JSON.toJSONString(response));
        if (!EAdapayStatus.SUCCEEDED.getCode().equals(response.get("status"))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "支付失败，原因：" + response.get("error_code") + "," + response.get("error_msg") + "," + response.get("error_type"));
        }
//        //调用sdk方法，创建支付，得到支付对象
//        Map<String, Object> payment = new HashMap<>();
//        try {
//            payment = Payment.create(paymentParams, merchantKey);
//        } catch (BaseAdaPayException e) {
//            e.printStackTrace();
//        }
//
//        String error_code = (String) payment.get("error_code");
//        if (null != error_code) {
//            System.out.println("创建支付返回参数：" + JSON.toJSONString(payment));
//
//            String error_msg = (String) payment.get("error_msg");
//            System.out.println("error_code:" + error_code + "............." + error_msg);
//        }
        log.info("汇付支付入参：");
        System.out.println(response);
        return response;
    }

    /**
     * 执行一个支付交易
     *
     * @return 创建的支付对象
     * @throws Exception 异常
     */
    public Map<String, Object> executePayment(String orderNo, String payChannel, BigDecimal amount, String bizNote) {
        log.info("=======execute payment begin=======");
        //创建支付对象的参数，全部参数请参考 https://docs.adapay.tech/api/index.html
        Map<String, Object> paymentParams = new HashMap<>(10);

        // 设置超时时间 单位毫秒 类型 int
        // adapay_connection_request_timeout 类型 int, 单位：毫秒 ms
        // 非必须, 默认 10000 指从连接池获取连接的 timeout
        paymentParams.put("adapay_connection_request_timeout", 1500);
        // adapay_connect_timeout 单位：毫秒 ms
        // 非必须, 默认 30000 指客户端和服务器建立连接的timeout
        paymentParams.put("adapay_connect_timeout", 1500);
        // adapay_socket_timeout 单位：毫秒 ms
        // 非必须, 默认 30000 指客户端从服务器读取数据的timeout，超出后会抛出SocketTimeOutException
        paymentParams.put("adapay_socket_timeout", 1500);

        // 设置网络区域
        // 非必须, 默认 shanghai, 如果要使用其他区域请提交工单备注服务器公网出口IP地址申请开通（如：beijing）
//        paymentParams.put("adapay_region", "beijing");

        paymentParams.put("app_id", adapayConfig.getAppid());
        // 回调地址
        paymentParams.put("notify_url", adapayConfig.getBackurl());
        paymentParams.put("order_no", orderNo);
        paymentParams.put("pay_channel", payChannel);
        paymentParams.put("pay_amt", amount);

        paymentParams.put("goods_title", "下单支付");
        paymentParams.put("goods_desc", bizNote);
        // 失效时间
        paymentParams.put("time_expire", DateUtil.dateToStr(DateUtils.addMinutes(new Date(), 1), "yyyyMMddHHmmss"));
//        paymentParams.put("div_members", "");

        //调用sdk方法，创建支付，得到支付对象
        Map<String, Object> payment = new HashMap<>();
        try {
            log.info("支付交易，请求参数：" + JSON.toJSONString(paymentParams));
            payment = Payment.create(paymentParams);

        } catch (BaseAdaPayException e) {
            log.error("支付失败，原因：" + e.getMessage());
            e.printStackTrace();
        }
        log.info("支付交易，返回参数：" + JSON.toJSONString(payment));
        log.info("=======execute payment end=======");
        return payment;
    }

    @Override
    public Boolean doRefund(Long paySerialNumber, BigDecimal realAmount, BigDecimal refundAmount,
                            String reason) {

        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            return true;
        }

        int refundPrice = refundAmount.multiply(new BigDecimal(100)).intValue();
        int realPrice = realAmount.multiply(new BigDecimal(100)).intValue();

        WXRefund refund = new WXRefund();
        refund.setWECHAT_REFUND_URL(wechatConfig.getRefundUrl());
        // 微信支付分配的公众账号ID
        refund.setAppid(wechatConfig.getAppid());
        // 商户号
        refund.setMch_id(wechatConfig.getMerchantId());
        String randomStr = MD5.GetMD5String(String.valueOf(new Random().nextInt(10000)));
        refund.setNonce_str(MD5Util.MD5Encode(randomStr, "utf-8").toLowerCase());
        refund.setOut_trade_no(paySerialNumber.toString());
//        refund.setOut_refund_no(paySerialNumber.toString());
        refund.setOut_refund_no(IdGeneratorUtil.generator().toString());
        refund.setRefund_fee(refundPrice);
        refund.setTotal_fee(realPrice);
        refund.setPrivateKey(wechatConfig.getMerchantPrivatekey());

//        refund.setBizType(bizType.getCode());
        refund.setBizNote(reason);

        String returnCode = refund.submitXmlRefund();

        if (StringUtils.isNotBlank(returnCode) && returnCode.contains("SUCCESS")) {
            log.info("微信退款成功,支付组号{}", paySerialNumber);
            return true;
        }

        return false;
    }


    @Override
    public Map<String, String> split(String urlparam) {
        Map<String, String> map = new HashMap<String, String>();
        String[] param = urlparam.split("&");
        for (String keyvalue : param) {
            String[] pair = keyvalue.split("=");
            if (pair.length == 2) {
                map.put(pair[0], WebUtils.decode(pair[1]));
            }
        }
        return map;
    }

    /**
     * 组装对象
     */
    @Override
    public AdapayCallbackInfo assembly(Map<String, String> map) {
        AdapayCallbackInfo callbackInfo = transferMapToUser(map, AdapayCallbackInfo.class);
        return callbackInfo;
    }

    @Override
    public AdaPayStatus detailStatus(String paymentId) {
        AdaPayStatus status = new AdaPayStatus();
        try {
            Map<String, Object> response = Payment.query(paymentId);
            status.setStatus((String) response.get("status"));
        } catch (BaseAdaPayException e) {
            log.error("支付结果查询出错{}", e.getMessage());
        }

        return status;
    }

    public static <T> T transferMapToUser(Map<String, String> map, Class<T> classT) {
        try {
            return transfer(map, classT.newInstance());
        } catch (InstantiationException ex) {
            log.error("map转义错误:{}", ex.getMessage());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "map转义错误");
        } catch (IllegalAccessException ex) {
            log.error("map转义错误:{}", ex.getMessage());
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "map转义错误");
        }
    }

    public static <T> T transfer(Map<String, String> map, Object obj) {

        try {
            BeanInfo beanInfo = Introspector.getBeanInfo(obj.getClass());

            PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
            for (PropertyDescriptor property : propertyDescriptors) {
                String key = property.getName();
                if (map.containsKey(key)) {
                    Object value = map.get(key);
                    // 得到property对应的setter方法
                    Method setter = property.getWriteMethod();
                    try {
                        setter.invoke(obj, value);
                    } catch (IllegalArgumentException ex) {
                        log.error("property对应的setter方法:{}", ex.getMessage());
                    }
                }
            }
        } catch (Exception ex) {
            log.error("map转义开始错误:{}", ex.getMessage());
        }

        return (T) obj;
    }

//    public static void main(String[] args) {
//        String data="{\"app_id\":\"app_511dc9b5-63d1-4e5d-9668-0582d29ef447\",\"created_time\":\"20220301214437\",\"description\":\"\",\"end_time\":\"20220301214519\",\"expend\":{\"sub_open_id\":\"2088812979469849\"},\"fee_amt\":\"0.00\",\"id\":\"002112022030121443710344729850712469504\",\"order_no\":\"528456614949429248\",\"out_trans_id\":\"2022030122001469841449116639\",\"party_order_id\":\"02212203017827728601329\",\"pay_amt\":\"0.01\",\"pay_channel\":\"alipay\",\"status\":\"succeeded\"}";
//        AdapayCallbackData callbackData = JSON.parseObject(data, AdapayCallbackData.class);
//        System.out.println("success");
//    }

    private String createSign(SortedMap<String, String> packageParams,
                              String AppKey) {
        StringBuffer sb = new StringBuffer();
        Set es = packageParams.entrySet();
        Iterator it = es.iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            String k = (String) entry.getKey();
            String v = (String) entry.getValue();
            if (null != v && !"".equals(v) && !"sign".equals(k)
                    && !"key".equals(k)) {
                sb.append(k + "=" + v + "&");
            }
        }
        sb.append("key=" + AppKey);
        String sign = MD5Util.MD5Encode(sb.toString(), "UTF-8").toUpperCase();
        return sign;
    }

    private boolean reqOrderquery(String paymentId, String orderNo) {
        log.info("******* 开始汇付订单查询 ******");
        try {
            Map<String, Object> response = Payment.query(paymentId);
            if (null == response.get("status") || !EAdapayStatus.SUCCEEDED.getCode().equals(response.get("status")) || !orderNo
                    .equals(response.get("order_no"))) {
//                log.error("汇付回调检验失败，回调状态不正确或订单号不一致");
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "汇付回调检验失败，回调状态不正确或订单号不一致");
            }
            log.info("******* 汇付订单查询结束，结果正确 ******");

            return true;

        } catch (BaseAdaPayException e) {
            log.error("汇付回调检验接口调用失败，原因{}{}", e.getMessage());
        }

        log.info("******* 汇付订单查询结束，结果不正确 ******");
        return false;
    }


    public static void main(String[] args) {
//        String apiKey = "api_live_4a8d8286-e01d-4854-8e89-dcf847ffec17";
//        String mockApiKey = "api_test_bc402229-801c-49d2-b995-028833c7cae3";
//        String rsaPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANV9tuQ8CZmHbgjludGO/3WG42FFllyKlA1gCiYt6Vp9Vg5GiNw6Oey/lbC/82MtGq3dMvZYZTVWt4eocqeZnb0ddXJVLZtCYoy4YNwWXi2JWZc87+dmNxoIjKR4ANCFr719p1W+UerbUsi9B9C7woAHoqhFtIQbB8kxVIZEOC7zAgMBAAECgYBe/MMuA0RmKeqcTNIDWjNxMCXk9pgy7nl3Bf8eA5lq6I8sZNep3MI/AvLwJEd/Hedb6iotjyDgvYeE9T6mMWQLm3CJtvd7XddjLwdo2jrSrbPqa6AAPdghsMsGfI1lhbmqcwCUhkl9uUqJA9TpG9/79jreEuqeWp1+eaIkRWvx6QJBAPJCYU9LymoxYrLEpTQP6dqWoPIT9vr36TNjNpp/tNjw7gpIBdYurD65OoX29Gd7zrM2aVHSSSlS14v38AZALFcCQQDhmZ4u7t4vua8z2Ku03/pMpV/O0cGSuYlWxn23QxbjkAXr2jqFbh/ZthhHC4yzk4Xp2/8yvQ5BNMEe7ODEEHDFAkAH1yAhGdnWL/z6viR+l9lAqslQrFa87pMMh7R3sZRfxQRfLs+Ji/8lFIeRWFm2k6ov4J3t+PlHLhgtvnt1KFSHAkBxBMR6PrCQuDVNg/6BXrPGMhMNSYfwOYLUNhxE8xdEaaKNxYYL0l+icdc3wFF8pSsxPUVb5dp+UC9vjrwLfTEBAkEApOGgdRb8XO3bYKb/5B4qySMLoriaHkJcNlu82r+79tew1nPUpVupg+PXTjDDua5uqUIiLHmbbpKy/+WHeSYjXg==";
//
//        MerConfig merConfig = new MerConfig();
//        merConfig.setApiKey(apiKey);
//        merConfig.setApiMockKey(mockApiKey);
//        merConfig.setRSAPrivateKey(rsaPrivateKey);
//        try {
//            Adapay.initWithMerConfig(merConfig);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        try {
//            Map<String, Object> response = Payment.query("002112022030211114010344932951253237760");
//            System.out.println(response.get("status"));
//        } catch (BaseAdaPayException e) {
//            log.error("aa" + e.getMessage());
//        }

        System.out.println(2855190 % 5);
    }
}
