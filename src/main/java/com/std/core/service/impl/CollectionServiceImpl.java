package com.std.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.StringValidator;
import com.std.core.enums.EApproveRecordRefType;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChannelType;
import com.std.core.enums.ECollectionCategory;
import com.std.core.enums.ECollectionContentType;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailOwnerType;
import com.std.core.enums.ECollectionDetailRefType;
import com.std.core.enums.ECollectionDetailSource;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.ECollectionDetailTransferRecordType;
import com.std.core.enums.ECollectionFileType;
import com.std.core.enums.ECollectionMetaBizType;
import com.std.core.enums.ECollectionPayStatus;
import com.std.core.enums.ECollectionPeriodCategory;
import com.std.core.enums.ECollectionPeriodPlateCategory;
import com.std.core.enums.ECollectionPeriodStatus;
import com.std.core.enums.ECollectionPhotoCategory;
import com.std.core.enums.ECollectionRightDetailType;
import com.std.core.enums.ECollectionRightDropType;
import com.std.core.enums.ECollectionRightSpecificType;
import com.std.core.enums.ECollectionRightType;
import com.std.core.enums.ECollectionRightsDetailNumberType;
import com.std.core.enums.ECollectionSaleDemandBuyType;
import com.std.core.enums.ECollectionStatus;
import com.std.core.enums.ECollectionThreeChannelType;
import com.std.core.enums.ECollectionType;
import com.std.core.enums.ECollectionYaoConfigType;
import com.std.core.enums.ECompanyDivideStatus;
import com.std.core.enums.ECompanyStatus;
import com.std.core.enums.EContractChain;
import com.std.core.enums.EContractProtocol;
import com.std.core.enums.EContractStatus;
import com.std.core.enums.EContractTokenStatus;
import com.std.core.enums.EContractTokenType;
import com.std.core.enums.ECurrency;
import com.std.core.enums.EDropOrderType;
import com.std.core.enums.EDropRecordStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EForumActionType;
import com.std.core.enums.EJourBizTypeSystem;
import com.std.core.enums.EJourBizTypeUser;
import com.std.core.enums.EJourCommon;
import com.std.core.enums.EPayRecordBizType;
import com.std.core.enums.EPayType;
import com.std.core.enums.EProducedRefType;
import com.std.core.enums.ESmsRefType;
import com.std.core.enums.ESmsTarget;
import com.std.core.enums.ESystemAccount;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.CollectionDetailMapper;
import com.std.core.mapper.CollectionMapper;
import com.std.core.mapper.UserMapper;
import com.std.core.pojo.domain.Account;
import com.std.core.pojo.domain.ApproveRecord;
import com.std.core.pojo.domain.BabyBankPayInfo;
import com.std.core.pojo.domain.BabyPayInfo;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionCategory;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.CollectionDetailUserHaveNumber;
import com.std.core.pojo.domain.CollectionDropUserList;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodRelation;
import com.std.core.pojo.domain.CollectionRight;
import com.std.core.pojo.domain.CollectionRightCompany;
import com.std.core.pojo.domain.CollectionRightRecord;
import com.std.core.pojo.domain.CollectionRightsDetail;
import com.std.core.pojo.domain.CollectionSaleDemand;
import com.std.core.pojo.domain.CollectionYaoConfig;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.Contract;
import com.std.core.pojo.domain.ContractToken;
import com.std.core.pojo.domain.DropOrder;
import com.std.core.pojo.domain.DropRecord;
import com.std.core.pojo.domain.GoodsActivity;
import com.std.core.pojo.domain.MemberConfig;
import com.std.core.pojo.domain.MonthCondition;
import com.std.core.pojo.domain.PayRecord;
import com.std.core.pojo.domain.PeriodCollectionStatistics;
import com.std.core.pojo.domain.Produced;
import com.std.core.pojo.domain.UploadFile;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserBak;
import com.std.core.pojo.domain.UserBindCard;
import com.std.core.pojo.domain.YeeBankPayInfo;
import com.std.core.pojo.request.CollectionAddPlatRemainQuantityReq;
import com.std.core.pojo.request.CollectionAssembleCreateReq;
import com.std.core.pojo.request.CollectionBlindBoxReceiveReq;
import com.std.core.pojo.request.CollectionChallengeListReq;
import com.std.core.pojo.request.CollectionChallengeNeedListReq;
import com.std.core.pojo.request.CollectionChallengeNeedReq;
import com.std.core.pojo.request.CollectionChallengePageReq;
import com.std.core.pojo.request.CollectionCompanyNotSellPageReq;
import com.std.core.pojo.request.CollectionCreateCompanyPlatformReq;
import com.std.core.pojo.request.CollectionCreatePlatformReq;
import com.std.core.pojo.request.CollectionDownXmetaReq;
import com.std.core.pojo.request.CollectionDropReq;
import com.std.core.pojo.request.CollectionDropUserCollectionReq;
import com.std.core.pojo.request.CollectionDropUserCompanyReq;
import com.std.core.pojo.request.CollectionDropUserListReq;
import com.std.core.pojo.request.CollectionDropUserReq;
import com.std.core.pojo.request.CollectionListFrontReq;
import com.std.core.pojo.request.CollectionListReq;
import com.std.core.pojo.request.CollectionModifyCompanyPlatformReq;
import com.std.core.pojo.request.CollectionModifyIntegralReq;
import com.std.core.pojo.request.CollectionModifyPlateCategoryReq;
import com.std.core.pojo.request.CollectionModifyPlatformAuditReq;
import com.std.core.pojo.request.CollectionModifyPlatformReq;
import com.std.core.pojo.request.CollectionModifyReq;
import com.std.core.pojo.request.CollectionModifySingleMaxReq;
import com.std.core.pojo.request.CollectionModifyU3dReq;
import com.std.core.pojo.request.CollectionOssAuditReq;
import com.std.core.pojo.request.CollectionOssCreateReq;
import com.std.core.pojo.request.CollectionOssModifyReq;
import com.std.core.pojo.request.CollectionOssPublishReq;
import com.std.core.pojo.request.CollectionPageFrontReq;
import com.std.core.pojo.request.CollectionPageMyOwnerReq;
import com.std.core.pojo.request.CollectionPageOrderReq;
import com.std.core.pojo.request.CollectionPagePeriodCollectionStatisticsReq;
import com.std.core.pojo.request.CollectionPagePeriodListReq;
import com.std.core.pojo.request.CollectionPagePeriodReq;
import com.std.core.pojo.request.CollectionPageReq;
import com.std.core.pojo.request.CollectionPublishXmetaReq;
import com.std.core.pojo.request.CollectionRightOssCreateReq;
import com.std.core.pojo.request.CollectionRightsDetailCreateAutoReq;
import com.std.core.pojo.request.CollectionRightsDetailListFrontReq;
import com.std.core.pojo.request.CollectionRightsDetailListReq;
import com.std.core.pojo.request.CollectionRightsModifyReq;
import com.std.core.pojo.request.CollectionSaleDemandCreateReq;
import com.std.core.pojo.request.CollectionYaoConfigCreateReq;
import com.std.core.pojo.request.PersonSellCollectionPageReq;
import com.std.core.pojo.request.TransferBatchCollectionReq;
import com.std.core.pojo.response.AdaPayInfo;
import com.std.core.pojo.response.AlipayPayOrderRes;
import com.std.core.pojo.response.CollectionChallengeRes;
import com.std.core.pojo.response.CollectionCompanyNotSellPageRes;
import com.std.core.pojo.response.CollectionContractTokenRes;
import com.std.core.pojo.response.CollectionDetailRes;
import com.std.core.pojo.response.CollectionIntegralPageRes;
import com.std.core.pojo.response.CollectionListPeriodFrontRes;
import com.std.core.pojo.response.CollectionMyOwnerPageRes;
import com.std.core.pojo.response.CollectionOrderRes;
import com.std.core.pojo.response.CollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodDetailFrontRes;
import com.std.core.pojo.response.CollectionPitPageRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.pojo.response.PersonSellCollectionPageRes;
import com.std.core.pojo.response.WechatAppPayInfo;
import com.std.core.service.IAccountService;
import com.std.core.service.IAdapayService;
import com.std.core.service.IAlipayService;
import com.std.core.service.IApproveRecordService;
import com.std.core.service.IBabyPayService;
import com.std.core.service.IBusinessChannelService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionBuyOrderService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionDetailTransferRecordService;
import com.std.core.service.ICollectionPeriodRelationService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.service.ICollectionRightCompanyService;
import com.std.core.service.ICollectionRightRecordService;
import com.std.core.service.ICollectionRightService;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.service.ICollectionSaleDemandService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICollectionYaoConfigService;
import com.std.core.service.ICommonService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IConfigService;
import com.std.core.service.IContractService;
import com.std.core.service.IContractTokenInPool15Service;
import com.std.core.service.IContractTokenService;
import com.std.core.service.IDropOrderService;
import com.std.core.service.IDropRecordService;
import com.std.core.service.IFishBoatService;
import com.std.core.service.IForumActionService;
import com.std.core.service.IGoodsActivityService;
import com.std.core.service.IMemberConfigService;
import com.std.core.service.IProducedService;
import com.std.core.service.ISmsService;
import com.std.core.service.IUserBindCardService;
import com.std.core.service.IUserService;
import com.std.core.service.IWechatService;
import com.std.core.service.IYeePayService;
import com.std.core.util.IdGeneratorUtil;
import com.std.core.util.SysConstants;
import com.std.core.util.SysConstantsCache;
import com.xmeta.opensdk.model.vo.GoodsCountVO;
import com.xmeta.opensdk.service.IXmetaService;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 藏品ServiceImpl
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
@Service
@Slf4j
public class CollectionServiceImpl implements ICollectionService {

    @Resource
    private CollectionMapper collectionMapper;

    @Resource
    private CollectionDetailMapper collectionDetailMapper;

    @Resource
    private IUserService userService;

    @Resource
    private UserMapper userMapper;

    @Resource
    private IAccountService accountService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ICollectionPeriodService collectionPeriodService;

    @Resource
    private IConfigService configService;

    @Resource
    private IContractTokenService contractTokenService;

    @Resource
    private IForumActionService forumActionService;

    @Resource
    private IMemberConfigService memberConfigService;

    @Resource
    private IAlipayService alipayService;

    @Resource
    private IAdapayService adapayService;

    @Resource
    private IBabyPayService babyPayService;

    @Resource
    private IWechatService wechatService;

    @Resource
    private ICollectionRightService collectionRightService;

    @Resource
    private ICommonService commonService;

    @Resource
    private ICollectionRightsDetailService collectionRightsDetailService;

    @Resource
    private IContractTokenInPool15Service contractTokenInPool15Service;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private IProducedService producedService;

    @Resource
    private ICollectionBuyOrderService collectionBuyOrderService;

    @Resource
    private IContractService contractService;

    @Resource
    private ICollectionDetailTransferRecordService collectionDetailTransferRecordService;

    @Resource
    private IUserBindCardService userBindCardService;

    @Resource
    private IBusinessChannelService businessChannelService;

    @Resource
    private IYeePayService yeePayService;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Resource
    private IApproveRecordService approveRecordService;

    @Resource
    private ICollectionSaleDemandService collectionSaleDemandService;

    @Resource
    private IDropOrderService dropOrderService;

    @Resource
    private IDropRecordService dropRecordService;

    @Resource
    private ISmsService smsService;

    @Resource
    private ISmsOutService smsOutService;

    @Resource
    private ICollectionPeriodRelationService collectionPeriodRelationService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICollectionRightCompanyService collectionRightCompanyService;

    @Resource
    private ICollectionRightRecordService collectionRightRecordService;

    @Resource
    private IGoodsActivityService goodsActivityService;

    @Resource
    private ICollectionYaoConfigService collectionYaoConfigService;

    @Resource
    private IXmetaService xmetaService;

    @Resource
    private IFishBoatService fishBoatService;

    /**
     * 组装新增藏品
     *
     * @param req 新增藏品入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrderPayRes create(CollectionAssembleCreateReq req, User operator, Long channelId) {
        //校验用户权益
//        if (!memberConfigService.validMemberPrivilege(operator.getId(), EUserPrivilege.USER_PRIVILEGE_18.getCode())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "你当前会员等级，不能抢占数字藏品");
//        }

        // 判断是否支持的交易方式
//        businessChannelService.listPayCheck(EPayRecordBizType.PAY_RECORD_BIZTYPE_8.getCode(), req.getPayType());
//
//        UserBindCard userBindCard = new UserBindCard();
//        if (EPayType.BANK_APPLET.getId().equals(req.getPayType()) || EPayType.BANK_YEEPAY.getId()
//                .equals(req.getPayType())) {
//            if (null == req.getBindCardId()) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "卡宾id不能为空");
//            }
//            userBindCard = userBindCardService.detail(req.getBindCardId(), operator);
//        }
//
//        // 检测名称是否重复
//        String name = checkCollectionName(req.getName());
//        req.setName(name);
//
//        Long seriesId = checkCollection(req.getModuleIdList());
//
//        ChangeSeries changeSeries = changeSeriesService.detail(seriesId);
//        Collection collection = EntityUtils.copyData(req, Collection.class);
//        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
//        collection.setLevelType(ECollectionLevelType.COLLECTION_LEVEL_TYPE_0.getCode());
//        collection.setFileType(ECollectionFileType.COLLECTION_FILETYPE_0.getCode());
//        collection.setAuthorId(changeSeries.getAuthorId());
//
//        String chainType = configService.getStringValue(SysConstantsCache.SYSTEM_CHAIN_TYPE);
//
////        if (ECollectionPayType.COLLECTION_PAYTYPE_0.getCode().equals(collection.getPayType())) {
////            collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
////            collection.setPayDatetime(new Date());
////            collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
////        } else {
////            collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_0.getCode());
////            collection.setStatus(ECollectionStatus.COLLECTION_STATUS_3.getCode());
////        }
//        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_0.getCode());
//        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_3.getCode());
//
//        List<UploadFile> uploadFileList = new ArrayList<>();
//        UploadFile uploadFile = new UploadFile(ECollectionFileType.COLLECTION_FILETYPE_0.getCode(), req.getCoverFileUrl(), null);
//        uploadFileList.add(uploadFile);
//        collection.setFileUrl(JSONArray.toJSONString(uploadFileList));
//
//        //计算支付金额
//        ChangeModuleTotalAmountRes changeModuleTotalAmountRes = changeModuleService
//                .detailFrontTotalPrice(new ChangeModuleTotalAmountReq(req.getModuleIdList()));
//
//        collection.setChainType(chainType);
//        collection.setUserId(operator.getId());
//        collection.setType(ECollectionType.COLLECTION_TYPE_2.getCode());
//        collection.setTotalQuantity(1);
//        collection.setMarketQuantity(1);
//        collection.setRemainQuantity(0);
//        //文件大小及 元/MB存储费
//        collection.setFileSize(BigDecimal.ZERO);
//        collection.setStorageFee(BigDecimal.ZERO);
//
//        Long orderCode = IdGeneratorUtil.generator();
//        collection.setPayOrderCode(orderCode.toString());
//        collection.setPayAmount(changeModuleTotalAmountRes.getTotalAmount());
//        collection.setCommissionAmount(changeModuleTotalAmountRes.getTotalAmount());
//        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
//
//        collection.setContent(changeSeries.getIntroduce());
//        collection.setCreateDatetime(new Date());
//        collection.setModuleIds(StringUtils.join(req.getModuleIdList().toArray(), ","));
//        collection.setCategory(ECollectionPeriodCategory.DERIVATIVE.getCode());
//        collection.setContractId(changeSeries.getContractId());
//
//        collection.setLockTime(changeSeries.getLockTime());
//        collectionMapper.insertSelective(collection);
//
//        //藏品组件构成
////        changeCollectionService.create(req.getModuleIdList(), collection.getId(), null);
//
//        contractTokenService.distributionToken(collection, 1,
//                ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_2.getCode(), collection.getId(),
//                EContractTokenStatus.CONTRACT_TOKEN_STATUS_3, null);
//
//        if (EBigOrderPayType.ACCOUNT.getCode().equals(req.getPayType())) {
//            //校验支付密码
//            userService.checkTradePwd(operator.getId(), req.getPwd());
//            collection.setPayBalanceAmount(collection.getPayAmount());
//            collection.setPayCashAmount(BigDecimal.ZERO);
//            paySuccess(collection, operator, EJourBizTypeUser.Collection.CreateAssembleCollection,
//                    EJourBizTypeSystem.Fee.CollectionAssembleCreateFee,
//                    EJourBizTypeUser.UserDiamondIncome.CREATE_ASSEMBLE_COLLECTION,
//                    EJourBizTypeUser.Collection.CreateAssembleCollection_UnFrozen, "创建[" + collection.getName() + "]组装藏品",
//                    ECollectionDetailBuyChannel.TWO.getCode());
//            return new OrderPayRes(orderCode, EBoolean.YES.getCode());
//        } else {
//
//            collection.setPayBalanceAmount(BigDecimal.ZERO);
//            collection.setPayCashAmount(collection.getPayAmount());
//            OrderPayRes orderPayRes = new OrderPayRes();
//            orderPayRes = payCashAmount(req.getWxAppId(), userBindCard, collection, operator,
//                    EPayRecordBizType.PAY_RECORD_BIZTYPE_7, channelId, req.getRedirectUrl());
//
//            return orderPayRes;
//        }

        return null;
    }

    private OrderPayRes payCashAmount(String wxAppId, UserBindCard userBindCard, Collection collection,
            User operator, EPayRecordBizType recordBizType, Long channelId, String redirectUr) {
        Long orderCode = Long.valueOf(collection.getPayOrderCode());
        BigDecimal payAmount = collection.getPayCashAmount();
        if (BigDecimal.ZERO.compareTo(payAmount) >= 0) {
            return null;
        }
        OrderPayRes res = new OrderPayRes();
        res.setOrderId(orderCode);
//        res.setBuySuccessFlag(EBoolean.YES.getCode());

        if (EPayType.WECHAT.getCode().equals(collection.getPayType()) || EPayType.WECHAT.getId()
                .equals(collection.getPayType())) {
            WechatAppPayInfo result = wechatService.getAppPayInfo(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, wxAppId, collection.getId());
            res.setWechatAppPayInfo(result);
        } else if (EPayType.ALIPAY.getCode().equals(collection.getPayType()) || EPayType.ALIPAY.getId()
                .equals(collection.getPayType())) {
            String signOrder = alipayService.getTradeAppPaySignedOrder(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, collection.getId());
            AlipayPayOrderRes result = new AlipayPayOrderRes(signOrder);
            res.setAlipayPayOrderRes(result);
        } else if (EPayType.ADAPAY.getCode().equals(collection.getPayType()) || EPayType.ADAPAY.getId()
                .equals(collection.getPayType())) {
            AdaPayInfo appPayInfo = adapayService.getAppPayInfo(operator.getId(), recordBizType.getCode(),
                    recordBizType.getValue(), orderCode,
                    payAmount, collection.getId());
            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.WECHAT_APPLET.getCode().equals(collection.getPayType()) || EPayType.WECHAT_APPLET.getId()
                .equals(collection.getPayType())) {
            BabyPayInfo appPayInfo = babyPayService
                    .getAppPayInfo(EPayType.WECHAT_APPLET.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode,
                            payAmount);
//            res.setAdaPayInfo(appPayInfo);
        } else if (EPayType.BANK_APPLET.getCode().equals(collection.getPayType()) || EPayType.BANK_APPLET.getId()
                .equals(collection.getPayType())) {

            PayRecord payRecord = babyPayService
                    .getAppBankPayInfo(EPayType.BANK_APPLET.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode, userBindCard,
                            payAmount, collection.getId());
            BabyBankPayInfo payInfo = new BabyBankPayInfo();
            payInfo.setBizCode(payRecord.getBizCode().toString());
            payInfo.setBizType(payRecord.getPayType());
            res.setBabyBankPayInfo(payInfo);
        } else if (EPayType.BANK_YEEPAY.getCode().equals(collection.getPayType()) || EPayType.BANK_YEEPAY.getId()
                .equals(collection.getPayType())) {

            YeeBankPayInfo payInfo = yeePayService
                    .getAppBankPayInfo(EPayType.BANK_YEEPAY.getCode(), operator.getId(), recordBizType.getCode(),
                            recordBizType.getValue(), orderCode, userBindCard,
                            payAmount, collection.getId(), channelId, redirectUr);
            res.setYeeBankPayInfo(payInfo);
        } else {
            throw new BizException(EErrorCode.CORE00000, "支付方式不支持");
        }

        return res;

    }

    @Override
    public String checkCollectionName(String name) {
        Collection condition = new Collection();
        condition.setNameStr(name);
        List<String> statusList = new ArrayList<>();
        statusList.add(ECollectionStatus.COLLECTION_STATUS_7.getCode());
        condition.setNoStatusList(statusList);
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(collectionList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "藏品名称已存在！");
        }

        try {
            return configService.checkSensitiveKeyword(name);
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "藏品名称带有敏感词汇");
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchTransfer(TransferBatchCollectionReq request, User operator) {
        User fromUser = userService.detailSimple(request.getFromUserId());
        User toUser = userService.detailSimple(request.getToUserId());
        Collection collection = collectionService.detailSimple(request.getCollectionId());

        List<CollectionDetail> collectionDetailList = collectionDetailService
                .list(ECollectionDetailOwnerType.CUSER.getCode(), request.getFromUserId(),
                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), request.getCollectionId());

        if (CollectionUtils.isEmpty(collectionDetailList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "来源用户没有可转赠的" + collection.getName() + "藏品");
        } else {
            if (collectionDetailList.size() < request.getQuantity().intValue()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "来源用户没有可转赠藏品数量不足");
            }
        }

        List<Long> collectionDetailIdList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(collectionDetailList)) {
            if (null != request.getQuantity() && collectionDetailList.size() > request.getQuantity()) {
                collectionDetailList = collectionDetailList.subList(0, request.getQuantity());
            }

            for (CollectionDetail collectionDetail : collectionDetailList) {
                collectionDetail = collectionDetailService.detailForUpdate(collectionDetail.getId());
                if (!ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                    throw new BizException(EErrorCode.CORE00000.getCode(), collectionDetail.getId() + "藏品状态不是可售卖");
                }

                //变更藏品拥有者
                collectionDetail.setOwnerId(toUser.getId());
                collectionDetail.setUpdateTime(System.currentTimeMillis());
                collectionDetailMapper.updateByPrimaryKeySelective(collectionDetail);

                collectionDetailIdList.add(collectionDetail.getId());
            }
        }

        //插入转赠记录
        collectionDetailTransferRecordService
                .createByAccount(fromUser, toUser, ECollectionDetailTransferRecordType.OSS.getCode(), collectionDetailIdList,
                        BigDecimal.ZERO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyU3d(CollectionModifyU3dReq req, User operator) {
        Collection collection = collectionMapper.selectByPrimaryKey(req.getId());

        Collection data = new Collection();
        data.setId(collection.getId());
        if (EBoolean.YES.getCode().equals(req.getU3dFlag())) {
            data.setU3dFlag(EBoolean.YES.getCode());
        } else {
            data.setU3dFlag(EBoolean.NO.getCode());
        }
        data.setUpdater(operator.getId());
        data.setUpdateDatetime(new Date());
        collectionMapper.updateByPrimaryKeySelective(data);
    }

    @Override
    public List<Collection> queryU3dCollectionList() {
        Collection condition = new Collection();
        condition.setU3dFlag(EBoolean.YES.getCode());
        return collectionMapper.selectByCondition(condition);
    }

    @Override
    public void drop(CollectionDropReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
//        if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
//        }

        Collection collection = collectionService.detailSimple(req.getCollectionId());

        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }

        // 检查是否有操作权限
        checkOperation(collection);

//        if ((!EUserKind.SYS.getCode().equals(operator.getKind()) && !EUserKind.BP.getCode().equals(operator.getKind())) || (
//                EUserKind.BP.getCode().equals(operator.getKind()) && !collection.getAuthorId().equals(operator.getCompanyId()))) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
//        }

        List<UserBak> userBakList = userMapper.selectByConditionRefId(new UserBak());
        log.error("空投藏品数" + userBakList.size());

//        req.setDropNumber(1);
        Integer totalDropNum = 0;
        if (collection.getRemainQuantity() < userBakList.size()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品剩余量不足");
        }
        if (CollectionUtils.isNotEmpty(userBakList)) {
            for (UserBak userBak : userBakList) {
                for (int i = 0; i < userBak.getQuantity(); i++) {
                    try {
                        collectionDetailService.dropCollection(collection, userBak, collection.getLockTime(), i);
                        totalDropNum++;
                    } catch (Exception e) {
                        log.error("空投错误userBakId[{}]", userBak.getId());
                    }
                    try {
                        Thread.sleep(100);
                    } catch (Exception e) {

                    }
                }
            }
        }

        //更新作品剩余量
        collection.setRemainQuantity(collection.getRemainQuantity() - totalDropNum);
        collectionService.modify(collection);
    }

    @Override
    public void checkOperation(Collection collection) {

        if (!ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode().equals(collection.getThreeChannelType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "作品不属于麦塔");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void drop(CollectionDropUserReq req, User operator) {
        // 判断是否拥有该渠道的发布权限
//        if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
//        }

        Collection collection = collectionService.detailSimple(req.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }
        // 检查是否有操作权限
        checkOperation(collection);
        Integer dropQuantity = req.getDropNumber().intValue() * req.getUserIdList().size();

        Integer remainQuantity = collection.getRemainQuantity();

        if (remainQuantity < dropQuantity) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "剩余份数还有" + remainQuantity + "份,不足于空投");
        }

        if (collection != null && !ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }

        int totalDropNum = 0;

        // 记录序号
        Long orderId = IdGeneratorUtil.generator();
        DropOrder dropOrder = new DropOrder();
        dropOrder.setId(orderId);
        dropOrder.setType(EDropOrderType.DROP_ORDER_TYPE_0.getCode());
        dropOrder.setCollectionId(collection.getId());
        dropOrder.setDropNumber(req.getDropNumber());
        dropOrder.setTotalDropNumber(dropQuantity);
        dropOrder.setDropUserNumber(req.getUserIdList().size());
        dropOrder.setCreater(operator.getId());
        dropOrder.setCreaterKind(operator.getKind());
        dropOrder.setCreatrName(operator.getLoginName());
        dropOrder.setCreateDatetime(new Date());

        List<DropRecord> dropRecordList = new ArrayList<>();
        for (Long userId : req.getUserIdList()) {

            i:
            for (int i = 0; i < req.getDropNumber(); i++) {
                // 检查用户持有该藏品数量是否上限
//                boolean flag = collectionDetailService.checkCollectionCount(req.getCollectionId(), collection.getSingleMaxQuantity(), 1, operator);
//                if (flag) {
//                    break i;
//                }

                User user = userService.detail(userId);

                DropRecord dropRecord = new DropRecord();
                dropRecord.setOrderId(orderId);
                dropRecord.setCollectionId(collection.getId());
                dropRecord.setType(EDropOrderType.DROP_ORDER_TYPE_0.getCode());
                dropRecord.setUserId(user.getId());

                collectionDetailService.dropCollectionUser(collection, user, collection.getLockTime());
                totalDropNum++;
                dropRecord.setStatus(EDropRecordStatus.DROP_RECORD_STATUS_1.getCode());
                dropRecord.setCreater(operator.getId());
                dropRecord.setCreaterKind(operator.getKind());
                dropRecord.setCreatrName(operator.getLoginName());
                dropRecord.setCreateDatetime(new Date());
                dropRecordList.add(dropRecord);
            }
        }
        dropOrder.setSuccessDropUserNumber(totalDropNum);
        dropOrder.setFailureDropUserNumber(dropQuantity - totalDropNum);

        //更新作品剩余量
        collection.setRemainQuantity(collection.getRemainQuantity() - totalDropNum);

        collectionService.modify(collection);

        // 生成空投记录
        dropOrderService.create(dropOrder);
        // 生成空投记录明细
        dropRecordService.batchCreate(dropRecordList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dropUserMobile(CollectionDropUserCompanyReq req, User operator) {

        Collection collection = collectionService.detailSimple(req.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }
        // 检查是否有操作权限
        checkOperation(collection);

        Set<String> setMobileList = new HashSet<>(req.getMobileList());

        if (setMobileList.size() != req.getMobileList().size()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "存在重复的手机号");
        }

        Integer dropQuantity = req.getDropNumber().intValue() * req.getMobileList().size();

        Integer remainQuantity = 0;
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {
            remainQuantity = collection.getRemainQuantity();
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
        }

        if (remainQuantity < dropQuantity) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "剩余份数还有" + remainQuantity + "份,不足于空投");
        }

        if (collection != null && !ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }

        int totalDropNum = 0;

        // 记录序号
        Long orderId = IdGeneratorUtil.generator();
        DropOrder dropOrder = new DropOrder();
        dropOrder.setId(orderId);
        dropOrder.setType(EDropOrderType.DROP_ORDER_TYPE_0.getCode());
        dropOrder.setCollectionId(collection.getId());
        dropOrder.setDropNumber(req.getDropNumber());
        dropOrder.setTotalDropNumber(dropQuantity);
        dropOrder.setDropUserNumber(req.getMobileList().size());
        dropOrder.setCreater(operator.getId());
        dropOrder.setCreaterKind(operator.getKind());
        dropOrder.setCreatrName(operator.getLoginName());
        dropOrder.setCreateDatetime(new Date());

        List<DropRecord> dropRecordList = new ArrayList<>();
        for (String mobile : req.getMobileList()) {

            i:
            for (int i = 0; i < req.getDropNumber(); i++) {
                // 检查用户持有该藏品数量是否上限
//                boolean flag = collectionDetailService.checkCollectionCount(req.getCollectionId(), collection.getSingleMaxQuantity(), 1, operator);
//                if (flag) {
//                    break i;
//                }

                DropRecord dropRecord = new DropRecord();
                dropRecord.setOrderId(orderId);
                dropRecord.setCollectionId(collection.getId());
                dropRecord.setType(EDropOrderType.DROP_ORDER_TYPE_0.getCode());
                dropRecord.setMobile(mobile);
                User user = userService.detailBrief(mobile, EUserKind.C.getCode());
                if (null == user) {
                    dropRecord.setStatus(EDropRecordStatus.DROP_RECORD_STATUS_0.getCode());
                    dropRecord.setRemark("用户不存在");
                } else {
                    dropRecord.setUserId(user.getId());
                    collectionDetailService.dropCollectionUser(collection, user, collection.getLockTime());
                    totalDropNum++;
                    dropRecord.setStatus(EDropRecordStatus.DROP_RECORD_STATUS_1.getCode());
                }

                dropRecord.setCreater(operator.getId());
                dropRecord.setCreaterKind(operator.getKind());
                dropRecord.setCreatrName(operator.getLoginName());
                dropRecord.setCreateDatetime(new Date());
                dropRecordList.add(dropRecord);
            }
        }
        dropOrder.setSuccessDropUserNumber(totalDropNum);
        dropOrder.setFailureDropUserNumber(dropQuantity - totalDropNum);

        //更新作品剩余量
        collection.setRemainQuantity(collection.getRemainQuantity() - totalDropNum);
        collectionService.modify(collection);

        // 生成空投记录
        dropOrderService.create(dropOrder);
        // 生成空投记录明细
        dropRecordService.batchCreate(dropRecordList);
    }

    @Override
    public void create(Collection collection) {
        collectionMapper.channelInsert(collection);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void blindBoxReceive(CollectionBlindBoxReceiveReq req, User operator) {
        Collection collection = collectionMapper.selectByPrimaryKey(req.getId());
        if (!ECollectionStatus.COLLECTION_STATUS_TORECEIVE.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "背包状态不是待领取");
        }
        collection.setName(req.getName());
        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_0.getCode());
        collection.setContent(req.getContent());
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        collectionMapper.updateByPrimaryKeySelective(collection);
        //查询型号更新状态
        List<CollectionDetail> collectionDetailList = collectionDetailService
                .list(req.getId(), ECollectionStatus.COLLECTION_STATUS_TORECEIVE
                        .getCode());
        if (CollectionUtils.isNotEmpty(collectionDetailList)) {
            for (CollectionDetail collectionDetail : collectionDetailList) {
                collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
                collectionDetail.setUpdateTime(System.currentTimeMillis());
                collectionDetailService.modify(collectionDetail);
            }
        }
    }

    /**
     * 检查组装的藏品是否已存在
     */
    @Override
    public Long checkCollection(List<Long> moduleIdList) {

//        if (CollectionUtils.isEmpty(moduleIdList)) {
//            return null;
//        }
//        Collection condition = new Collection();
//        String moduleIds = StringUtils.join(moduleIdList, ",");
//        condition.setModuleIds(moduleIds);
//        condition.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
//        List<Collection> collectionList = collectionMapper.selectByCondition(condition);
//
//        if (CollectionUtils.isNotEmpty(collectionList)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该组件组合已存在");
//        }
//
//        Long seriesId = null;
//        List<Long> collectionIdList = new ArrayList<>();
//        List<Long> moduleTypeIdList = new ArrayList<>();
//        for (Long id : moduleIdList) {
//            ChangeModule changeModule = changeModuleService.detail(id);
//
//            if (null == seriesId) {
//                seriesId = changeModule.getSeriesId();
//            }
//
//            if (!seriesId.equals(changeModule.getSeriesId())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "有组件不是同一系列的");
//            }
//
//            ChangeCollection collection = new ChangeCollection();
//            collection.setModuleId(id);
//            collection.setCollectionIdList(collectionIdList);
//            collectionIdList = changeCollectionService.checkCollection(collection);
//
//            if (CollectionUtils.isEmpty(collectionIdList)) {
//                break;
//            }
//        }
//
//        // 查询所有组件类型
//        for (Long id : moduleIdList) {
//            ChangeModule changeModule = changeModuleService.detail(id);
//
//            moduleTypeIdList.add(changeModule.getTypeId());
//        }
//
//        List<Long> typeIdList = changeTypeService.getChooseType(seriesId);
//        for (Long typeId : typeIdList) {
//            if (!moduleTypeIdList.contains(typeId)) {
//                ChangeType changeType = changeTypeService.detail(typeId);
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择【" + changeType.getName() + "】的组件");
//            }
//        }
//
//        if (CollectionUtils.isNotEmpty(collectionIdList)) {
//            for (Long collectionId : collectionIdList) {
//                int size = changeCollectionService.detailCountByCollectionId(collectionId);
//                if (size == moduleIdList.size()) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该组合已存在，请重新选择");
//                }
//            }
//        }
//
//        return seriesId;
        return null;
    }

    @Override
    public List<CollectionListPeriodFrontRes> periodList(CollectionPagePeriodListReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        condition.setOrderBy("t.id desc");
        condition.setNoType(ECollectionType.COLLECTION_TYPE_2.getCode());
        condition.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorId(operator.getCompanyId());
        }
        List<Collection> collectionList = collectionMapper.selectByConditionOss(condition);

        List<CollectionListPeriodFrontRes> resList = collectionList.stream().map(x -> {
            CollectionListPeriodFrontRes res = new CollectionListPeriodFrontRes();
            BeanUtils.copyProperties(x, res);

//            Company company = companyService.detail(res.getAuthorId());
//            res.setAuthor(company.getShortName());
//            res.setAuthorPic(company.getLogo());
            return res;
        }).collect(Collectors.toList());
        return resList;
    }

    @Override
    public List<Collection> periodPage(CollectionPagePeriodReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setOrderBy("id desc");

        if (StringUtils.isNotBlank(request.getLockTimeFlag())) {
            if (EBoolean.NO.getCode().equals(request.getLockTimeFlag())) {
                condition.setLockTime(-1);
            } else if (EBoolean.YES.getCode().equals(request.getLockTimeFlag())) {
                condition.setLockTimeMin(0);
            }
        }

        if (null == request.getTypeList() || CollectionUtils.isEmpty(request.getTypeList())) {
            List<String> typeList = new ArrayList<>();
            typeList.add(ECollectionType.COLLECTION_TYPE_1.getCode());
            typeList.add(ECollectionType.COLLECTION_TYPE_3.getCode());
            condition.setTypeList(typeList);
        }

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorId(operator.getCompanyId());
            List<String> noStatusList = new ArrayList<>();
            noStatusList.add(ECollectionStatus.COLLECTION_STATUS_6.getCode());
            noStatusList.add(ECollectionStatus.COLLECTION_STATUS_7.getCode());
            condition.setNoStatusList(noStatusList);
        }
        condition.setRemainQuantityFlag(null);
        if (StringUtils.isNotBlank(request.getRemainQuantityFlag()) && EBoolean.YES.getCode().equals(request.getRemainQuantityFlag())) {
            condition.setRemainQuantityFlag(EBoolean.YES.getCode());
        }
        if (EBoolean.NO.getCode().equals(request.getUseFlag())) {
            List<String> buyTypeList = new ArrayList<>();
            buyTypeList.add(ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode());
            buyTypeList.add(ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode());
            condition.setBuyTypeList(buyTypeList);
        }

        List<Collection> collectionList = collectionMapper.selectByConditionOss(condition);
        // 转译UserId
        collectionList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(item.getFileUrl(),
                        UploadFile.class);
                item.setFileList(uploadFiles);
            }
            if (StringUtils.isNotBlank(item.getTags())) {
                List<String> result = Arrays.asList(item.getTags().split(","));
                item.setTagList(result);
            }

            Company company = companyService.detail(item.getAuthorId());
            item.setAuthor(company.getName());
            item.setAuthorPic(company.getLogo());
            if (null != item.getPayBalanceAmount() && item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
                item.setIsDeduction(EBoolean.YES.getCode());
            } else {
                item.setIsDeduction(EBoolean.NO.getCode());
            }
            if (null != item.getProducedId()) {
                Produced produced = producedService.detail(item.getProducedId());
                item.setProducedId(produced.getId());
                item.setProducedName(produced.getName());
                item.setProducedPic(produced.getPic());
            }

            if (null != item.getDivideAuthorId()) {
                item.setDivideAuthor(companyService.detail(item.getDivideAuthorId()).getName());
            }

            //获取权益列表
            CollectionRightsDetailListReq collectionRightsDetailListReq = new CollectionRightsDetailListReq();
            collectionRightsDetailListReq.setCollectionId(item.getId());
            item.setRightsDetailList(collectionRightsDetailService.list(collectionRightsDetailListReq));

            item.setCollectionRightList(collectionRightService.list(item.getId()));
            if (CollectionUtils.isNotEmpty(item.getCollectionRightList())) {
                for (CollectionRight collectionRight : item.getCollectionRightList()) {
                    if (ECollectionRightDetailType.COLLECTION_RIGHT_DETAIL_TYPE_1.getCode().equals(collectionRight.getType())
                            && null != collectionRight.getRefId()) {
//                        ChangeSeries changeSeries = changeSeriesService.detail(collectionRight.getRefId());
//                        collectionRight.setRefName(changeSeries.getName());
                    }
                }
            }

            // 价格出参
            CollectionSaleDemand saleDemand = collectionSaleDemandService.detailByCollectionId(item.getId());
            item.setCollectionSaleDemand(saleDemand);
            if (null != saleDemand) {
                item.setPrice(saleDemand.getPrice());
            }

            if (null != item.getContractId()) {
                Contract contract = contractService.detail(item.getContractId());
                item.setChain(contract.getChain());
            }

            // 获取爻转化的数量
            item.setYinYao(BigDecimal.ZERO);
            item.setYangYao(BigDecimal.ZERO);
            CollectionYaoConfig collectionYaoConfig = collectionYaoConfigService
                    .detailByCollection(item.getId(), ECollectionYaoConfigType.COLLECTION_YAO_CONFIG_TYPE_0.getCode());
            if (null != collectionYaoConfig) {
                item.setYinYao(collectionYaoConfig.getYinYao());
                item.setYangYao(collectionYaoConfig.getYangYao());
                item.setYaoConfigStatus(collectionYaoConfig.getStatus());
            }
        });

        return collectionList;
    }

    @Override
    public List<Collection> periodPageSimple(CollectionPagePeriodReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setOrderBy("id desc");

        if (null == request.getTypeList() || CollectionUtils.isEmpty(request.getTypeList())) {
            List<String> typeList = new ArrayList<>();
            typeList.add(ECollectionType.COLLECTION_TYPE_1.getCode());
            typeList.add(ECollectionType.COLLECTION_TYPE_3.getCode());
            condition.setTypeList(typeList);
        }

        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            condition.setAuthorId(operator.getCompanyId());
            List<String> noStatusList = new ArrayList<>();
            noStatusList.add(ECollectionStatus.COLLECTION_STATUS_7.getCode());
            condition.setNoStatusList(noStatusList);
        }
        condition.setRemainQuantityFlag(null);
        if (StringUtils.isNotBlank(request.getRemainQuantityFlag()) && EBoolean.YES.getCode().equals(request.getRemainQuantityFlag())) {
            condition.setRemainQuantityFlag(EBoolean.YES.getCode());
        }
        if (EBoolean.NO.getCode().equals(request.getUseFlag())) {
            List<String> buyTypeList = new ArrayList<>();
            buyTypeList.add(ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode());
            buyTypeList.add(ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_1.getCode());
            condition.setBuyTypeList(buyTypeList);
        }

        List<Collection> collectionList = collectionMapper.selectByConditionOss(condition);

        return collectionList;
    }


    @Override
    public List<CollectionPageRes> listPersonFront(CollectionListFrontReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setOrderBy("id desc");
        condition.setOwnerId(operator.getId());
        condition.setDownStatus(EBoolean.NO.getCode());
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(ECollectionStatus.COLLECTION_STATUS_TORECEIVE.getCode());
        condition.setNoStatusList(noStatusList);

        if (ECollectionPhotoCategory.PHOTO.getCode().equals(request.getCategory())) {
            condition.setType(ECollectionType.COLLECTION_TYPE_2.getCode());
        }
        List<Collection> collectionList = collectionMapper.selectextendByCondition(condition);

        List<CollectionPageRes> resList = collectionList.stream().map((entity) -> {

            CollectionPageRes res = new CollectionPageRes();
            if (StringUtils.isNotBlank(entity.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(entity.getFileUrl(),
                        UploadFile.class);
                entity.setFileList(uploadFiles);
            }
            BeanUtils.copyProperties(entity, res);
            res.setRemainQuantity(entity.getAvailableSellQuantity().intValue());
            if (ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(res.getStatus())) {

                if (collectionDetailService
                        .countByCondition(operator.getId(), entity.getId(),
                                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode())
                        > 0) {
                    res.setStatus(ECollectionStatus.COLLECTION_STATUS_2.getCode());
                }

                if (res.getRemainQuantity() == 1) {
                    CollectionDetail collectionDetail = collectionDetailService
                            .detail(res.getId(), operator.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_3.getCode());
                    if (collectionDetail != null) {
                        res.setStatus(ECollectionStatus.COLLECTION_STATUS_4.getCode());
                    }
                }
            }

            if (ECollectionType.COLLECTION_TYPE_2.getCode().equals(entity.getType())) {
                List<CollectionDetail> collectionDetailList = collectionDetailService.list(entity.getId());
                if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                    res.setCollectionDetailId(collectionDetailList.get(0).getId());
                }
            }
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public List<CollectionPageRes> collectionDetailPersonFront(CollectionListFrontReq request, User operator) {
//        CollectionDetail condition = new CollectionDetail();
//        condition.setOrderBy("id desc");
//        condition.setOwnerId(operator.getId());
//        List<CollectionPageRes> collectionPageRes = collectionDetailService.collectionDetailPersonFront(condition);
//
//        for (CollectionPageRes res : collectionPageRes) {
//            if (StringUtils.isNotBlank(res.getFileUrl())) {
//                List<UploadFile> uploadFiles = JSONArray.parseArray(res.getFileUrl(),
//                        UploadFile.class);
//                res.setFileList(uploadFiles);
//            }
//
//            if (ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(res.getStatus())) {
//
//                if (collectionDetailService
//                        .countByCondition(operator.getId(), res.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode())
//                        > 0) {
//                    res.setStatus(ECollectionStatus.COLLECTION_STATUS_2.getCode());
//                }
//
//                if (res.getRemainQuantity() == 1) {
//                    CollectionDetail collectionDetail = collectionDetailService
//                            .detail(res.getId(), operator.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_3.getCode());
//                    if (collectionDetail != null) {
//                        res.setStatus(ECollectionStatus.COLLECTION_STATUS_4.getCode());
//                    }
//                }
//            }
//
//            if (ECollectionType.COLLECTION_TYPE_2.getCode().equals(res.getType())) {
//                List<CollectionDetail> collectionDetailList = collectionDetailService.list(res.getId());
//                if (CollectionUtils.isNotEmpty(collectionDetailList)) {
//                    res.setCollectionDetailId(collectionDetailList.get(0).getId());
//                }
//            }
//        }
//        return collectionPageRes;
        return null;
    }

    @Override
    public List<Collection> pageOss(CollectionPageReq request) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        if (StringUtils.isNotBlank(request.getLockFlag())) {
            if (EBoolean.YES.getCode().equals(request.getLockFlag())) {
                condition.setLockTime(-1);
            } else {
                condition.setNoLockTime(1);
            }
        }

        condition.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        return collectionMapper.selectByConditionOss(condition);
    }

    @Override
    public List<CollectionChallengeRes> pageChallengeByCompany(CollectionPageReq request) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        return collectionMapper.selectChallengeByCondition(condition);
    }

    @Override
    public List<CollectionMyOwnerPageRes> selectMyOwnerPage(CollectionPageMyOwnerReq request, User operator) {
        StringValidator.validateEmoji(request.getKeywords());

        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setUserId(operator.getId());
        return collectionMapper.selectMyOwnerPage(condition);
    }

    /**
     * 修改板块
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyPlateCategory(CollectionModifyPlateCategoryReq request, User operator) {
        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(request.getPlateCategory());
        for (Long id : request.getIdList()) {
            Collection collection = detailSimple(id);
            Collection modifyCollection = new Collection();
            modifyCollection.setId(collection.getId());
            modifyCollection.setPlateCategory(request.getPlateCategory());
            collectionMapper.updateByPrimaryKeySelective(modifyCollection);

            // 藏品修改板块
            collectionDetailService.modifyPlateCategory(id, request.getPlateCategory());
            // 期数修改板块
            collectionPeriodService.modifyPlateCategory(id, request.getPlateCategory());
        }
    }

    /**
     * 增加平台数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addPlatRemainQuantity(CollectionAddPlatRemainQuantityReq request, User operator) {
        Collection collection = detailForUpdate(request.getId());
        int platRemainQuantity = collection.getPlatRemainQuantity() + request.getPlatRemainQuantity();
        if (platRemainQuantity < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "平台剩余数量过小");
        }

        int remainQuantity = collection.getRemainQuantity() - request.getPlatRemainQuantity();
        if (remainQuantity < 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方剩余数量过小");
        }

        Collection modifyCollection = new Collection();
        modifyCollection.setId(request.getId());
        modifyCollection.setPlatRemainQuantity(platRemainQuantity);
        modifyCollection.setRemainQuantity(remainQuantity);
        collectionMapper.updateByPrimaryKeySelective(modifyCollection);
    }

    @Override
    public Integer sumCollectionCount(Long authorId, String status) {
        return collectionMapper.sumTotalQuantity(authorId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDown(CollectionOssPublishReq req, User operator) {
        for (Long id : req.getIdList()) {
            Collection collection = collectionMapper.selectByPrimaryKey(id);
            checkOperation(collection);
            if (!EUserKind.SYS.getCode().equals(operator.getKind()) && !EUserKind.BP.getCode().equals(operator.getKind())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
            } else if (!EUserKind.SYS.getCode().equals(operator.getKind()) && !operator.getCompanyId().equals(collection.getAuthorId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
            }
            if (ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                collection.setStatus(ECollectionStatus.COLLECTION_STATUS_6.getCode());
            }
            collection.setUpdater(operator.getUpdater());
            collection.setUpdaterName(operator.getUpdaterName());
            collection.setUpdateDatetime(new Date());

            collectionMapper.updateByPrimaryKeySelective(collection);
        }

    }

    @Override
    public Collection detailForUpdate(Long id) {
        Collection collection = collectionMapper.selectForUpdate(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collection;
    }

    /**
     * 前端查询所有作品
     */
    @Override
    public CollectionPeriodDetailFrontRes detailFrontAll(Long id, User operator) {
        Collection collection = detail(id);

        CollectionPeriodDetailFrontRes res = new CollectionPeriodDetailFrontRes();
        BeanUtils.copyProperties(collection, res);

        Company company = companyService.detail(collection.getAuthorId());
        res.setAuthor(company.getShortName());
        res.setAuthorPic(company.getLogo());
        res.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());

        if (StringUtils.isNotBlank(res.getTags())) {
            List<String> result = Arrays.asList(res.getTags().split(","));
            res.setTagList(result);
        }

        // 展示文件
        CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes = new CollectionPeriodCollectionPageRes();
        BeanUtils.copyProperties(collection, collectionPeriodCollectionPageRes);
        collectionPeriodCollectionPageRes.setFileList(collection.getFileList());
        collectionPeriodCollectionPageRes.setFileType(collection.getFileType());

        List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
        sellCollectionList.add(collectionPeriodCollectionPageRes);
        res.setSellCollectionList(sellCollectionList);

        //链上信息todo
//        CollectionContractTokenRes contractTokenRes = contractTokenService.periodToken(id);
        CollectionContractTokenRes contractTokenRes = contractTokenService.collectionToken(id);
        res.setContractToken(contractTokenRes);
        List<CollectionContractTokenRes> contractTokenList = new ArrayList<>();
        contractTokenList.add(contractTokenRes);
        res.setContractTokenList(contractTokenList);

        res.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
        res.setBuyNotice(configService.getStringValue(SysConstants.COLLECTION_PERIOD_BUY_NOTE));

        //分享介绍
        res.setIntroduce(configService.getStringValue(SysConstants.SHARE_NOTE));

//        if (null != res.getLockTime() && -1 != res.getLockTime()) {
//            res.setLockDay(res.getLockTime() / 24L);
//            res.setLockHour(res.getLockTime() % 24L);
//        }

        res.setLockTime(res.getTransformLimitTime());

        if (null != res.getTransformLimitTime() && -1 != res.getTransformLimitTime()) {
            res.setLockDay(res.getTransformLimitTime() / 24L);
            res.setLockHour(res.getTransformLimitTime() % 24L);
        }

        //获取权益列表
        if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(res.getRightType())) {
            //获取权限列表
            CollectionRightsDetailListFrontReq rightsDetailListFrontReq = new CollectionRightsDetailListFrontReq();
            rightsDetailListFrontReq.setCollectionId(collection.getId());
            res.setRightList(collectionRightsDetailService.listFrontNotRemain(rightsDetailListFrontReq, null));
        }

        return res;
    }

    @Override
    public List<Collection> challengePage(CollectionChallengePageReq request) {
        Collection condititon = new Collection();
        condititon.setAuthorId(request.getCompanyId());
        condititon.setName(request.getName());
        return collectionMapper.challengePage(condititon);
    }

    @Override
    public List<Collection> challengeList(CollectionChallengeListReq request) {
        Collection condititon = new Collection();
        condititon.setAuthorId(request.getCompanyId());
        condititon.setName(request.getName());
        condititon.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        return collectionMapper.challengePage(condititon);
    }

    @Override
    public List<Collection> challengeNeed(CollectionChallengeNeedReq request) {
        Collection condititon = new Collection();
        condititon.setName(request.getName());
        condititon.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        return collectionMapper.selectByCondition(condititon);
    }

    @Override
    public List<Collection> challengeNeedList(CollectionChallengeNeedListReq request) {
        Collection condititon = new Collection();
        condititon.setName(request.getName());
        condititon.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        return collectionMapper.selectByCondition(condititon);
    }

    void toPay(Collection collection, User operator) {

        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_3.getCode());
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_0.getCode());
        collection.setPayDatetime(new Date());
        collectionMapper.updateByPrimaryKeySelective(collection);
    }

    @Override
    public Collection getCollectionByBizCode(String bizCode) {

        Collection condition = new Collection();
        condition.setPayOrderCode(bizCode);
        List<Collection> list = collectionMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void payOrderCancel(String orderCode) {

        Collection collection = getCollectionByBizCode(orderCode);
        if (!collection.getStatus().equals(ECollectionStatus.COLLECTION_STATUS_3.getCode())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "创建藏品不是待支付状态");
        }
        if (!EBigOrderPayType.ACCOUNT.getCode().equals(collection.getPayType()) && collection.getPayBalanceAmount() != null &&
                collection.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            Account account = accountService.getAccount(collection.getUserId(), ECurrency.CNY.getCode());

            accountService.unfrozenAmount(account, collection.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, EJourBizTypeUser.Collection.CreateCollection_UnFrozen,
                    collection.getId(), EJourBizTypeUser.Collection.CreateCollection_UnFrozen, collection.getName());
        }
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_2.getCode());
        collectionMapper.updateByPrimaryKeySelective(collection);

        //代币tokenId释放
        ContractToken contractToken = new ContractToken();
        contractToken.setStatus(EContractTokenStatus.CONTRACT_TOKEN_STATUS_3.getCode());
        contractToken.setType(EContractTokenType.CONTRACT_TOKEN_TYPE_0.getCode());
        contractToken.setCollectionId(collection.getId());
        List<ContractToken> contractTokenList = contractTokenService.list(contractToken);
        for (ContractToken token : contractTokenList) {
            token.setCollectionId(0L);
            token.setStatus(EContractTokenStatus.CONTRACT_TOKEN_STATUS_0.getCode());
            contractTokenService.update(token);
        }
    }

    void paySuccess(Collection collection, User operator, EJourCommon changeBizType,
            EJourCommon platChangeBizType, EJourCommon diamondChangeBizType, EJourCommon unfrozenBizType, String remark,
            String buyChannel) {
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        collection.setPayDatetime(new Date());
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        collectionMapper.updateByPrimaryKeySelective(collection);

        BigDecimal price = collection.getPayAmount().divide(new BigDecimal(collection.getTotalQuantity()), 2, RoundingMode.DOWN);

        Integer firstMarketLockHour = collection.getLockTime();
        collectionDetailService.doGenerateDetail(operator, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_2
                        .getCode(), collection.getId(), collection, price,
                collection.getTotalQuantity(), buyChannel, firstMarketLockHour, collection.getTransformLimitTime(),
                ECollectionDetailSource.CHANGE_TYPE_STATUS_4.getCode(), null);

        Account buyAccount = accountService.getAccount(collection.getUserId(), ECurrency.CNY.getCode());
        if (!EBigOrderPayType.ACCOUNT.getCode().equals(collection.getPayType()) && collection.getPayBalanceAmount() != null &&
                collection.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {

            accountService.unfrozenAmount(buyAccount, collection.getPayBalanceAmount(),
                    EJourBizTypeUser.Collection.Collection, unfrozenBizType,
                    collection.getId(), EJourBizTypeUser.Collection.CreateCollection_UnFrozen, collection.getName());
        }

        pay(collection, collection.getPayBalanceAmount(), collection.getPayAmount(),
                buyAccount, changeBizType, platChangeBizType,
                diamondChangeBizType, remark);

        // 根据作品序号查询相关组件序号
//        List<Long> moduleIdList = changeCollectionService.detailModuleIdByCollectionId(collection.getId());
//
//        List<Long> recommendIdList = changeRecommendService.getAllRecommend();
//        if (CollectionUtils.isNotEmpty(recommendIdList)) {
//            for (Long moduleId : moduleIdList) {
//                ChangeRecommendModule condition = new ChangeRecommendModule();
//                condition.setRecommendIdList(recommendIdList);
//                condition.setModuleId(moduleId);
//                recommendIdList = changeRecommendModuleService.getRecommendByModuleId(condition);
//
//                if (CollectionUtils.isEmpty(recommendIdList)) {
//                    break;
//                }
//            }
//        }
//
//        // 如果已售的款式和推荐的重合，说明该推荐已售，下架掉
//        for (Long recommendId : recommendIdList) {
//            int i = changeRecommendModuleService.detailCountByRecommendId(recommendId);
//
//            if (i == moduleIdList.size()) {
//                ChangeRecommend recommend = changeRecommendService.detail(recommendId);
//                recommend.setStatus(EChangeRecommendStatus.CHANGE_RECOMMEND_STATUS_3.getCode());
//                changeRecommendService.modify(recommend);
//            }
//        }

    }

    /**
     * 支付金额，奖励钻石
     */
    @Override
    public void pay(Collection collection, BigDecimal buyAmount, BigDecimal payAmount, Account buyAccount, EJourCommon changeBizType,
            EJourCommon platChangeBizType, EJourCommon diamondChangeBizType, String remark) {

        //平台获取创建藏品手续费
        Long buyUserId = buyAccount.getUserId();
        Long refId = collection.getId();
        //用户账户扣减取现金额
        accountService.changeAmount(buyAccount, buyAmount.negate(),
                EChannelType.INNER.getCode(),
                collection.getId().toString(), collection.getId(),
                EJourBizTypeUser.Collection.Collection,
                changeBizType,
                changeBizType,
                collection.getName());

        Account incomeAccount = accountService.getAccount(ESystemAccount.BIZ.INCOME.getAccountNumber());
        //盈亏账户获得创建藏品手续费至
        accountService.changeAmount(incomeAccount, payAmount,
                EChannelType.INNER.getCode(),
                refId.toString(), refId,
                EJourBizTypeSystem.Fee.Fee, platChangeBizType,
                platChangeBizType, collection.getName());


    }

    @Override
    public void createThread(List<User> userList) {
//        CollectionBlindBoxCreateReq req = new CollectionBlindBoxCreateReq();
//
        int i = 1;
//        req.setPwd("888888");
        for (User user : userList) {
//            create(req, user);
            log.info("当前线程：总共用户" + userList.size() + "，线程名：" + Thread.currentThread().getName() + "---" + i);
            i++;
        }


    }

    @Override
    public int changeCollectionQuantity(Long id) {
        return collectionMapper.changeCollectionQuantity(id);
    }

    @Override
    public Long selectTotalTradedCount(String type, Date endDate) {
        Collection condition = new Collection();
        condition.setType(type);
        condition.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        condition.setPayDatetimeEnd(endDate);

        return collectionMapper.selectCountByCondition(condition);
    }

    @Override
    public BigDecimal selectTotalTradedAmount(String type, Date endDate) {
        Collection condition = new Collection();
        condition.setType(type);
        condition.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode());
        condition.setPayDatetimeEnd(endDate);

        return collectionMapper.selectTradeTotalAmount(condition);
    }

    @Override
    public void refreshMarketQuantity(List<Long> collectionIdList) {
        if (null == collectionIdList || CollectionUtils.isEmpty(collectionIdList)) {
            return;
        }
        Collection collection = new Collection();
        collection.setCollectionIdList(collectionIdList);
        collectionMapper.refreshMarketQuantity(collection);
    }

    @Override
    public void dropTest() {
        List<CollectionDropUserList> reqList = collectionMapper.getDropUserListReq();

        CollectionDropUserListReq request = new CollectionDropUserListReq();
        request.setReqList(reqList);
        request.setCollectionId(reqList.get(0).getCollectionId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dropList(CollectionDropUserListReq request, User operator) {
        CollectionDropUserReq req = new CollectionDropUserReq();
        // 判断是否拥有该渠道的发布权限
        if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        Collection collection = collectionService.detailSimple(request.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }
        // 检查是否有操作权限
        checkOperation(collection);

        if ((!EUserKind.SYS.getCode().equals(operator.getKind()) && !EUserKind.BP.getCode().equals(operator.getKind())) || (
                EUserKind.BP.getCode().equals(operator.getKind()) && !collection.getAuthorId().equals(operator.getCompanyId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
        }

        int totalDropNum = 0;
        for (CollectionDropUserList userReq : request.getReqList()) {
            log.info("执行用户：" + userReq.getLoginName() + "开始");
            User user = userService.selectUserByMobile(userReq.getLoginName(), EUserKind.C.getCode());

            collectionDetailService.dropCollectionUser(collection, user, collection.getLockTime(), userReq.getTotalQuantity());
            log.info("执行用户：" + userReq.getLoginName() + "结束");

            totalDropNum = totalDropNum + userReq.getTotalQuantity();
            log.info("已空投:" + totalDropNum);
        }

        if (collection.getRemainQuantity() < totalDropNum) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "剩余份数还有" + collection.getRemainQuantity() + "份,不足于空投");
        }

        //更新作品剩余量
        collection.setRemainQuantity(collection.getRemainQuantity() - totalDropNum);
        collectionService.modify(collection);
    }

    /**
     * 空投给拥有某个藏品的用户
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dropByUserCollection(CollectionDropUserCollectionReq request, User operator, String dropType) {
        Collection collection = collectionMapper.selectForUpdate(request.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品还未上架");
        }
        // 检查是否有操作权限
        checkOperation(collection);

        Collection detailSimple = detailSimple(request.getNeedCollectionId());
        // 检查是否有操作权限
        checkOperation(detailSimple);
        // 根据渠道查询
        List<CollectionDetailUserHaveNumber> haveNumberList = collectionDetailService
                .detailUserHaveCollectionNumber(request.getNeedCollectionId());

        if (CollectionUtils.isEmpty(haveNumberList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "暂无用户拥有" + detailSimple.getName() + "藏品");
        }

        Integer sum = 0;
        for (CollectionDetailUserHaveNumber x : haveNumberList) {
            sum = sum + x.getQuantity() * request.getQuantity();
        }

        Integer remainQuantity = collection.getRemainQuantity();
        if (remainQuantity < sum) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "空投需要份数：" + sum + ",剩余份数还有" + remainQuantity + "份,不足于空投");
        }

        // 记录序号
        Long orderId = IdGeneratorUtil.generator();
        DropOrder dropOrder = new DropOrder();
        dropOrder.setId(orderId);
        dropOrder.setType(EDropOrderType.DROP_ORDER_TYPE_1.getCode());
        dropOrder.setCollectionId(collection.getId());
        dropOrder.setRefCollectionId(request.getNeedCollectionId());
        dropOrder.setDropNumber(request.getQuantity());
        dropOrder.setTotalDropNumber(sum);
        dropOrder.setDropUserNumber(haveNumberList.size());
        dropOrder.setCreater(operator.getId());
        dropOrder.setCreaterKind(operator.getKind());
        dropOrder.setCreatrName(operator.getLoginName());
        dropOrder.setCreateDatetime(new Date());

        List<DropRecord> dropRecordList = new ArrayList<>();
        for (CollectionDetailUserHaveNumber userCollection : haveNumberList) {
            User user = userService.detail(userCollection.getUserId());
            DropRecord dropRecord = new DropRecord();
            dropRecord.setOrderId(orderId);
            dropRecord.setCollectionId(collection.getId());
            dropRecord.setType(EDropOrderType.DROP_ORDER_TYPE_1.getCode());
            dropRecord.setUserId(user.getId());

            collectionDetailService
                    .dropCollectionUser(collection, user, collection.getLockTime(), userCollection.getQuantity() * request.getQuantity());
            dropRecord.setStatus(EDropRecordStatus.DROP_RECORD_STATUS_1.getCode());

            dropRecord.setCreater(operator.getId());
            dropRecord.setCreaterKind(operator.getKind());
            dropRecord.setCreatrName(operator.getLoginName());
            dropRecord.setCreateDatetime(new Date());
            dropRecordList.add(dropRecord);
        }

        // 发行方使用该功能权益次数减一
        if (EUserKind.BP.getCode().equals(operator.getKind()) && ECollectionRightDropType.E_COLLECTION_RIGHT_DROP_TYPE_1.getCode()
                .equals(dropType)) {

            CollectionRightsDetail condition = new CollectionRightsDetail();
            condition.setCollectionId(request.getNeedCollectionId());
            condition.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
            List<CollectionRightsDetail> rightsDetailList = collectionRightsDetailService.list(condition);

            CollectionRightCompany rightCompany = null;
            CollectionRightsDetail rightsDetail = null;
            for (CollectionRightsDetail collectionRightsDetail : rightsDetailList) {
                CollectionRightCompany rightCreateReq = new CollectionRightCompany();
                rightCreateReq.setPlateCategory(collection.getPlateCategory());
                rightCreateReq.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
                rightCreateReq.setRefId(collectionRightsDetail.getId());
                rightCreateReq.setCompanyId(operator.getCompanyId());
                List<CollectionRightCompany> list = collectionRightCompanyService.list(rightCreateReq);

                if (CollectionUtils.isNotEmpty(list)) {
                    rightCompany = collectionRightCompanyService.detailForUpdate(list.get(0).getId());
                    rightsDetail = collectionRightsDetailService.detailForUpdate(collectionRightsDetail.getId());

                    for (int i = 0; i < request.getQuantity(); i++) {
                        if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                                .equals(rightsDetail.getNumberFlag())) {
                            rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() - 1);
                        } else if (rightsDetail.getRemainNumber() >= 1) {
                            rightCompany.setNumber(rightCompany.getNumber() + 1);
                            rightsDetail.setRemainNumber(rightsDetail.getRemainNumber() - 1);
                        }
                    }

                    collectionRightCompanyService.modify(rightCompany);
                    collectionRightsDetailService.modify(rightsDetail);

                    // 生成权益使用记录
                    CollectionRightRecord rightRecord = new CollectionRightRecord();
                    rightRecord.setRightId(rightsDetail.getId());
                    rightRecord.setRightCompanyId(rightCompany.getId());
                    rightRecord.setCollectionId(rightsDetail.getCollectionId());
                    rightRecord.setRefType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode());
                    rightRecord.setDropType(EBoolean.YES.getCode());
                    rightRecord.setRefId(orderId);
                    rightRecord.setRefCollectionId(collection.getId());
                    rightRecord.setDropNumber(request.getQuantity());
                    rightRecord.setCreateDatetime(new Date());
                    collectionRightRecordService.create(rightRecord);

                    break;
                }
            }
        }

        dropOrder.setSuccessDropUserNumber(sum);
        dropOrder.setFailureDropUserNumber(sum);

        //更新作品剩余量
        collection.setRemainQuantity(collection.getRemainQuantity() - sum);
        collectionService.modify(collection);

        // 生成空投记录
        dropOrderService.create(dropOrder);
        // 生成空投记录明细
        dropRecordService.batchCreate(dropRecordList);
    }
//
//    @Override
//    public void dropList(CollectionDropUserListReq request, User operator) {
//        CollectionDropUserReq req = new CollectionDropUserReq();
//        List<Long> userIdList = new ArrayList<>();
//        for (CollectionDropUserList userReq : request.getReqList()) {
//            User user = userService.selectUserByMobile(userReq.getLoginName(), EUserKind.C.getCode());
//            for (int i = 0; i < userReq.getTotalQuantity(); i++) {
//                userIdList.add(user.getId());
//            }
//        }
//        req.setUserIdList(userIdList);
//        req.setCollectionId(request.getCollectionId());
//        req.setDropNumber(1);
//        collectionService.drop(req, operator);
//    }

    @Override
    public void dropTest(Long collectionId) {
        List<CollectionDropUserList> reqList = collectionMapper.getDropUserListReq();

        CollectionDropUserListReq request = new CollectionDropUserListReq();
        request.setReqList(reqList);
        request.setCollectionId(collectionId);
        dropList(request, null);
    }

    @Override
    public void quantitySubtractByDetail(Long orderDetailId) {
        collectionMapper.quantitySubtractByDetail(orderDetailId);
    }

    /**
     * 积分藏品列表
     */
    @Override
    public List<CollectionIntegralPageRes> pageCollectionIntegral() {
        List<CollectionIntegralPageRes> resList = collectionMapper.selectPageCollectionIntegral();
        GoodsActivity activity = goodsActivityService.detail();
        // 判断是否处于翻倍时间段
        boolean doubleFlag = false;
        if (null != activity.getIntegralDoubleStartDatetime() && null != activity.getIntegralDoubleEndDatetime()) {
            Date date = new Date();
            if (date.after(activity.getIntegralDoubleStartDatetime()) && date.before(activity.getIntegralDoubleEndDatetime())) {
                doubleFlag = true;
            }
        }
        if (doubleFlag) {
            for (CollectionIntegralPageRes res : resList) {
                BigDecimal integralPrice = res.getIntegralPrice();
                int doubleSize = null == activity.getDoubleSize() ? 1 : activity.getDoubleSize();
                integralPrice = integralPrice.multiply(new BigDecimal(doubleSize)).setScale(2, BigDecimal.ROUND_DOWN);
                res.setIntegralPrice(integralPrice);
            }
        }

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyIntegral(CollectionModifyIntegralReq req, User operator) {
        for (Long id : req.getIdList()) {
            Collection collection = collectionMapper.selectForUpdate(id);
            checkOperation(collection);
//            collection.setIntegralPrice(req.getIntegralPrice());
            collection.setUpdater(operator.getId());
            collection.setUpdaterName(operator.getLoginName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);
        }
    }

    /**
     * 修改单人拥有最大数量
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifySingleMax(CollectionModifySingleMaxReq req, User operator) {
        for (Long id : req.getIdList()) {
            Collection collection = collectionMapper.selectForUpdate(id);
            collection.setSingleMaxQuantity(req.getSingleMaxQuantity());
            collection.setUpdater(operator.getId());
            collection.setUpdaterName(operator.getLoginName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyCollection(CollectionModifyIntegralReq req, User operator) {
        if (null != req.getDivideAuthorId()) {
            companyService.detail(req.getDivideAuthorId());
        }
        for (Long id : req.getIdList()) {
            Collection collection = collectionMapper.selectForUpdate(id);
            checkOperation(collection);
            if ((!EUserKind.SYS.getCode().equals(operator.getKind()) && !EUserKind.BP.getCode().equals(operator.getKind())) || (
                    EUserKind.BP.getCode().equals(operator.getKind()) && !collection.getAuthorId().equals(operator.getCompanyId()))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
            }

            collection.setSingleMaxQuantity(req.getSingleMaxQuantity());
            collection.setPlatDivideRate(req.getPlatDivideRate());
//            collection.setIntegralPrice(req.getIntegralPrice());
            collection.setAccountIntegral(req.getAccountIntegral());
            if (null != req.getDivideAuthorId()) {
                collection.setDivideAuthorId(req.getDivideAuthorId());
            }

            if (StringUtils.isNotBlank(req.getPlateCategory())) {
                ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(req.getPlateCategory());
                collection.setPlateCategory(req.getPlateCategory());
                // 藏品修改板块
                collectionDetailService.modifyPlateCategory(id, req.getPlateCategory());
                // 期数修改板块
                collectionPeriodService.modifyPlateCategory(id, req.getPlateCategory());
            }

            if (null != req.getPlatRemainQuantity()) {
                int addQuantity = req.getPlatRemainQuantity() - collection.getPlatRemainQuantity();
                int platRemainQuantity = collection.getPlatRemainQuantity() + addQuantity;
                if (platRemainQuantity < 0) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "平台剩余数量过小");
                }

                int remainQuantity = collection.getRemainQuantity() - addQuantity;
                if (remainQuantity < 0) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "发行方剩余数量过小");
                }

                collection.setPlatRemainQuantity(platRemainQuantity);
                collection.setRemainQuantity(remainQuantity);
            }

            collection.setUpdater(operator.getId());
            collection.setUpdaterName(operator.getLoginName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);

            // 修改阴阳爻
            if (null != req.getYinYao() || null != req.getYangYao()) {
                CollectionYaoConfigCreateReq request = new CollectionYaoConfigCreateReq();
                request.setCollectionId(collection.getId());
                request.setType(ECollectionYaoConfigType.COLLECTION_YAO_CONFIG_TYPE_0.getCode());
                request.setYangYao(null == req.getYangYao() ? BigDecimal.ZERO : req.getYangYao());
                request.setYinYao(null == req.getYinYao() ? BigDecimal.ZERO : req.getYinYao());
                collectionYaoConfigService.create(request, operator);
            }
        }
    }

    @Override
    public List<CollectionPitPageRes> pagePit(CollectionChallengeNeedReq request) {
        List<CollectionPitPageRes> resList = collectionMapper.selectPagePit(request);

        for (CollectionPitPageRes res : resList) {
            if (StringUtils.isBlank(res.getLevelType())) {
                res.setLevelType("");
            }
        }
        return resList;
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (CollectionPitPageRes collectionPitPageRes : list) {
//                if (StringUtils.isBlank(collectionPitPageRes.getLevelType())) {
//                    collectionPitPageRes.setLevelType(ECollectionLevelType.COLLECTION_LEVEL_TYPE_0.getCode());
//                }
//            }
//        }
//
//        return list;
    }

    @Override
    public Collection detailThreeChannelCollection(String threeChannelTypeCode, Long threeChannelId) {
        Collection condition = new Collection();
        condition.setThreeChannelType(threeChannelTypeCode);
        condition.setThreeChannelId(threeChannelId);
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(collectionList)) {
            return collectionList.get(0);
        }
        return null;
    }

    /**
     * 删除藏品
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改藏品
     *
     * @param req 修改藏品入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionModifyReq req, User operator) {
        Collection data = collectionMapper.selectByPrimaryKey(req.getId());
        if (!data.getUserId().equals(operator.getId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "你不是发表者，无权修改");
        }
        Collection collection = EntityUtils.copyData(req, Collection.class);
        collectionMapper.updateByPrimaryKeySelective(collection);
    }

    @Override
    public void modify(Collection collection) {
        collectionMapper.updateByPrimaryKeySelective(collection);
    }

    //    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void create(CollectionOssCreateReq req, User operator) {
//        Collection collection = EntityUtils.copyData(req, Collection.class);
//
//        if (EUserKind.SYS.getCode().equals(operator.getKind()) && null == req.getAuthorId()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方不能为空");
//        } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
//            collection.setAuthorId(operator.getCompanyId());
//        } else if (!EUserKind.BP.getCode().equals(operator.getKind()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限用户");
//        }
//
//        // 检测名称是否重复
//        String name = checkCollectionName(req.getName());
//        req.setName(name);
//
//        try {
//            if (StringUtils.isNotBlank(collection.getPlateCategory())) {
//                ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
//                        .getCollectionPeriodPlateCategory(collection.getPlateCategory());
//            }
//        } catch (Exception e) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
//        }
//
//        if ((ECollectionRightType.COLLECTION_PAYTYPE_0.getCode().equals(collection.getRightType())
//                || ECollectionRightType.COLLECTION_PAYTYPE_1.getCode().equals(collection.getRightType()))
//                && StringUtils.isBlank(collection.getRightContent())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益内容");
//        }
//
//        // 检查发行方
//        companyService.checkCompany(collection.getAuthorId());
//
//        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
//        collection.setType(ECollectionType.COLLECTION_TYPE_3.getCode());
//        collection.setUserId(null);
//        collection.setContentType(req.getContentType());
//        String chainType = configService.getStringValue(SysConstantsCache.SYSTEM_CHAIN_TYPE);
//        collection.setChainType(chainType);
//        collection.setPayAmount(BigDecimal.ZERO);
//        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_3.getCode());
//        collection.setPayDatetime(null);
//
//        Long orderCode = IdGeneratorUtil.generator();
//        collection.setPayOrderCode(orderCode.toString());
//        collection.setMarketQuantity(collection.getTotalQuantity());
//
//        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
//            collection.setTotalQuantity(1);
//            collection.setPlatRemainQuantity(0);
//            collection.setRemainQuantity(1);
//            collection.setMarketQuantity(1);
//        } else {
//            if (EUserKind.SYS.getCode().equals(operator.getKind())) {
//                // 给平台预留10%，至少100
//                BigDecimal platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_MIN_RATE);
//                Integer giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_MIN_NUMBER);
//                BigDecimal platRemainDecimal = new BigDecimal(collection.getTotalQuantity()).multiply(platMinRate);
//                int platRemain = platRemainDecimal.intValue();
//                if (giveMinNumber > collection.getTotalQuantity()) {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品数量不能小于" + giveMinNumber);
//                }
//                collection.setPlatRemainQuantity(platRemain < giveMinNumber ? giveMinNumber : platRemain);
//                collection.setRemainQuantity(collection.getTotalQuantity() - collection.getPlatRemainQuantity());
//            } else {
//                collection.setPlatRemainQuantity(0);
//                collection.setRemainQuantity(collection.getTotalQuantity());
//            }
//
//        }
//
//        Produced produced = producedService.detail(req.getProducedId());
//        collection.setProducedId(produced.getId());
//
//        // 检查分账机构是否存在
//        try {
//            Company company = companyService.detail(collection.getAuthorId());
////            if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus())) {
////                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方不存在");
////            }
//        } catch (Exception e) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方不存在");
//        }
//
//        //文件大小及 元/MB存储费
//        collection.setFileSize(BigDecimal.ZERO);
//
//        if (CollectionUtils.isNotEmpty(req.getFileList())) {
//            for (UploadFile uploadFile : req.getFileList()) {
//                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
//                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
//                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
//                }
//            }
//            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
//        }
//
//        collection.setStorageFee(BigDecimal.ZERO);
//        collection.setCommissionAmount(BigDecimal.ZERO);
//        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
//        Date date = new Date();
//        collection.setCreateDatetime(date);
//        collection.setUpdater(operator.getId());
//        collection.setUpdaterName(operator.getLoginName());
//        collection.setUpdateDatetime(date);
//
//        if (CollectionUtils.isNotEmpty(req.getTagList())) {
//            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
//            collection.setTags(newStr);
//        }
//
////        Contract contract = contractService.detail(req.getContractId());
////        if (EContractStatus.TO_PUT.getCode().equals(contract.getStatus())) {
////            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约还未上架");
////        }
////        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory()) && EContractProtocol.ERC1155.getCode()
////                .equals(contract.getProtocol())) {
////            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约错误，版权不能使用ERC1155");
////        }
//
//        collection.setMaxExchangeTime(req.getMaxExchangeTime());
////        collection.setContractId(contract.getId());
//        collectionMapper.insertSelective(collection);
//
//        //插入作品权益
//        collectionRightsDetailService.create(collection.getId(), req.getRightsDetailList(), operator);
//    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(CollectionOssCreateReq req, User operator) {
        Collection collection = EntityUtils.copyData(req, Collection.class);
        collection.setExchangeXmeta(EBoolean.NO.getCode());
        // 检测名称是否重复
        String name = checkCollectionName(req.getName());
        req.setName(name);

        //默认锁仓时间
        collection.setLockTime(-1);
        collection.setBuyType(ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_0.getCode());
        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())
                && !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode()
                .equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非藏品分类的作品售卖方式必须是非售卖");
        }
//
//        try {
//            if (StringUtils.isNotBlank(collection.getPlateCategory())) {
//                ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
//                        .getCollectionPeriodPlateCategory(collection.getPlateCategory());
//            }
//        } catch (Exception e) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
//        }

        // 权益说明
        collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
        if (CollectionUtils.isNotEmpty(req.getRightsDetailList())) {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
        }

        collection.setAuthorId(SysConstants.COMPANY_DEFAULT_ID);
        collection.setDivideAuthorId(SysConstants.COMPANY_DEFAULT_ID);
        // 检查发行方
        companyService.checkCompany(collection.getAuthorId());

        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        collection.setType(ECollectionType.COLLECTION_TYPE_3.getCode());
        collection.setUserId(null);
        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
        String chainType = configService.getStringValue(SysConstantsCache.SYSTEM_CHAIN_TYPE);
        collection.setChainType(chainType);
        collection.setPayAmount(BigDecimal.ZERO);
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_3.getCode());
        collection.setPayDatetime(null);

        Long orderCode = IdGeneratorUtil.generator();
        collection.setPayOrderCode(orderCode.toString());
        collection.setMarketQuantity(collection.getTotalQuantity());

        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(collection.getTotalQuantity());
        }

        Produced condition = new Produced();
        condition.setRefId(SysConstants.COMPANY_DEFAULT_ID);
        List<Produced> list = producedService.list(condition);
        collection.setProducedId(list.get(0).getId());

        //文件大小及 元/MB存储费
        collection.setFileSize(BigDecimal.ZERO);

        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        collection.setStorageFee(BigDecimal.ZERO);
        collection.setCommissionAmount(BigDecimal.ZERO);
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
        Date date = new Date();
        collection.setCreateDatetime(date);
        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }

        collection.setMaxExchangeTime(req.getMaxExchangeTime());

        Contract contract = new Contract();
        contract.setChain(EContractChain.BSN.getCode());
        contract.setStatus(EContractStatus.PUTON.getCode());
        List<Contract> contractList = contractService.list(contract);
        if (CollectionUtils.isNotEmpty(contractList)) {
            collection.setContractId(contractList.get(0).getId());
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "暂无链的合约，请联系管理员");
        }
        collectionMapper.insertSelective(collection);

        //插入作品权益
        //默认0处理
        if (CollectionUtils.isNotEmpty(req.getRightsDetailList())) {
            List<CollectionRightsDetailCreateAutoReq> rightsDetailList = req.getRightsDetailList();
            for (CollectionRightsDetailCreateAutoReq autoReq : rightsDetailList) {
                List<Long> CompanyIdList = new ArrayList<>();
                CompanyIdList.add(0L);
                autoReq.setCompanyIdList(CompanyIdList);

                List<String> plateList = new ArrayList<>();
                plateList.add("0");
                autoReq.setPlateCategoryList(plateList);
            }
        }
        collectionRightsDetailService.create(collection, req.getRightsDetailList(), operator);
    }

    /**
     * 新增作品（发行方平台端）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCompanyPlatform(CollectionCreateCompanyPlatformReq req, User operator) {

        Collection collection = EntityUtils.copyData(req, Collection.class);

        // 检测名称是否重复
        String name = checkCollectionName(req.getName());
        req.setName(name);

        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
        // 权益说明
        collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
//        collection.setRightContent(req.getCollectionSaleDemandCreateReq().getContent());

        try {
            ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collection.getPlateCategory());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
        }

        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        collection.setType(ECollectionType.COLLECTION_TYPE_1.getCode());
        collection.setUserId(null);

        collection.setChainType(req.getCollectionSaleDemand().getChain());
        collection.setPayAmount(BigDecimal.ZERO);
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_3.getCode());
        collection.setPayDatetime(null);

        collection.setBuyType(req.getCollectionSaleDemand().getBuyType());
        collection.setTotalQuantity(req.getCollectionSaleDemand().getQuantity());
        collection.setMarketQuantity(collection.getTotalQuantity());

        // 给平台预留10%，至少100
        if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setSingleMaxQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            if (null == req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预发售数量不能为空");
            }
            // 给平台预留10%，至少100
            BigDecimal platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_MIN_RATE);
            Integer giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_MIN_NUMBER);
            BigDecimal platRemainDecimal = new BigDecimal(collection.getTotalQuantity()).multiply(platMinRate);
            int platRemain = platRemainDecimal.intValue();
            if (giveMinNumber > collection.getTotalQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品数量不能小于" + giveMinNumber);
            }
            collection.setPlatRemainQuantity(platRemain < giveMinNumber ? giveMinNumber : platRemain);
            collection.setRemainQuantity(collection.getTotalQuantity() - collection.getPlatRemainQuantity());
        }

        Long orderCode = IdGeneratorUtil.generator();
        collection.setPayOrderCode(orderCode.toString());

        Produced produced = producedService.detail(req.getProducedId());
        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())
                || (EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !produced.getRefId()
                .equals(operator.getCompanyId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), produced.getName() + "该出品方不属于您");
        }
        collection.setProducedId(produced.getId());

        // 锁仓时间写死为-1
        collection.setLockTime(-1);

        collection.setAuthorId(operator.getCompanyId());
        // 检查发行方
        companyService.checkCompany(collection.getAuthorId());

        //文件大小及 元/MB存储费
        collection.setFileSize(BigDecimal.ZERO);

        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        collection.setStorageFee(BigDecimal.ZERO);
        collection.setCommissionAmount(BigDecimal.ZERO);
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
        Date date = new Date();
        collection.setCreateDatetime(date);
        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }

        collectionMapper.insertSelective(collection);

        // 插入数字藏品发行需求
        collectionSaleDemandService.create(collection, req.getCollectionSaleDemand(), operator);
    }

    @Override
    public void checkCompany(User operator) {
        if (-1L == operator.getCompanyId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您还没发起入驻");
        }
        Company company = companyService.detailSimple(operator.getCompanyId());
        if (!operator.getId().equals(company.getId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您无权修改");
        }

        if (ECompanyStatus.INFO_MODIFT.getCode().equals(company.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "信息修改期间不能发起申请");
        }

        if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您还没入驻成功");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Collection createCollectionCompanyPlatform(CollectionCreatePlatformReq req, User operator, String periodCategory) {
        Collection collection = EntityUtils.copyData(req, Collection.class);
        init(req.getExchangeXmeta(), collection);

        Company company = companyService.checkCompany(operator.getCompanyId());
        // 检测名称是否重复
        String name = checkCollectionName(req.getName());
        req.setName(name);

        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())
                && !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode()
                .equals(req.getCollectionSaleDemand().getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非藏品分类的作品售卖方式必须是非售卖");
        }

        // 检验发行方是否有资格新增道具藏品
        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(req.getCategory()) && !EBoolean.YES.getCode()
                .equals(company.getPlatFlag())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您无权新增道具藏品");
        }

        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
        // 权益说明
        collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
        if (CollectionUtils.isNotEmpty(req.getRightsDetailList())) {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
        }

        try {
            ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collection.getPlateCategory());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
        }

        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        collection.setType(ECollectionType.COLLECTION_TYPE_1.getCode());
        collection.setUserId(null);

        String chainType = configService.getStringValue(SysConstantsCache.SYSTEM_CHAIN_TYPE);
        collection.setChainType(chainType);
        collection.setPayAmount(BigDecimal.ZERO);
        collection.setPayStatus(ECollectionPayStatus.COLLECTION_PAYSTATUS_3.getCode());
        collection.setPayDatetime(null);

        collection.setBuyType(req.getCollectionSaleDemand().getBuyType());
        collection.setTotalQuantity(req.getCollectionSaleDemand().getQuantity());
        collection.setMarketQuantity(collection.getTotalQuantity());

        // 给平台预留10%，至少100
        if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setSingleMaxQuantity(1);
            collection.setMarketQuantity(1);
            req.getCollectionSaleDemand().setQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            if (null == req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预发售数量不能为空");
            }

            // 给平台预留10%，至少100
            BigDecimal platMinRate = BigDecimal.ZERO;
            Integer giveMinNumber = 0;

            if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(periodCategory)) {
                // 给平台预留10%，至少100
                platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_BLIND_MIN_RATE);
                giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_BLIND_MIN_NUMBER);
            } else if (!ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
                // 给平台预留10%，至少100
                platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_MIN_RATE);
                giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_MIN_NUMBER);
            }
            BigDecimal platRemainDecimal = new BigDecimal(collection.getTotalQuantity()).multiply(platMinRate);
            int platRemain = platRemainDecimal.intValue();
            if (giveMinNumber > collection.getTotalQuantity() && ECollectionCategory.DERIVATIVE.getCode()
                    .equals(collection.getCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品数量不能小于" + giveMinNumber);
            }
            collection.setPlatRemainQuantity(platRemain < giveMinNumber ? giveMinNumber : platRemain);

            collection.setRemainQuantity(collection.getTotalQuantity() - collection.getPlatRemainQuantity());

        }

        Long orderCode = IdGeneratorUtil.generator();
        collection.setPayOrderCode(orderCode.toString());

        Produced produced = producedService.detail(req.getProducedId());
        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())
                || (EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !produced.getRefId()
                .equals(operator.getCompanyId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), produced.getName() + "该出品方不属于您");
        }
        collection.setProducedId(produced.getId());

        // 锁仓时间写死为-1
        collection.setLockTime(-1);

        collection.setAuthorId(operator.getCompanyId());
        collection.setDivideAuthorId(operator.getCompanyId());
        // 检查发行方
        companyService.checkCompany(collection.getAuthorId());

        //文件大小及 元/MB存储费
        collection.setFileSize(BigDecimal.ZERO);

        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        collection.setStorageFee(BigDecimal.ZERO);
        collection.setCommissionAmount(BigDecimal.ZERO);
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
        Date date = new Date();
        collection.setCreateDatetime(date);
        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }
//        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
//            collection.setLevelType(ECollectionLevelType.COLLECTION_LEVEL_TYPE_6.getCode());
//        }

        Contract condition = new Contract();
        condition.setChain(req.getCollectionSaleDemand().getChain());
        condition.setStatus(EContractStatus.PUTON.getCode());
        List<Contract> contractList = contractService.list(condition);
        if (CollectionUtils.isNotEmpty(contractList)) {
            collection.setContractId(contractList.get(0).getId());
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "暂无该" + req.getCollectionSaleDemand().getChain() + "链的合约，请联系管理员");
        }
        collectionMapper.insertSelective(collection);

        CollectionSaleDemandCreateReq demandCreateReq = EntityUtils
                .copyData(req.getCollectionSaleDemand(), CollectionSaleDemandCreateReq.class);
        // 插入数字藏品发行需求
        collectionSaleDemandService.create(collection, demandCreateReq, operator);

        // 插入权益
        collectionRightsDetailService.create(collection, req.getRightsDetailList(), operator);

        return collection;
    }

    private void init(String exchangeXmeta, Collection collection) {
        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
            if (StringUtils.isBlank(exchangeXmeta)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "是否可兑换藏品XMeta上架不能为空");
            }
            collection.setExchangeXmeta(exchangeXmeta);
        } else {
            collection.setExchangeXmeta(EBoolean.NO.getCode());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Collection modifyCollectionCompanyPlatform(CollectionModifyPlatformReq req, User operator, String periodCategory) {
        Collection detail = detailSimple(req.getId());

        CollectionPeriodRelation relation = new CollectionPeriodRelation();
        relation.setCollectionId(req.getId());
        List<CollectionPeriodRelation> list = collectionPeriodRelationService.list(relation);
        for (CollectionPeriodRelation periodRelation : list) {
            CollectionPeriod detailsimple = collectionPeriodService.detailsimple(periodRelation.getPeriodId());
            if (!ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_0.getCode().equals(detailsimple.getStatus())
                    && !ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_4.getCode().equals(detailsimple.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品" + detail.getName() + "相关期数已提交审核");
            }
        }
        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        if (!detail.getName().equals(req.getName())) {
            // 检测名称是否重复
            String name = checkCollectionName(req.getName());
            req.setName(name);
        }
        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(detail.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9.getCode()
                .equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发布的可以修改");
        }

        checkOperation(detail);

        Collection collection = EntityUtils.copyData(req, Collection.class);
        init(req.getExchangeXmeta(), collection);

        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())
                && !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode()
                .equals(req.getCollectionSaleDemand().getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "非藏品分类的作品售卖方式必须是非售卖");
        }

        try {
            ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collection.getPlateCategory());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
        }

        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        collection.setBuyType(req.getCollectionSaleDemand().getBuyType());
        collection.setTotalQuantity(req.getCollectionSaleDemand().getQuantity());
        collection.setMarketQuantity(collection.getTotalQuantity());

        if (CollectionUtils.isEmpty(req.getRightsDetailList())) {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
        } else {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
        }

        // 给平台预留10%，至少100
        if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setSingleMaxQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            if (null == req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预发售数量不能为空");
            }
            // 给平台预留10%，至少100
            BigDecimal platMinRate = BigDecimal.ZERO;
            Integer giveMinNumber = 0;
            if (ECollectionPeriodCategory.BLINDBOX.getCode().equals(periodCategory)) {
                // 给平台预留10%，至少100
                platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_BLIND_MIN_RATE);
                giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_BLIND_MIN_NUMBER);
            } else if (!ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
                // 给平台预留10%，至少100
                platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_MIN_RATE);
                giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_MIN_NUMBER);
            }
            BigDecimal platRemainDecimal = new BigDecimal(collection.getTotalQuantity()).multiply(platMinRate);
            int platRemain = platRemainDecimal.intValue();
            if (giveMinNumber > collection.getTotalQuantity() && ECollectionCategory.DERIVATIVE.getCode()
                    .equals(collection.getCategory())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品数量不能小于" + giveMinNumber);
            }
            collection.setPlatRemainQuantity(platRemain < giveMinNumber ? giveMinNumber : platRemain);
            collection.setRemainQuantity(collection.getTotalQuantity() - collection.getPlatRemainQuantity());

        }

        Produced produced = producedService.detail(req.getProducedId());
        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())
                || (EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !produced.getRefId()
                .equals(operator.getCompanyId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), produced.getName() + "该出品方不属于您");
        }
        collection.setProducedId(produced.getId());

        collection.setAuthorId(operator.getCompanyId());
        // 检查发行方
        companyService.checkCompany(collection.getAuthorId());

        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        Date date = new Date();

        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }
//        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
        collection.setType(detail.getType());

        Contract condition = new Contract();
        condition.setChain(req.getCollectionSaleDemand().getChain());
        condition.setStatus(EContractStatus.PUTON.getCode());
        List<Contract> contractList = contractService.list(condition);
        if (CollectionUtils.isNotEmpty(contractList)) {
            collection.setContractId(contractList.get(0).getId());
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                    "暂无该" + req.getCollectionSaleDemand().getChain() + "链的合约，请联系管理员");
        }

//        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
//            collection.setLevelType(ECollectionLevelType.COLLECTION_LEVEL_TYPE_6.getCode());
//        }

        collectionMapper.updateByPrimaryKeySelective(collection);

        CollectionSaleDemandCreateReq demandCreateReq = EntityUtils
                .copyData(req.getCollectionSaleDemand(), CollectionSaleDemandCreateReq.class);
        // 插入数字藏品发行需求
        collectionSaleDemandService.create(collection, demandCreateReq, operator);

        // 插入权益
        collectionRightsDetailService.create(collection, req.getRightsDetailList(), operator);

        collection.setStatus(detail.getStatus());
        collection.setLockTime(detail.getLockTime());
        return collection;
    }

    @Override
    public Collection modifyCollectionPlatPlatform(CollectionModifyPlatformAuditReq req, BigDecimal platDivideRate,
            Long contractId, User operator, String periodCategory) {
        Collection detail = detailSimple(req.getId());

        Collection collection = EntityUtils.copyData(req, Collection.class);
        collection.setPlateCategory(detail.getPlateCategory());
        collection.setAuthorId(detail.getAuthorId());

        collection.setContractId(contractId);
        if (!detail.getName().equals(req.getName())) {
            // 检测名称是否重复
            String name = checkCollectionName(req.getName());
            req.setName(name);
        }
        // 各类检查
        extracted(detail, collection);

        collection.setThreeChannelType(ECollectionThreeChannelType.E_COLLECTION_THREE_CHANNEL_TYPE_0.getCode());
        collection.setBuyType(req.getCollectionSaleDemand().getBuyType());
        collection.setTotalQuantity(req.getCollectionSaleDemand().getQuantity());
        collection.setMarketQuantity(collection.getTotalQuantity());

        if (CollectionUtils.isEmpty(req.getRightsDetailList())) {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());
        } else {
            collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
        }

        // 给平台预留10%，至少100
        if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setSingleMaxQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            if (null == req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预发售数量不能为空");
            }
            if (null == req.getRemainQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方剩余数量不能为空");
            }

            if (req.getRemainQuantity() > req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方剩余数量不能大于总数量");
            }
            collection.setRemainQuantity(req.getRemainQuantity());
            collection.setPlatRemainQuantity(collection.getTotalQuantity() - collection.getRemainQuantity());
        }

//        if (CollectionUtils.isNotEmpty(req.getFileList())) {
//            for (UploadFile uploadFile : req.getFileList()) {
//                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
//                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
//                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
//                }
//            }
//            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
//        }

        Date date = new Date();

        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
        collection.setType(detail.getType());

        // 如果包含元宇宙入场券权益
        for (CollectionRightsDetailCreateAutoReq rightsDetailCreateAutoReq : req.getRightsDetailList()) {
            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightsDetailCreateAutoReq.getType())) {
                collection.setU3dFlag(EBoolean.YES.getCode());
                collection.setTicketType(rightsDetailCreateAutoReq.getTicketType());
            }
        }
        // 审核通过校验
        extracted(collection);
        collection.setPlatDivideRate(platDivideRate);

        collectionMapper.updateByPrimaryKeySelective(collection);

        CollectionSaleDemandCreateReq demandCreateReq = EntityUtils
                .copyData(req.getCollectionSaleDemand(), CollectionSaleDemandCreateReq.class);
        // 插入数字藏品发行需求
        collectionSaleDemandService.create(collection, demandCreateReq, operator);

        // 插入权益
        collectionRightsDetailService.create(collection, req.getRightsDetailList(), operator);

        collection.setLockTime(detail.getLockTime());
        return collection;
    }

    private void extracted(Collection collection) {
        contractTokenInPool15Service.doCheckExist(collection);

        // 检查合约
        Contract contract = contractService.detail(collection.getContractId());
        if (EContractStatus.TO_PUT.getCode().equals(contract.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约还未上架");
        }
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory()) && EContractProtocol.ERC1155.getCode()
                .equals(contract.getProtocol())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约错误，版权不能使用ERC1155");
        }

        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
    }

    private void extracted(Collection detail, Collection collection) {
        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(detail.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9.getCode()
                .equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发布的可以修改");
        }

        checkOperation(detail);

        try {
            ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collection.getPlateCategory());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
        }

        // 检查发行方
        companyService.checkCompany(detail.getAuthorId());
        Produced produced = producedService.detail(collection.getProducedId());
        if (EProducedRefType.PRODUCED_REFTYPE_0.getCode().equals(produced.getRefType())
                || (EProducedRefType.PRODUCED_REFTYPE_1.getCode().equals(produced.getRefType()) && !produced.getRefId()
                .equals(detail.getAuthorId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), produced.getName() + "出品方与发行方不匹配");
        }


    }

    @Override
    public List<CollectionCategory> categoryList(User operator) {
        Company company = companyService.checkCompany(operator.getCompanyId());
        List<CollectionCategory> resList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (EBoolean.YES.getCode().equals(company.getIndependencePlatFlag())) {
            map.put(ECollectionCategory.DERIVATIVE.getCode(), ECollectionCategory.DERIVATIVE.getValue());
            map.put(ECollectionCategory.COPYRIGHT.getCode(), ECollectionCategory.COPYRIGHT.getValue());
        } else if (EBoolean.YES.getCode().equals(company.getPlatFlag())) {
            map.put(ECollectionCategory.DERIVATIVE.getCode(), ECollectionCategory.DERIVATIVE.getValue());
            map.put(ECollectionCategory.COPYRIGHT.getCode(), ECollectionCategory.COPYRIGHT.getValue());
            map.put(ECollectionCategory.NOT_COLLECTION.getCode(), ECollectionCategory.NOT_COLLECTION.getValue());
        } else {
            map.put(ECollectionCategory.DERIVATIVE.getCode(), ECollectionCategory.DERIVATIVE.getValue());
            map.put(ECollectionCategory.COPYRIGHT.getCode(), ECollectionCategory.COPYRIGHT.getValue());
        }

        for (String category : map.keySet()) {
            CollectionCategory res = new CollectionCategory();
            res.setCategory(category);
            res.setCategoryName(map.get(category));
            resList.add(res);
        }

        return resList;
    }

    @Override
    public List<Collection> channelTransferpage(CollectionPagePeriodReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setOrderBy("id desc");
        List<String> typeList = new ArrayList<>();
        typeList.add(ECollectionType.COLLECTION_TYPE_1.getCode());
        typeList.add(ECollectionType.COLLECTION_TYPE_3.getCode());
        condition.setTypeList(typeList);

        List<Collection> collectionList = collectionMapper.selectChannelTransfer(condition);
        // 转译UserId
        collectionList.forEach(item -> {
            if (StringUtils.isNotBlank(item.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(item.getFileUrl(),
                        UploadFile.class);
                item.setFileList(uploadFiles);
            }
            if (StringUtils.isNotBlank(item.getTags())) {
                List<String> result = Arrays.asList(item.getTags().split(","));
                item.setTagList(result);
            }

//            Company company = companyService.detail(item.getAuthorId());
//            item.setAuthor(company.getName());
//            item.setAuthorPic(company.getLogo());
//            if (null != item.getPayBalanceAmount() && item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
//                item.setIsDeduction(EBoolean.YES.getCode());
//            } else {
//                item.setIsDeduction(EBoolean.NO.getCode());
//            }
//            if (null != item.getProducedId()) {
//                Produced produced = producedService.detail(item.getProducedId());
//                item.setProducedId(produced.getId());
//                item.setProducedName(produced.getName());
//                item.setProducedPic(produced.getPic());
//            }
//
//            if (null != item.getDivideAuthorId()) {
//                item.setDivideAuthor(companyService.detail(item.getDivideAuthorId()).getName());
//            }
//
//            //获取权益列表
//            CollectionRightsDetailListReq collectionRightsDetailListReq = new CollectionRightsDetailListReq();
//            collectionRightsDetailListReq.setCollectionId(item.getId());
//            item.setRightsDetailList(collectionRightsDetailService.list(collectionRightsDetailListReq));
//
//            item.setCollectionRightList(collectionRightService.list(item.getId()));
//            if (CollectionUtils.isNotEmpty(item.getCollectionRightList())) {
//                for (CollectionRight collectionRight : item.getCollectionRightList()) {
//                    if (ECollectionRightDetailType.COLLECTION_RIGHT_DETAIL_TYPE_1.getCode().equals(collectionRight.getType())
//                            && null != collectionRight.getRefId()) {
//                        ChangeSeries changeSeries = changeSeriesService.detail(collectionRight.getRefId());
//                        collectionRight.setRefName(changeSeries.getName());
//                    }
//                }
//            }
//
//            // 价格出参
//            CollectionSaleDemand saleDemand = collectionSaleDemandService.detailByCollectionId(item.getId());
//            item.setCollectionSaleDemand(saleDemand);
//            if (null != saleDemand) {
//                item.setPrice(saleDemand.getPrice());
//            }
        });

        return collectionList;
    }

    @Override
    public List<CollectionMyOwnerPageRes> selectMyChannelTransferPageFront(CollectionPageMyOwnerReq request, User operator) {
        Collection condition = EntityUtils.copyData(request, Collection.class);
        condition.setUserId(operator.getId());
        return collectionMapper.selectMyChannelTransferOwnerPage(condition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(BaseIdReq request, User operator) {
        Collection collection = detailForUpdate(request.getId());
        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(collection.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9
                .getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "无法删除，只有待审核和审核失败的作品可以删除");
        }
        Collection collectionModify = new Collection();
        collectionModify.setId(collection.getId());
        collectionModify.setStatus(ECollectionStatus.COLLECTION_STATUS_7.getCode());
        collectionMapper.updateByPrimaryKeySelective(collectionModify);

    }

    @Override
    public List<PeriodCollectionStatistics> periodCollectionStatistics(CollectionPagePeriodCollectionStatisticsReq request, User operator) {
        List<PeriodCollectionStatistics> resList = collectionPeriodRelationService.periodCollectionStatistics(request);

        Map<Long, String> map = new HashMap<>();
        for (PeriodCollectionStatistics res : resList) {
            if (null == res.getProducedId()) {
                res.setProducedName("无");
            } else {
                Produced produced = producedService.detail(res.getProducedId());
                res.setProducedName(produced.getName());
            }
            String authorName = map.get(res.getAuthorId());
            if (StringUtils.isBlank(authorName)) {
                Company company = companyService.detailSimple(res.getAuthorId());
                authorName = company.getName();
                map.put(res.getAuthorId(), authorName);
            }
            res.setAuthorName(authorName);

            String divideAuthorName = map.get(res.getDivideAuthorId());
            if (StringUtils.isBlank(divideAuthorName)) {
                Company company = companyService.detailSimple(res.getDivideAuthorId());
                divideAuthorName = company.getName();
                map.put(res.getDivideAuthorId(), divideAuthorName);
            }
            res.setDivideAuthorName(divideAuthorName);
            res.setCompanyDivideRate(new BigDecimal(100).subtract(res.getPlatDivideRate()));

            BigDecimal totalPrice = res.getPrice().multiply(new BigDecimal(res.getSaleQuantity())).setScale(2, BigDecimal.ROUND_DOWN);
            res.setTotalPrice(totalPrice);

            BigDecimal platTotalPrice = totalPrice
                    .multiply(res.getPlatDivideRate().divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN))
                    .setScale(2, BigDecimal.ROUND_DOWN);
            BigDecimal companyTotalPrice = totalPrice.subtract(platTotalPrice);
            res.setPlatTotalPrice(platTotalPrice);
            res.setCompanyTotalPrice(companyTotalPrice);

        }
        return resList;
    }

    @Override
    public void modifyClassId(Long collectionId, String classId) {
        Collection collection = collectionMapper.selectByPrimaryKey(collectionId);
        // 原先作品的分类id为空时，则新赋值
        if (StringUtils.isBlank(collection.getClassId())) {
            collection.setClassId(classId);
            collectionMapper.updateByPrimaryKeySelective(collection);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doPublishXmeta(CollectionPublishXmetaReq req, User operator) {
        Collection collection = collectionMapper.selectByPrimaryKey(req.getId());
        if (!ECollectionType.COLLECTION_TYPE_1.getCode().equals(collection.getType())
                && !ECollectionType.COLLECTION_TYPE_3.getCode().equals(collection.getType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品类型不是平台代售或发行方发售，不能同步");
        }
        if (EBoolean.YES.getCode().equals(collection.getPublishXmeta())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品已同步上架到XMeta");
        }
        //系列名称赋值
        collection.setSerialName(req.getSerialName());

        List<Collection> collectionList = new ArrayList<>();
        collectionList.add(collection);
        xmetaService.archiveTransfer(collectionList, operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doDownXmeta(CollectionDownXmetaReq req, User operator) {
        Collection condition = new Collection();
        condition.setCollectionIdList(req.getIdList());
        condition.setPublishXmeta(EBoolean.YES.getCode());

        List<Collection> collectionList = collectionMapper.selectByCondition(condition);
        for (Collection collection : collectionList) {
            if (EBoolean.YES.getCode().equals(collection.getPublishXmeta())) {
                collection.setPublishXmeta(EBoolean.NO.getCode());
                collectionService.modify(collection);
            }
        }
    }

    @Override
    public List<GoodsCountVO> selectMyHavePublishXmetaCollectionCount(User user) {
        Collection condition = new Collection();
        condition.setUserId(user.getId());
        return collectionMapper.selectMyHavePublishXmetaCollectionCount(condition);
    }

    /**
     * 查询发行方可售卖藏品列表
     */
    @Override
    public List<CollectionCompanyNotSellPageRes> companyNotSellCollectionPage(CollectionCompanyNotSellPageReq request, User operator) {

        Company company = companyService.checkCompany(request.getCompanyId());

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        List<Collection> collectionList = collectionMapper.selectCompanyNotSellCollectionPage(request.getCompanyId());
        List<CollectionCompanyNotSellPageRes> resList = collectionList.stream().map(x -> {
            CollectionCompanyNotSellPageRes res = new CollectionCompanyNotSellPageRes();
            BeanUtils.copyProperties(x, res);
            res.setAuthor(company.getName());
            res.setAuthorPic(company.getLogo());

            res.setTotalQuantityUnit(ECollectionPeriodCategory.BLINDBOX.getUnit());
            if (ECollectionCategory.COPYRIGHT.getCode().equals(x.getCategory())) {
                res.setTotalQuantityUnit(ECollectionPeriodCategory.COPYRIGHT.getUnit());
            }

            if (StringUtils.isNotBlank(x.getTags())) {
                List<String> result = Arrays.asList(x.getTags().split(","));
                res.setTagList(result);
            }

            if (StringUtils.isNotBlank(x.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(x.getFileUrl(),
                        UploadFile.class);
                res.setFileList(uploadFiles);
            }
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionList, resList);

    }

    @Override
    public List<Collection> listCreateRight(CollectionRightCompany condition) {
        List<Collection> collections = collectionMapper.selectCollectionCreatePeriod(condition);
        return collections;
    }

    /**
     * 新增作品权益
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRightOss(CollectionRightOssCreateReq request, User operator) {
        Collection collection = collectionMapper.selectForUpdate(request.getId());

        checkOperation(collection);

        if ((!EUserKind.SYS.getCode().equals(operator.getKind()) && !EUserKind.BP.getCode().equals(operator.getKind())) || (
                EUserKind.BP.getCode().equals(operator.getKind()) && !collection.getAuthorId().equals(operator.getCompanyId()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权操作");
        }

        Collection modify = new Collection();
        modify.setId(collection.getId());
        modify.setRightContent(request.getRightContent());
        modify.setRightType(request.getRightType());

        if (!ECollectionRightType.COLLECTION_PAYTYPE_2.getCode().equals(collection.getRightType()) && StringUtils
                .isBlank(collection.getRightContent())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益内容");
        }
        collectionMapper.updateByPrimaryKeySelective(modify);
        //插入作品权益
        collectionRightService.create(collection.getId(), request.getCollectionRightList(), operator);
    }

    /**
     * 修改作品权益
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyRightsOss(CollectionRightsModifyReq request, User operator) {
        Collection collection = collectionMapper.selectForUpdate(request.getId());
        collection.setRightType(request.getRightType());
        collection.setRightContent(request.getRightContent());
        if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(request.getRightType())) {
            if (CollectionUtils.isEmpty(request.getRightsDetailList())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益列表");
            }
        }
        collection.setUpdater(operator.getId());
        collection.setUpdateDatetime(new Date());
        collectionMapper.updateByPrimaryKeySelective(collection);

        //插入作品权益
        collectionRightsDetailService.create(request.getId(), request.getRightsDetailList(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionOssModifyReq req, User operator) {
        Collection detail = detail(req.getId());

        if (!detail.getName().equals(req.getName())) {
            // 检测名称是否重复
            String name = checkCollectionName(req.getName());
            req.setName(name);
        }
        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(detail.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9
                .getCode()
                .equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发布的可以修改");
        }

        checkOperation(detail);

        Collection collection = EntityUtils.copyData(req, Collection.class);

        collection.setMarketQuantity(collection.getTotalQuantity());
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(collection.getTotalQuantity());
        }

        // 出品方
        Produced condition = new Produced();
        condition.setRefId(SysConstants.COMPANY_DEFAULT_ID);
        List<Produced> list = producedService.list(condition);
        collection.setProducedId(list.get(0).getId());

        //文件大小及 元/MB存储费
        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(new Date());

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }

        collection.setMaxExchangeTime(req.getMaxExchangeTime());

        collectionMapper.updateByPrimaryKeySelective(collection);

        //插入作品权益
        collectionRightsDetailService.create(collection, req.getRightsDetailList(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyCompanyPlatform(CollectionModifyCompanyPlatformReq req, User operator) {
        Collection detail = detailSimple(req.getId());
        // 检查发行方
        companyService.checkCompany(operator.getCompanyId());
        if (!detail.getName().equals(req.getName())) {
            // 检测名称是否重复
            String name = checkCollectionName(req.getName());
            req.setName(name);
        }
        if (!ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(detail.getStatus()) && !ECollectionStatus.COLLECTION_STATUS_9.getCode()
                .equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待发布的可以修改");
        }

        checkOperation(detail);

        Collection collection = EntityUtils.copyData(req, Collection.class);

        if (EUserKind.BP.getCode().equals(operator.getKind()) && !detail.getAuthorId().equals(operator.getCompanyId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改");
        } else if (!EUserKind.BP.getCode().equals(operator.getKind()) && !EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限用户");
        }

        collection.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
        // 权益说明
        collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_2.getCode());

        try {
            ECollectionPeriodPlateCategory collectionPeriodPlateCategory = ECollectionPeriodPlateCategory
                    .getCollectionPeriodPlateCategory(collection.getPlateCategory());
        } catch (Exception e) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块类别错误");
        }

        collection.setBuyType(req.getCollectionSaleDemand().getBuyType());
        collection.setTotalQuantity(req.getCollectionSaleDemand().getQuantity());
        collection.setMarketQuantity(collection.getTotalQuantity());

        // 给平台预留10%，至少100
        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            collection.setTotalQuantity(1);
            collection.setPlatRemainQuantity(0);
            collection.setRemainQuantity(1);
            collection.setSingleMaxQuantity(1);
            collection.setMarketQuantity(1);
        } else {
            if (null == req.getSingleMaxQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "单人拥有最大数量不能为空");
            }
            if (null == req.getCollectionSaleDemand().getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "预发售数量不能为空");
            }
            // 给平台预留10%，至少100
            BigDecimal platMinRate = configService.getBigDecimalValue(SysConstants.GIVE_PLAT_MIN_RATE);
            Integer giveMinNumber = configService.getIntegerValue(SysConstants.GIVE_PLAT_MIN_NUMBER);
            BigDecimal platRemainDecimal = new BigDecimal(collection.getTotalQuantity()).multiply(platMinRate);
            int platRemain = platRemainDecimal.intValue();
            if (giveMinNumber > collection.getTotalQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品数量不能小于" + giveMinNumber);
            }
            collection.setPlatRemainQuantity(platRemain < giveMinNumber ? giveMinNumber : platRemain);
            collection.setRemainQuantity(collection.getTotalQuantity() - collection.getPlatRemainQuantity());
        }

        Produced produced = producedService.detail(req.getProducedId());
        collection.setProducedId(produced.getId());

        if (CollectionUtils.isNotEmpty(req.getFileList())) {
            for (UploadFile uploadFile : req.getFileList()) {
                if (ECollectionFileType.COLLECTION_FILETYPE_2.getCode().equals(uploadFile.getType())
                        || ECollectionFileType.COLLECTION_FILETYPE_4.getCode().equals(uploadFile.getType())) {
                    uploadFile.setFirstAddress(uploadFile.getAddress() + "?x-oss-process=video/snapshot,t_1,f_jpg");
                }
            }
            collection.setFileUrl(JSONArray.toJSONString(req.getFileList()));
        }

        Date date = new Date();
        collection.setUpdater(operator.getId());
        collection.setUpdaterName(operator.getLoginName());
        collection.setUpdateDatetime(date);

        if (CollectionUtils.isNotEmpty(req.getTagList())) {
            String newStr = req.getTagList().stream().collect(Collectors.joining(","));
            collection.setTags(newStr);
        }

        collectionMapper.updateByPrimaryKeySelective(collection);

        // 插入数字藏品发行需求
        collectionSaleDemandService.create(collection, req.getCollectionSaleDemand(), operator);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toPublish(CollectionOssPublishReq req, User operator) {

        for (Long id : req.getIdList()) {
            Collection collection = detailForUpdate(id);
//            // 线上环境只能一处登录,其他环境看系统参数配置
//            if (EProfileActive.PROD.getCode().equals(profilesActive)) {
//                contractTokenInPool15Service.doCheckExist(id);
//            }

            Company company = companyService.detailSimple(collection.getAuthorId());
//            if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
//            }
            checkOperation(collection);

            if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
                if (!EBoolean.YES.getCode().equals(company.getPlatFlag())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不具备非藏品区作品发售权力");
                }
            }

            if (ECollectionStatus.COLLECTION_STATUS_6.getCode().equals(collection.getStatus())) {
                collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
            } else if (ECollectionStatus.COLLECTION_STATUS_5.getCode().equals(collection.getStatus())
                    || ECollectionStatus.COLLECTION_STATUS_9.getCode().equals(collection.getStatus())) {
                collection.setStatus(ECollectionStatus.COLLECTION_STATUS_8.getCode());
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品状态错误");
            }
            collection.setUpdater(operator.getUpdater());
            collection.setUpdaterName(operator.getUpdaterName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void undoPublish(CollectionOssPublishReq request, User operator) {
        for (Long id : request.getIdList()) {
            Collection collection = detailForUpdate(id);
//            // 线上环境只能一处登录,其他环境看系统参数配置
//            if (EProfileActive.PROD.getCode().equals(profilesActive)) {
//                contractTokenInPool15Service.doCheckExist(id);
//            }

            Company company = companyService.detailSimple(collection.getAuthorId());
            if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), company.getName() + "还未分账申请通过");
            }
            checkOperation(collection);

            if (!EUserKind.SYS.getCode().equals(operator.getKind()) && (
                    (EUserKind.BP.getCode().equals(operator.getKind()) && !collection.getAuthorId().equals(operator.getCompanyId()))
                            || !EUserKind.BP.getCode().equals(operator.getKind()))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权撤回当前作品");
            }

            if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
                if (!EBoolean.YES.getCode().equals(company.getPlatFlag())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不具备非藏品区作品撤回权力");
                }
            } else if (!EBoolean.YES.getCode().equals(company.getIndependencePlatFlag())
                    && !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品不能操作");
            }

            if (!ECollectionStatus.COLLECTION_STATUS_8.getCode().equals(collection.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品状态错误");
            }
            collection.setStatus(ECollectionStatus.COLLECTION_STATUS_5.getCode());
            collection.setUpdater(operator.getUpdater());
            collection.setUpdaterName(operator.getUpdaterName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void toAudit(Long id, CollectionOssAuditReq request, User operator) {
        Collection collection = detailForUpdate(id);
        if (!ECollectionStatus.COLLECTION_STATUS_8.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是待审核的作品");
        }

        Company company = companyService.detailSimple(collection.getAuthorId());
        User user = userService.detailBrief(company.getId());
        if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
            if (!EBoolean.YES.getCode().equals(company.getPlatFlag())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该发行方不具备非藏品区作品发售权力");
            }
        } else if (!EBoolean.YES.getCode().equals(company.getIndependencePlatFlag())
                && !ECollectionSaleDemandBuyType.COLLECTION_SALE_DEMAND_BUYTYPE_2.getCode().equals(collection.getBuyType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品不能上架");
        }

        checkOperation(collection);
//        collection.setRedPacketFlag(request.getRedPacketFlag());
        if (EBoolean.YES.getCode().equals(request.getStatus())) {
            auditOn(request, operator, collection);

//            //插入作品权益
//            collectionRightsDetailService.create(id, request.getRightsDetailList(), operator);

            // 审核不通过发消息给发行方
            if (ECollectionType.COLLECTION_TYPE_1.getCode().equals(collection.getType())) {
                String title = "作品审核通过";
                String content = "您发起的" + collection.getName() + "作品新增申请已通过";

                smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), collection.getAuthorId(), title, content,
                        ESmsRefType.COMPANY_SMS.getCode(),
                        ESmsRefType.COMPANY_SMS.getValue());

                smsOutService.sendSmsOut(user.getMobile(), content, null);
            }
        } else {

            ApproveRecord approveRecord = new ApproveRecord();
            approveRecord.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_1.getCode());
            approveRecord.setRefId(id);
            approveRecord.setCreater(operator.getId());
            approveRecord.setCreaterName(operator.getLoginName());
            approveRecord.setCreateDatetime(new Date());

            if (StringUtils.isBlank(request.getOpinion())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
            }
            approveRecord.setOpinion(request.getOpinion());
            approveRecord.setHistoryData(collection.toString());
            approveRecordService.create(approveRecord);

            collection.setStatus(ECollectionStatus.COLLECTION_STATUS_9.getCode());
            collection.setUpdater(operator.getUpdater());
            collection.setUpdaterName(operator.getUpdaterName());
            collection.setUpdateDatetime(new Date());
            collectionMapper.updateByPrimaryKeySelective(collection);

            // 审核不通过发消息给发行方
            if (ECollectionType.COLLECTION_TYPE_1.getCode().equals(collection.getType())) {
                String title = "作品审核未通过";
                String content = "您发起的" + collection.getName() + "作品新增申请已被打回,原因：" + request.getOpinion();

                smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), collection.getAuthorId(), title, content,
                        ESmsRefType.COMPANY_SMS.getCode(),
                        ESmsRefType.COMPANY_SMS.getValue());

                smsOutService.sendSmsOut(user.getMobile(), content, null);
            }
        }
    }

    @Override
    public void auditOn(CollectionOssAuditReq request, User operator, Collection collection) {
        contractTokenInPool15Service.doCheckExist(collection);

//        if (null == request.getDivideAuthorId()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分账主体不能为空");
//        } else if (null == request.getContractId()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约Id不能为空");
//        } else if (null == request.getPlatDivideRate()) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "平台分成不能为空");
//        }

        // 检查分账机构是否存在
//        Company company = new Company();
//        try {
//            company = companyService.detail(request.getDivideAuthorId());
//        } catch (Exception e) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分账主体不存在");
//        }
//        if (!ECompanyDivideStatus.E_COMPANY_DIVIDE_STATUS_2.getCode().equals(company.getDivideStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分账主体未通过三方申请");
//        }

        // 检查合约
//        Contract contract = contractService.detail(request.getContractId());
//        if (EContractStatus.TO_PUT.getCode().equals(contract.getStatus())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约还未上架");
//        }
//        if (ECollectionPeriodCategory.COPYRIGHT.getCode().equals(collection.getCategory()) && EContractProtocol.ERC1155.getCode()
//                .equals(contract.getProtocol())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "合约错误，版权不能使用ERC1155");
//        }

        // 分账不能都给发行方需要留点给平台用于支付手续费
        BigDecimal divideMinRate = configService.getBigDecimalValue(SysConstants.YEEPAY_DIVIDE_MIN_RATE);
//        if (request.getPlatDivideRate().compareTo(divideMinRate) < 0) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "平台分成比例不能小于" + divideMinRate + "，用于支付手续费");
//        }

//        collection.setDivideAuthorId(request.getDivideAuthorId());
//        collection.setContractId(request.getContractId());
//        collection.setPlatDivideRate(request.getPlatDivideRate());
        collection.setStatus(ECollectionStatus.COLLECTION_STATUS_1.getCode());
        collection.setUpdater(operator.getUpdater());
        collection.setUpdaterName(operator.getUpdaterName());
        collection.setUpdateDatetime(new Date());

//        // 权益
//        collection.setRightType(request.getRightType());
//        if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(request.getRightType())) {
//            if (CollectionUtils.isEmpty(request.getRightsDetailList())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写权益列表");
//            }
//        }

        CollectionRightsDetail collectionRightsDetail = new CollectionRightsDetail();
        collectionRightsDetail.setCollectionId(collection.getId());
        collectionRightsDetail.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
        List<CollectionRightsDetail> rightsDetailList = collectionRightsDetailService.list(collectionRightsDetail);
        // 如果包含元宇宙入场券权益
        if (CollectionUtils.isNotEmpty(rightsDetailList)) {
            CollectionRightsDetail rightsDetail = rightsDetailList.get(0);
            collection.setU3dFlag(EBoolean.YES.getCode());
            collection.setTicketType(rightsDetail.getTicketType());
        }

        collectionMapper.updateByPrimaryKeySelective(collection);
    }

    /**
     * 详情查询藏品
     *
     * @param id 主键ID
     * @return 藏品对象
     */
    @Override
    public Collection detail(Long id) {
        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        collection.setUser(userService.selectSummaryInfo(collection.getUserId()));
        if (StringUtils.isNotBlank(collection.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
                    UploadFile.class);
            collection.setFileList(uploadFiles);
        }

        Company company = companyService.detail(collection.getAuthorId());
        collection.setAuthor(company.getName());
        collection.setAuthorPic(company.getLogo());

        if (StringUtils.isNotBlank(collection.getTags())) {
            List<String> result = Arrays.asList(collection.getTags().split(","));
            collection.setTagList(result);
        }

        if (null != collection.getPayBalanceAmount() && collection.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0) {
            collection.setIsDeduction(EBoolean.YES.getCode());
        } else {
            collection.setIsDeduction(EBoolean.NO.getCode());
        }

        //获取权益列表
        CollectionRightsDetailListReq collectionRightsDetailListReq = new CollectionRightsDetailListReq();
        collectionRightsDetailListReq.setCollectionId(collection.getId());
        List<CollectionRightsDetail> rightsDetails = collectionRightsDetailService.list(collectionRightsDetailListReq);
        rightsDetails.forEach(item -> {
            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(item.getType())) {
                item.setMetaBizType(collection.getMetaBizType());
                if (null != collection.getMetaBizId()) {
                    item.setMetaBizId(collection.getMetaBizId());
                    item.setMetaBizName(fishBoatService.detail(collection.getMetaBizId()).getName());
                }
            }
        });
        collection.setRightsDetailList(rightsDetails);

        collection.setCollectionRightList(collectionRightService.list(collection.getId()));
        if (CollectionUtils.isNotEmpty(collection.getCollectionRightList())) {
            for (CollectionRight collectionRight : collection.getCollectionRightList()) {
                if (ECollectionRightDetailType.COLLECTION_RIGHT_DETAIL_TYPE_1.getCode().equals(collectionRight.getType())
                        && null != collectionRight.getRefId()) {
//                    ChangeSeries changeSeries = changeSeriesService.detail(collectionRight.getRefId());
//                    collectionRight.setRefName(changeSeries.getName());
                }
            }
        }

        if (null != collection.getDivideAuthorId()) {
            collection.setDivideAuthor(companyService.detail(collection.getDivideAuthorId()).getName());
        }

        if (null != collection.getProducedId()) {
            collection.setProducedName(producedService.detail(collection.getProducedId()).getName());
        }

        if (ECollectionStatus.COLLECTION_STATUS_9.getCode().equals(collection.getStatus())) {
            collection.setApproveOpinion(
                    approveRecordService.detail(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_1.getCode(), collection.getId()));
        }

        CollectionSaleDemand saleDemand = collectionSaleDemandService.detailByCollectionId(collection.getId());
        collection.setCollectionSaleDemand(saleDemand);

        if (null != saleDemand) {
            collection.setPrice(saleDemand.getPrice());
        }

        // 获取爻转化的数量
        collection.setYinYao(BigDecimal.ZERO);
        collection.setYangYao(BigDecimal.ZERO);
        CollectionYaoConfig collectionYaoConfig = collectionYaoConfigService
                .detailByCollection(collection.getId(), ECollectionYaoConfigType.COLLECTION_YAO_CONFIG_TYPE_0.getCode());
        if (null != collectionYaoConfig) {
            collection.setYinYao(collectionYaoConfig.getYinYao());
            collection.setYangYao(collectionYaoConfig.getYangYao());
        }

        if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_1.getCode().equals(collection.getMetaBizType())) {
            collection.setMetaBizName(fishBoatService.detail(collection.getMetaBizId()).getName());
        }
        return collection;
    }

    @Override
    public Collection detailSimple(Long id) {
        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return collection;
    }

    /**
     * 藏品是否存在
     */
    @Override
    public Boolean isExist(Long id) {
        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (collection == null) {
            return false;
        }

        return true;
    }

    /**
     * 分页查询藏品
     *
     * @param req 分页查询藏品入参
     * @return 分页藏品对象
     */
    @Override
    public List<Collection> page(CollectionPageReq req) {
        Collection condition = EntityUtils.copyData(req, Collection.class);
        condition.setOrderBy("id desc");
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);
        // 转译UserId
        collectionList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            if (StringUtils.isNotBlank(item.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(item.getFileUrl(),
                        UploadFile.class);
                item.setFileList(uploadFiles);
            }
        });

        return collectionList;
    }

    @Override
    public List<Collection> page(Collection condition) {
        condition.setOrderBy("id desc");
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);

        return collectionList;
    }

    @Override
    public List<CollectionOrderRes> page(CollectionPageOrderReq req) {
        List<CollectionOrderRes> resList = new ArrayList<>();
        Collection condition = EntityUtils.copyData(req, Collection.class);
        condition.setType(ECollectionType.COLLECTION_TYPE_2.getCode());
        condition.setOrderBy("id desc");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getIsDeduction()) && EBoolean.NO.getCode()
                .equals(req.getIsDeduction())) {
            condition.setNoDeduction(EBoolean.YES.getCode());
        } else if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getIsDeduction()) && EBoolean.YES.getCode()
                .equals(req.getIsDeduction())) {
            condition.setDeduction(EBoolean.YES.getCode());
        }
        condition.setPayDatetimeStart(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeStart(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        condition.setPayDatetimeEnd(
                com.std.core.util.DateUtil.dateTime(req.getPayDatetimeEnd(), com.std.core.util.DateUtil.YYYY_MM_DD_HH_MM_SS));
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);

        // 转译UserId
        collectionList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            if (StringUtils.isNotBlank(item.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(item.getFileUrl(),
                        UploadFile.class);
                item.setFileList(uploadFiles);
            }

            CollectionOrderRes collectionOrderRes = new CollectionOrderRes();
            BeanUtils.copyProperties(item, collectionOrderRes);
            Company company = companyService.detail(item.getAuthorId());
            collectionOrderRes.setCompany(company);

            if (null != item.getPayBalanceAmount() && item.getPayBalanceAmount().compareTo(BigDecimal.ZERO) > 0
                    && !EBigOrderPayType.ACCOUNT
                    .getCode().equals(item.getPayType())) {
                collectionOrderRes.setIsDeduction(EBoolean.YES.getCode());
            } else {
                collectionOrderRes.setIsDeduction(EBoolean.NO.getCode());
            }

            resList.add(collectionOrderRes);
        });

        return PageInfoUtil.listToPage(collectionList, resList);
    }

    @Override
    public List<Collection> listOss(CollectionListReq req) {
        Collection condition = EntityUtils.copyData(req, Collection.class);
        List<Collection> collectionList = collectionMapper.selectByConditionOss(condition);
        return collectionList;
    }

    @Override
    public List<Collection> list(Collection condition) {

        List<Collection> collectionList = collectionMapper.selectByCondition(condition);

        return collectionList;
    }

    @Override
    public Collection getCreateCollectionOrderByPayOrderCode(String payOrderCode) {
        Collection condition = new Collection();
        condition.setPayOrderCode(payOrderCode);
        List<Collection> collectionList = collectionMapper.selectByCondition(condition);
        if (CollectionUtils.isEmpty(collectionList)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "订单信息不存在");
        }
        return collectionList.get(0);
    }

    /**
     * 前端详情查询藏品
     *
     * @param id 主键ID
     * @return 藏品对象
     */
    @Override
    public CollectionDetailRes detailFront(Long id, User operator) {
        CollectionDetailRes res = new CollectionDetailRes();

        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        if (operator != null) {
            CollectionDetail collectionDetail = new CollectionDetail();
            if (ECollectionType.COLLECTION_TYPE_2.getCode().equals(collection.getType())) {
                List<CollectionDetail> collectionDetailList = collectionDetailService.list(collection.getId());
                if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                    res.setCollectionDetailId(collectionDetailList.get(0).getId());
                }
            }

            collectionDetail.setCollectionId(collection.getId());
            collectionDetail.setOwnerId(operator.getId());
            collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
            collection.setAvailableSellQuantity(Long.valueOf(collectionDetailService.countByCondition(collectionDetail)));

            collectionDetail = new CollectionDetail();
            collectionDetail.setCollectionId(collection.getId());
            collectionDetail.setOwnerId(operator.getId());
            collection.setRemainQuantity(collectionDetailService.countByCondition(collectionDetail));

            if (forumActionService.isCollection(operator.getId(), collection.getId(), EForumActionType.MOMENT.getCode())) {
                res.setIsCollection(EBoolean.YES.getCode());
            } else {
                res.setIsCollection(EBoolean.NO.getCode());
            }
        }
        // 转译UserId
        collection.setUser(userService.selectSummaryInfo(collection.getUserId()));
        if (StringUtils.isNotBlank(collection.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
                    UploadFile.class);
            collection.setFileList(uploadFiles);
        }
        BeanUtils.copyProperties(collection, res);

        MemberConfig memberConfig = memberConfigService.getMemberConfig(collection.getUserId());
        res.setMemberName(memberConfig.getName());
        res.setMemberLevel(memberConfig.getLevel());
        //出品方名称显示
        if (null != collection.getProducedId()) {
            res.setProducedId(collection.getProducedId());
            Produced produced = producedService.detail(collection.getProducedId());
            res.setProducedPic(produced.getPic());
            res.setProducedName(produced.getName());
        }

        return res;
    }


    /**
     * 详情查询藏品
     *
     * @param id 主键ID
     * @return 藏品对象
     */
    @Override
    public CollectionDetailRes detailFront2(Long id) {
        CollectionDetailRes res = new CollectionDetailRes();

        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        CollectionDetail collectionDetail = new CollectionDetail();
        collectionDetail.setCollectionId(collection.getId());
        collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
        collection.setAvailableSellQuantity(Long.valueOf(collectionDetailService.countByCondition(collectionDetail)));
        collectionDetail = new CollectionDetail();
        collectionDetail.setCollectionId(collection.getId());
        collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode());
        int count = collectionDetailService.countByCondition(collectionDetail);
        collection.setRemainQuantity(count + collection.getAvailableSellQuantity().intValue());

        if (StringUtils.isNotBlank(collection.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
                    UploadFile.class);
            collection.setFileList(uploadFiles);
        }
        BeanUtils.copyProperties(collection, res);
        if (count > 0) {
            res.setSellType(collectionDetailService.sellType(collectionDetail));
            res.setStatus(ECollectionStatus.COLLECTION_STATUS_2.getCode());
        }

        return res;
    }

    @Override
    public CollectionDetailRes selectSummaryInfo(Long id) {
        CollectionDetailRes res = new CollectionDetailRes();

        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        if (StringUtils.isNotBlank(collection.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
                    UploadFile.class);
            res.setFileList(uploadFiles);
        }
        res.setId(collection.getId());
        res.setName(collection.getName());
        res.setLevelType(collection.getLevelType());
        res.setFileType(collection.getFileType());
        res.setCoverFileUrl(collection.getCoverFileUrl());

        return res;
    }

    @Override
    public CollectionDetailRes selectSummaryInfo(Long id, Long collectionDetailId) {
        CollectionDetailRes res = new CollectionDetailRes();

        Collection collection = collectionMapper.selectByPrimaryKey(id);
        if (null == collection) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        if (StringUtils.isNotBlank(collection.getFileUrl())) {
            List<UploadFile> uploadFiles = JSONArray.parseArray(collection.getFileUrl(),
                    UploadFile.class);
            res.setFileList(uploadFiles);
        }
        res.setId(collection.getId());
        res.setName(collection.getName());
        res.setLevelType(collection.getLevelType());
        res.setFileType(collection.getFileType());
        res.setCoverFileUrl(collection.getCoverFileUrl());

        return res;
    }

    @Override
    public CollectionDetailRes selectSummaryInfoHaveUser(Long id, Long collectionDetailId) {
        CollectionDetailRes res = new CollectionDetailRes();

        Collection collection = collectionService.detail(id);

        BeanUtils.copyProperties(collection, res);
        res.setTotalQuantity(collection.getMarketQuantity());

        if (StringUtils.isNotBlank(collection.getTags())) {
            List<String> result = Arrays.asList(collection.getTags().split(","));
            res.setTagList(result);
        }

        res.setUser(userService.selectSummaryInfo(collection.getUserId()));

        res.setContractToken(contractTokenService.detailFrontByCollectionDetail(collectionDetailId));

        res.setAuthorId(collection.getAuthorId());
        res.setAuthor(collection.getAuthor());
        res.setAuthorPic(collection.getAuthorPic());

        // 统计收藏人数
        res.setCollectCount(collectionDetailService.getCollectCount(collection.getId()));
        // 统计出售人数
        res.setOfferCount(collectionDetailService.getOfferCount(collection.getId()));
        //分享介绍
        res.setIntroduce(configService.getStringValue(SysConstants.SHARE_NOTE));

        res.setRightType(collection.getRightType());
        res.setRightContent(collection.getRightContent());

        //获取权益列表
        if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(res.getRightType())) {
            //获取权限列表
            CollectionRightsDetailListFrontReq rightsDetailListFrontReq = new CollectionRightsDetailListFrontReq();
            rightsDetailListFrontReq.setCollectionId(collection.getId());
            res.setRightList(collectionRightsDetailService.listFront(rightsDetailListFrontReq, collectionDetailId));
        }

        //出品方名称显示
        if (null != collection.getProducedId()) {
            res.setProducedId(collection.getProducedId());
            Produced produced = producedService.detail(collection.getProducedId());
            res.setProducedPic(produced.getPic());
            res.setProducedName(produced.getName());
        }
        return res;
    }

    /**
     * 前端分页查询藏品
     *
     * @param req 前端分页查询藏品入参
     * @return 分页藏品对象
     */
    @Override
    public List<CollectionPageRes> pageFront(CollectionPageFrontReq req, User operator) {
        Collection condition = EntityUtils.copyData(req, Collection.class);
        condition.setOrderBy("id desc");
        condition.setOwnerId(operator.getId());
        condition.setDownStatus(EBoolean.NO.getCode());
        List<String> noStatusList = new ArrayList<>();
        noStatusList.add(ECollectionStatus.COLLECTION_STATUS_TORECEIVE.getCode());
        condition.setNoStatusList(noStatusList);

        if (ECollectionPhotoCategory.PHOTO.getCode().equals(req.getCategory())) {
            condition.setType(ECollectionType.COLLECTION_TYPE_2.getCode());
        }
        List<Collection> collectionList = collectionMapper.selectextendByCondition(condition);

        List<CollectionPageRes> resList = collectionList.stream().map((entity) -> {

            CollectionPageRes res = new CollectionPageRes();
            if (StringUtils.isNotBlank(entity.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(entity.getFileUrl(),
                        UploadFile.class);
                entity.setFileList(uploadFiles);
            }
            BeanUtils.copyProperties(entity, res);
            res.setRemainQuantity(entity.getAvailableSellQuantity().intValue());
            if (ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(res.getStatus())) {

                if (collectionDetailService
                        .countByCondition(operator.getId(), entity.getId(),
                                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode())
                        > 0) {
                    res.setStatus(ECollectionStatus.COLLECTION_STATUS_2.getCode());
                }

                if (res.getRemainQuantity() == 1) {
                    CollectionDetail collectionDetail = collectionDetailService
                            .detail(res.getId(), operator.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_3.getCode());
                    if (collectionDetail != null) {
                        res.setStatus(ECollectionStatus.COLLECTION_STATUS_4.getCode());
                    }
                }
            }

            if (ECollectionType.COLLECTION_TYPE_2.getCode().equals(entity.getType())) {
                List<CollectionDetail> collectionDetailList = collectionDetailService.list(entity.getId());
                if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                    res.setCollectionDetailId(collectionDetailList.get(0).getId());
                }
            }
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionList, resList);
    }

    @Override
    public List<CollectionPageRes> pageUserSellFront(CollectionPageFrontReq req) {
        Collection condition = EntityUtils.copyData(req, Collection.class);
        condition.setOrderBy("id desc");
        condition.setDownStatus(EBoolean.NO.getCode());
        if (StringUtils.isNotBlank(req.getSellStatus())) {
            if (EBoolean.NO.getCode().equals(req.getSellStatus())) {
                condition.setSellStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
            } else {
                condition.setSellStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode());
            }
        }

        List<Collection> collectionList = collectionMapper.selectextendByCondition(condition);

        List<CollectionPageRes> resList = collectionList.stream().map((entity) -> {

            CollectionPageRes res = new CollectionPageRes();
            if (StringUtils.isNotBlank(entity.getFileUrl())) {
                List<UploadFile> uploadFiles = JSONArray.parseArray(entity.getFileUrl(),
                        UploadFile.class);
                entity.setFileList(uploadFiles);
            }
            BeanUtils.copyProperties(entity, res);
            res.setRemainQuantity(entity.getAvailableSellQuantity().intValue());
            if (ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(res.getStatus())) {

                if (collectionDetailService
                        .countByCondition(entity.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_2.getCode())
                        > 0) {
                    res.setStatus(ECollectionStatus.COLLECTION_STATUS_2.getCode());
                }
            }
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionList, resList);
    }

    @Override
    public void doCallback(String bizCode, String payment, EJourCommon unfrozenBizType, String code) {
        Collection collection = getCreateCollectionOrderByPayOrderCode(bizCode);
        User user = userService.detail(collection.getUserId());
        collection.setPayType(payment);
        if (ECollectionPayStatus.COLLECTION_PAYSTATUS_1.getCode().equals(collection.getPayStatus())) {
            log.info("diy订单已支付，回调重复，作品id：" + collection.getId());
            return;
        }

        String buyChannel = null;
        if (EPayRecordBizType.PAY_RECORD_BIZTYPE_7.getCode().equals(code)) {
            buyChannel = ECollectionDetailBuyChannel.TWO.getCode();
        }

        paySuccess(collection, user, EJourBizTypeUser.Collection.CreateCollection, EJourBizTypeSystem.Fee.CollectionCreateFee,
                EJourBizTypeUser.UserDiamondIncome.CREATE_COLLECTION, unfrozenBizType, "创建[" + collection.getName() + "]藏品",
                buyChannel);
    }

    @Override
    public List<PersonSellCollectionPageRes> personSellCollection(PersonSellCollectionPageReq req) {
        CollectionDetail condition = EntityUtils.copyData(req, CollectionDetail.class);
//        condition.setOrderBy("read_count desc");
        List<Long> collectionList = collectionDetailService.myCollectionList(condition);

        List<PersonSellCollectionPageRes> resList = collectionList.stream().map((entity) -> {
            PersonSellCollectionPageRes res = new PersonSellCollectionPageRes();
            BeanUtils.copyProperties(entity, res);
            CollectionDetailRes collectionDetailRes = selectSummaryInfo(entity);
            res.setCollectionDetailRes(collectionDetailRes);
            res.setCollectionId(entity);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionList, resList);
    }

    @Override
    public BigDecimal selectMonthAmount(List<User> teamList) {
        if (CollectionUtils.isEmpty(teamList)) {
            return BigDecimal.ZERO;
        }
        MonthCondition monthCondition = new MonthCondition();
        monthCondition.setTeamList(teamList);
        monthCondition.setMonthStart(com.std.core.util.DateUtil.getCurrMonthFirstDay());
        monthCondition.setMonthEnd(com.std.core.util.DateUtil.getCurrMonthLastDay());
        return collectionMapper.selectMonthAmount(monthCondition);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ossUp(Long id, User operator) {
        if (!operator.getKind().equals(EUserKind.SYS.getCode())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不是管理员，无权操作");
        }
        Collection collection = collectionService.detail(id);
        checkOperation(collection);
        if (collection.getDownStatus().equals(EBoolean.NO.getCode())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品已上架");
        }

        collection.setDownStatus(EBoolean.NO.getCode());
        collectionMapper.updateByPrimaryKeySelective(collection);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void ossOff(Long id, User operator) {

        if (!operator.getKind().equals(EUserKind.SYS.getCode())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不是管理员，无权操作");
        }

        Collection collection = collectionService.detail(id);
        if (collection.getDownStatus().equals(EBoolean.YES.getCode())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品已下架");
        }

        collection.setDownStatus(EBoolean.YES.getCode());
        collectionMapper.updateByPrimaryKeySelective(collection);

        //        //一口价中下架相关
//        BuyoutProducts buyoutCondition = new BuyoutProducts();
//        buyoutCondition.setCollectionId(id);
//        List<String> statusList = new ArrayList<>();
//        statusList.add(EBuyoutProductsStatus.BUYOUT_PRODUCTS_STATUS_0.getCode());
//        buyoutCondition.setStatusList(statusList);
//        List<BuyoutProducts> buyoutProductsList = buyoutProductsMapper.selectByCondition(buyoutCondition);
//        for (BuyoutProducts buyoutProducts : buyoutProductsList) {
//            BuyoutProductsOffReq req = new BuyoutProductsOffReq();
//            req.setId(buyoutProducts.getId());
//            buyoutProductsService.productOff(req, operator);
//        }
//
//        //竞拍下架相关
//        AuctionProducts auctionCondition = new AuctionProducts();
//        auctionCondition.setCollectionId(id);
//        List<String> auctionStatusList = new ArrayList<>();
//        statusList.add(EAuctionProductsStatus.AUCTION_PRODUCTS_STATUS_0.getCode());
//        auctionCondition.setStatusList(auctionStatusList);
//        List<AuctionProducts> auctionProductsList = auctionProductsService.list(auctionCondition);
//        for (AuctionProducts auctionProducts : auctionProductsList) {
//            AuctionProductsOffReq req = new AuctionProductsOffReq();
//            req.setId(auctionProducts.getId());
//            auctionProductsService.ossProductOff(req, operator);
//        }
    }
}