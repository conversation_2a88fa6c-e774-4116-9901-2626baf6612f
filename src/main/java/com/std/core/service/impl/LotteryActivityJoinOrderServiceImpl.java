package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.DateUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.ECollectionDetailOwnerType;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ELotteryActivityJoinOrderStatus;
import com.std.core.enums.ELotteryActivityJoinRecordStatus;
import com.std.core.mapper.LotteryActivityJoinOrderMapper;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.Express;
import com.std.core.pojo.domain.LotteryActivity;
import com.std.core.pojo.domain.LotteryActivityCollection;
import com.std.core.pojo.domain.LotteryActivityJoinOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LotteryActivityJoinOrderAddAddressReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderListFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderListReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderModifyReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderPageFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderPageReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderSendLogisticsReq;
import com.std.core.pojo.response.LotteryActivityCollectionListRes;
import com.std.core.pojo.response.LotteryActivityJoinOrderDetailRes;
import com.std.core.pojo.response.LotteryActivityJoinOrderListRes;
import com.std.core.pojo.response.LotteryActivityJoinOrderPageRes;
import com.std.core.pojo.response.LotteryActivityJoinRecordDetailRes;
import com.std.core.pojo.response.LotteryActivityPageFrontRes;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.IExpressService;
import com.std.core.service.ILotteryActivityCollectionService;
import com.std.core.service.ILotteryActivityJoinOrderService;
import com.std.core.service.ILotteryActivityJoinRecordService;
import com.std.core.service.ILotteryActivityService;
import com.std.core.service.IUserService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 抽奖活动用户参与订单ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-07-08 11:46
 */
@Service
public class LotteryActivityJoinOrderServiceImpl implements ILotteryActivityJoinOrderService {

    @Resource
    private LotteryActivityJoinOrderMapper lotteryActivityJoinOrderMapper;

    @Resource
    private IUserService userService;

    @Resource
    private ILotteryActivityService lotteryActivityService;

    @Resource
    private ILotteryActivityJoinRecordService lotteryActivityJoinRecordService;

    @Resource
    private ILotteryActivityCollectionService lotteryActivityCollectionService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private IExpressService expressService;

    /**
     * 新增抽奖活动用户参与订单
     */
    @Override
    public Long create(LotteryActivity lotteryActivity, User operator) {
        //查询是否已经有存在的报名订单
        Date date = new Date();
        LotteryActivityJoinOrder data = selectOne(lotteryActivity.getId(), operator.getId());
        if (data != null) {
            data.setJoinTime(data.getJoinTime() + 1);
            data.setUpdateDatetime(date);
            lotteryActivityJoinOrderMapper.updateByPrimaryKeySelective(data);
            return data.getId();
        }

        //没有就创建
        LotteryActivityJoinOrder lotteryActivityJoinOrder = new LotteryActivityJoinOrder();
        lotteryActivityJoinOrder.setActivityId(lotteryActivity.getId());
        lotteryActivityJoinOrder.setActivityName(lotteryActivity.getName());
        lotteryActivityJoinOrder.setUserId(operator.getId());
        lotteryActivityJoinOrder.setStatus(ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_0.getCode());
        lotteryActivityJoinOrder.setJoinTime(1);
        lotteryActivityJoinOrder.setSoldQuantity(0);
        lotteryActivityJoinOrder.setCreateDatetime(date);
        lotteryActivityJoinOrder.setUpdateDatetime(date);
        lotteryActivityJoinOrderMapper.insertSelective(lotteryActivityJoinOrder);

        return lotteryActivityJoinOrder.getId();
    }

    private LotteryActivityJoinOrder selectOne(Long activityId, Long userId) {
        LotteryActivityJoinOrder condition = new LotteryActivityJoinOrder();
        condition.setActivityId(activityId);
        condition.setUserId(userId);
        List<LotteryActivityJoinOrder> list = lotteryActivityJoinOrderMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }

        return null;
    }

    /**
     * 删除抽奖活动用户参与订单
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        lotteryActivityJoinOrderMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改抽奖活动用户参与订单
     *
     * @param req 修改抽奖活动用户参与订单入参
     * @param operator 操作人
     */
    @Override
    public void modify(LotteryActivityJoinOrderModifyReq req, User operator) {
        LotteryActivityJoinOrder lotteryActivityJoinOrder = EntityUtils.copyData(req, LotteryActivityJoinOrder.class);
        lotteryActivityJoinOrderMapper.updateByPrimaryKeySelective(lotteryActivityJoinOrder);
    }

    /**
     * 详情查询抽奖活动用户参与订单
     *
     * @param id 主键ID
     * @return 抽奖活动用户参与订单对象
     */
    @Override
    public LotteryActivityJoinOrder detail(Long id) {
        LotteryActivityJoinOrder lotteryActivityJoinOrder = lotteryActivityJoinOrderMapper.selectByPrimaryKey(id);
        if (null == lotteryActivityJoinOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        // 转译UserId
        lotteryActivityJoinOrder.setUser(userService.selectSummaryInfo(lotteryActivityJoinOrder.getUserId()));

        // 报名记录
        LotteryActivity lotteryActivity = lotteryActivityService.detail(lotteryActivityJoinOrder.getActivityId());
        lotteryActivityJoinOrder.setType(lotteryActivity.getType());
        List<LotteryActivityJoinRecordDetailRes> activityJoinRecordDetailList = lotteryActivityJoinRecordService
                .detailJoinRecordDetailList(lotteryActivityJoinOrder.getUserId(), lotteryActivity);

        for (LotteryActivityJoinRecordDetailRes detailRes : activityJoinRecordDetailList) {
            detailRes.setCreateDatetime(DateUtil.dateToStr(new Date(detailRes.getCreateTime()), "yyyy-MM-dd HH:mm:ss.SSS"));
        }
        lotteryActivityJoinOrder.setActivityJoinRecordDetailList(activityJoinRecordDetailList);

        return lotteryActivityJoinOrder;
    }

    /**
     * 分页查询抽奖活动用户参与订单
     *
     * @param req 分页查询抽奖活动用户参与订单入参
     * @return 分页抽奖活动用户参与订单对象
     */
    @Override
    public List<LotteryActivityJoinOrder> page(LotteryActivityJoinOrderPageReq req) {
        LotteryActivityJoinOrder condition = EntityUtils.copyData(req, LotteryActivityJoinOrder.class);

        List<LotteryActivityJoinOrder> lotteryActivityJoinOrderList = lotteryActivityJoinOrderMapper.selectByCondition(condition);
        // 转译UserId
        lotteryActivityJoinOrderList.forEach(item -> {
            item.setUser(userService.selectSummaryInfo(item.getUserId()));
            LotteryActivity lotteryActivity = lotteryActivityService.detailSimple(item.getActivityId());
            item.setType(lotteryActivity.getType());
        });

        return lotteryActivityJoinOrderList;
    }

    /**
     * 列表查询抽奖活动用户参与订单
     *
     * @param req 列表查询抽奖活动用户参与订单入参
     * @return 列表抽奖活动用户参与订单对象
     */
    @Override
    public List<LotteryActivityJoinOrder> list(LotteryActivityJoinOrderListReq req) {
        LotteryActivityJoinOrder condition = EntityUtils.copyData(req, LotteryActivityJoinOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), LotteryActivityJoinOrder.class));

        return lotteryActivityJoinOrderMapper.selectByCondition(condition);
    }

    /**
     * 前端详情查询抽奖活动用户参与订单
     *
     * @param id 主键ID
     * @return 抽奖活动用户参与订单对象
     */
    @Override
    public LotteryActivityJoinOrderDetailRes detailFront(Long id) {
        LotteryActivityJoinOrderDetailRes res = new LotteryActivityJoinOrderDetailRes();

        LotteryActivityJoinOrder lotteryActivityJoinOrder = lotteryActivityJoinOrderMapper.selectByPrimaryKey(id);
        if (null == lotteryActivityJoinOrder) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        BeanUtils.copyProperties(lotteryActivityJoinOrder, res);

        // 报名记录
        LotteryActivity lotteryActivity = lotteryActivityService.detail(lotteryActivityJoinOrder.getActivityId());
        res.setType(lotteryActivity.getType());
        List<LotteryActivityJoinRecordDetailRes> activityJoinRecordDetailList = lotteryActivityJoinRecordService
                .detailJoinRecordDetailList(lotteryActivityJoinOrder.getUserId(), lotteryActivity);

        for (LotteryActivityJoinRecordDetailRes detailRes : activityJoinRecordDetailList) {
            detailRes.setCreateDatetime(DateUtil.dateToStr(new Date(detailRes.getCreateTime()), "yyyy-MM-dd HH:mm:ss.SSS"));
        }
        res.setActivityJoinRecordDetailList(activityJoinRecordDetailList);

        // 快递公司初始化
        Express express = expressService.detailByKdnCode(lotteryActivityJoinOrder.getLogisticsCompany());
        if (null == express) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
        }
        res.setLogisticsCompany(express.getName());

        return res;
    }

    /**
     * 前端分页查询抽奖活动用户参与订单
     *
     * @param req 前端分页查询抽奖活动用户参与订单入参
     * @return 分页抽奖活动用户参与订单对象
     */
    @Override
    public List<LotteryActivityJoinOrderPageRes> pageFront(LotteryActivityJoinOrderPageFrontReq req, User operator) {
        LotteryActivityJoinOrder condition = EntityUtils.copyData(req, LotteryActivityJoinOrder.class);
        condition.setUserId(operator.getId());

        if (ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_1.getCode().equals(req.getStatus())) {
            List<String> statusList = new ArrayList<>();
            statusList.add(ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_1.getCode());
            statusList.add(ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_3.getCode());
            statusList.add(ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_4.getCode());
            condition.setStatusList(statusList);
            condition.setStatus(null);
        }

        List<LotteryActivityJoinOrder> lotteryActivityJoinOrderList = lotteryActivityJoinOrderMapper.selectByCondition(condition);

        List<LotteryActivityJoinOrderPageRes> resList = lotteryActivityJoinOrderList.stream().map((entity) -> {
            LotteryActivityJoinOrderPageRes res = new LotteryActivityJoinOrderPageRes();
            BeanUtils.copyProperties(entity, res);

            LotteryActivity activity = lotteryActivityService.detail(entity.getActivityId());
            LotteryActivityPageFrontRes activityRes = new LotteryActivityPageFrontRes();
            BeanUtils.copyProperties(activity, activityRes);
            res.setActivity(activityRes);
            res.setJoinQuantity(entity.getJoinTime());
            //只有待开奖才计算剩余报名次数
            if (ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_0.getCode().equals(entity.getStatus())) {
                Integer singleNumber = null;
                List<LotteryActivityCollection> collectionList = lotteryActivityCollectionService.detailByActivity(entity.getActivityId());
                for (LotteryActivityCollection activityCollection : collectionList) {
                    List<CollectionDetail> list = collectionDetailService.list(ECollectionDetailOwnerType.CUSER.getCode(), operator.getId(),
                            ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), activityCollection.getCollectionId());
                    LotteryActivityCollectionListRes activityCollectionRes = new LotteryActivityCollectionListRes();
                    BeanUtils.copyProperties(activityCollection, activityCollectionRes);
                    activityCollectionRes.setQuantity(list.size());
                    activityCollectionRes.setNeedQuantity(activityCollection.getQuantity());

                    if (CollectionUtils.isEmpty(list)) {
                        singleNumber = 0;
                    } else {
                        int i = list.size() / activityCollection.getQuantity();
                        if (null == singleNumber) {
                            singleNumber = i;
                        } else if (singleNumber > i) {
                            singleNumber = i;
                        }
                    }
                }
                res.setSingleNumber(singleNumber);
            } else {
                activityRes.setSoldQuantity(entity.getSoldQuantity());
            }
            activityRes.setTotalQuantity(activity.getJoinQuantity());
            activityRes.setJoinQuantity(activity.getJoinQuantity());

            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(lotteryActivityJoinOrderList, resList);
    }

    /**
     * 前端列表查询抽奖活动用户参与订单
     *
     * @param req 前端列表查询抽奖活动用户参与订单入参
     * @return 列表抽奖活动用户参与订单对象
     */
    @Override
    public List<LotteryActivityJoinOrderListRes> listFront(LotteryActivityJoinOrderListFrontReq req) {
        LotteryActivityJoinOrder condition = EntityUtils.copyData(req, LotteryActivityJoinOrder.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), LotteryActivityJoinOrder.class));

        List<LotteryActivityJoinOrder> lotteryActivityJoinOrderList = lotteryActivityJoinOrderMapper.selectByCondition(condition);

        List<LotteryActivityJoinOrderListRes> resList = lotteryActivityJoinOrderList.stream().map((entity) -> {
            LotteryActivityJoinOrderListRes res = new LotteryActivityJoinOrderListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addAddress(LotteryActivityJoinOrderAddAddressReq req, User operator) {
        LotteryActivityJoinOrder lotteryActivityJoinOrder = lotteryActivityJoinOrderMapper.selectByPrimaryKeyForUpdate(req.getId());
        if (!ELotteryActivityJoinRecordStatus.LOTTERY_ACTIVITY_JOIN_RECORD_STATUS_1.getCode()
                .equals(lotteryActivityJoinOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不是中奖状态，无法绑定地址");
        }
        if (!operator.getId().equals(lotteryActivityJoinOrder.getUserId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您无权操作");
        }
        lotteryActivityJoinOrder.setStatus(ELotteryActivityJoinRecordStatus.LOTTERY_ACTIVITY_JOIN_RECORD_STATUS_3.getCode());
        lotteryActivityJoinOrder.setReceiver(req.getReceiver());
        lotteryActivityJoinOrder.setReMobile(req.getReMobile());
        lotteryActivityJoinOrder.setReAddress(req.getReAddress());
        lotteryActivityJoinOrder.setUpdateDatetime(new Date());
        lotteryActivityJoinOrderMapper.updateByPrimaryKeySelective(lotteryActivityJoinOrder);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendLogistics(LotteryActivityJoinOrderSendLogisticsReq req, User operator) {
        LotteryActivityJoinOrder lotteryActivityJoinOrder = lotteryActivityJoinOrderMapper.selectByPrimaryKeyForUpdate(req.getId());
        if (!ELotteryActivityJoinOrderStatus.LOTTERY_ACTIVITY_JOIN_ORDER_STATUS_3.getCode()
                .equals(lotteryActivityJoinOrder.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "未绑定地址，无法发货");
        }
        lotteryActivityJoinOrder.setStatus(ELotteryActivityJoinRecordStatus.LOTTERY_ACTIVITY_JOIN_RECORD_STATUS_4.getCode());
        lotteryActivityJoinOrder.setDeliverer(operator.getId().toString());
        lotteryActivityJoinOrder.setDeliveryDatetime(new Date());
        lotteryActivityJoinOrder.setLogisticsCode(req.getLogisticsCode());
        lotteryActivityJoinOrder.setLogisticsCompany(req.getLogisticsCompany());
        lotteryActivityJoinOrderMapper.updateByPrimaryKeySelective(lotteryActivityJoinOrder);
    }

}