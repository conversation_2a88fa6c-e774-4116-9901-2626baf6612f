package com.std.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageHelper;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.service.ISmsOutService;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EApproveRecordRefType;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChallengeBuyStatus;
import com.std.core.enums.EChallengeCollectionBackType;
import com.std.core.enums.EChallengeCollectionLockCondition;
import com.std.core.enums.EChallengeDistributionType;
import com.std.core.enums.EChallengeHistoryType;
import com.std.core.enums.EChallengeIsFinal;
import com.std.core.enums.EChallengeOrderStatus;
import com.std.core.enums.EChallengeStartStatus;
import com.std.core.enums.EChallengeStatus;
import com.std.core.enums.EChallengeType;
import com.std.core.enums.ECollectionCategory;
import com.std.core.enums.ECollectionContentType;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailRecordTradeType;
import com.std.core.enums.ECollectionDetailRefType;
import com.std.core.enums.ECollectionDetailSource;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.ECollectionFileType;
import com.std.core.enums.ECollectionPeriodCategory;
import com.std.core.enums.ECollectionRightType;
import com.std.core.enums.ECollectionStatus;
import com.std.core.enums.ECompanyEntityStatus;
import com.std.core.enums.EContractTokenStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.ELockBizType;
import com.std.core.enums.ESmsRefType;
import com.std.core.enums.ESmsTarget;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.ChallengeMapper;
import com.std.core.pojo.domain.ApproveRecord;
import com.std.core.pojo.domain.Challenge;
import com.std.core.pojo.domain.ChallengeCondition;
import com.std.core.pojo.domain.ChallengeConditionDetail;
import com.std.core.pojo.domain.ChallengeOrder;
import com.std.core.pojo.domain.ChallengeOrderDetail;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.CompanyEntity;
import com.std.core.pojo.domain.CompanyInfo;
import com.std.core.pojo.domain.Express;
import com.std.core.pojo.domain.Produced;
import com.std.core.pojo.domain.UploadFile;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChallengeBatchAuditReq;
import com.std.core.pojo.request.ChallengeBatchDownReq;
import com.std.core.pojo.request.ChallengeByCollectionPageFrontReq;
import com.std.core.pojo.request.ChallengeConditionCompanyCreateReq;
import com.std.core.pojo.request.ChallengeConditionCreateReq;
import com.std.core.pojo.request.ChallengeConditionDetailChildReq;
import com.std.core.pojo.request.ChallengeConditionDetailListReq;
import com.std.core.pojo.request.ChallengeConditionListFrontReq;
import com.std.core.pojo.request.ChallengeConditionPageFrontReq;
import com.std.core.pojo.request.ChallengeCreateMetaCompanyReq;
import com.std.core.pojo.request.ChallengeCreateReq;
import com.std.core.pojo.request.ChallengeFinalH5Req;
import com.std.core.pojo.request.ChallengeFinalReq;
import com.std.core.pojo.request.ChallengeListFrontReq;
import com.std.core.pojo.request.ChallengeListReq;
import com.std.core.pojo.request.ChallengeModifyMetaCompanyReq;
import com.std.core.pojo.request.ChallengeModifyReq;
import com.std.core.pojo.request.ChallengeOrderNoReq;
import com.std.core.pojo.request.ChallengePageFrontReq;
import com.std.core.pojo.request.ChallengePageHistoryReq;
import com.std.core.pojo.request.ChallengePageReq;
import com.std.core.pojo.request.ChallengeStartReviewReq;
import com.std.core.pojo.request.CollectionRightsDetailListFrontReq;
import com.std.core.pojo.response.ChallengeAwardDeatilRes;
import com.std.core.pojo.response.ChallengeByCollectionPageRes;
import com.std.core.pojo.response.ChallengeConditionDetailFrontRes;
import com.std.core.pojo.response.ChallengeDetailAwardRes;
import com.std.core.pojo.response.ChallengeDetailEntityRes;
import com.std.core.pojo.response.ChallengeDetailRes;
import com.std.core.pojo.response.ChallengeHistoryPageRes;
import com.std.core.pojo.response.ChallengeListRes;
import com.std.core.pojo.response.ChallengePageRes;
import com.std.core.pojo.response.CollectionContractTokenRes;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.service.IApproveRecordService;
import com.std.core.service.IChallengeConditionDetailService;
import com.std.core.service.IChallengeConditionService;
import com.std.core.service.IChallengeOrderDetailService;
import com.std.core.service.IChallengeOrderService;
import com.std.core.service.IChallengeService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionDetailRecordService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyChannelService;
import com.std.core.service.ICompanyEntityService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IConfigService;
import com.std.core.service.IContractTokenService;
import com.std.core.service.IExpressService;
import com.std.core.service.ILockService;
import com.std.core.service.IProducedService;
import com.std.core.service.ISmsService;
import com.std.core.service.IUserService;
import com.std.core.util.CopyObjectUtil;
import com.std.core.util.DateUtil;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstants;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 挑战ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2021-12-28 10:49
 */
@Service
public class ChallengeServiceImpl implements IChallengeService {

    @Resource
    private ChallengeMapper challengeMapper;

    @Autowired
    private ICompanyService companyService;

    @Autowired
    private ICollectionService collectionService;

    @Autowired
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ICollectionDetailRecordService collectionDetailRecordService;

    @Autowired
    private ICompanyEntityService companyEntityService;

    @Autowired
    private ICollectionRightsDetailService collectionRightsDetailService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IChallengeOrderService challengeOrderService;

    @Resource
    private IContractTokenService contractTokenService;

    @Resource
    private IChallengeOrderDetailService challengeOrderDetailService;

    @Resource
    private IExpressService expressService;

    @Resource
    private IConfigService configService;

    @Resource
    private IChallengeConditionService challengeConditionService;

    @Resource
    private IChallengeConditionDetailService challengeConditionDetailService;

    @Resource
    private ILockService lockService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICompanyChannelService companyChannelService;

    @Resource
    private IApproveRecordService approveRecordService;

    @Resource
    private IProducedService producedService;

    @Resource
    private ISmsService smsService;

    @Resource
    private ISmsOutService smsOutService;

    /**
     * 新增挑战
     *
     * @param req 新增挑战入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ChallengeCreateReq req, User operator) {
        Challenge challenge = EntityUtils.copyData(req, Challenge.class);

        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
            if (!EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请创建数字藏品合成");
            }
            req.setCompanyId(operator.getCompanyId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        if (null == req.getCompanyId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "机构id不能为空");
        }

        Company company = companyService.detail(req.getCompanyId());

//        // 处理emoji
////        challenge.setName(EmojiParser.removeAllEmojis(challenge.getName()));
//        CopyObjectUtil.stringRemoveEmoji(challenge);
        // 判断开始时间
        Date date = com.std.common.utils.DateUtil.strToDate(req.getStartTime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2);
        challenge.setStartTime(date);
        if (challenge.getStartTime().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战开始时间早于当前时间");
        }

        // 设置结束时间
        Date endTime = DateUtil.addHours(challenge.getStartTime(), challenge.getEffectiveHours());
        if (endTime.before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战结束时间早于当前时间");
        }
        challenge.setEndTime(endTime);

        // 剩余数量=总数量
        challenge.setAwardRemainQuantity(challenge.getAwardQuantity());
        if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());
            // 检查作品是否属于麦塔
            collectionService.checkOperation(collection);
            if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择作品不可用");
            }

            if (!collection.getAuthorId().equals(company.getId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励的nft藏品不属于" + company.getName() + "机构");
            }
            // 作品剩余数量分发行方和平台
            if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
            }
        } else if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            if (!companyEntity.getCompanyId().equals(challenge.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), companyEntity.getName() + "实物不属于该机构");
            }
        }

        challenge.setTotalCollectionNum(0);
        challenge.setStatus(EChallengeStatus.ZERO.getCode());
        challenge.setApplyUserId(operator.getId());
        challenge.setApplyDatetime(new Date());
        challengeMapper.insertSelective(challenge);

    }

    /**
     * 发行方新增挑战
     *
     * @param req 新增挑战入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ChallengeCreateMetaCompanyReq req, User operator) {
        Company company = companyService.checkCompany(operator.getCompanyId());
        Challenge challenge = EntityUtils.copyData(req, Challenge.class);

        ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, channelMerchant.getId());
            challenge.setCompanyId(operator.getCompanyId());
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

//        // 处理emoji
////        challenge.setName(EmojiParser.removeAllEmojis(challenge.getName()));
//        CopyObjectUtil.stringRemoveEmoji(challenge);
        // 判断开始时间
        Date date = com.std.common.utils.DateUtil.strToDate(req.getStartTime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2);
        challenge.setStartTime(date);
        if (challenge.getStartTime().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战开始时间早于当前时间");
        }
        challenge.setChannelId(channelMerchant.getId());
        // 设置结束时间
        Date endTime = DateUtil.addHours(challenge.getStartTime(), challenge.getEffectiveHours());
        if (endTime.before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战结束时间早于当前时间");
        }
        challenge.setEndTime(endTime);

        // 剩余数量=总数量
        challenge.setAwardRemainQuantity(challenge.getAwardQuantity());
        if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());
            // 检查作品是否属于麦塔
            collectionService.checkOperation(collection);
            if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择作品不可用");
            }

            if (!collection.getAuthorId().equals(company.getId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励的nft藏品不属于" + company.getName() + "机构");
            }
            // 作品剩余数量分发行方和平台
            if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
            }
        } else if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            if (!companyEntity.getCompanyId().equals(challenge.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), companyEntity.getName() + "实物不属于该机构");
            }
            if (!ECompanyEntityStatus.E_COMPANY_ENTITY_STATUS_2.getCode().equals(companyEntity.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), companyEntity.getName() + "实物未审核通过");
            }
        }

        challenge.setTotalCollectionNum(0);
        challenge.setStatus(EChallengeStatus.ZERO.getCode());
        challenge.setApplyUserId(operator.getId());
        challenge.setApplyDatetime(new Date());
        challengeMapper.insertSelective(challenge);

        // 设置挑战条件
        for (ChallengeConditionCompanyCreateReq companyCreateReq : req.getChallengeConditionList()) {
            ChallengeConditionCreateReq conditionCreateReq = new ChallengeConditionCreateReq();
            CopyObjectUtil.copyProperties(companyCreateReq, conditionCreateReq);
            conditionCreateReq.setChallengeId(challenge.getId());
            challengeConditionService.create(conditionCreateReq, operator);

        }
    }

    /**
     * 删除挑战
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        challengeMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改挑战
     *
     * @param req 修改挑战入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ChallengeModifyReq req, User operator) {
        Challenge detail = detail(req.getId());

        if (!EChallengeStatus.ZERO.getCode().equals(detail.getStatus()) && !EChallengeStatus.THREE.getCode().equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待审核或审核失败的可以修改");
        }
        Challenge challenge = EntityUtils.copyData(req, Challenge.class);
        challenge.setCompanyId(detail.getCompanyId());
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyChannelService.checkPermission(operator, req.getChannelId());
            if (!challenge.getCompanyId().equals(operator.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改该挑战");
            }
            if (!EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请创建数字藏品合成");
            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        // 校验是否有修改权限
        checkModifyPermissions(operator, challenge);

//        // 处理emoji
////        challenge.setName(EmojiParser.removeAllEmojis(challenge.getName()));
//        CopyObjectUtil.stringRemoveEmoji(challenge);
//         判断开始时间
        Date date = com.std.common.utils.DateUtil.strToDate(req.getStartTime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2);
        challenge.setStartTime(date);
        if (challenge.getStartTime().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战开始时间早于当前时间");
        }
        // 设置结束时间
        Date endTime = DateUtil.addHours(challenge.getStartTime(), challenge.getEffectiveHours());
        if (endTime.before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战结束时间早于当前时间");
        }
        challenge.setEndTime(endTime);

        // 剩余数量=总数量
        challenge.setAwardRemainQuantity(challenge.getAwardQuantity());

        // 奖励退回
        if (!challenge.getType().equals(detail.getType()) || (challenge.getType().equals(detail.getType()) && !challenge.getAwardRefId()
                .equals(detail.getAwardRefId()))) {

            if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());
                // 检查作品是否属于麦塔
                collectionService.checkOperation(collection);
                if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择作品不可用");
                }
                if (!collection.getAuthorId().equals(detail.getCompanyId())) {
                    Company company = companyService.detail(detail.getCompanyId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励的nft藏品不属于" + company.getName() + "机构");
                }

                // 作品剩余数量分发行方和平台
                if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
                }
            } else if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
                CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
                if (!companyEntity.getCompanyId().equals(detail.getCompanyId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), companyEntity.getName() + "实物不属于该机构");
                }
            }
        } else if (!challenge.getAwardQuantity().equals(detail.getAwardQuantity()) && EChallengeType.CHALLENGE_TYPE_0.getCode()
                .equals(challenge.getType())) {
            Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());

            if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
            }
        }

        challengeMapper.updateByPrimaryKeySelective(challenge);
    }

    /**
     * 麦塔发行方修改挑战
     *
     * @param req 修改挑战入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(ChallengeModifyMetaCompanyReq req, User operator) {
        Challenge detail = detail(req.getId());

        if (!EChallengeStatus.ZERO.getCode().equals(detail.getStatus()) && !EChallengeStatus.THREE.getCode().equals(detail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有待审核或审核失败的可以修改");
        }
        Challenge challenge = EntityUtils.copyData(req, Challenge.class);
        challenge.setCompanyId(detail.getCompanyId());
        // 判断是否拥有该渠道的发布权限
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (!challenge.getCompanyId().equals(operator.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限修改该挑战");
            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限的用户");
        }

        // 校验是否有修改权限
        checkModifyPermissions(operator, challenge);

//        // 处理emoji
////        challenge.setName(EmojiParser.removeAllEmojis(challenge.getName()));
//        CopyObjectUtil.stringRemoveEmoji(challenge);
//         判断开始时间
        Date date = com.std.common.utils.DateUtil.strToDate(req.getStartTime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2);
        challenge.setStartTime(date);
        if (challenge.getStartTime().before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战开始时间早于当前时间");
        }
        // 设置结束时间
        Date endTime = DateUtil.addHours(challenge.getStartTime(), challenge.getEffectiveHours());
        if (endTime.before(new Date())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战结束时间早于当前时间");
        }
        challenge.setEndTime(endTime);

        // 剩余数量=总数量
        challenge.setAwardRemainQuantity(challenge.getAwardQuantity());

        // 奖励退回
        if (!challenge.getType().equals(detail.getType()) || (challenge.getType().equals(detail.getType()) && !challenge.getAwardRefId()
                .equals(detail.getAwardRefId()))) {

            if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());
                // 检查作品是否属于麦塔
                collectionService.checkOperation(collection);
                if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "选择作品不可用");
                }
                if (!collection.getAuthorId().equals(detail.getCompanyId())) {
                    Company company = companyService.detail(detail.getCompanyId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励的nft藏品不属于" + company.getName() + "机构");
                }

                // 作品剩余数量分发行方和平台
                if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
                }
            } else if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
                CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
                if (!companyEntity.getCompanyId().equals(detail.getCompanyId())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), companyEntity.getName() + "实物不属于该机构");
                }
            }
        } else if (!challenge.getAwardQuantity().equals(detail.getAwardQuantity()) && EChallengeType.CHALLENGE_TYPE_0.getCode()
                .equals(challenge.getType())) {
            Collection collection = collectionService.detailForUpdate(challenge.getAwardRefId());

            if (collection.getRemainQuantity() < challenge.getAwardQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励作品剩余数量不足");
            }
        }

        challengeMapper.updateByPrimaryKeySelective(challenge);

        // 删除原有挑战条件
        challengeConditionService.removeByChallenge(challenge.getId());
        // 设置挑战条件
        for (ChallengeConditionCompanyCreateReq companyCreateReq : req.getChallengeConditionList()) {
            ChallengeConditionCreateReq conditionCreateReq = new ChallengeConditionCreateReq();
            CopyObjectUtil.copyProperties(companyCreateReq, conditionCreateReq);
            conditionCreateReq.setChallengeId(challenge.getId());
            challengeConditionService.create(conditionCreateReq, operator);
        }
    }

    @Override
    public void checkModifyPermissions(User operator, Challenge challenge) {
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (!challenge.getCompanyId().equals(operator.getCompanyId())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), challenge.getName() + "无修改权限");
            }
//            if (!EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请创建数字藏品合成");
//            }
        } else if (!EUserKind.SYS.getCode().equals(operator.getKind())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权限用户");
        }
    }

    private void awardBack(String type, Long awardRefId, Integer quantity) {

        // 如果奖励是nft那就需要把退回的数量加回去
        if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(type)) {
            Collection collection = collectionService.detailForUpdate(awardRefId);
            collection.setRemainQuantity(collection.getRemainQuantity() + quantity);
            collectionService.modify(collection);
        }
    }

    /**
     * 详情查询挑战
     *
     * @param id 主键ID
     * @return 挑战对象
     */
    @Override
    public Challenge detail(Long id) {
        Challenge challenge = challengeMapper.selectByPrimaryKey(id);
        if (null == challenge) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return challenge;
    }

    @Override
    public Challenge detailOss(Long id) {
        Challenge challenge = challengeMapper.selectByPrimaryKey(id);
        if (null == challenge) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        challenge.setCompany(companyService.detail(challenge.getCompanyId()));
        challenge.setApplyUserInfo(userService.detail(challenge.getApplyUserId()));
        challenge.setApproveUserInfo(null != challenge.getApproveUserId() ? userService.detail(challenge.getApproveUserId()) : null);
        challenge.setChannelName(channelMerchantService.detail(challenge.getChannelId()).getName());
        // 组装兑换条件
//        List<ChallengeCollection> collectionList = challengeCollectionService.detailByChallengeOss(challenge.getId());
//
//        Map<Long, ChallengeCollection> map = new HashMap<>();
//        for (ChallengeCollection collection : collectionList) {
//            ChallengeCollection challengeCollection = map.get(collection.getCollectionId());
//            if (null == challengeCollection) {
//                map.put(collection.getCollectionId(), collection);
//            } else {
//                challengeCollection.setCollectionQuantity(challengeCollection.getCollectionQuantity() + collection.getCollectionQuantity());
//                map.put(collection.getCollectionId(), challengeCollection);
//            }
//        }
//        collectionList.clear();
//        for (Long aLong : map.keySet()) {
//            ChallengeCollection collection = map.get(aLong);
//            collectionList.add(collection);
//        }
//        challenge.setCollectionList(collectionList);

        // 挑战条件列表
        List<ChallengeCondition> challengeConditionList = challengeConditionService.list(id);
        if (CollectionUtils.isNotEmpty(challengeConditionList)) {
            for (ChallengeCondition challengeCondition : challengeConditionList) {
                List<ChallengeConditionDetail> challengeConditionDetailList = challengeConditionDetailService
                        .list(challengeCondition.getId());
                challengeCondition.setChallengeConditionDetailList(challengeConditionDetailList);
            }
        }
        challenge.setChallengeConditionList(challengeConditionList);

        // 根据挑战类型，查找奖励信息
        if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            Collection collection = collectionService.detail(challenge.getAwardRefId());
            challenge.setAwardName(collection.getName());
            challenge.setAwardPic(collection.getCoverFileUrl());
        } else {
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            challenge.setAwardName(companyEntity.getName());
            challenge.setAwardPic(companyEntity.getImageUrl());
        }
        return challenge;
    }

    @Override
    public Challenge detailForUpdate(Long id) {
        Challenge challenge = challengeMapper.selectForUpdate(id);
        if (null == challenge) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return challenge;
    }

    /**
     * 分页查询挑战
     *
     * @param req 分页查询挑战入参
     * @return 分页挑战对象
     */
    @Override
    public List<Challenge> page(ChallengePageReq req, User operator) {
        Challenge condition = EntityUtils.copyData(req, Challenge.class);
        if (EUserKind.BP.getCode().equals(operator.getKind())) {
            if (null != req.getChannelId()) {
                companyChannelService.checkPermission(operator, req.getChannelId());
            }
            condition.setCompanyId(operator.getCompanyId());
        }
        condition.setOrderBy("t.id desc");

        List<Challenge> challengeList = challengeMapper.selectByCondition(condition);

        for (Challenge challenge : challengeList) {
            challenge.setCompany(companyService.detail(challenge.getCompanyId()));
            challenge.setApplyUserInfo(userService.detailSimple(challenge.getApplyUserId()));
            challenge.setApproveUserInfo(
                    null != challenge.getApproveUserId() ? userService.detailSimple(challenge.getApproveUserId()) : null);

            challenge.setChannelName(channelMerchantService.detail(challenge.getChannelId()).getName());
            if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                Collection collection = collectionService.detail(challenge.getAwardRefId());
                challenge.setAwardName(collection.getName());
                challenge.setAwardPic(collection.getCoverFileUrl());
            } else {
                CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
                challenge.setAwardName(companyEntity.getName());
                challenge.setAwardPic(companyEntity.getImageUrl());
            }
        }
        return challengeList;
    }

    /**
     * 列表查询挑战
     *
     * @param req 列表查询挑战入参
     * @return 列表挑战对象
     */
    @Override
    public List<Challenge> list(ChallengeListReq req) {
        Challenge condition = EntityUtils.copyData(req, Challenge.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Challenge.class));

        List<Challenge> challengeList = challengeMapper.selectByCondition(condition);

        return challengeList;
    }

    @Override
    public List<Challenge> list(Challenge req) {
        return challengeMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询挑战
     *
     * @param id 主键ID
     * @return 挑战对象
     */
    @Override
    public ChallengeDetailRes detailFront(Long id, User operator) {

        ChallengeDetailRes res = challengeMapper.selectByPrimaryKeyFront(id);

        if (null == res) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        // 如果已开始就计算结束倒计时
        long timesStart = ((res.getStartTime().getTime() - System.currentTimeMillis()) / 1000) * 1000;
        res.setStartTimeLong(timesStart);
        if (timesStart <= 0 && EChallengeStartStatus.ZERO.getCode().equals(res.getStartStatus())) {
            res.setStartStatus(EChallengeStartStatus.ONE.getCode());
        }

        long times = ((res.getEndTime().getTime() - System.currentTimeMillis()) / 1000) * 1000;
        res.setEndTimeLong(times);
        if (times <= 0 && EChallengeStartStatus.ONE.getCode().equals(res.getStartStatus())) {
            res.setStartStatus(EChallengeStartStatus.TWO.getCode());
        }

        if (EChallengeStartStatus.THREE.getCode().equals(res.getStartStatus())) {
            res.setStartStatus(EChallengeStartStatus.TWO.getCode());
        }

        // 奖品信息
        ChallengeDetailAwardRes awardRes = res.getAwardRes();
        awardRes.setAwardQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());

        if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(res.getType())) {
            CompanyEntity companyEntity = companyEntityService.detail(awardRes.getId());
            awardRes.setName(companyEntity.getName());
            awardRes.setCoverFileUrl(companyEntity.getImageUrl());
        } else if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(res.getType())) {
            Collection collection = collectionService.detail(awardRes.getId());
            awardRes.setName(collection.getName());
            awardRes.setLevelType(collection.getLevelType());
            awardRes.setCoverFileUrl(collection.getCoverFileUrl());
            awardRes.setFileType(collection.getFileType());

            if (StringUtils.isNotBlank(collection.getTags())) {
                List<String> result = Arrays.asList(collection.getTags().split(","));
                awardRes.setTagList(result);
            }
        }

        res.setAwardRes(awardRes);

        // 挑战状态
        ChallengeOrder challengeOrder = null;
//        List<Long> detailIdList = new ArrayList<>();

        if (null != operator) {
            challengeOrder = challengeOrderService.detail(res.getId(), operator);
            // 获取用户未在该挑战兑换过的所有藏品
//            detailIdList = getDetailIdList(res.getId(), operator);
        }
        if (EChallengeStartStatus.ZERO.getCode().equals(res.getStartStatus())) {
            res.setBuyStatus(EChallengeBuyStatus.THREE.getCode());
            res.setIsFinal(EChallengeIsFinal.ZERO.getCode());
        } else if (null != challengeOrder
                && !EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode().equals(challengeOrder.getStatus())
                && EChallengeStartStatus.TWO.getCode().equals(res.getStartStatus())) {
            res.setBuyStatus(EChallengeBuyStatus.ONE.getCode());
            res.setIsFinal(EChallengeIsFinal.TWO.getCode());
        } else if (null != challengeOrder
                && EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode().equals(challengeOrder.getStatus())
                && EChallengeStartStatus.TWO.getCode().equals(res.getStartStatus())) {
            res.setBuyStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode());
            res.setRemark(challengeOrder.getRemark());
            res.setIsFinal(EChallengeIsFinal.TWO.getCode());
        } else if (EChallengeStartStatus.TWO.getCode().equals(res.getStartStatus())) {
            res.setBuyStatus(EChallengeBuyStatus.FOUR.getCode());
            res.setIsFinal(EChallengeIsFinal.ZERO.getCode());
        } else {
            res.setBuyStatus(EChallengeBuyStatus.ZERO.getCode());

            Integer finalCount = finalCount(res.getId(), operator);
            res.setFinalCount(finalCount);
            res.setIsFinal(EChallengeIsFinal.ZERO.getCode());
            if (finalCount.equals(res.getTotalCollectionNum())) {
                res.setIsFinal(EChallengeIsFinal.ONE.getCode());
            }
            res.setRemainCount(res.getTotalCollectionNum() - res.getFinalCount());
            res.setFinalRate(
                    new BigDecimal(res.getFinalCount()).divide(new BigDecimal(res.getTotalCollectionNum()), 2, BigDecimal.ROUND_DOWN));
        }

        // 兑换条件
//        List<ChallengeExchangeConditionsRes> resList = challengeCollectionService
//                .detail(res.getId(), operator, res.getBuyStatus(), res.getStartStatus(), detailIdList);

        res.setExchangeConditionList(challengeConditionService.selectByChallengeId(res.getId(), operator));
        res.setExchangeSum(res.getExchangeConditionList().size());

        //分享介绍
        res.setIntroduce(configService.getStringValue(SysConstants.SHARE_NOTE));

        return res;
    }

    @Override
    public ChallengeDetailRes detailFrontSimple(Long id) {
        ChallengeDetailRes res = challengeMapper.selectByPrimaryKeyFront(id);

        return res;
    }

    /**
     * 前端分页查询挑战
     *
     * @param req 前端分页查询挑战入参
     * @return 分页挑战对象
     */
    @Override
    public List<ChallengePageRes> pageFront(ChallengePageFrontReq req, User operator, Long channelId) {

        if (null == channelId) {
            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
            channelId = channelMerchant.getId();
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize());

        Challenge condition = EntityUtils.copyData(req, Challenge.class);
        condition.setStatus(EChallengeStatus.TWO.getCode());
        if (StringUtils.isNotBlank(req.getStartStatus()) && EChallengeStartStatus.TWO.getCode().equals(req.getStartStatus())) {
            condition.setStartStatus(null);
            List<String> startStatusList = new ArrayList<>();
            startStatusList.add(EChallengeStartStatus.TWO.getCode());
            startStatusList.add(EChallengeStartStatus.THREE.getCode());
            condition.setStartStatusList(startStatusList);
        }
        condition.setOrderBy("a.startStatus<>'1',a.startStatus asc,a.orderNo desc");
        condition.setChannelId(channelId);
        List<ChallengePageRes> resList = challengeMapper.selectByConditionFront(condition);

        for (ChallengePageRes res : resList) {
            // 如果已开始就计算结束倒计时

            long timesStart = ((res.getStartTime().getTime() - System.currentTimeMillis()) / 1000) * 1000;
            res.setStartTimeLong(timesStart);
            if (timesStart <= 0 && EChallengeStartStatus.ZERO.getCode().equals(res.getStartStatus())) {
                res.setStartStatus(EChallengeStartStatus.ONE.getCode());
            }

            long times = ((res.getEndTime().getTime() - System.currentTimeMillis()) / 1000) * 1000;
            res.setEndTimeLong(times);
            if (times <= 0 && EChallengeStartStatus.ONE.getCode().equals(res.getStartStatus())) {
                res.setStartStatus(EChallengeStartStatus.TWO.getCode());
            }

            if (EChallengeStartStatus.THREE.getCode().equals(res.getStartStatus())) {
                res.setStartStatus(EChallengeStartStatus.TWO.getCode());
            }

            if (null == operator) {
                res.setFinalCount(0);
                res.setFinalRate(BigDecimal.ZERO);
                res.setIsFinal(EChallengeIsFinal.ZERO.getCode());
            } else {
                ChallengeOrder challengeOrder = challengeOrderService.detail(res.getId(), operator);

                if (null != challengeOrder && EChallengeStartStatus.TWO.getCode().equals(res.getStartStatus())) {
                    // 获得已完成的藏品数量
                    res.setFinalCount(res.getTotalCollectionNum());
                    res.setFinalRate(new BigDecimal(100));
                    res.setIsFinal(EChallengeIsFinal.TWO.getCode());
                } else {
                    // 获得拥有的藏品数量
                    Integer finalCount = finalCount(res.getId(), operator);
                    res.setFinalCount(finalCount);
                    res.setIsFinal(EChallengeIsFinal.ZERO.getCode());
                    if (finalCount.equals(res.getTotalCollectionNum())) {
                        res.setIsFinal(EChallengeIsFinal.ONE.getCode());
                    }

                    res.setFinalRate(new BigDecimal(res.getFinalCount())
                            .divide(new BigDecimal(res.getTotalCollectionNum()), 2, BigDecimal.ROUND_DOWN));
                }
            }
        }

        return resList;
    }

    /**
     * 前端分页查询挑战藏品
     *
     * @param req 前端分页查询挑战入参
     * @return 分页挑战对象
     */
    @Override
    public List<ChallengeByCollectionPageRes> collectionPageFront(ChallengeByCollectionPageFrontReq req, User operator) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ChallengeByCollectionPageRes> resList = challengeMapper.selectCollectionByChallengeCondition(req.getAuthorId());

        for (ChallengeByCollectionPageRes res : resList) {
            List<String> result = new ArrayList<>();
            result.addAll(Arrays.asList(res.getTags().split(",")));
            res.setTagList(result);
            createAuthor(res);
            res.setTotalQuantityUnit(SysConstants.COLLECTION_QUANTITY_DEFAULT_UNIT);
        }

        return resList;
    }

    private void createAuthor(ChallengeByCollectionPageRes res) {
        if (null != res.getAuthorId()) {
            List<CompanyInfo> authorList = new ArrayList<>();
            Company company = companyService.detail(res.getAuthorId());
            CompanyInfo companyInfo = new CompanyInfo();
            BeanUtils.copyProperties(company, companyInfo);
            companyInfo.setName(company.getShortName());
            authorList.add(companyInfo);

            res.setAuthorId(company.getId());
            res.setAuthor(company.getShortName());
            res.setAuthorPic(company.getLogo());
            res.setAuthorList(authorList);
        }
    }

    /**
     * 历史挑战
     */
    @Override
    public List<ChallengeHistoryPageRes> pageFrontHistory(ChallengePageHistoryReq req, User operator) {
        ChallengeOrder condition = new ChallengeOrder();
        condition.setUserId(operator.getId());
        if (StringUtils.isNotBlank(req.getType()) && EChallengeHistoryType.CHALLENGE_TYPE_0.getCode().equals(req.getType())) {
            List<String> statusList = new ArrayList<>();
            statusList.add(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_0.getCode());
            statusList.add(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_1.getCode());
            condition.setStatusList(statusList);
        } else if (StringUtils.isNotBlank(req.getType()) && EChallengeHistoryType.CHALLENGE_TYPE_1.getCode().equals(req.getType())) {
            condition.setStatus(EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode());
        }
        condition.setOrderBy("t.id desc");
        List<ChallengeOrder> orderList = challengeOrderService.page(condition);

        List<ChallengeHistoryPageRes> resList = new ArrayList<>();
        for (ChallengeOrder order : orderList) {
            ChallengeHistoryPageRes res = new ChallengeHistoryPageRes();
            ChallengeDetailRes detailRes = challengeMapper.selectByPrimaryKeyFront(order.getChallengeId());
            BeanUtils.copyProperties(detailRes, res);

            if (EChallengeStartStatus.ONE.getCode().equals(res.getStartStatus())) {
                long times = ((res.getEndTime().getTime() - System.currentTimeMillis()) / 1000) * 1000;
                res.setEndTimeLong(times);
                if (times < 0) {
                    res.setStartStatus(EChallengeStartStatus.TWO.getCode());
                }
            }

            if (EChallengeStartStatus.THREE.getCode().equals(res.getStartStatus())) {
                res.setStartStatus(EChallengeStartStatus.TWO.getCode());
            }

            if (EChallengeOrderStatus.CHALLENGE_ORDER_STATUS_2.getCode().equals(order.getStatus())) {
                res.setStatus(EBoolean.YES.getCode());
            } else {
                res.setStatus(EBoolean.NO.getCode());
            }
            res.setId(order.getChallengeId());
            resList.add(res);
        }

        return PageInfoUtil.listToPage(orderList, resList);
    }

    private Integer finalCount(Long challengeId, User operator) {
        if (null == operator) {
            return 0;
        }

        Integer finalCount = 0;
        List<ChallengeCondition> challengeConditionList = challengeConditionService.list(challengeId);
        if (CollectionUtils.isNotEmpty(challengeConditionList)) {
            for (ChallengeCondition challengeCondition : challengeConditionList) {
                List<Long> collectionIdList = challengeConditionDetailService
                        .selectDistinctCollectionIdList(challengeCondition.getId());

                Integer totalMyCount = collectionDetailService.detailListByCount(collectionIdList, operator.getId(),
                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challengeId, challengeCondition.getId());
                if (totalMyCount > challengeCondition.getQuantity()) {
                    totalMyCount = challengeCondition.getQuantity();
                }
                finalCount = finalCount + totalMyCount;
            }
        }

        return finalCount;
    }

    private List<Long> getDetailIdList(Long challengeId, User operator) {
        List<CollectionDetail> detailList = collectionDetailService
                .detailList(null, operator.getId(), ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challengeId);
        List<Long> detailIdList = detailList.stream().map(x -> {
            return x.getCollectionId();
        }).collect(Collectors.toList());
        return detailIdList;
    }

    private Integer finalCountOld(Long challengeId, User operator) {
        if (null == operator) {
            return 0;
        }
        Integer finalCount = collectionDetailService.detailChallengeFinalCount(challengeId, operator.getId());

        return finalCount;
    }

    /**
     * 前端列表查询挑战
     *
     * @param req 前端列表查询挑战入参
     * @return 列表挑战对象
     */
    @Override
    public List<ChallengeListRes> listFront(ChallengeListFrontReq req) {
        Challenge condition = EntityUtils.copyData(req, Challenge.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), Challenge.class));

        List<Challenge> challengeList = challengeMapper.selectByCondition(condition);

        List<ChallengeListRes> resList = challengeList.stream().map((entity) -> {
            ChallengeListRes res = new ChallengeListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    /**
     * 提起审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void startReview(ChallengeStartReviewReq request, User operator) {
        for (Long id : request.getIdList()) {
            Challenge challenge = detailForUpdate(id);

            if (!EChallengeStatus.ZERO.getCode().equals(challenge.getStatus()) && !EChallengeStatus.THREE.getCode()
                    .equals(challenge.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), challenge.getName() + "不是待审核或审核失败的记录");
            }

            // 校验是否有修改权限
            checkModifyPermissions(operator, challenge);
            //查询条件，将所有的数量加起来
            challenge.setTotalCollectionNum(challengeConditionService.selectTotalCount(id));
            if (challenge.getTotalCollectionNum() <= 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战条件未设置");
            }

            challenge.setStatus(EChallengeStatus.ONE.getCode());
            challenge.setUpdater(operator.getId());
            challenge.setUpdateDatetime(new Date());
            challengeMapper.updateByPrimaryKeySelective(challenge);
        }
    }

    /**
     * 撤回申请
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void undoReview(ChallengeStartReviewReq request, User operator) {
        for (Long id : request.getIdList()) {
            Challenge challenge = detailForUpdate(id);

            if (!EChallengeStatus.ONE.getCode().equals(challenge.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), challenge.getName() + "不是审核中的记录");
            }

            // 校验是否有修改权限
            checkModifyPermissions(operator, challenge);

            challenge.setStatus(EChallengeStatus.ZERO.getCode());
            challenge.setUpdater(operator.getId());
            challenge.setUpdateDatetime(new Date());
            challengeMapper.updateByPrimaryKeySelective(challenge);
        }
    }

    /**
     * 批量审核
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAudit(ChallengeBatchAuditReq request, User operator) {

        Map<Long, Collection> map = new HashMap<>();
        List<Challenge> challengeList = new ArrayList<>();
        for (Long id : request.getIdList()) {
            Challenge challenge = detail(id);
            challengeList.add(challenge);
            // 校验是否有修改权限
            checkModifyPermissions(operator, challenge);

            if (!EChallengeStatus.ONE.getCode().equals(challenge.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有审核中的的挑战可以审核");
            }

            //todo 挑战条件无报错
            challenge.setStatus(request.getStatus());

            // 审核不通过把作品数量退回
            if (EChallengeStatus.THREE.getCode().equals(request.getStatus())) {
//                Collection collection = map.get(challenge.getAwardRefId());
//
//                if (null == collection) {
//                    collection = collectionService.detailForUpdate(challenge.getAwardRefId());
//                }
//
//                collection.setRemainQuantity(collection.getRemainQuantity() + challenge.getAwardQuantity());
//
//                map.put(challenge.getAwardRefId(), collection);

                ApproveRecord approveRecord = new ApproveRecord();
                approveRecord.setRefType(EApproveRecordRefType.APPROVE_RECORD_REFTYPE_2.getCode());
                approveRecord.setRefId(id);
                approveRecord.setCreater(operator.getId());
                approveRecord.setCreaterName(operator.getLoginName());
                approveRecord.setCreateDatetime(new Date());

                if (StringUtils.isBlank(request.getApproveNote())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核意见不能为空");
                }
                approveRecord.setOpinion(request.getApproveNote());
                approveRecord.setHistoryData(challenge.toString());
                approveRecordService.create(approveRecord);
            } else if (EChallengeStatus.TWO.getCode().equals(request.getStatus())) {

                if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                    Collection collection = map.get(challenge.getAwardRefId());

                    if (null == collection) {
                        collection = collectionService.detailForUpdate(challenge.getAwardRefId());
                    }

                    if (collection.getRemainQuantity() < challenge.getAwardRemainQuantity()) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战奖励" + collection.getName() + "剩余数量不足");
                    }

                    collection.setRemainQuantity(collection.getRemainQuantity() - challenge.getAwardRemainQuantity());
                    map.put(challenge.getAwardRefId(), collection);
                    // 挑战上架给作品分配tokenId
//                    Collection collection = collectionService.detailSimple(challenge.getAwardRefId());
                    contractTokenService.distributionToken(collection, challenge.getAwardQuantity(),
                            ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_8.getCode(), challenge.getId(),
                            EContractTokenStatus.CONTRACT_TOKEN_STATUS_3, null);
                }
                //查询条件，将所有的数量加起来
                challenge.setTotalCollectionNum(challengeConditionService.selectTotalCount(id));
                if (challenge.getTotalCollectionNum() <= 0) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战条件未设置");
                }
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "审核状态错误");
            }

            // 获取最大的序号
            Integer maxOrderNo = challengeMapper.selectMaxOrderNo();

            challenge.setApproveUserId(operator.getId());
            challenge.setApproveNote(request.getApproveNote());
            challenge.setApproveDatetime(new Date());
            challenge.setOrderNo(maxOrderNo + 1);
            challengeMapper.updateByPrimaryKeySelective(challenge);
        }

        for (Long id : map.keySet()) {
            Collection collection = map.get(id);

            collectionService.modify(collection);
        }

        for (Challenge challenge : challengeList) {
            String title = null;
            String content = null;
            if (EChallengeStatus.TWO.getCode().equals(request.getStatus())) {
                if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                    title = "藏品兑换审核通过";
                    content = "您发起的" + challenge.getName() + "藏品兑换申请已通过";
                } else {
                    title = "实物奖品兑付审核通过";
                    content = "您发起的" + challenge.getName() + "实物奖品兑付申请已通过";
                }


            } else {
                if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
                    title = "藏品兑换审核未通过";
                    content = "您发起的" + challenge.getName() + "藏品兑换申请已被打回,原因：" + request.getApproveNote();
                } else {
                    title = "实物奖品兑付审核未通过";
                    content = "您发起的" + challenge.getName() + "实物奖品兑付申请已被打回,原因：" + request.getApproveNote();
                }

            }
            smsService.sendMyMsg(ESmsTarget.COMPANY.getCode(), challenge.getCompanyId(), title, content,
                    ESmsRefType.COMPANY_SMS.getCode(),
                    ESmsRefType.COMPANY_SMS.getValue());

            User user = userService.detailBrief(challenge.getCompanyId());
            smsOutService.sendSmsOut(user.getMobile(), content, null);
        }

    }

    /**
     * 下架挑战
     */
    @Override
    public void batchDown(ChallengeBatchDownReq request, User operator) {

        Map<Long, Collection> map = new HashMap<>();
        for (Long id : request.getIdList()) {
            Challenge challenge = detailForUpdate(id);
            // 校验是否有修改权限
            checkModifyPermissions(operator, challenge);

            if (!EChallengeStatus.TWO.getCode().equals(challenge.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有审核通过的的挑战可以下架");
            }
            challenge.setStatus(EChallengeStatus.FOUR.getCode());
            challenge.setStartStatus(EChallengeStartStatus.THREE.getCode());

            if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType()) && challenge.getAwardRemainQuantity() > 0) {
                // 下架就把作品数量退回
                Collection collection = map.get(challenge.getAwardRefId());
                if (null == collection) {
                    collection = collectionService.detailForUpdate(challenge.getAwardRefId());
                }
                collection.setRemainQuantity(collection.getRemainQuantity() + challenge.getAwardRemainQuantity());
                map.put(collection.getId(), collection);

                // 挑战下架，剩余数量加回作品，废弃剩余的tokenId
                contractTokenService.discardToken(collection.getId()
                        , challenge.getId()
                        , ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_8.getCode());

                // 挑战下架把剩余的奖励，变成藏品挂在机构名下
//                Company company = companyService.detail(challenge.getCompanyId());
//                collectionDetailService.doGenerateDetailCompany(company, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_8
//                                .getCode(), ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_7
//                                .getCode(), challenge.getId(), collection.getId(), BigDecimal.ZERO, challenge.getAwardRemainQuantity()
//                        , ECollectionDetailBuyChannel.CHALLENGE.getCode(), collection.getLockTime());
            }

            challenge.setActualEndTime(new Date());

            challenge.setUpdater(operator.getId());
            challenge.setUpdateDatetime(new Date());
            challengeMapper.updateByPrimaryKeySelective(challenge);
        }

        // 修改藏品数量
        for (Long collectionId : map.keySet()) {
            Collection collection = map.get(collectionId);
            collectionService.modify(collection);
        }
    }

    /**
     * 挑战完成兑换功能
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long challengeFinal(ChallengeFinalReq request, User operator) {
//         校验验证码 -- Robin
//        String uuid = request.getUuid();
//        String verifyCode = request.getVerifyCode();
//        if (StringUtils.isBlank(uuid)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写UUID");
//        }
//        if (StringUtils.isBlank(verifyCode)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写验证码");
//        }
//        String key = String.format(RedisKeyList.MT_CAPTCHA_KEY, uuid);
//        String code = null;
//        if (redisUtil.hasKey(key)) {
//            code = redisUtil.get(key).toString();
//        }
//        if (StringUtils.isBlank(code)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "验证码已失效，请刷新后重试");
//        }
//        if (!verifyCode.equals(code)) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "验证码不匹配，请重试");
//        }
//        redisUtil.del(key);

        Challenge challenge = detail(request.getId());
        // 判断挑战是否结束
        if (challenge.getAwardRemainQuantity() < 1) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励已被领完，挑战已结束");
        }
        if (!EChallengeStatus.TWO.getCode().equals(challenge.getStatus()) || !EChallengeStartStatus.ONE.getCode()
                .equals(challenge.getStartStatus()) || challenge.getEndTime().before(new Date()) || 0 >= challenge
                .getAwardRemainQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战已结束");
        }
        // 实物奖品需要用户的地址
        if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType()) && null == request.getAddressId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请输入收货地址");
        } else if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            // 检查用户持有该藏品数量是否上限
            Collection collection = collectionService.detailSimple(challenge.getAwardRefId());
            boolean flag = collectionDetailService
                    .checkCollectionCount(collection.getId(), collection.getSingleMaxQuantity(), 1, operator);
            if (flag) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您拥有的" + collection.getName() + "数量已达上限");
            }
        }

//        List<ChallengeCollection> collectionList = challengeCollectionService.detailListByChallengeId(challenge.getId());
//
        // 获取用户所有的未参与过该挑战的藏品
//        List<CollectionDetail> collectionDetailList = collectionDetailService
//                .detailList(null, operator.getId(),
//                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challenge.getId());
//        Company company = companyService.detail(challenge.getCompanyId());
//        List<ChallengeOrderDetail> orderDetailList = new ArrayList<>();
//        // 要修改流通总量的作品
//        List<Long> collectionIdList = new ArrayList<>();
//
//        for (ChallengeCollection challengeCollection : collectionList) {
//
//            Boolean haveFlag = false;
//            List<CollectionDetail> collectionDetailNewList = new ArrayList<>();
//            // 遍历用户的所有藏品，查看里面是否含有需要的藏品
//
//            int i = challengeCollection.getCollectionQuantity();
//            for (CollectionDetail detail : collectionDetailList) {
//
//                // 如果找到符合的藏品，进行兑换操作
//                if (detail.getCollectionId().equals(challengeCollection.getCollectionId()) && i > 0) {
//                    // 重新查找一遍，并增加航行锁
//                    CollectionDetail collectionDetail = collectionDetailService.detailForUpdate(detail.getId());
//
//                    // 以防万一再查询并校验一遍
//                    if (!collectionDetail.getOwnerType().equals(EBoolean.NO.getCode())
//                            || !collectionDetail.getOwnerId().equals(operator.getId())
//                            || !ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前兑换人数较多，请稍后再试");
//                    }
//
//                    // 消耗的就把藏品给到机构
//                    if (challengeCollection.getBackType().equals(EChallengeCollectionBackType.CHALLENGE_TYPE_0.getCode())) {
//                        // 把用户的藏品给到机构
//                        // 重新记录流转记录，并修改之前的所属权限
//                        collectionDetailRecordService
//                                .modifyOwnershipCompany(collectionDetail, collectionDetail.getOwnerId(), company.getId(),
//                                        ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_8.getCode(),
//                                        ECollectionDetailBuyChannel.CHALLENGE.getCode(), challenge.getId());
//
//                        if (!collectionIdList.contains(collectionDetail.getCollectionId())) {
//                            collectionIdList.add(collectionDetail.getCollectionId());
//                        }
//
//                    } else {
//                        // 赋能的就增加赋能次数，藏品所属权不修改
//                        collectionDetailService.addChallengeNumber(collectionDetail.getId());
//                    }
//                    ChallengeOrderDetail orderDetail = new ChallengeOrderDetail();
//                    orderDetail.setCollectionDetailId(detail.getId());
//                    orderDetail.setBackType(challengeCollection.getBackType());
//                    orderDetailList.add(orderDetail);
//                    i--;
//                } else {
//                    collectionDetailNewList.add(detail);
//                }
//
//                if (i == 0) {
//                    haveFlag = true;
//                }
//            }
//
//            if (!haveFlag) {
//                Collection collection = collectionService.detailSimple(challengeCollection.getCollectionId());
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "兑换缺少" + collection.getName() + "藏品");
//            }
//            collectionDetailList = collectionDetailNewList;
//        }

        // 要修改流通总量的作品
        List<Long> collectionIdList = new ArrayList<>();

        // 获取用户所有的未参与过该挑战的藏品
        List<ChallengeOrderDetail> orderDetailList = new ArrayList<>();
        Company company = companyService.detail(challenge.getCompanyId());

        for (ChallengeConditionDetailListReq conditionReq : request.getExchangeConditionList()) {
            //每个兑换条件数量进行判断是否满足
            Integer totalQuantity = 0;
            ChallengeCondition challengeCondition = challengeConditionService.detail(conditionReq.getConditionId());

            List<Long> collectionIdsList = challengeConditionDetailService.selectDistinctCollectionIdList(challengeCondition.getId());
            List<CollectionDetail> collectionDetailList = collectionDetailService.detailList(collectionIdsList, operator.getId(),
                    ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challenge.getId(), challengeCondition.getId(), null);
            if (collectionDetailList.size() < challengeCondition.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "[" + challengeCondition.getName() + "]系列的总可用数量不足");
            }

            //遍历具体条件下的藏品列表，每个藏品和数量进行判断
            for (ChallengeConditionDetailChildReq conditionDetailReq : conditionReq.getCollectionList()) {
                List<Long> childCollectionIdsList = new ArrayList<>();
                childCollectionIdsList.add(conditionDetailReq.getCollectionId());

                ChallengeConditionDetail challengeConditionDetail = challengeConditionDetailService
                        .detail(conditionReq.getConditionId(), conditionDetailReq.getCollectionId());
                List<CollectionDetail> childCollectionDetailList = getCollectionDetails(operator, challenge, challengeConditionDetail,
                        challengeCondition, childCollectionIdsList);
                if (childCollectionDetailList.size() < conditionDetailReq.getQuantity()) {
                    Collection collection = collectionService.detail(conditionDetailReq.getCollectionId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "可用藏品数量不足");
                }

                //每个作品的数量统计，等于要兑换的总数量时跳出
                int collectionQuantity = 0;
                for (CollectionDetail collectionDetail : childCollectionDetailList) {
                    collectionDetail = collectionDetailService.detailForUpdate(collectionDetail.getId());
                    if (!collectionDetail.getOwnerType().equals(EBoolean.NO.getCode())
                            || !collectionDetail.getOwnerId().equals(operator.getId())
                            || !ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前兑换人数较多，请稍后再试");
                    }

                    // 消耗的就把藏品给到机构
                    if (challengeCondition.getBackType().equals(EChallengeCollectionBackType.CHALLENGE_TYPE_0.getCode())) {
                        // 把用户的藏品给到机构
                        // 重新记录流转记录，并修改之前的所属权限
                        collectionDetailRecordService
                                .modifyOwnershipCompany(collectionDetail, collectionDetail.getOwnerId(), company.getId(),
                                        ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_8.getCode(),
                                        ECollectionDetailBuyChannel.CHALLENGE.getCode(), challenge.getId(),
                                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());

                        if (!collectionIdList.contains(collectionDetail.getCollectionId())) {
                            collectionIdList.add(collectionDetail.getCollectionId());
                        }
                    } else {
                        // 赋能的就增加赋能次数，藏品所属权不修改
                        collectionDetailService.addChallengeNumber(collectionDetail.getId());
                    }

                    ChallengeOrderDetail orderDetail = new ChallengeOrderDetail();
                    orderDetail.setLockCondition(challengeConditionDetail.getLockCondition());
                    orderDetail.setConditionId(challengeCondition.getId());
                    orderDetail.setCollectionDetailId(collectionDetail.getId());
                    orderDetail.setBackType(challengeCondition.getBackType());
                    orderDetailList.add(orderDetail);

                    totalQuantity++;

                    //当藏品遍历累计达到数量之后，就跳出
                    collectionQuantity++;
                    if (collectionQuantity >= conditionDetailReq.getQuantity()) {
                        break;
                    }
                }
            }

            //如果数量不符合，报错
            if (!totalQuantity.equals(challengeCondition.getQuantity())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), challengeCondition.getName() + "选择数量不够");
            }
        }

        // 奖品给到用户
        Long aLong = challengeOrderService.create(challenge, operator, request.getAddressId());

        challengeOrderDetailService.create(aLong, challenge.getId(), operator, orderDetailList);

        int i = challengeMapper.updateAwardRemainQuantity(challenge.getId());

        if (i <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "兑换奖励不足");
        }

        // 更新消耗的藏品所对应的作品的流通总量
        collectionService.refreshMarketQuantity(collectionIdList);

        return aLong;
    }

    private List<CollectionDetail> getCollectionDetails(User operator, Challenge challenge,
            ChallengeConditionDetail challengeConditionDetail,
            ChallengeCondition challengeCondition, List<Long> childCollectionIdsList) {
        Integer lockTime = null;
        if (EChallengeCollectionLockCondition.UNLOCK.getCode().equals(challengeConditionDetail.getLockCondition())) {
            lockTime = 0;
        } else if (EChallengeCollectionLockCondition.LOCK.getCode().equals(challengeConditionDetail.getLockCondition())) {
            lockTime = -1;
        }

        //判断增加藏品参与条件
        List<CollectionDetail> childCollectionDetailList = collectionDetailService
                .detailList(childCollectionIdsList, operator.getId(),
                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challenge.getId(),
                        challengeCondition.getId(), lockTime);
        return childCollectionDetailList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long challengeFinalH5(ChallengeFinalH5Req request, Long channelId, User operator) {

        Challenge challenge = detail(request.getId());

        if (!challenge.getChannelId().equals(channelId)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道错误");
        }

        // 判断挑战是否结束
        if (challenge.getAwardRemainQuantity() < 1) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "奖励已被领完，挑战已结束");
        }
        if (!EChallengeStatus.TWO.getCode().equals(challenge.getStatus()) || !EChallengeStartStatus.ONE.getCode()
                .equals(challenge.getStartStatus()) || challenge.getEndTime().before(new Date()) || 0 >= challenge
                .getAwardRemainQuantity()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "挑战已结束");
        }
        // 实物奖品需要用户的地址
        if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType()) && null == request.getAddressId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请输入收货地址");
        } else if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            // 检查用户持有该藏品数量是否上限
            Collection collection = collectionService.detailSimple(challenge.getAwardRefId());
            boolean flag = collectionDetailService
                    .checkCollectionCount(collection.getId(), collection.getSingleMaxQuantity(), 1, operator);
            if (flag) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "您拥有的" + collection.getName() + "数量已达上限");
            }
        }

        // 要修改流通总量的作品
        List<Long> collectionIdList = new ArrayList<>();

        // 获取用户所有的未参与过该挑战的藏品
        List<ChallengeOrderDetail> orderDetailList = new ArrayList<>();
        Company company = companyService.detail(challenge.getCompanyId());

        for (ChallengeConditionDetailListReq conditionReq : request.getExchangeConditionList()) {
            //每个兑换条件数量进行判断是否满足
            Integer totalQuantity = 0;
            ChallengeCondition challengeCondition = challengeConditionService.detail(conditionReq.getConditionId());
            List<Long> collectionIdsList = challengeConditionDetailService.selectDistinctCollectionIdList(challengeCondition.getId());
            List<CollectionDetail> collectionDetailList = collectionDetailService.detailList(collectionIdsList, operator.getId(),
                    ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode(), challenge.getId(), challengeCondition.getId(), null);
            if (collectionDetailList.size() < challengeCondition.getQuantity()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "[" + challengeCondition.getName() + "]系列的总可用数量不足");
            }

            //遍历具体条件下的藏品列表，每个藏品和数量进行判断
            for (ChallengeConditionDetailChildReq conditionDetailReq : conditionReq.getCollectionList()) {
                List<Long> childCollectionIdsList = new ArrayList<>();
                childCollectionIdsList.add(conditionDetailReq.getCollectionId());

                ChallengeConditionDetail challengeConditionDetail = challengeConditionDetailService
                        .detail(conditionReq.getConditionId(), conditionDetailReq.getCollectionId());
                List<CollectionDetail> childCollectionDetailList = getCollectionDetails(operator, challenge, challengeConditionDetail,
                        challengeCondition, childCollectionIdsList);

                if (childCollectionDetailList.size() < conditionDetailReq.getQuantity()) {
                    Collection collection = collectionService.detail(conditionDetailReq.getCollectionId());
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "可用藏品数量不足");
                }

                //每个作品的数量统计，等于要兑换的总数量时跳出
                int collectionQuantity = 0;
                for (CollectionDetail collectionDetail : childCollectionDetailList) {
                    collectionDetail = collectionDetailService.detailForUpdate(collectionDetail.getId());
                    if (!collectionDetail.getOwnerType().equals(EBoolean.NO.getCode())
                            || !collectionDetail.getOwnerId().equals(operator.getId())
                            || !ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前兑换人数较多，请稍后再试");
                    }

                    // 消耗的就把藏品给到机构
                    if (challengeCondition.getBackType().equals(EChallengeCollectionBackType.CHALLENGE_TYPE_0.getCode())) {
                        // 把用户的藏品给到机构
                        // 重新记录流转记录，并修改之前的所属权限
                        collectionDetailRecordService
                                .modifyOwnershipCompany(collectionDetail, collectionDetail.getOwnerId(), company.getId(),
                                        ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_8.getCode(),
                                        ECollectionDetailBuyChannel.CHALLENGE.getCode(), challenge.getId(),
                                        ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());

                        if (!collectionIdList.contains(collectionDetail.getCollectionId())) {
                            collectionIdList.add(collectionDetail.getCollectionId());
                        }
                    } else {
                        // 赋能的就增加赋能次数，藏品所属权不修改
                        collectionDetailService.addChallengeNumber(collectionDetail.getId());
                    }

                    ChallengeOrderDetail orderDetail = new ChallengeOrderDetail();
                    orderDetail.setLockCondition(challengeConditionDetail.getLockCondition());
                    orderDetail.setConditionId(challengeCondition.getId());
                    orderDetail.setCollectionDetailId(collectionDetail.getId());
                    orderDetail.setBackType(challengeCondition.getBackType());
                    orderDetailList.add(orderDetail);

                    totalQuantity++;

                    //当藏品遍历累计达到数量之后，就跳出
                    collectionQuantity++;
                    if (collectionQuantity >= conditionDetailReq.getQuantity()) {
                        break;
                    }
                }
            }

            //如果数量不符合，报错
            if (!totalQuantity.equals(challengeCondition.getQuantity())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), challengeCondition.getName() + "选择数量不够");
            }
        }

        // 奖品给到用户
        Long aLong = challengeOrderService.create(challenge, operator, request.getAddressId());

        challengeOrderDetailService.create(aLong, challenge.getId(), operator, orderDetailList);

        int i = challengeMapper.updateAwardRemainQuantity(challenge.getId());

        if (i <= 0) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "兑换奖励不足");
        }

        // 更新消耗的藏品所对应的作品的流通总量
        collectionService.refreshMarketQuantity(collectionIdList);

        return aLong;
    }

    @Override
    public void modifyOrderNo(ChallengeOrderNoReq challengeOrderNoReq, User operator) {
        Challenge detail = detail(challengeOrderNoReq.getId());
        // 校验是否有修改权限
        checkModifyPermissions(operator, detail);
        Challenge challenge = new Challenge();
        challenge.setId(challengeOrderNoReq.getId());
        challenge.setOrderNo(challengeOrderNoReq.getOrderNo());
        challenge.setUpdater(operator.getId());
        challenge.setUpdateDatetime(new Date());

        challengeMapper.updateByPrimaryKeySelective(challenge);
    }

    @Override
    public void modifyTotalCollectionNum(Long challengeId, Integer totalCollectionNum, User operator) {
        Challenge challenge = challengeMapper.selectForUpdate(challengeId);
        challenge.setTotalCollectionNum(totalCollectionNum);
        challenge.setUpdater(operator.getId());
        challenge.setUpdateDatetime(new Date());
        challengeMapper.updateByPrimaryKeySelective(challenge);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendAward(User operator, Long awardRefId, Long orderId) {
        lockService.create(ELockBizType.CHALLENGE.getCode(), orderId.toString());

        ChallengeOrder challengeOrder = challengeOrderService.detailForUpdate(orderId);
        if (!EChallengeDistributionType.CHALLENGE_TYPE_1.getCode().equals(challengeOrder.getIsDistribution())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "订单已分配");
        }
        Collection collection = collectionService.detail(awardRefId);
        ECollectionDetailSource source = null;
        if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_0;
        } else if (ECollectionCategory.DERIVATIVE.getCode().equals(collection.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_1;
        } else if (ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
            source = ECollectionDetailSource.CHANGE_TYPE_STATUS_1;
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品分类不支持");
        }

        collectionDetailService.doGenerateDetail(operator, ECollectionDetailRefType.COLLECTION_DETAIL_REF_TYPE_8
                        .getCode(), orderId, collection, BigDecimal.ZERO, 1, ECollectionDetailBuyChannel.CHALLENGE.getCode(),
                collection.getLockTime(), collection.getTransformLimitTime(), source.getCode(), null);
    }

    /**
     * 查看实物奖励
     */
    @Override
    public ChallengeDetailEntityRes detailEntity(Long id, User operator) {
        ChallengeDetailEntityRes res = new ChallengeDetailEntityRes();
        Challenge challenge = detail(id);
        if (!EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该挑战奖励不是实物");
        }
        ChallengeOrder challengeOrder = challengeOrderService.detail(id, operator);
        if (null == challengeOrder) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "还没有兑换订单");
        }
        BeanUtils.copyProperties(challengeOrder, res);

        res.setId(challenge.getId());
        Express express = expressService.detailByKdnCode(challengeOrder.getLogisticsCompany());

        if (null == express) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "快递物流错误");
        }
        res.setLogisticsCompany(express.getName());
        res.setName(challengeOrder.getAwardName());
        res.setCoverPicUrl(challengeOrder.getAwardPic());
        return res;
    }

    @Override
    public void challengeChange() {
        challengeMapper.challengeStart(new Date());
        challengeMapper.challengeEnd(new Date());
    }

    /**
     * 奖励详情
     */
    @Override
    public ChallengeAwardDeatilRes awardDetail(Long id) {
        ChallengeAwardDeatilRes res = new ChallengeAwardDeatilRes();

        Challenge challenge = detail(id);
        res.setAwardType(challenge.getType());
        if (EChallengeType.CHALLENGE_TYPE_0.getCode().equals(challenge.getType())) {
            Collection collection = collectionService.detail(challenge.getAwardRefId());

            BeanUtils.copyProperties(collection, res);

            Company company = companyService.detail(collection.getAuthorId());
            res.setAuthor(company.getShortName());
            res.setAuthorPic(company.getLogo());
            res.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());

            if (StringUtils.isNotBlank(res.getTags())) {
                List<String> result = Arrays.asList(res.getTags().split(","));
                res.setTagList(result);
            }

            // 展示文件
            CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes = new CollectionPeriodCollectionPageRes();
            BeanUtils.copyProperties(collection, collectionPeriodCollectionPageRes);
            collectionPeriodCollectionPageRes.setFileList(collection.getFileList());
            collectionPeriodCollectionPageRes.setFileType(collection.getFileType());

            List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
            sellCollectionList.add(collectionPeriodCollectionPageRes);
            res.setSellCollectionList(sellCollectionList);

            //链上信息
            CollectionContractTokenRes contractTokenRes = contractTokenService.collectionToken(challenge.getAwardRefId());
            res.setContractToken(contractTokenRes);
            List<CollectionContractTokenRes> contractTokenResList = new ArrayList<>();
            contractTokenResList.add(res.getContractToken());
            res.setContractTokenList(contractTokenResList);

            res.setTotalQuantity(challenge.getAwardQuantity());
            res.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
            res.setBuyNotice(configService.getStringValue(SysConstants.COLLECTION_PERIOD_BUY_NOTE));

//            if (null != res.getLockTime() && -1 != res.getLockTime()) {
//                res.setLockDay(res.getLockTime() / 24L);
//                res.setLockHour(res.getLockTime() % 24L);
//            }
            res.setLockTime(collection.getTransformLimitTime());

            if (null != collection.getTransformLimitTime() && -1 != collection.getTransformLimitTime()) {
                res.setLockDay(collection.getTransformLimitTime() / 24L);
                res.setLockHour(collection.getTransformLimitTime() % 24L);
            }

            //分享介绍
            res.setIntroduce(configService.getStringValue(SysConstants.SHARE_NOTE));

            //权益是列表
            if (ECollectionRightType.COLLECTION_PAYTYPE_3.getCode().equals(collection.getRightType())) {
                //获取权限列表
                CollectionRightsDetailListFrontReq rightsDetailListFrontReq = new CollectionRightsDetailListFrontReq();
                rightsDetailListFrontReq.setCollectionId(collection.getId());
                res.setRightList(collectionRightsDetailService.listFront(rightsDetailListFrontReq, null));
            }
            res.setLevelType(collection.getLevelType());

            //出品方名称显示
            if (null != collection.getProducedId()) {
                res.setProducedId(collection.getProducedId());
                Produced produced = producedService.detail(collection.getProducedId());
                res.setProducedName(produced.getName());
                res.setProducedPic(produced.getPic());
            }
        } else if (EChallengeType.CHALLENGE_TYPE_1.getCode().equals(challenge.getType())) {
            CompanyEntity companyEntity = companyEntityService.detail(challenge.getAwardRefId());
            res.setCoverFileUrl(companyEntity.getImageUrl());
            res.setName(companyEntity.getName());

            Company company = companyService.detail(companyEntity.getCompanyId());
            res.setAuthorId(company.getId());
            res.setAuthor(company.getShortName());
            res.setAuthorPic(company.getLogo());
            res.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());

            res.setTotalQuantity(challenge.getAwardQuantity());
            res.setTotalQuantityUnit(ECollectionPeriodCategory.DERIVATIVE.getUnit());
            res.setFileType(ECollectionFileType.COLLECTION_FILETYPE_0.getCode());
            res.setIntroduce(companyEntity.getContent());

            CollectionPeriodCollectionPageRes collectionPeriodCollectionPageRes = new CollectionPeriodCollectionPageRes();
            collectionPeriodCollectionPageRes.setName(companyEntity.getName());
            collectionPeriodCollectionPageRes.setFileType(ECollectionFileType.COLLECTION_FILETYPE_0.getCode());
            collectionPeriodCollectionPageRes.setFileUrl(companyEntity.getImageUrl());
            if (StringUtils.isNotBlank(companyEntity.getImageUrl())) {
                String concat = "[{\"address\":\"".concat(companyEntity.getImageUrl()).concat("\",\"type\":\"0\"}]");
                List<UploadFile> uploadFiles = JSONArray.parseArray(concat,
                        UploadFile.class);
                collectionPeriodCollectionPageRes.setFileList(uploadFiles);
            }
            List<CollectionPeriodCollectionPageRes> sellCollectionList = new ArrayList<>();
            sellCollectionList.add(collectionPeriodCollectionPageRes);
            res.setSellCollectionList(sellCollectionList);

            List<String> result = Arrays.asList("实物奖品".split(","));
            res.setTagList(result);

            res.setContentType(ECollectionContentType.COLLECTION_CONTENT_TYPE_1.getCode());
            res.setContent(companyEntity.getContent());
        }

        return res;
    }

    @Override
    public void doChallengeFinal() {
        Challenge challenge = new Challenge();
        challenge.setActualEndTime(new Date());
        challengeMapper.doChallengeFinal(challenge);
    }

    @Override
    public List<ChallengeConditionDetailFrontRes> selectMyOwnerConditionList(User operator,
            ChallengeConditionListFrontReq request) {
        ChallengeConditionDetail condition = new ChallengeConditionDetail();
        condition.setUserId(operator.getId());

        // 暂时不做渠道区分
//        if (null != channelId) {
//            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
//            if (channelId.equals(channelMerchant.getId())) {
//                channelId = null;
//            }
//        }
        if (EBoolean.YES.getCode().equals(request.getOwnerFlag())) {
            return challengeConditionDetailService.selectMyOwnerQuantityList(operator, request.getConditionId());
        } else if (EBoolean.NO.getCode().equals(request.getOwnerFlag())) {
            return challengeConditionDetailService.selectMyNoOwnerQuantityList(operator, request.getConditionId());
        } else {
            return challengeConditionDetailService.selectFrontList(request.getConditionId());
        }
    }

    @Override
    public List<ChallengeConditionDetailFrontRes> selectMyOwnerConditionPage(User operator,
            ChallengeConditionPageFrontReq request) {
        ChallengeConditionDetail condition = new ChallengeConditionDetail();
        condition.setUserId(operator.getId());

        if (EBoolean.YES.getCode().equals(request.getOwnerFlag())) {
            return challengeConditionDetailService.selectMyOwnerQuantityList(operator, request.getConditionId());
        } else if (EBoolean.NO.getCode().equals(request.getOwnerFlag())) {
            return challengeConditionDetailService.selectMyNoOwnerQuantityList(operator, request.getConditionId());
        } else {
            return challengeConditionDetailService.selectFrontList(request.getConditionId());
        }
    }

    @Override
    public List<ChannelMerchant> listChannelMerchant(User operator) {
        List<Long> channelIdList = challengeMapper.selectChannelMerchant(operator.getCompanyId());

        if (CollectionUtils.isEmpty(channelIdList)) {
            channelIdList = companyChannelService.listByCompany(operator.getCompanyId());
        } else {
            List<Long> channelIdListNew = companyChannelService.listByCompany(operator.getCompanyId());
            channelIdList.addAll(channelIdListNew);
        }

        ChannelMerchant condition = new ChannelMerchant();
        condition.setIdList(channelIdList);

        return channelMerchantService.list(condition);
    }

    @Override
    public boolean checkLasterPutOnNew(String type, Date startDate) {
        Challenge condition = new Challenge();
        condition.setType(type);
        condition.setStatus(EChallengeStatus.TWO.getCode());
        condition.setApproveDatetimeStart(startDate);

        List<Challenge> list = challengeMapper.selectByCondition(condition);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }

        return false;
    }
}