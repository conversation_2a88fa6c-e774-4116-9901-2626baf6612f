package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EFishPrayPropStatus;
import com.std.core.enums.EFishPrayPropType;
import com.std.core.mapper.FishPrayPropMapper;
import com.std.core.pojo.domain.FishPrayProp;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.FishPrayPropDetailRes;
import com.std.core.pojo.response.FishPrayPropListRes;
import com.std.core.pojo.response.FishPrayPropPageRes;
import com.std.core.service.IFishPrayPropService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* 渔光祈福道具/门票ServiceImpl
*
* <AUTHOR> wzh
* @since : 2023-02-28 15:34
*/
@Service
public class FishPrayPropServiceImpl implements IFishPrayPropService {

    @Resource
    private FishPrayPropMapper fishPrayPropMapper;

    /**
     * 新增渔光祈福道具/门票
     *
     * @param req 新增渔光祈福道具/门票入参
     * @param operator 操作人
     */
    @Override
    public void create(FishPrayPropCreateReq req, User operator) {
        FishPrayProp fishPrayProp = EntityUtils.copyData(req, FishPrayProp.class);

        fishPrayProp.setStatus(EFishPrayPropStatus.FISH_PRAY_PROP_STATUS_0.getCode());
        fishPrayProp.setRemainQuantity(fishPrayProp.getTotalQuantity());
        fishPrayProp.setCreater(operator.getId());
        fishPrayProp.setCreaterName(operator.getLoginName());
        fishPrayProp.setCreateDatetime(new Date());
        fishPrayProp.setUpdater(operator.getId());
        fishPrayProp.setUpdaterName(operator.getLoginName());
        fishPrayProp.setUpdateDatetime(new Date());
        fishPrayPropMapper.insertSelective(fishPrayProp);
    }

    /**
     * 删除渔光祈福道具/门票
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        fishPrayPropMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改渔光祈福道具/门票
     *
     * @param req 修改渔光祈福道具/门票入参
     * @param operator 操作人
     */
    @Override
    public void modify(FishPrayPropModifyReq req, User operator) {
        FishPrayProp fishPrayProp = EntityUtils.copyData(req, FishPrayProp.class);
        FishPrayProp prop = detail(req.getId());
        if (req.getTotalQuantity() >= prop.getTotalQuantity()) {
            fishPrayProp.setRemainQuantity(prop.getRemainQuantity() + req.getTotalQuantity() - prop.getTotalQuantity());
        }else {
            if (req.getTotalQuantity() <= prop.getRemainQuantity()) {
                fishPrayProp.setRemainQuantity(req.getTotalQuantity());
            }
        }
        fishPrayProp.setTotalQuantity(req.getTotalQuantity());
        fishPrayProp.setUpdater(operator.getId());
        fishPrayProp.setUpdaterName(operator.getLoginName());
        fishPrayProp.setUpdateDatetime(new Date());
        fishPrayPropMapper.updateByPrimaryKeySelective(fishPrayProp);
    }

    /**
     * 详情查询渔光祈福道具/门票
     *
     * @param id 主键ID
     * @return 渔光祈福道具/门票对象
     */
    @Override
    public FishPrayProp detail(Long id) {
        FishPrayProp fishPrayProp = fishPrayPropMapper.selectByPrimaryKey(id);
        if (null == fishPrayProp) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        return fishPrayProp;
    }

    /**
     * 分页查询渔光祈福道具/门票
     *
     * @param req 分页查询渔光祈福道具/门票入参
     * @return 分页渔光祈福道具/门票对象
     */
    @Override
    public List<FishPrayProp> page(FishPrayPropPageReq req) {
        FishPrayProp condition = EntityUtils.copyData(req, FishPrayProp.class);
        condition.setOrderBy("t.order_no desc");
        List<FishPrayProp> fishPrayPropList = fishPrayPropMapper.selectByCondition(condition);

        return fishPrayPropList;
    }

    /**
     * 列表查询渔光祈福道具/门票
     *
     * @param req 列表查询渔光祈福道具/门票入参
     * @return 列表渔光祈福道具/门票对象
     */
    @Override
    public List<FishPrayProp> list(FishPrayPropListReq req) {
        FishPrayProp condition = EntityUtils.copyData(req, FishPrayProp.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), FishPrayProp.class));

        List<FishPrayProp> fishPrayPropList = fishPrayPropMapper.selectByCondition(condition);

        return fishPrayPropList;
    }

    /**
     * 前端详情查询渔光祈福道具/门票
     *
     * @param id 主键ID
     * @return 渔光祈福道具/门票对象
     */
    @Override
    public FishPrayPropDetailRes detailFront(Long id) {
        FishPrayPropDetailRes res = new FishPrayPropDetailRes();

        FishPrayProp fishPrayProp = fishPrayPropMapper.selectByPrimaryKey(id);
        if (null == fishPrayProp) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(fishPrayProp, res);
        return res;
    }

    /**
     * 前端分页查询渔光祈福道具/门票
     *
     * @param req 前端分页查询渔光祈福道具/门票入参
     * @return 分页渔光祈福道具/门票对象
     */
    @Override
    public List< FishPrayPropPageRes> pageFront(FishPrayPropPageFrontReq req) {
        FishPrayProp condition = EntityUtils.copyData(req, FishPrayProp.class);
        condition.setOrderBy("t.order_no desc");
        condition.setStatus(EFishPrayPropStatus.FISH_PRAY_PROP_STATUS_1.getCode());
        List<FishPrayProp> fishPrayPropList = fishPrayPropMapper.selectByCondition(condition);

        List< FishPrayPropPageRes> resList = fishPrayPropList.stream().map((entity) -> {
            FishPrayPropPageRes res = new FishPrayPropPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(fishPrayPropList, resList);
    }

    /**
     * 前端列表查询渔光祈福道具/门票
     *
     * @param req 前端列表查询渔光祈福道具/门票入参
     * @return 列表渔光祈福道具/门票对象
     */
    @Override
    public List< FishPrayPropListRes> listFront(FishPrayPropListFrontReq req) {
        FishPrayProp condition = EntityUtils.copyData(req, FishPrayProp.class);
        condition.setOrderBy("t.order_no desc");
        condition.setStatus(EFishPrayPropStatus.FISH_PRAY_PROP_STATUS_1.getCode());
        List<FishPrayProp> fishPrayPropList = fishPrayPropMapper.selectByCondition(condition);

        List< FishPrayPropListRes> resList = fishPrayPropList.stream().map((entity) -> {
            FishPrayPropListRes res = new FishPrayPropListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(FishPrayPropBatchUpDownReq request,User operator) {
        for (Long id : request.getIdList()) {
            FishPrayProp fishPrayProp = new FishPrayProp();
            fishPrayProp.setId(id);
            fishPrayProp.setStatus(request.getStatus());
            fishPrayProp.setUpdater(operator.getId());
            fishPrayProp.setUpdaterName(operator.getLoginName());
            fishPrayProp.setUpdateDatetime(new Date());
            fishPrayPropMapper.updateByPrimaryKeySelective(fishPrayProp);
        }
    }

    @Override
    public void changeRemainQuantity(Long id, Integer quantity) {
        fishPrayPropMapper.changeRemainQuantity(id,quantity);
    }

}