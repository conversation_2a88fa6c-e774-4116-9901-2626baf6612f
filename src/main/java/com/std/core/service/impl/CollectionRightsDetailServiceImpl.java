package com.std.core.service.impl;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.core.enums.EBoolean;
import com.std.core.enums.ECollectionCategory;
import com.std.core.enums.ECollectionDetailOwnerType;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.ECollectionMetaBizType;
import com.std.core.enums.ECollectionPeriodPlateCategory;
import com.std.core.enums.ECollectionRightCompanyStatus;
import com.std.core.enums.ECollectionRightSpecificType;
import com.std.core.enums.ECollectionRightType;
import com.std.core.enums.ECollectionRightsDetailCreateType;
import com.std.core.enums.ECollectionRightsDetailNumberType;
import com.std.core.enums.ECollectionStatus;
import com.std.core.enums.ECompanyStatus;
import com.std.core.enums.EErrorCode;
import com.std.core.enums.EFishBoatStatus;
import com.std.core.enums.EJPushActivityKeyType;
import com.std.core.enums.EMetaTicketMyStatus;
import com.std.core.enums.EMetaTicketRecordStatus;
import com.std.core.enums.EMetaTicketType;
import com.std.core.enums.ESmsRefType;
import com.std.core.enums.EYaoTaskConfigStatus;
import com.std.core.enums.EYaoTaskConfigType;
import com.std.core.mapper.CollectionRightsDetailMapper;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.CollectionRightCompany;
import com.std.core.pojo.domain.CollectionRightRecord;
import com.std.core.pojo.domain.CollectionRightsDetail;
import com.std.core.pojo.domain.CollectionRightsDetailHistory;
import com.std.core.pojo.domain.Company;
import com.std.core.pojo.domain.Dict;
import com.std.core.pojo.domain.FishBoat;
import com.std.core.pojo.domain.MetaTicketRecord;
import com.std.core.pojo.domain.TicketType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.YaoTaskConfig;
import com.std.core.pojo.request.CollectionRightsDeatilTicketEnterMetaReq;
import com.std.core.pojo.request.CollectionRightsDetailCreateAutoReq;
import com.std.core.pojo.request.CollectionRightsDetailCreateReq;
import com.std.core.pojo.request.CollectionRightsDetailCreateRightReq;
import com.std.core.pojo.request.CollectionRightsDetailListFrontReq;
import com.std.core.pojo.request.CollectionRightsDetailListReq;
import com.std.core.pojo.request.CollectionRightsDetailModifyContentReq;
import com.std.core.pojo.request.CollectionRightsDetailModifyRightReq;
import com.std.core.pojo.request.CollectionRightsDetailPageFrontReq;
import com.std.core.pojo.request.CollectionRightsDetailPageReq;
import com.std.core.pojo.request.CollectionRightsMyTicketPageFrontReq;
import com.std.core.pojo.request.DictListReq;
import com.std.core.pojo.request.MetaTicketMyReq;
import com.std.core.pojo.request.TicketTypeListReq;
import com.std.core.pojo.response.CollectionRightsDeatilTicketEnterMetaRes;
import com.std.core.pojo.response.CollectionRightsDetailDetailRes;
import com.std.core.pojo.response.CollectionRightsDetailListRes;
import com.std.core.pojo.response.CollectionRightsDetailPageRes;
import com.std.core.pojo.response.CollectionRightsDetailTicketCheckRes;
import com.std.core.pojo.response.CollectionRightsMyTicketPageRes;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionRightCompanyService;
import com.std.core.service.ICollectionRightRecordService;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.ICompanyService;
import com.std.core.service.IDictService;
import com.std.core.service.IFishBoatService;
import com.std.core.service.IMetaTicketRecordService;
import com.std.core.service.ISmsService;
import com.std.core.service.IUserEntryRecordService;
import com.std.core.service.IUserService;
import com.std.core.service.IYaoTaskConfigService;
import com.std.core.util.DateUtil;
import com.std.core.util.SysConstantsDict;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 作品权益明细表ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-07-06 18:03
 */
@Service
public class CollectionRightsDetailServiceImpl implements ICollectionRightsDetailService {

    @Resource
    private CollectionRightsDetailMapper collectionRightsDetailMapper;

    @Resource
    private IDictService dictService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private ICollectionRightCompanyService collectionRightCompanyService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private ICollectionRightRecordService collectionRightRecordService;

    @Resource
    private ISmsService smsService;

    @Resource
    private IUserService userService;

    @Resource
    private IMetaTicketRecordService metaTicketRecordService;

    @Resource
    private IUserEntryRecordService userEntryRecordService;

    @Resource
    private IYaoTaskConfigService yaoTaskConfigService;

    @Resource
    private IFishBoatService fishBoatService;

    @Override
    public void create(Long collectionId, List<CollectionRightsDetailCreateReq> collectionRightsDetailList, User operator) {
        //根据作品删除原有权益数据
        collectionRightsDetailMapper.deleteByCollectionId(collectionId);

        if (CollectionUtils.isNotEmpty(collectionRightsDetailList)) {
            for (CollectionRightsDetailCreateReq rightReq : collectionRightsDetailList) {
                CollectionRightsDetail collectionRightDetail = new CollectionRightsDetail();
                collectionRightDetail.setType(rightReq.getType());
                Dict dict = dictService.detail(SysConstantsDict.COLLECTION_RIGHT_DETAIL_TYPE, rightReq.getType());
                collectionRightDetail.setName(dict.getValue());
                collectionRightDetail.setContent(rightReq.getContent());
                collectionRightDetail.setCollectionId(collectionId);
                collectionRightDetail.setUpdater(operator.getId());
                collectionRightDetail.setUpdaterName(operator.getLoginName());
                collectionRightDetail.setUpdateDatetime(new Date());
                collectionRightsDetailMapper.insertSelective(collectionRightDetail);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Collection collection, List<CollectionRightsDetailCreateAutoReq> collectionRightsCreateAutoReqList, User operator) {
        //根据作品删除原有权益数据
        collectionRightCompanyService.removeByCollectionId(collection.getId());
        collectionRightsDetailMapper.deleteByCollectionId(collection.getId());

        List<String> rightTypeList = new ArrayList<>();
        List<BigDecimal> rateList = new ArrayList<>();
        if (null == collectionRightsCreateAutoReqList) {
            return;
        }

        for (CollectionRightsDetailCreateAutoReq rightReq : collectionRightsCreateAutoReqList) {
            CollectionRightsDetail collectionRightDetail = new CollectionRightsDetail();
            ECollectionRightSpecificType collectionRightSpecificTypeType = ECollectionRightSpecificType
                    .getCollectionRightSpecificTypeType(rightReq.getType());
            collectionRightDetail.setType(rightReq.getType());
            Dict dict = dictService.detail(SysConstantsDict.COLLECTION_RIGHT_DETAIL_TYPE, rightReq.getType());
            collectionRightDetail.setName(dict.getValue());
            collectionRightDetail.setContent(rightReq.getContent());
            collectionRightDetail.setCollectionId(collection.getId());
            collectionRightDetail.setOrderNo(rightReq.getOrderNo());

            if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())) {
                // type为1的除了折扣比例其他不能重复存在，折扣值不能重复
                if (rightTypeList.contains(rightReq.getType())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益重复");
                } else if (!ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(rightReq.getType())) {
                    rightTypeList.add(rightReq.getType());
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(rightReq.getType())) {
                    if (rateList.contains(rightReq.getDiscountRate())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "折扣比例重复");
                    }
                    rateList.add(rightReq.getDiscountRate());
                }

                if (!ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                    if (StringUtils.isBlank(rightReq.getNumberFlag()) ||
                            (!ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                                    .equals(rightReq.getNumberFlag())
                                    && !ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                                    .equals(rightReq.getNumberFlag()))) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "次数标识错误");
                    } else if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                            .equals(rightReq.getNumberFlag()) && (null == rightReq.getTotalNumber() || 1 > rightReq.getTotalNumber())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
                    } else if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                            .equals(rightReq.getNumberFlag())) {
                        rightReq.setTotalNumber(0);
                    }

                    collectionRightDetail.setNumberFlag(rightReq.getNumberFlag());
                }

                if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(rightReq.getType())) {
                    if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "版权区作品不能拥有空投权益");
                    }
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setDropNumber(1);
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(rightReq.getType())) {
                    collectionRightDetail.setAdvanceMins(rightReq.getAdvanceMins());
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(rightReq.getType())) {
                    collectionRightDetail.setDiscountRate(rightReq.getDiscountRate());
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                    // 元宇宙进入权限
                    if (StringUtils.isBlank(rightReq.getMetaBizType())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写元宇宙关联类型");
                    } else if (!ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_0.getCode().equals(rightReq.getMetaBizType())
                            && null == rightReq.getMetaBizId()) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请填写元宇宙关联序号");
                    }

                    // 更新藏品进入元宇宙权益
                    Collection collectionModify = new Collection();
                    collectionModify.setId(collection.getId());
                    collectionModify.setU3dFlag(EBoolean.YES.getCode());
                    collectionModify.setMetaBizId(rightReq.getMetaBizId());
                    collectionModify.setMetaBizType(rightReq.getMetaBizType());
                    collectionService.modify(collectionModify);

                    // 根据元宇宙关联类型处理
                    if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_1.getCode().equals(collectionModify.getMetaBizType())) {
                        FishBoat fishBoat = fishBoatService.detail(collectionModify.getMetaBizId());
                        if (!EFishBoatStatus.FISH_BOAT_STATUS_1.getCode().equals(fishBoat.getStatus())) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), fishBoat.getName() + "渔船还未上架");
                        }
                    } else if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_2.getCode().equals(collectionModify.getMetaBizType())) {

                    } else if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_0.getCode().equals(collectionModify.getMetaBizType())) {

                    } else {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "元宇宙关联类型错误");
                    }

                    collectionRightDetail.setTicketType(EMetaTicketType.META_TICKET_TYPE_0.getCode());
                    collectionRightDetail
                            .setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode());
//                    collectionRightDetail.setTicketType(rightReq.getTicketType());

//                    // 设置门票
//                    if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(rightReq.getTicketType())) {
//                        if (null == rightReq.getTotalNumber()) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
//                        }
//                        if (!ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有道具藏品才能设置" + collectionRightSpecificTypeType.getValue() + "权益类型为单次");
//                        }
//                        collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
//                        collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
//                    } else if (EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(rightReq.getTicketType())) {
//                        if (null == rightReq.getTotalNumber()) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
//                        }
//                        if (null == rightReq.getTicketTime()) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "单次时效不能为空");
//                        }
//                        collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
//                        collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
//                        collectionRightDetail.setTicketTime(rightReq.getTicketTime());
//                    } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(rightReq.getTicketType())) {
//                        if (org.apache.tika.utils.StringUtils.isBlank(rightReq.getTicketStartDatetime()) || org.apache.tika.utils.StringUtils.isBlank(rightReq.getTicketEndDatetime())) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "时效范围不能为空");
//                        }
//
//                        try {
//                            collectionRightDetail.setTicketStartDatetime(DateUtil.strToDate(rightReq.getTicketStartDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2));
//                            collectionRightDetail.setTicketEndDatetime(DateUtil.strToDate(rightReq.getTicketEndDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2));
//                        } catch (Exception e) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "时效范围时间错误");
//                        }
//
//                        if (null == collectionRightDetail.getTicketStartDatetime() || null == collectionRightDetail.getTicketEndDatetime()) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "时效范围时间错误");
//                        }
//
//                        if (collectionRightDetail.getTicketEndDatetime().before(collectionRightDetail.getTicketStartDatetime())) {
//                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "开始时间不能晚于结束时间");
//                        }
//                    } else if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(rightReq.getTicketType())) {
//                        collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode());
//                    } else {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "门票类型错误");
//                    }
                }
            } else if (StringUtils.isBlank(rightReq.getContent())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益说明不能为空");
            }
            collectionRightDetail.setUpdater(operator.getId());
            collectionRightDetail.setUpdaterName(operator.getLoginName());
            collectionRightDetail.setUpdateDatetime(new Date());
            collectionRightsDetailMapper.insertSelective(collectionRightDetail);

            if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())
                    && !ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                HashSet<Long> companyIdList = new HashSet<>(rightReq.getCompanyIdList());
                HashSet<String> plateCategoryList = new HashSet<>(rightReq.getPlateCategoryList());

                if (CollectionUtils.isEmpty(companyIdList)) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                            collectionRightSpecificTypeType.getValue() + "权益发行方不能为空");
                }
                if (CollectionUtils.isEmpty(plateCategoryList)) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益板块不能为空");
                }
                List<CollectionRightCompany> rightCompanyList = new ArrayList<>();
                for (Long companyId : companyIdList) {
                    if (0L != companyId) {
                        Company company = companyService.detailSimple(companyId);
                        if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus()) && !ECompanyStatus.INFO_MODIFT.getCode()
                                .equals(company.getStatus())) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方" + company.getName() + "不可选择");
                        }
                    } else if (0L == companyId && companyIdList.size() > 1) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方选择全部后无需再选其他发行方");
                    }

                    for (String plateCategory : plateCategoryList) {
                        CollectionRightCompany rightCompany = new CollectionRightCompany();
                        if (!EBoolean.NO.getCode().equals(plateCategory)) {
                            ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);
                        } else if (EBoolean.NO.getCode().equals(plateCategory) && plateCategoryList.size() > 1) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块选择全部后无需再选其他板块");
                        }
                        rightCompany.setPlateCategory(plateCategory);
                        rightCompany.setCompanyId(companyId);
                        rightCompany.setRefId(collectionRightDetail.getId());
                        rightCompany.setRefType(rightReq.getType());
                        rightCompany.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode());

                        rightCompany.setUpdater(operator.getId());
                        rightCompany.setUpdaterName(operator.getLoginName());
                        rightCompany.setUpdateDatetime(new Date());
                        rightCompanyList.add(rightCompany);
                    }
                }

                if (CollectionUtils.isNotEmpty(rightCompanyList)) {
                    collectionRightCompanyService.createBatch(rightCompanyList);
                }
            }
        }

    }

    @Override
    public void create(CollectionRightsDetail collectionRightsDetail) {
        collectionRightsDetailMapper.insertSelective(collectionRightsDetail);
    }

    /**
     * 删除作品权益明细表
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionRightsDetailMapper.deleteByPrimaryKey(id);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(CollectionRightsDetail rightsDetail, Integer totalNumber, String numberFlag) {

        ECollectionRightSpecificType collectionRightSpecificTypeType = ECollectionRightSpecificType
                .getCollectionRightSpecificTypeType(rightsDetail.getType());
        if (EBoolean.NO.getCode().equals(collectionRightSpecificTypeType.getType()) ||
                (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())
                        && ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightsDetail.getType()))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益无需修改次数");
        }
        if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(numberFlag)) {
            if (null == totalNumber) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "权益次数不能为空");
            }

            CollectionRightRecord condition = new CollectionRightRecord();
            condition.setRightId(rightsDetail.getId());
            List<CollectionRightRecord> list = collectionRightRecordService.list(condition);

            if (list.size() > totalNumber) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "权益次数不能小于已使用次数" + list.size());
            }
            rightsDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
            rightsDetail.setTotalNumber(totalNumber);
            rightsDetail.setRemainNumber(rightsDetail.getTotalNumber() - list.size());
        } else {
            rightsDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode());
        }

//        rightsDetail.setUpdater(operator.getId());
//        rightsDetail.setUpdaterName(operator.getLoginName());
//        rightsDetail.setUpdateDatetime(new Date());
//        collectionRightsDetailMapper.updateByPrimaryKeySelective(rightsDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyContnet(CollectionRightsDetailModifyContentReq req, User operator) {
        CollectionRightsDetail rightsDetail = detailForUpdate(req.getId());
        rightsDetail.setContent(req.getContent());
        rightsDetail.setUpdater(operator.getId());
        rightsDetail.setUpdaterName(operator.getLoginName());
        rightsDetail.setUpdateDatetime(new Date());
        collectionRightsDetailMapper.updateContent(rightsDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRight(CollectionRightsDetailCreateRightReq request, User operator) {
        Collection collection = collectionService.detailForUpdate(request.getCollectionId());
        if (!ECollectionStatus.COLLECTION_STATUS_1.getCode().equals(collection.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "不是已上架作品不能新增权益");
        }

        //发行方 版权方 默认0处理
        List<Long> CompanyIdList = new ArrayList<>();
        CompanyIdList.add(0L);
        request.setCompanyIdList(CompanyIdList);

        List<String> plateList = new ArrayList<>();
        plateList.add("0");
        request.setPlateCategoryList(plateList);

        CollectionRightsDetail collectionRightDetail = new CollectionRightsDetail();
        ECollectionRightSpecificType collectionRightSpecificTypeType = ECollectionRightSpecificType
                .getCollectionRightSpecificTypeType(request.getType());
        collectionRightDetail.setType(request.getType());
        Dict dict = dictService.detail(SysConstantsDict.COLLECTION_RIGHT_DETAIL_TYPE, request.getType());
        collectionRightDetail.setName(dict.getValue());
        collectionRightDetail.setContent(request.getContent());
        collectionRightDetail.setCollectionId(collection.getId());
        collectionRightDetail.setOrderNo(request.getOrderNo());
        collectionRightDetail.setSendFlag(request.getSendFlag());
        // 校验权益是否重复
        if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())) {
            CollectionRightsDetail condition = new CollectionRightsDetail();
            condition.setCollectionId(collection.getId());
            condition.setType(request.getType());
            List<CollectionRightsDetail> detailList = collectionRightsDetailMapper.selectByCondition(condition);

            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(request.getType())) {
                if (null == request.getDiscountRate()) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "折扣比例不能为空");
                }
                detailList.forEach(x -> {
                    if (x.getDiscountRate().compareTo(request.getDiscountRate()) == 0) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "折扣比例重复");
                    }
                });
            } else if (CollectionUtils.isNotEmpty(detailList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collection.getName() + "权益重复");
            }
        }

        if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())) {
            if (!ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(request.getType())) {
                if (StringUtils.isBlank(request.getNumberFlag()) ||
                        (!ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                                .equals(request.getNumberFlag())
                                && !ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                                .equals(request.getNumberFlag()))) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "次数标识错误");
                } else if (
                        ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode().equals(request.getNumberFlag())
                                && (null == request.getTotalNumber() || 1 > request.getTotalNumber())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                            collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
                } else if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                        .equals(request.getNumberFlag())) {
                    request.setTotalNumber(0);
                }
                collectionRightDetail.setNumberFlag(request.getNumberFlag());
            }

            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(request.getType())) {
                if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                            collectionRightSpecificTypeType.getValue() + "版权区作品不能拥有空投权益");
                }
                collectionRightDetail.setTotalNumber(request.getTotalNumber());
                collectionRightDetail.setRemainNumber(request.getTotalNumber());
                collectionRightDetail.setDropNumber(1);
            } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(request.getType())) {
                collectionRightDetail.setAdvanceMins(request.getAdvanceMins());
                collectionRightDetail.setTotalNumber(request.getTotalNumber());
                collectionRightDetail.setRemainNumber(request.getTotalNumber());
            } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(request.getType())) {
                collectionRightDetail.setDiscountRate(request.getDiscountRate());
                collectionRightDetail.setTotalNumber(request.getTotalNumber());
                collectionRightDetail.setRemainNumber(request.getTotalNumber());
            } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(request.getType())) {
                collectionRightDetail.setTicketType(request.getTicketType());

                // 更新藏品进入元宇宙权益
                collection.setMetaBizId(request.getMetaBizId());
                collection.setMetaBizType(request.getMetaBizType());

                // 根据元宇宙关联类型处理
                if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_1.getCode().equals(collection.getMetaBizType())) {
                    FishBoat fishBoat = fishBoatService.detail(collection.getMetaBizId());
                    if (!EFishBoatStatus.FISH_BOAT_STATUS_1.getCode().equals(fishBoat.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), fishBoat.getName() + "渔船还未上架");
                    }
                } else if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_2.getCode().equals(collection.getMetaBizType())) {

                } else if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_0.getCode().equals(collection.getMetaBizType())) {

                } else {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "元宇宙关联类型错误");
                }

                collectionRightDetail.setTicketType(EMetaTicketType.META_TICKET_TYPE_0.getCode());
                collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode());
                collection.setU3dFlag(EBoolean.YES.getCode());

//                // 设置门票
//                if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(request.getTicketType())) {
//                    if (null == request.getTotalNumber()) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
//                    }
//                    if (!ECollectionCategory.NOT_COLLECTION.getCode().equals(collection.getCategory())) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "只有道具藏品才能设置" + collectionRightSpecificTypeType.getValue() + "权益类型为单次");
//                    }
//                    collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
//                    collectionRightDetail.setTotalNumber(request.getTotalNumber());
//                } else if (EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(request.getTicketType())) {
//                    if (null == request.getTotalNumber()) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
//                    }
//                    if (null == request.getTicketTime()) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "单次时效不能为空");
//                    }
//                    collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
//                    collectionRightDetail.setTotalNumber(request.getTotalNumber());
//                    collectionRightDetail.setTicketTime(request.getTicketTime());
//                } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(request.getTicketType())) {
//                    if (org.apache.tika.utils.StringUtils.isBlank(request.getTicketStartDatetime()) || org.apache.tika.utils.StringUtils.isBlank(request.getTicketEndDatetime())) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "时效范围不能为空");
//                    }
//
//                    try {
//                        collectionRightDetail.setTicketStartDatetime(DateUtil.strToDate(request.getTicketStartDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2));
//                        collectionRightDetail.setTicketEndDatetime(DateUtil.strToDate(request.getTicketEndDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_2));
//                    } catch (Exception e) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "时效范围时间错误");
//                    }
//
//                    if (collectionRightDetail.getTicketEndDatetime().before(collectionRightDetail.getTicketStartDatetime())) {
//                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "开始时间不能晚于结束时间");
//                    }
//                } else if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(request.getTicketType())) {
//                    collectionRightDetail.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode());
//                } else {
//                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getValue() + "门票类型错误");
//                }
            }
        } else if (StringUtils.isBlank(request.getContent())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益说明不能为空");
        }
        collectionRightDetail.setUpdater(operator.getId());
        collectionRightDetail.setUpdaterName(operator.getLoginName());
        collectionRightDetail.setUpdateDatetime(new Date());
        collectionRightsDetailMapper.insertSelective(collectionRightDetail);

        collection.setRightType(ECollectionRightType.COLLECTION_PAYTYPE_3.getCode());
        collectionService.modify(collection);

        if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())
                && !ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(request.getType())) {
            HashSet<Long> companyIdList = new HashSet<>(request.getCompanyIdList());
            HashSet<String> plateCategoryList = new HashSet<>(request.getPlateCategoryList());

            if (CollectionUtils.isEmpty(companyIdList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益发行方不能为空");
            }
            if (CollectionUtils.isEmpty(plateCategoryList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益板块不能为空");
            }
            List<CollectionRightCompany> rightCompanyList = new ArrayList<>();
            for (Long companyId : companyIdList) {
                // todo 选择权限
                if (0L != companyId) {
                    Company company = companyService.detailSimple(companyId);
                    if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus()) && !ECompanyStatus.INFO_MODIFT.getCode()
                            .equals(company.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方" + company.getName() + "不可选择");
                    }
                } else if (0L == companyId && companyIdList.size() > 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方选择全部后无需再选其他发行方");
                }

                for (String plateCategory : plateCategoryList) {
                    CollectionRightCompany rightCompany = new CollectionRightCompany();
                    if (!EBoolean.NO.getCode().equals(plateCategory)) {
                        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);
                    } else if (EBoolean.NO.getCode().equals(plateCategory) && plateCategoryList.size() > 1) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块选择全部后无需再选其他发行方");
                    }
                    rightCompany.setPlateCategory(plateCategory);
                    rightCompany.setCompanyId(companyId);
                    rightCompany.setRefId(collectionRightDetail.getId());
                    rightCompany.setRefType(request.getType());
                    rightCompany.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode());

                    rightCompany.setUpdater(operator.getId());
                    rightCompany.setUpdaterName(operator.getLoginName());
                    rightCompany.setUpdateDatetime(new Date());
                    rightCompanyList.add(rightCompany);
                }
            }

            if (CollectionUtils.isNotEmpty(rightCompanyList)) {
                collectionRightCompanyService.createBatch(rightCompanyList);
            }
        }

        // 站内信以及极光通知
        List<Long> ownerIdList = collectionDetailService.detailCollectionDetailUserId(collection.getId());
        if (EBoolean.YES.getCode().equals(request.getSendFlag())) {
            for (Long ownerId : ownerIdList) {
                User ownerUser = userService.detailBrief(ownerId);
                smsService.sendMyMsg(ownerUser, "权益变更", "您拥有的藏品[" + collection.getName() + "]拥有新的权益了，快去看看吧",
                        ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue(),
                        EJPushActivityKeyType.HOME.getCode(), collection.getId().toString());
            }
        } else {
            for (Long ownerId : ownerIdList) {
                User ownerUser = userService.detailBrief(ownerId);
                smsService.sendMyMsg(ownerUser, "权益变更", "您拥有的藏品[" + collection.getName() + "]拥有新的权益了，快去看看吧",
                        ESmsRefType.COLLECTION_MESSAGE.getCode(), ESmsRefType.COLLECTION_MESSAGE.getValue());
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyRight(CollectionRightsDetailModifyRightReq req, User operator) {
        CollectionRightsDetail rightsDetail = detailForUpdate(req.getId());
        rightsDetail.setContent(req.getContent());
        rightsDetail.setTicketType(req.getTicketType());
        rightsDetail.setAdvanceMins(req.getAdvanceMins());
        rightsDetail.setDiscountRate(req.getDiscountRate());
        rightsDetail.setUpdater(operator.getId());
        rightsDetail.setUpdaterName(operator.getLoginName());
        rightsDetail.setUpdateDatetime(new Date());
        rightsDetail.setOrderNo(req.getOrderNo());

        ECollectionRightSpecificType collectionRightSpecificTypeType = ECollectionRightSpecificType
                .getCollectionRightSpecificTypeType(rightsDetail.getType());
        if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())
                && !ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightsDetail.getType())) {
            modify(rightsDetail, req.getTotalNumber(), req.getNumberFlag());

            HashSet<Long> companyIdList = new HashSet<>(req.getCompanyIdList());
            HashSet<String> plateCategoryList = new HashSet<>(req.getPlateCategoryList());

            if (CollectionUtils.isEmpty(companyIdList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益发行方不能为空");
            }
            if (CollectionUtils.isEmpty(plateCategoryList)) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益板块不能为空");
            }

            CollectionRightCompany condition = new CollectionRightCompany();
            condition.setRefId(rightsDetail.getId());
            condition.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode());
            List<CollectionRightCompany> rightCompanyList = collectionRightCompanyService.list(condition);
            if (CollectionUtils.isNotEmpty(rightCompanyList)) {
                for (CollectionRightCompany right : rightCompanyList) {
                    right.setUpdater(operator.getId());
                    right.setUpdaterName(operator.getLoginName());
                    right.setUpdateDatetime(new Date());
                    right.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_0.getCode());
                }
                collectionRightCompanyService.updateBatchDown(rightCompanyList);
            }

            for (Long companyId : companyIdList) {
                if (0L != companyId) {
                    Company company = companyService.detailSimple(companyId);
                    if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus()) && !ECompanyStatus.INFO_MODIFT.getCode()
                            .equals(company.getStatus())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方" + company.getName() + "不可选择");
                    }
                } else if (0L == companyId && companyIdList.size() > 1) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方选择全部后无需再选其他发行方");
                }

                for (String plateCategory : plateCategoryList) {

                    if (!EBoolean.NO.getCode().equals(plateCategory)) {
                        ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);
                    } else if (EBoolean.NO.getCode().equals(plateCategory) && plateCategoryList.size() > 1) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块选择全部后无需再选其他发行方");
                    }

                    CollectionRightCompany right = new CollectionRightCompany();
                    right.setPlateCategory(plateCategory);
                    right.setCompanyId(companyId);
                    right.setRefId(rightsDetail.getId());
                    List<CollectionRightCompany> list = collectionRightCompanyService.list(right);
                    if (CollectionUtils.isNotEmpty(list)) {
                        CollectionRightCompany rightCompany = list.get(0);
                        rightCompany.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode());
                        rightCompany.setUpdater(operator.getId());
                        rightCompany.setUpdaterName(operator.getLoginName());
                        rightCompany.setUpdateDatetime(new Date());
                        collectionRightCompanyService.modify(rightCompany);
                    } else {
                        CollectionRightCompany rightCompany = new CollectionRightCompany();
                        rightCompany.setPlateCategory(plateCategory);
                        rightCompany.setCompanyId(companyId);
                        rightCompany.setRefId(rightsDetail.getId());
                        rightCompany.setRefType(rightsDetail.getType());

                        rightCompany.setStatus(ECollectionRightCompanyStatus.COLLECTION_RIGHT_COMPANY_STATUS_1.getCode());
                        rightCompany.setUpdater(operator.getId());
                        rightCompany.setUpdaterName(operator.getLoginName());
                        rightCompany.setUpdateDatetime(new Date());

                        collectionRightCompanyService.create(rightCompany);
                    }
                }
            }
        }

        collectionRightsDetailMapper.updateByPrimaryKeySelective(rightsDetail);
    }

    /**
     * 详情查询作品权益明细表
     *
     * @param id 主键ID
     * @return 作品权益明细表对象
     */
    @Override
    public CollectionRightsDetail detail(Long id) {
        CollectionRightsDetail collectionRightsDetail = collectionRightsDetailMapper.selectByPrimaryKey(id);
        if (null == collectionRightsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collectionRightsDetail;
    }

    @Override
    public CollectionRightsDetail detailOss(Long id) {
        CollectionRightsDetail collectionRightsDetail = collectionRightsDetailMapper.selectByPrimaryKeyOss(id);
        if (null == collectionRightsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        DictListReq dictListReq = new DictListReq();
        dictListReq.setParentKey(SysConstantsDict.METAVERSE_TICKET_TYPE);
        List<Dict> dictList = dictService.listAll(dictListReq);
        Map<String, String> map = new HashMap<>();
        for (Dict dict : dictList) {
            map.put(dict.getKey(), dict.getValue());
        }
        List<Long> companyIdList = collectionRightCompanyService.listCompany(collectionRightsDetail.getId());

//        List<CollectionRightsObject> rightsObjectList = new ArrayList<>();
//        for (Long companyId : companyIdList) {
//            CollectionRightsObject rightsObject = new CollectionRightsObject();
//            if (0L == companyId) {
//                rightsObject.setId("0");
//                rightsObject.setName("全部");
//            } else {
//                Company company = companyService.detailSimple(companyId);
//                rightsObject.setId(company.getId().toString());
//                rightsObject.setName(company.getName());
//            }
//            rightsObjectList.add(rightsObject);
//        }
//        collectionRightsDetail.setCompanyList(rightsObjectList);

        List<String> plateCategoryList = collectionRightCompanyService.listPlateCategory(collectionRightsDetail.getId());

//        List<CollectionRightsObject> plateCategoryMap = new ArrayList<>();
//        for (String platCategory : plateCategoryList) {
//            CollectionRightsObject rightsObject = new CollectionRightsObject();
//            if (EBoolean.NO.getCode().equals(platCategory)) {
//                rightsObject.setId("0");
//                rightsObject.setName("全部");
//            } else {
//                rightsObject.setId(platCategory);
//                rightsObject.setName(ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory).getValue());
//            }
//            plateCategoryMap.add(rightsObject);
//        }
//        collectionRightsDetail.setPlateCategoryList(plateCategoryMap);

        if (StringUtils.isNotBlank(collectionRightsDetail.getTicketType())) {
            collectionRightsDetail.setTicketTypeName(map.get(collectionRightsDetail.getTicketType()));

            Collection collection = collectionService.detailSimple(collectionRightsDetail.getCollectionId());
            collectionRightsDetail.setMetaBizType(collection.getMetaBizType());
            if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_1.getCode().equals(collection.getMetaBizType())) {
                collectionRightsDetail.setMetaBizId(collection.getMetaBizId());
                FishBoat fishBoat = fishBoatService.detail(collection.getMetaBizId());
                collectionRightsDetail.setMetaBizName(fishBoat.getName());
            }
        }

        Company company = companyService.detailSimple(collectionRightsDetail.getCompanyId());
        collectionRightsDetail.setCompanyName(company.getName());
        return collectionRightsDetail;
    }

    @Override
    public CollectionRightsDetail detailForUpdate(Long id) {
        CollectionRightsDetail collectionRightsDetail = collectionRightsDetailMapper.selectForUpdate(id);
        if (null == collectionRightsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        return collectionRightsDetail;
    }

    /**
     * 分页查询作品权益明细表
     *
     * @param req 分页查询作品权益明细表入参
     * @return 分页作品权益明细表对象
     */
    @Override
    public List<CollectionRightsDetail> page(CollectionRightsDetailPageReq req) {
        CollectionRightsDetail condition = EntityUtils.copyData(req, CollectionRightsDetail.class);

        List<CollectionRightsDetail> collectionRightsDetailList = collectionRightsDetailMapper.selectByConditionOss(condition);
        DictListReq dictListReq = new DictListReq();
        dictListReq.setParentKey(SysConstantsDict.METAVERSE_TICKET_TYPE);
        List<Dict> dictList = dictService.listAll(dictListReq);
        Map<String, String> map = new HashMap<>();
        for (Dict dict : dictList) {
            map.put(dict.getKey(), dict.getValue());
        }
        for (CollectionRightsDetail rightsDetail : collectionRightsDetailList) {
//            List<Long> companyIdList = collectionRightCompanyService.listCompany(rightsDetail.getId());
//            List<String> plateCategoryList = collectionRightCompanyService.listPlateCategory(rightsDetail.getId());
            if (StringUtils.isNotBlank(rightsDetail.getTicketType())) {
                rightsDetail.setTicketTypeName(map.get(rightsDetail.getTicketType()));

                Collection collection = collectionService.detailSimple(rightsDetail.getCollectionId());
                rightsDetail.setMetaBizType(collection.getMetaBizType());
                if (ECollectionMetaBizType.E_COLLECTION_META_BIZ_TYPE_1.getCode().equals(collection.getMetaBizType())) {
                    rightsDetail.setMetaBizId(collection.getMetaBizId());
                    FishBoat fishBoat = fishBoatService.detail(collection.getMetaBizId());
                    rightsDetail.setMetaBizName(fishBoat.getName());
                }
            }

            Company company = companyService.detailSimple(rightsDetail.getCompanyId());
            rightsDetail.setCompanyName(company.getName());
        }
        return collectionRightsDetailList;
    }

    /**
     * 列表查询作品权益明细表
     *
     * @param req 列表查询作品权益明细表入参
     * @return 列表作品权益明细表对象
     */
    @Override
    public List<CollectionRightsDetail> list(CollectionRightsDetailListReq req) {
        CollectionRightsDetail condition = EntityUtils.copyData(req, CollectionRightsDetail.class);
        condition.setOrderBy("t.order_no desc,t.id asc");

        List<CollectionRightsDetail> collectionRightsDetailList = collectionRightsDetailMapper.selectByCondition(condition);

        DictListReq dictListReq = new DictListReq();
        dictListReq.setParentKey(SysConstantsDict.METAVERSE_TICKET_TYPE);
        List<Dict> dictList = dictService.listAll(dictListReq);
        Map<String, String> map = new HashMap<>();
        for (Dict dict : dictList) {
            map.put(dict.getKey(), dict.getValue());
        }
        for (CollectionRightsDetail rightsDetail : collectionRightsDetailList) {
            List<Long> companyIdList = collectionRightCompanyService.listCompany(rightsDetail.getId());

//            List<CollectionRightsObject> rightsObjectList = new ArrayList<>();
//            for (Long companyId : companyIdList) {
//                CollectionRightsObject rightsObject = new CollectionRightsObject();
//                if (0L == companyId) {
//                    rightsObject.setId("0");
//                    rightsObject.setName("全部");
//                } else {
//                    Company company = companyService.detailSimple(companyId);
//                    rightsObject.setId(company.getId().toString());
//                    rightsObject.setName(company.getName());
//                }
//                rightsObjectList.add(rightsObject);
//            }
//            rightsDetail.setCompanyList(rightsObjectList);

            List<String> plateCategoryList = collectionRightCompanyService.listPlateCategory(rightsDetail.getId());

//            List<CollectionRightsObject> plateCategoryMap = new ArrayList<>();
//            for (String platCategory : plateCategoryList) {
//                CollectionRightsObject rightsObject = new CollectionRightsObject();
//                if (EBoolean.NO.getCode().equals(platCategory)) {
//                    rightsObject.setId("0");
//                    rightsObject.setName("全部");
//                } else {
//                    rightsObject.setId(platCategory);
//                    rightsObject.setName(ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory).getValue());
//                }
//                plateCategoryMap.add(rightsObject);
//            }
//            rightsDetail.setPlateCategoryList(plateCategoryMap);

            if (StringUtils.isNotBlank(rightsDetail.getTicketType())) {
                rightsDetail.setTicketTypeName(map.get(rightsDetail.getTicketType()));
            }

            if (null != rightsDetail.getTicketStartDatetime()) {
                rightsDetail.setTicketStartDatetimeStr(
                        DateUtil.dateToStr(rightsDetail.getTicketStartDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
            }

            if (null != rightsDetail.getTicketEndDatetime()) {
                rightsDetail.setTicketEndDatetimeStr(
                        DateUtil.dateToStr(rightsDetail.getTicketEndDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1));
            }
        }
        return collectionRightsDetailList;
    }

    @Override
    public List<CollectionRightsDetail> list(CollectionRightsDetail req) {
        return collectionRightsDetailMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询作品权益明细表
     *
     * @param id 主键ID
     * @return 作品权益明细表对象
     */
    @Override
    public CollectionRightsDetailDetailRes detailFront(Long id) {
        CollectionRightsDetailDetailRes res = new CollectionRightsDetailDetailRes();

        CollectionRightsDetail collectionRightsDetail = collectionRightsDetailMapper.selectByPrimaryKey(id);
        if (null == collectionRightsDetail) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(collectionRightsDetail, res);

        return res;
    }

    /**
     * 前端分页查询作品权益明细表
     *
     * @param req 前端分页查询作品权益明细表入参
     * @return 分页作品权益明细表对象
     */
    @Override
    public List<CollectionRightsDetailPageRes> pageFront(CollectionRightsDetailPageFrontReq req) {
        CollectionRightsDetail condition = EntityUtils.copyData(req, CollectionRightsDetail.class);
        List<CollectionRightsDetail> collectionRightsDetailList = collectionRightsDetailMapper.selectByCondition(condition);

        List<CollectionRightsDetailPageRes> resList = collectionRightsDetailList.stream().map((entity) -> {
            CollectionRightsDetailPageRes res = new CollectionRightsDetailPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionRightsDetailList, resList);
    }

    /**
     * 前端列表查询作品权益明细表
     *
     * @param req 前端列表查询作品权益明细表入参
     * @return 列表作品权益明细表对象
     */
    @Override
    public List<CollectionRightsDetailListRes> listFront(CollectionRightsDetailListFrontReq req, Long collectionDetailId) {
        CollectionRightsDetail condition = EntityUtils.copyData(req, CollectionRightsDetail.class);
        condition.setOrderBy("t.order_no desc,t.id asc");
        condition.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
        List<CollectionRightsDetail> collectionRightsDetailList = collectionRightsDetailMapper.selectByCondition(condition);

        Map<Long, String> companyMap = new HashMap<>();
        List<CollectionRightsDetailListRes> resList = collectionRightsDetailList.stream().map((entity) -> {
            CollectionRightsDetailListRes res = new CollectionRightsDetailListRes();
            BeanUtils.copyProperties(entity, res);

            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(res.getType())
                    || ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(res.getType())
                    || ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(res.getType())) {

//                List<Long> companyIdList = collectionRightCompanyService.listCompany(entity.getId());
//                String companyName = null;
//                for (Long companyId : companyIdList) {
//                    if (0L == companyId) {
//                        companyName = "任意发行方";
//                    } else {
//                        String companyStr = companyMap.get(companyId);
//                        if (StringUtils.isBlank(companyStr)) {
//                            Company company = companyService.detailSimple(companyId);
//                            companyStr = company.getName();
//                            companyMap.put(companyId, companyStr);
//                        }
//                        if (null == companyName) {
//                            companyName = companyStr;
//                        } else {
//                            companyName = companyName + "、" + companyStr;
//                        }
//                    }
//                }

                String content = "";
//                if (StringUtils.isNotBlank(companyName)) {
//                    content = "发行方：".concat(companyName).concat("\n");
//                }

//                String plateCategoryName = null;
//                List<String> plateCategoryList = collectionRightCompanyService.listPlateCategory(entity.getId());
//
//                for (String platCategory : plateCategoryList) {
//                    if (EBoolean.NO.getCode().equals(platCategory)) {
//                        plateCategoryName = "任意板块";
//                    } else {
//
//                        if (null == plateCategoryName) {
//                            plateCategoryName = ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory).getValue();
//                        } else {
//                            plateCategoryName =
//                                    plateCategoryName + "、" + ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory)
//                                            .getValue();
//                        }
//                    }
//                }
//                if (StringUtils.isNotBlank(plateCategoryName)) {
//                    content = content.concat("板块：" + plateCategoryName).concat("\n");
//                }

                if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(res.getType())) {
                    content = content.concat("优先时间：" + entity.getAdvanceMins() + "分钟").concat("\n");
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(res.getType())) {
                    String rate = entity.getDiscountRate().multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_DOWN)
                            .stripTrailingZeros().toPlainString();
                    content = content.concat("折扣：" + rate + "折").concat("\n");
                }
                if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode().equals(entity.getNumberFlag())) {
                    content = content.concat("总次数：无限次");
                } else {
                    content = content.concat("总次数：" + entity.getTotalNumber() + "次（" + "剩余" + entity.getRemainNumber() + "次" + "）");
                }
                if (StringUtils.isNotBlank(entity.getContent())) {
                    content = content.concat("\n").concat(entity.getContent());
                }
                res.setContent(content);
            } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(res.getType())) {
//                Dict dict = dictService.detail(SysConstantsDict.METAVERSE_TICKET_TYPE, entity.getTicketType());
                String content = "";
                if (StringUtils.isNotBlank(entity.getContent())) {
                    content = "元宇宙进城门票".concat("\n").concat(entity.getContent());
                } else {
                    content = "元宇宙进城门票";
                }

                if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(entity.getTicketType())) {
                    if (null != collectionDetailId) {
                        MetaTicketRecord ticketRecord = new MetaTicketRecord();
                        ticketRecord.setCollectionDetailId(collectionDetailId);
                        ticketRecord.setTicketType(entity.getTicketType());
                        List<MetaTicketRecord> list = metaTicketRecordService.list(ticketRecord);
                        String remainNumberNote = "";
                        if (entity.getTotalNumber() - list.size() > 0) {
                            remainNumberNote = "(剩余" + (entity.getTotalNumber() - list.size()) + "次)";
                        } else {
                            remainNumberNote = "(已使用完毕)";
                        }
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次").concat(remainNumberNote);
                    } else {
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次");
                    }

                } else if (EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(entity.getTicketType())) {
                    String ticketTimeNote =
                            "每次有效时长" + new BigDecimal(entity.getTicketTime()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_DOWN)
                                    .stripTrailingZeros().toPlainString() + "小时，每次有效时长内可重复进入";
                    if (null != collectionDetailId) {
                        MetaTicketRecord ticketRecord = new MetaTicketRecord();
                        ticketRecord.setCollectionDetailId(collectionDetailId);
                        ticketRecord.setTicketType(entity.getTicketType());
                        List<MetaTicketRecord> list = metaTicketRecordService.list(ticketRecord);
                        String remainNumberNote = "";
                        if (entity.getTotalNumber() - list.size() > 0) {
                            remainNumberNote = "(剩余" + (entity.getTotalNumber() - list.size()) + "次)";
                        } else {
                            remainNumberNote = "(已使用完毕)";
                        }
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次，").concat(ticketTimeNote)
                                .concat(remainNumberNote);
                    } else {
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次，").concat(ticketTimeNote);
                    }

                } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(entity.getTicketType())) {
                    content = content.concat("\n").concat("可于" + DateUtil
                            .dateToStr(entity.getTicketStartDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "--" + DateUtil
                            .dateToStr(entity.getTicketEndDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "进入元宇宙，不限次数");
                }
//                else if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(entity.getTicketType())) {
//                    content = content.concat("\n").concat("持有者可永久进入元宇宙，不限时间次数");
//                }

                res.setContent(content);
            }
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public void modify(CollectionRightsDetail rightsDetail) {
        collectionRightsDetailMapper.updateByPrimaryKeySelective(rightsDetail);
    }

    @Override
    public List<CollectionRightsDetailListRes> listFrontNotRemain(CollectionRightsDetailListFrontReq req, Long collectionDetailId) {
        CollectionRightsDetail condition = EntityUtils.copyData(req, CollectionRightsDetail.class);
        condition.setOrderBy("t.order_no desc,t.id asc");
        condition.setCreateType(ECollectionRightsDetailCreateType.E_COLLECTION_RIGHTS_DETAIL_CREATE_TYPE_0.getCode());
        List<CollectionRightsDetail> collectionRightsDetailList = collectionRightsDetailMapper.selectByCondition(condition);

        Map<Long, String> companyMap = new HashMap<>();
        List<CollectionRightsDetailListRes> resList = collectionRightsDetailList.stream().map((entity) -> {
            CollectionRightsDetailListRes res = new CollectionRightsDetailListRes();
            BeanUtils.copyProperties(entity, res);

            if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(res.getType())
                    || ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(res.getType())
                    || ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(res.getType())) {

                String content = "";
//                List<Long> companyIdList = collectionRightCompanyService.listCompany(entity.getId());
//                String companyName = null;
//                for (Long companyId : companyIdList) {
//                    if (0L == companyId) {
//                        companyName = "任意发行方";
//                    } else {
//                        String companyStr = companyMap.get(companyId);
//                        if (StringUtils.isBlank(companyStr)) {
//                            Company company = companyService.detailSimple(companyId);
//                            companyStr = company.getName();
//                            companyMap.put(companyId, companyStr);
//                        }
//                        if (null == companyName) {
//                            companyName = companyStr;
//                        } else {
//                            companyName = companyName + "、" + companyStr;
//                        }
//                    }
//                }
//
//                if (StringUtils.isNotBlank(companyName)) {
//                    content = "发行方：".concat(companyName).concat("\n");
//                }

//                String plateCategoryName = null;
//                List<String> plateCategoryList = collectionRightCompanyService.listPlateCategory(entity.getId());
//
//                for (String platCategory : plateCategoryList) {
//                    if (EBoolean.NO.getCode().equals(platCategory)) {
//                        plateCategoryName = "任意板块";
//                    } else {
//
//                        if (null == plateCategoryName) {
//                            plateCategoryName = ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory).getValue();
//                        } else {
//                            plateCategoryName =
//                                    plateCategoryName + "、" + ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(platCategory)
//                                            .getValue();
//                        }
//                    }
//                }
//
//                if (StringUtils.isNotBlank(plateCategoryName)) {
//                    content = content.concat("板块：" + plateCategoryName).concat("\n");
//                }

                if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(res.getType())) {
                    content = content.concat("优先时间：" + entity.getAdvanceMins() + "分钟").concat("\n");
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(res.getType())) {
                    String rate = entity.getDiscountRate().multiply(new BigDecimal(10)).setScale(2, BigDecimal.ROUND_DOWN)
                            .stripTrailingZeros().toPlainString();
                    content = content.concat("折扣：" + rate + "折").concat("\n");
                }

                if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode().equals(entity.getNumberFlag())) {
                    content = content.concat("总次数：无限次");
                } else {
                    content = content.concat("总次数：" + entity.getTotalNumber() + "次");
                }
                if (StringUtils.isNotBlank(entity.getContent())) {
                    content = content.concat("\n").concat(entity.getContent());
                }
                res.setContent(content);
            } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(res.getType())) {

                String content = "";
                if (StringUtils.isNotBlank(entity.getContent())) {
                    content = "元宇宙进城门票".concat("\n").concat(entity.getContent());
                } else {
                    content = "元宇宙进城门票";
                }

                if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(entity.getTicketType())) {
                    if (null != collectionDetailId) {
                        MetaTicketRecord ticketRecord = new MetaTicketRecord();
                        ticketRecord.setCollectionDetailId(collectionDetailId);
                        ticketRecord.setTicketType(entity.getTicketType());
                        List<MetaTicketRecord> list = metaTicketRecordService.list(ticketRecord);
                        String remainNumberNote = "";
                        if (entity.getTotalNumber() - list.size() > 0) {
                            remainNumberNote = "(剩余" + (entity.getTotalNumber() - list.size()) + "次)";
                        } else {
                            remainNumberNote = "(已使用完毕)";
                        }
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次").concat(remainNumberNote);
                    } else {
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次");
                    }

                } else if (EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(entity.getTicketType())) {
                    String ticketTimeNote =
                            "每次有效时长" + new BigDecimal(entity.getTicketTime()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_DOWN)
                                    .stripTrailingZeros().toPlainString() + "小时，每次有效时长内可重复进入";
                    if (null != collectionDetailId) {
                        MetaTicketRecord ticketRecord = new MetaTicketRecord();
                        ticketRecord.setCollectionDetailId(collectionDetailId);
                        ticketRecord.setTicketType(entity.getTicketType());
                        List<MetaTicketRecord> list = metaTicketRecordService.list(ticketRecord);
                        String remainNumberNote = "";
                        if (entity.getTotalNumber() - list.size() > 0) {
                            remainNumberNote = "(剩余" + (entity.getTotalNumber() - list.size()) + "次)";
                        } else {
                            remainNumberNote = "(已使用完毕)";
                        }
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次，").concat(ticketTimeNote)
                                .concat(remainNumberNote);
                    } else {
                        content = content.concat("\n").concat("持有者可凭本入场券进入麦塔元宇宙" + entity.getTotalNumber() + "次，").concat(ticketTimeNote);
                    }

                } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(entity.getTicketType())) {
                    content = content.concat("\n").concat("可于" + DateUtil
                            .dateToStr(entity.getTicketStartDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "--" + DateUtil
                            .dateToStr(entity.getTicketEndDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1) + "进入元宇宙，不限次数");
                }
//                else if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(entity.getTicketType())) {
//                    content = content.concat("\n").concat("持有者可永久进入元宇宙，不限时间次数");
//                }

                res.setContent(content);
            }
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public List<CollectionRightsDetailHistory> listHistoryDate(Long collectionId) {
        return collectionRightsDetailMapper.selectHistoryDate(collectionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealHistoryDate() {
        List<Long> collectionIdList = collectionRightsDetailMapper.selectHistoryDateCollectionId();
        for (Long collectionId : collectionIdList) {
            Collection collection = collectionService.detailSimple(collectionId);
            List<CollectionRightsDetailCreateAutoReq> collectionRightsCreateAutoReqList = new ArrayList<>();

            List<CollectionRightsDetailHistory> collectionRightsDetailHistories = listHistoryDate(collectionId);

            for (CollectionRightsDetailHistory detailHistory : collectionRightsDetailHistories) {
                CollectionRightsDetailCreateAutoReq autoReq = new CollectionRightsDetailCreateAutoReq();
                List<String> list = Arrays.asList(detailHistory.getCompany().split(","));

                List<Long> companyIdList = new ArrayList<>();
                for (String company : list) {
                    companyIdList.add(Long.parseLong(company));
                }
                autoReq.setCompanyIdList(companyIdList);
                List<String> plateCategoryList = new ArrayList<>();
                plateCategoryList.add(detailHistory.getPlateCategory());
                autoReq.setPlateCategoryList(plateCategoryList);
                autoReq.setType(detailHistory.getRightType());
                autoReq.setContent(detailHistory.getContent());
                if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(detailHistory.getRightType())) {
                    autoReq.setTicketType("0");
                }
                autoReq.setNumberFlag(ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode());
                autoReq.setTotalNumber(detailHistory.getTotalNumber());
//                autoReq.setRemainNumber(detailHistory.getRemainNumber());
                autoReq.setAdvanceMins(detailHistory.getAdvanceMins());
                autoReq.setDiscountRate(detailHistory.getDiscountRate());

                collectionRightsCreateAutoReqList.add(autoReq);
            }

            createTest(collection, collectionRightsCreateAutoReqList);
        }
    }

    @Override
    public List<CollectionRightsMyTicketPageRes> myTicketpageFront(CollectionRightsMyTicketPageFrontReq req, User operator) {
        MetaTicketMyReq request = new MetaTicketMyReq();
        if (EBoolean.NO.getCode().equals(req.getIsFailure())) {
            request.setUnFailure(EBoolean.YES.getCode());
        } else {
            request.setFailure(EBoolean.YES.getCode());
        }
        request.setDate(new Date());
        request.setOwnerId(operator.getId());

        List<CollectionRightsMyTicketPageRes> metaTicketList = collectionRightsDetailMapper.selectMyMetaTicket(request);

        for (CollectionRightsMyTicketPageRes res : metaTicketList) {

            if (null != res.getTicketTimeInt()) {
                res.setTicketTime(
                        new BigDecimal(res.getTicketTimeInt()).divide(new BigDecimal(60), 2, BigDecimal.ROUND_DOWN).stripTrailingZeros()
                                .toPlainString());
            }

            if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(res.getType())) {
                res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_0.getCode());
            } else if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(res.getType()) || EMetaTicketType.META_TICKET_TYPE_2.getCode()
                    .equals(res.getType())) {
                if (1 > res.getRemainNumber()) {
                    res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_2.getCode());
                } else {
                    res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_0.getCode());
                }
            } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(res.getType())) {
                if (new Date().before(res.getTicketStartDatetime())) {
                    res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_1.getCode());
                } else if (new Date().after(res.getTicketStartDatetime()) && new Date().before(res.getTicketEndDatetime())) {
                    res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_0.getCode());
                } else if (new Date().after(res.getTicketEndDatetime())) {
                    res.setStatus(EMetaTicketMyStatus.E_META_TICKET_STATUS_3.getCode());
                }
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "类型错误");
            }
        }

        return metaTicketList;
    }

    @Override
    public CollectionRightsDetailTicketCheckRes checkTicket(User operator) {
        CollectionRightsDetailTicketCheckRes res = new CollectionRightsDetailTicketCheckRes();
        res.setPhoto(operator.getPhoto());

        if (checkTicketIsHave(operator)) {
            res.setTicketFlag("2");
            res.setTicketNote("您暂未拥有元宇宙进城门票，不可进入");
            return res;
        }

        CollectionDetail collectionDetail = new CollectionDetail();
        collectionDetail.setOwnerType(ECollectionDetailOwnerType.CUSER.getCode());
        collectionDetail.setOwnerId(operator.getId());
        List<String> stringList = new ArrayList<>();
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_4.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_11.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_17.getCode());
        collectionDetail.setNoStatusList(stringList);
        List<CollectionDetail> collectionDetailList = new ArrayList<>();
        List<Long> collectionIdList = new ArrayList<>();

        // 查询是否有无限次数的门票
        CollectionRightsDetail condition = new CollectionRightsDetail();
        condition.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
        condition.setTicketType(EMetaTicketType.META_TICKET_TYPE_0.getCode());
        List<CollectionRightsDetail> ticketList = collectionRightsDetailMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(ticketList)) {
            collectionIdList = ticketList.stream().map(x -> {
                return x.getCollectionId();
            }).collect(Collectors.toList());

            collectionDetail.setCollectionIdList(collectionIdList);
            collectionDetailList = collectionDetailService.list(collectionDetail);

            if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                res.setTicketFlag(EBoolean.NO.getCode());
                return res;
            }
        }

        // 查询是否拥有固定时效且没时效的藏品
        List<CollectionRightsDetail> metaTicketList = detailTypeTwoCanUseTicket();

        if (CollectionUtils.isNotEmpty(metaTicketList)) {
            collectionIdList.clear();
            collectionIdList = metaTicketList.stream().map(x -> {
                return x.getCollectionId();
            }).collect(Collectors.toList());

            collectionDetail.setCollectionIdList(collectionIdList);
            collectionDetailList = collectionDetailService.list(collectionDetail);

            if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                res.setTicketFlag(EBoolean.NO.getCode());
                return res;
            }
        }

        // 查询是否拥有单次有效且还未过期的门票
        MetaTicketRecord ticketRecord = new MetaTicketRecord();
        ticketRecord.setTicketType(EMetaTicketType.META_TICKET_TYPE_2.getCode());
        ticketRecord.setStatus(EMetaTicketRecordStatus.META_TICKET_RECORD_STATUS_1.getCode());
        List<MetaTicketRecord> ticketRecords = metaTicketRecordService.list(ticketRecord);
        List<Long> collectionDetailIdList = ticketRecords.stream().map(x -> {
            return x.getCollectionDetailId();
        }).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(collectionDetailIdList)) {
            collectionDetail.setIdList(collectionDetailIdList);
            collectionDetail.setCollectionIdList(null);
            collectionDetailList.clear();
            collectionDetailList = collectionDetailService.list(collectionDetail);

            if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                res.setTicketFlag(EBoolean.NO.getCode());
                return res;
            }
        }

        res.setTicketFlag(EBoolean.YES.getCode());
        return res;
    }

    private boolean checkTicketIsHave(User operator) {
        CollectionRightsDetail condition = new CollectionRightsDetail();
        condition.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
        List<CollectionRightsDetail> ticketList = collectionRightsDetailMapper.selectByCondition(condition);

        List<Long> collectionIdList = ticketList.stream().map(x -> {
            return x.getCollectionId();
        }).collect(Collectors.toList());

        CollectionDetail collectionDetail = new CollectionDetail();
        collectionDetail.setOwnerType(ECollectionDetailOwnerType.CUSER.getCode());
        collectionDetail.setOwnerId(operator.getId());
        collectionDetail.setCollectionIdList(collectionIdList);
        List<String> stringList = new ArrayList<>();
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_4.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_11.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_17.getCode());
        collectionDetail.setNoStatusList(stringList);
        int count = collectionDetailService.countByCondition(collectionDetail);

        if (count < 1) {
            return true;
        }

        return false;
    }

    /**
     * 进入元宇宙
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CollectionRightsDeatilTicketEnterMetaRes enterMeta(CollectionRightsDeatilTicketEnterMetaReq request, User operator) {
        CollectionRightsDeatilTicketEnterMetaRes res = new CollectionRightsDeatilTicketEnterMetaRes();

        res.setRewardTime(-1);
        // 获取元宇宙在线奖励时长
        YaoTaskConfig yaoTaskConfig = yaoTaskConfigService.detailByType(EYaoTaskConfigType.YAO_TASK_CONFIG_TYPE_1.getCode());
        if (null != yaoTaskConfig && EYaoTaskConfigStatus.YAO_TASK_CONFIG_STATUS_1.getCode().equals(yaoTaskConfig.getStatus())) {
            res.setRewardTime(yaoTaskConfig.getOnlineDuration());
        }

        CollectionDetail detail = new CollectionDetail();
        detail.setOwnerType(ECollectionDetailOwnerType.CUSER.getCode());
        detail.setOwnerId(operator.getId());
        List<String> stringList = new ArrayList<>();
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_4.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_11.getCode());
        stringList.add(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_17.getCode());
        detail.setNoStatusList(stringList);
        List<CollectionDetail> collectionDetailList = new ArrayList<>();
        List<Long> collectionIdList = new ArrayList<>();

        // 查询是否有无限次数的门票
        CollectionRightsDetail condition = new CollectionRightsDetail();
        condition.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
        condition.setTicketType(EMetaTicketType.META_TICKET_TYPE_0.getCode());
        List<CollectionRightsDetail> ticketList = collectionRightsDetailMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(ticketList)) {
            collectionIdList = ticketList.stream().map(x -> {
                return x.getCollectionId();
            }).collect(Collectors.toList());

            detail.setCollectionIdList(collectionIdList);
            collectionDetailList = collectionDetailService.list(detail);

            if (CollectionUtils.isNotEmpty(collectionDetailList)) {
                res.setTimeShowFlag(EBoolean.NO.getCode());

                // 记录进入元宇宙
                userEntryRecordService.create(operator, EMetaTicketType.META_TICKET_TYPE_0.getCode(), null);
                return res;
            }
        }

        // 查询是否拥有固定时效且没时效的藏品
        List<CollectionRightsDetail> metaTicketList = detailTypeTwoCanUseTicketByUser(operator.getId());

        if (CollectionUtils.isNotEmpty(metaTicketList)) {
            Date date = null;
            Long ticketId = null;
            for (CollectionRightsDetail metaTicket : metaTicketList) {

                if (null == date || date.before(metaTicket.getTicketEndDatetime())) {
                    date = metaTicket.getTicketEndDatetime();
                    ticketId = metaTicket.getId();
                }
            }

            res.setTimeShowFlag(EBoolean.YES.getCode());
            long between = com.ais.common.utils.DateUtil.minuteBetween(new Date(), date);
            if (between <= 1L) {
                between = 1L;
            }

            String time = dealTime(between);
            res.setShowNote(time);

            // 记录进入元宇宙
            userEntryRecordService.create(operator, EMetaTicketType.META_TICKET_TYPE_3.getCode(), ticketId);

            return res;
        }

        // 查询是否拥有单次有效且还未过期的门票
        List<MetaTicketRecord> ticketRecords = metaTicketRecordService.detailNoFailureTicketByUser(operator.getId());

        if (CollectionUtils.isNotEmpty(ticketRecords)) {
            Date date = null;
            Long ticketId = null;
            for (MetaTicketRecord ticketRecord : ticketRecords) {
                if (null == date || date.before(ticketRecord.getFailureDatetime())) {
                    date = ticketRecord.getFailureDatetime();
                    ticketId = ticketRecord.getRightId();
                }
            }

            res.setTimeShowFlag(EBoolean.YES.getCode());
            long between = com.ais.common.utils.DateUtil.minuteBetween(new Date(), date);
            if (between <= 1L) {
                between = 1L;
            }
            String time = dealTime(between);
            res.setShowNote(time);

            // 记录进入元宇宙
            userEntryRecordService.create(operator, EMetaTicketType.META_TICKET_TYPE_3.getCode(), ticketId);
            return res;
        }

        if (null == request.getId()) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择门票");
        }

        CollectionDetail collectionDetail = collectionDetailService.detailForUpdate(request.getId());
        if (!ECollectionDetailOwnerType.CUSER.getCode().equals(collectionDetail.getOwnerType())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择本人可用门票");
        }
        if (!operator.getId().equals(collectionDetail.getOwnerId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择自己的门票");
        }
        if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_4.getCode().equals(collectionDetail.getStatus()) ||
                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_6.getCode().equals(collectionDetail.getStatus()) ||
                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_11.getCode().equals(collectionDetail.getStatus()) ||
                ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_17.getCode().equals(collectionDetail.getStatus())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择可用门票");
        }

        CollectionRightsDetail rightsDetail = new CollectionRightsDetail();
        rightsDetail.setCollectionId(collectionDetail.getCollectionId());
        rightsDetail.setType(ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode());
        List<CollectionRightsDetail> metaTickets = collectionRightsDetailMapper.selectByCondition(rightsDetail);
        if (CollectionUtils.isEmpty(metaTickets)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请选择正确门票");
        }

        CollectionRightsDetail metaTicket = metaTickets.get(0);

        if (EMetaTicketType.META_TICKET_TYPE_0.getCode().equals(metaTicket.getTicketType())) {
            res.setTimeShowFlag(EBoolean.NO.getCode());
            return res;
        } else if (EMetaTicketType.META_TICKET_TYPE_1.getCode().equals(metaTicket.getTicketType())
                || EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(metaTicket.getTicketType())) {
            MetaTicketRecord ticketRecord = new MetaTicketRecord();
            ticketRecord.setTicketType(metaTicket.getTicketType());
            ticketRecord.setCollectionDetailId(collectionDetail.getId());
            List<MetaTicketRecord> list = metaTicketRecordService.list(ticketRecord);

            if (metaTicket.getTotalNumber() <= list.size()) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "该门票次数已用完");
            }

            MetaTicketRecord record = new MetaTicketRecord();
            record.setRightId(metaTicket.getId());
            record.setTicketType(EMetaTicketType.META_TICKET_TYPE_1.getCode());
            record.setCollectionDetailId(collectionDetail.getId());
            record.setStatus(EMetaTicketRecordStatus.META_TICKET_RECORD_STATUS_0.getCode());
            record.setCreater(operator.getId());
            record.setCreateDatetime(new Date());

            // 记录进入元宇宙
            userEntryRecordService.create(operator, metaTicket.getTicketType(), metaTicket.getId());

            if (EMetaTicketType.META_TICKET_TYPE_2.getCode().equals(metaTicket.getTicketType())) {
                record.setTicketType(EMetaTicketType.META_TICKET_TYPE_2.getCode());
                Calendar calendar = Calendar.getInstance();
                calendar.add(Calendar.MINUTE, metaTicket.getTicketTime());
                record.setFailureDatetime(calendar.getTime());
                record.setStatus(EMetaTicketRecordStatus.META_TICKET_RECORD_STATUS_1.getCode());
                metaTicketRecordService.create(record);

                res.setTimeShowFlag(EBoolean.YES.getCode());
                String time = dealTime(metaTicket.getTicketTime());
                res.setShowNote(time);
                return res;
            } else {
                metaTicketRecordService.create(record);
                res.setTimeShowFlag(EBoolean.NO.getCode());
                return res;
            }

        } else if (EMetaTicketType.META_TICKET_TYPE_3.getCode().equals(metaTicket.getTicketType())) {
            Date date = new Date();
            if (date.before(metaTicket.getTicketStartDatetime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "还未到开放时间");
            } else if (date.after(metaTicket.getTicketEndDatetime())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "门票已失效");
            }

            res.setTimeShowFlag(EBoolean.YES.getCode());
            long between = com.ais.common.utils.DateUtil.minuteBetween(new Date(), metaTicket.getTicketEndDatetime());
            if (between <= 1L) {
                between = 1L;
            }
            String time = dealTime(between);
            res.setShowNote(time);

            // 记录进入元宇宙
            userEntryRecordService.create(operator, metaTicket.getTicketType(), metaTicket.getId());
            return res;
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "类型错误的门票");
        }
    }

    private String dealTime(long between) {
        String showNote = "";
        long day = between / (60 * 24);
        if (day > 0) {
            showNote = day + "天";
        }
        long hourBetween = between - (day * 60 * 24);
        long hour = hourBetween / 60;
        if (hour > 0) {
            showNote = showNote + hour + "小时";
        }

        long minutes = hourBetween - (day * 60 * 24) - (hour * 60);
        if (minutes > 0) {
            showNote = showNote + minutes + "分钟";
        }
        showNote = "本次体验机会剩余" + showNote;

        return showNote;
    }

    private List<CollectionRightsDetail> detailTypeTwoCanUseTicketByUser(Long userId) {
        return collectionRightsDetailMapper.selectTypeTwoCanUseTicketByUser(new Date(), userId);
    }

    @Override
    public List<TicketType> ticketTypeList(User operator, TicketTypeListReq request) {

        Company company = companyService.checkCompany(operator.getCompanyId());
        List<TicketType> resList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();

        if (EBoolean.YES.getCode().equals(company.getPlatFlag()) && ECollectionCategory.NOT_COLLECTION.getCode()
                .equals(request.getCategory())) {
            map.put(EMetaTicketType.META_TICKET_TYPE_1.getCode(), EMetaTicketType.META_TICKET_TYPE_1.getValue());
        } else {

            map.put(EMetaTicketType.META_TICKET_TYPE_2.getCode(), EMetaTicketType.META_TICKET_TYPE_2.getValue());
            map.put(EMetaTicketType.META_TICKET_TYPE_3.getCode(), EMetaTicketType.META_TICKET_TYPE_3.getValue());

            if (504104039689232384L == company.getId()) {
                map.put(EMetaTicketType.META_TICKET_TYPE_0.getCode(), EMetaTicketType.META_TICKET_TYPE_0.getValue());
            }

        }

        for (String ticketType : map.keySet()) {
            TicketType res = new TicketType();
            res.setTicketType(ticketType);
            res.setTicketTypeName(map.get(ticketType));
            resList.add(res);
        }

        return resList;
    }

    @Override
    public void removeByRecord(Long refId, String type) {
        collectionRightsDetailMapper.deleteByRecord(refId, type);
    }

    private List<CollectionRightsDetail> detailTypeTwoCanUseTicket() {

        return collectionRightsDetailMapper.selectTypeTwoCanUseTicket(new Date());
    }

    private void createTest(Collection collection, List<CollectionRightsDetailCreateAutoReq> collectionRightsCreateAutoReqList) {

        //根据作品删除原有权益数据
        collectionRightCompanyService.removeByCollectionId(collection.getId());
        collectionRightsDetailMapper.deleteByCollectionId(collection.getId());

        List<String> rightTypeList = new ArrayList<>();
        if (null == collectionRightsCreateAutoReqList) {
            return;
        }
        for (CollectionRightsDetailCreateAutoReq rightReq : collectionRightsCreateAutoReqList) {
            CollectionRightsDetail collectionRightDetail = new CollectionRightsDetail();
            ECollectionRightSpecificType collectionRightSpecificTypeType = ECollectionRightSpecificType
                    .getCollectionRightSpecificTypeType(rightReq.getType());
            collectionRightDetail.setType(rightReq.getType());
            Dict dict = dictService.detail(SysConstantsDict.COLLECTION_RIGHT_DETAIL_TYPE, rightReq.getType());
            collectionRightDetail.setName(dict.getValue());
            collectionRightDetail.setContent(rightReq.getContent());
            collectionRightDetail.setCollectionId(collection.getId());

            if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType()) && rightTypeList.contains(rightReq.getType())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                        collectionRightSpecificTypeType.getValue() + "权益重复:" + collection.getId());
            }
            rightTypeList.add(rightReq.getType());

            if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())) {
                if (!ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                    if (StringUtils.isBlank(rightReq.getNumberFlag()) ||
                            (!ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                                    .equals(rightReq.getNumberFlag())
                                    && !ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                                    .equals(rightReq.getNumberFlag()))) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "次数标识错误");
                    } else if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_0.getCode()
                            .equals(rightReq.getNumberFlag()) && (null == rightReq.getTotalNumber() || 1 > rightReq.getTotalNumber())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "权益总次数不能为空");
                    } else if (ECollectionRightsDetailNumberType.E_COLLECTION_RIGHTS_DETAIL_NUMBER_TYPE_1.getCode()
                            .equals(rightReq.getNumberFlag())) {
                        rightReq.setTotalNumber(0);
                    }

                    collectionRightDetail.setNumberFlag(rightReq.getNumberFlag());
                }

                if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_0.getCode().equals(rightReq.getType())) {
                    if (ECollectionCategory.COPYRIGHT.getCode().equals(collection.getCategory())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                                collectionRightSpecificTypeType.getValue() + "版权区作品不能拥有空投权益");
                    }
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setDropNumber(1);
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_1.getCode().equals(rightReq.getType())) {
                    collectionRightDetail.setAdvanceMins(rightReq.getAdvanceMins());
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_2.getCode().equals(rightReq.getType())) {
                    collectionRightDetail.setDiscountRate(rightReq.getDiscountRate());
                    collectionRightDetail.setTotalNumber(rightReq.getTotalNumber());
                    collectionRightDetail.setRemainNumber(rightReq.getTotalNumber());
                } else if (ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                    collectionRightDetail.setTicketType(rightReq.getTicketType());
                }
            } else if (StringUtils.isBlank(rightReq.getContent())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益说明不能为空");
            }
            collectionRightsDetailMapper.insertSelective(collectionRightDetail);

            if (EBoolean.YES.getCode().equals(collectionRightSpecificTypeType.getType())
                    && !ECollectionRightSpecificType.E_COLLECTION_RIGHT_SPECIFIC_TYPE_3.getCode().equals(rightReq.getType())) {
                HashSet<Long> companyIdList = new HashSet<>(rightReq.getCompanyIdList());
                HashSet<String> plateCategoryList = new HashSet<>(rightReq.getPlateCategoryList());

                if (CollectionUtils.isEmpty(companyIdList)) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(),
                            collectionRightSpecificTypeType.getValue() + "权益发行方不能为空");
                }
                if (CollectionUtils.isEmpty(companyIdList)) {
                    throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), collectionRightSpecificTypeType.getValue() + "权益板块不能为空");
                }
                List<CollectionRightCompany> rightCompanyList = new ArrayList<>();
                for (Long companyId : companyIdList) {
                    // todo 选择权限
                    if (0L != companyId) {
                        Company company = companyService.detailSimple(companyId);
                        if (!ECompanyStatus.PUT_ON.getCode().equals(company.getStatus()) && !ECompanyStatus.INFO_MODIFT.getCode()
                                .equals(company.getStatus())) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方" + company.getName() + "不可选择");
                        }
                    } else if (0L == companyId && companyIdList.size() > 1) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方选择全部后无需再选其他发行方");
                    }

                    for (String plateCategory : plateCategoryList) {
                        CollectionRightCompany rightCompany = new CollectionRightCompany();
                        if (!EBoolean.NO.getCode().equals(plateCategory)) {
                            ECollectionPeriodPlateCategory.getCollectionPeriodPlateCategory(plateCategory);
                        } else if (EBoolean.NO.getCode().equals(plateCategory) && plateCategoryList.size() > 1) {
                            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "板块选择全部后无需再选其他发行方");
                        }

                        if (companyIdList.size() > 1 && 497533073408401410L != companyId) {
//                            rightCompany.setNumber(rightReq.getTotalNumber() - rightReq.getRemainNumber());
                        }
                        rightCompany.setPlateCategory(plateCategory);
                        rightCompany.setCompanyId(companyId);
                        rightCompany.setRefId(collectionRightDetail.getId());
                        rightCompany.setRefType(rightReq.getType());
                        rightCompanyList.add(rightCompany);
                    }
                }

                if (CollectionUtils.isNotEmpty(rightCompanyList)) {
                    collectionRightCompanyService.createBatch(rightCompanyList);
                }
            }
        }
    }

}