package com.std.core.service.impl;

import com.ais.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.YaoMilletConfigMapper;
import com.std.core.pojo.domain.YaoMilletConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.YaoMilletConfigRecord;
import com.std.core.pojo.domain.YaoMilletOrderDetail;
import com.std.core.pojo.request.YaoMilletConfigCreateReq;
import com.std.core.pojo.request.YaoMilletConfigListReq;
import com.std.core.pojo.request.YaoMilletConfigListFrontReq;
import com.std.core.pojo.request.YaoMilletConfigModifyReq;
import com.std.core.pojo.request.YaoMilletConfigPageReq;
import com.std.core.pojo.request.YaoMilletConfigPageFrontReq;
import com.std.core.pojo.response.AccountDiamondExchangeMilletRes;
import com.std.core.pojo.response.YaoMilletConfigDetailRes;
import com.std.core.pojo.response.YaoMilletConfigListRes;
import com.std.core.pojo.response.YaoMilletConfigPageRes;
import com.std.core.service.IYaoMilletConfigRecordService;
import com.std.core.service.IYaoMilletConfigService;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import com.std.core.service.IYaoMilletOrderDetailService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 元粟配置ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-11-09 20:39
 */
@Service
public class YaoMilletConfigServiceImpl implements IYaoMilletConfigService {

    @Resource
    private YaoMilletConfigMapper yaoMilletConfigMapper;

    @Resource
    private IYaoMilletConfigRecordService yaoMilletConfigRecordService;

    @Resource
    private IYaoMilletOrderDetailService yaoMilletOrderDetailService;

    /**
     * 新增元粟配置
     *
     * @param req      新增元粟配置入参
     * @param operator 操作人
     */
    @Override
    public void create(YaoMilletConfigCreateReq req, User operator) {
        YaoMilletConfig yaoMilletConfig = EntityUtils.copyData(req, YaoMilletConfig.class);
        yaoMilletConfigMapper.insertSelective(yaoMilletConfig);
    }

    /**
     * 删除元粟配置
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        yaoMilletConfigMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改元粟配置
     *
     * @param req      修改元粟配置入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(YaoMilletConfigModifyReq req, User operator) {
        YaoMilletConfig milletConfig = detail(req.getId());
        if (milletConfig.getDayMax().compareTo(req.getDayMax()) != 0) {
            int dateNumber = yaoMilletConfigRecordService.createDateNumber();
            YaoMilletConfigRecord configRecord = yaoMilletConfigRecordService.detailByDateNumberForUpdate(dateNumber, milletConfig.getCurrency());

            if (req.getDayMax().compareTo(configRecord.getTotalQuantity().subtract(configRecord.getRemainQuantity())) < 0) {
                req.setDayMax(configRecord.getTotalQuantity().subtract(configRecord.getRemainQuantity()));
            }

            BigDecimal subtract = milletConfig.getDayMax().subtract(req.getDayMax());

            configRecord.setTotalQuantity(req.getDayMax());
            configRecord.setRemainQuantity(configRecord.getRemainQuantity().subtract(subtract));
            yaoMilletConfigRecordService.modify(configRecord);
        }
        YaoMilletConfig yaoMilletConfig = EntityUtils.copyData(req, YaoMilletConfig.class);
        yaoMilletConfig.setUpdater(operator.getId());
        yaoMilletConfig.setUpdaterName(operator.getLoginName());
        yaoMilletConfig.setUpdateDatetime(new Date());
        yaoMilletConfigMapper.updateByPrimaryKeySelective(yaoMilletConfig);
    }

    /**
     * 详情查询元粟配置
     *
     * @param id 主键ID
     * @return 元粟配置对象
     */
    @Override
    public YaoMilletConfig detail(Long id) {
        YaoMilletConfig yaoMilletConfig = yaoMilletConfigMapper.selectByPrimaryKey(id);
        if (null == yaoMilletConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        int dateNumber = yaoMilletConfigRecordService.createDateNumber();
        YaoMilletConfigRecord milletConfigRecord = yaoMilletConfigRecordService.detailByDateNumberForUpdate(dateNumber, yaoMilletConfig.getCurrency());
        YaoMilletOrderDetail yaoMilletOrderDetail = yaoMilletOrderDetailService.detailTotalByMillet(yaoMilletConfig.getCurrency());
        yaoMilletConfig.setTotalYinYao(yaoMilletOrderDetail.getYinYao());
        yaoMilletConfig.setTotalYangYao(yaoMilletOrderDetail.getYangYao());
        yaoMilletConfig.setQuantity(yaoMilletOrderDetail.getQuantity());
        yaoMilletConfig.setTodayRemainQuantity(milletConfigRecord.getRemainQuantity());
        return yaoMilletConfig;
    }

    /**
     * 分页查询元粟配置
     *
     * @param req 分页查询元粟配置入参
     * @return 分页元粟配置对象
     */
    @Override
    public List<YaoMilletConfig> page(YaoMilletConfigPageReq req) {
        YaoMilletConfig condition = EntityUtils.copyData(req, YaoMilletConfig.class);

        List<YaoMilletConfig> yaoMilletConfigList = yaoMilletConfigMapper.selectByCondition(condition);

        for (YaoMilletConfig milletConfig : yaoMilletConfigList) {
            int dateNumber = yaoMilletConfigRecordService.createDateNumber();
            YaoMilletConfigRecord milletConfigRecord = yaoMilletConfigRecordService.detailByDateNumberForUpdate(dateNumber, milletConfig.getCurrency());
            milletConfig.setTodayRemainQuantity(milletConfigRecord.getRemainQuantity());
            YaoMilletOrderDetail yaoMilletOrderDetail = yaoMilletOrderDetailService.detailTotalByMillet(milletConfig.getCurrency());
            milletConfig.setTotalYinYao(yaoMilletOrderDetail.getYinYao());
            milletConfig.setTotalYangYao(yaoMilletOrderDetail.getYangYao());
            milletConfig.setQuantity(yaoMilletOrderDetail.getQuantity());

        }

        return yaoMilletConfigList;
    }

    /**
     * 列表查询元粟配置
     *
     * @param req 列表查询元粟配置入参
     * @return 列表元粟配置对象
     */
    @Override
    public List<YaoMilletConfig> list(YaoMilletConfigListReq req) {
        YaoMilletConfig condition = EntityUtils.copyData(req, YaoMilletConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), YaoMilletConfig.class));

        List<YaoMilletConfig> yaoMilletConfigList = yaoMilletConfigMapper.selectByCondition(condition);

        return yaoMilletConfigList;
    }

    @Override
    public List<YaoMilletConfig> list(YaoMilletConfig req) {
        return yaoMilletConfigMapper.selectByCondition(req);
    }

    /**
     * 前端详情查询元粟配置
     *
     * @param id 主键ID
     * @return 元粟配置对象
     */
    @Override
    public YaoMilletConfigDetailRes detailFront(Long id) {
        YaoMilletConfigDetailRes res = new YaoMilletConfigDetailRes();

        YaoMilletConfig yaoMilletConfig = yaoMilletConfigMapper.selectByPrimaryKey(id);
        if (null == yaoMilletConfig) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(yaoMilletConfig, res);

        return res;
    }

    /**
     * 前端分页查询元粟配置
     *
     * @param req 前端分页查询元粟配置入参
     * @return 分页元粟配置对象
     */
    @Override
    public List<YaoMilletConfigPageRes> pageFront(YaoMilletConfigPageFrontReq req) {
        YaoMilletConfig condition = EntityUtils.copyData(req, YaoMilletConfig.class);
        List<YaoMilletConfig> yaoMilletConfigList = yaoMilletConfigMapper.selectByCondition(condition);

        List<YaoMilletConfigPageRes> resList = yaoMilletConfigList.stream().map((entity) -> {
            YaoMilletConfigPageRes res = new YaoMilletConfigPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(yaoMilletConfigList, resList);
    }

    /**
     * 前端列表查询元粟配置
     *
     * @param req 前端列表查询元粟配置入参
     * @return 列表元粟配置对象
     */
    @Override
    public List<YaoMilletConfigListRes> listFront(YaoMilletConfigListFrontReq req) {
        YaoMilletConfig condition = EntityUtils.copyData(req, YaoMilletConfig.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), YaoMilletConfig.class));

        List<YaoMilletConfig> yaoMilletConfigList = yaoMilletConfigMapper.selectByCondition(condition);

        List<YaoMilletConfigListRes> resList = yaoMilletConfigList.stream().map((entity) -> {
            YaoMilletConfigListRes res = new YaoMilletConfigListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public List<AccountDiamondExchangeMilletRes> listFront(Long userId) {
        return yaoMilletConfigMapper.selectListFront(userId);
    }

    @Override
    public YaoMilletConfig detailByCurrency(String currency) {
        YaoMilletConfig condition = new YaoMilletConfig();
        condition.setCurrency(currency);
        List<YaoMilletConfig> yaoMilletConfigs = yaoMilletConfigMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(yaoMilletConfigs)) {
            return yaoMilletConfigs.get(0);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpDown(YaoMilletConfigCreateReq request, User operator) {
        for (Long id : request.getIdList()) {
            YaoMilletConfig detail = detail(id);
            detail.setStatus(request.getStatus());
            detail.setUpdater(operator.getId());
            detail.setUpdaterName(operator.getLoginName());
            detail.setUpdateDatetime(new Date());
            yaoMilletConfigMapper.updateByPrimaryKeySelective(detail);
        }
    }

}