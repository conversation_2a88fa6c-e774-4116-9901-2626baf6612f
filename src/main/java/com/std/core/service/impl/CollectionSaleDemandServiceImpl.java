package com.std.core.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.std.common.exception.BizException;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.PageInfoUtil;
import com.std.common.utils.SqlUtil;
import com.std.core.enums.EErrorCode;
import com.std.core.mapper.CollectionSaleDemandMapper;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.CollectionSaleDemandCreateReq;
import com.std.core.pojo.request.CollectionSaleDemandListReq;
import com.std.core.pojo.request.CollectionSaleDemandListFrontReq;
import com.std.core.pojo.request.CollectionSaleDemandModifyReq;
import com.std.core.pojo.request.CollectionSaleDemandPageReq;
import com.std.core.pojo.request.CollectionSaleDemandPageFrontReq;
import com.std.core.pojo.response.CollectionSaleDemandDetailRes;
import com.std.core.pojo.response.CollectionSaleDemandListRes;
import com.std.core.pojo.response.CollectionSaleDemandPageRes;
import com.std.core.service.ICollectionSaleDemandService;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 数字藏品发售需求ServiceImpl
 *
 * <AUTHOR> ycj
 * @since : 2022-07-05 11:37
 */
@Service
public class CollectionSaleDemandServiceImpl implements ICollectionSaleDemandService {

    @Resource
    private CollectionSaleDemandMapper collectionSaleDemandMapper;

    /**
     * 新增数字藏品发售需求
     *
     * @param req      新增数字藏品发售需求入参
     * @param operator 操作人
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Collection collection, CollectionSaleDemandCreateReq req, User operator) {
        collectionSaleDemandMapper.deleteByCollectionId(collection.getId());

        CollectionSaleDemand collectionSaleDemand = EntityUtils.copyData(req, CollectionSaleDemand.class);
        if (CollectionUtils.isNotEmpty(req.getMandateUrlList())) {
            collectionSaleDemand.setMandateUrl(JSONArray.toJSONString(req.getMandateUrlList()));
        }
        collectionSaleDemand.setCollectionId(collection.getId());
        collectionSaleDemand.setCreater(operator.getId());
        collectionSaleDemand.setCreaterName(operator.getLoginName());
        collectionSaleDemand.setCreateDatetime(new Date());
        collectionSaleDemandMapper.insertSelective(collectionSaleDemand);
    }

    /**
     * 删除数字藏品发售需求
     *
     * @param id 主键ID
     */
    @Override
    public void remove(Long id) {
        collectionSaleDemandMapper.deleteByPrimaryKey(id);
    }

    /**
     * 修改数字藏品发售需求
     *
     * @param req      修改数字藏品发售需求入参
     * @param operator 操作人
     */
    @Override
    public void modify(CollectionSaleDemandModifyReq req, User operator) {
        CollectionSaleDemand collectionSaleDemand = EntityUtils.copyData(req, CollectionSaleDemand.class);
        collectionSaleDemandMapper.updateByPrimaryKeySelective(collectionSaleDemand);
    }

    /**
     * 详情查询数字藏品发售需求
     *
     * @param id 主键ID
     * @return 数字藏品发售需求对象
     */
    @Override
    public CollectionSaleDemand detail(Long id) {
        CollectionSaleDemand collectionSaleDemand = collectionSaleDemandMapper.selectByPrimaryKey(id);
        if (null == collectionSaleDemand) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }
        if (StringUtils.isNotBlank(collectionSaleDemand.getMandateUrl())) {
            List<CollectionSaleDemandMandateUrl> mandateUrlList = JSONArray.parseArray(collectionSaleDemand.getMandateUrl(),
                    CollectionSaleDemandMandateUrl.class);
            collectionSaleDemand.setMandateUrlList(mandateUrlList);
        }
        return collectionSaleDemand;
    }

    /**
     * 分页查询数字藏品发售需求
     *
     * @param req 分页查询数字藏品发售需求入参
     * @return 分页数字藏品发售需求对象
     */
    @Override
    public List<CollectionSaleDemand> page(CollectionSaleDemandPageReq req) {
        CollectionSaleDemand condition = EntityUtils.copyData(req, CollectionSaleDemand.class);

        List<CollectionSaleDemand> collectionSaleDemandList = collectionSaleDemandMapper.selectByCondition(condition);

        return collectionSaleDemandList;
    }

    /**
     * 列表查询数字藏品发售需求
     *
     * @param req 列表查询数字藏品发售需求入参
     * @return 列表数字藏品发售需求对象
     */
    @Override
    public List<CollectionSaleDemand> list(CollectionSaleDemandListReq req) {
        CollectionSaleDemand condition = EntityUtils.copyData(req, CollectionSaleDemand.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionSaleDemand.class));

        List<CollectionSaleDemand> collectionSaleDemandList = collectionSaleDemandMapper.selectByCondition(condition);

        return collectionSaleDemandList;
    }

    /**
     * 前端详情查询数字藏品发售需求
     *
     * @param id 主键ID
     * @return 数字藏品发售需求对象
     */
    @Override
    public CollectionSaleDemandDetailRes detailFront(Long id) {
        CollectionSaleDemandDetailRes res = new CollectionSaleDemandDetailRes();

        CollectionSaleDemand collectionSaleDemand = collectionSaleDemandMapper.selectByPrimaryKey(id);
        if (null == collectionSaleDemand) {
            throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
        }

        BeanUtils.copyProperties(collectionSaleDemand, res);

        return res;
    }

    /**
     * 前端分页查询数字藏品发售需求
     *
     * @param req 前端分页查询数字藏品发售需求入参
     * @return 分页数字藏品发售需求对象
     */
    @Override
    public List<CollectionSaleDemandPageRes> pageFront(CollectionSaleDemandPageFrontReq req) {
        CollectionSaleDemand condition = EntityUtils.copyData(req, CollectionSaleDemand.class);
        List<CollectionSaleDemand> collectionSaleDemandList = collectionSaleDemandMapper.selectByCondition(condition);

        List<CollectionSaleDemandPageRes> resList = collectionSaleDemandList.stream().map((entity) -> {
            CollectionSaleDemandPageRes res = new CollectionSaleDemandPageRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return PageInfoUtil.listToPage(collectionSaleDemandList, resList);
    }

    /**
     * 前端列表查询数字藏品发售需求
     *
     * @param req 前端列表查询数字藏品发售需求入参
     * @return 列表数字藏品发售需求对象
     */
    @Override
    public List<CollectionSaleDemandListRes> listFront(CollectionSaleDemandListFrontReq req) {
        CollectionSaleDemand condition = EntityUtils.copyData(req, CollectionSaleDemand.class);
        condition.setOrderBy(SqlUtil.parseSort(req.getSort(), CollectionSaleDemand.class));

        List<CollectionSaleDemand> collectionSaleDemandList = collectionSaleDemandMapper.selectByCondition(condition);

        List<CollectionSaleDemandListRes> resList = collectionSaleDemandList.stream().map((entity) -> {
            CollectionSaleDemandListRes res = new CollectionSaleDemandListRes();
            BeanUtils.copyProperties(entity, res);
            return res;
        }).collect(Collectors.toList());

        return resList;
    }

    @Override
    public CollectionSaleDemand detailByCollectionId(Long collectionId) {
        CollectionSaleDemand condition = new CollectionSaleDemand();
        condition.setCollectionId(collectionId);

        List<CollectionSaleDemand> collectionSaleDemandList = collectionSaleDemandMapper.selectByCondition(condition);

        if (CollectionUtils.isNotEmpty(collectionSaleDemandList)){
            CollectionSaleDemand saleDemand = collectionSaleDemandList.get(0);
            List<CollectionSaleDemandMandateUrl> mandateUrlList = JSONArray.parseArray(saleDemand.getMandateUrl(),
                    CollectionSaleDemandMandateUrl.class);
            saleDemand.setMandateUrlList(mandateUrlList);
            return saleDemand;
        }
        return null;
    }

}