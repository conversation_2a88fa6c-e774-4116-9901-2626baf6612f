package com.std.core.service;

import com.std.core.pojo.domain.IdentifyOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.IdentifyOrderAlipayApplyReq;
import com.std.core.pojo.request.IdentifyOrderCreateReq;
import com.std.core.pojo.request.IdentifyOrderFrontCreateReq;
import com.std.core.pojo.request.IdentifyOrderFrontTwoReq;
import com.std.core.pojo.request.IdentifyOrderFrontOcrReq;
import com.std.core.pojo.request.IdentifyOrderListFrontReq;
import com.std.core.pojo.request.IdentifyOrderListReq;
import com.std.core.pojo.request.IdentifyOrderPageFrontReq;
import com.std.core.pojo.request.IdentifyOrderPageReq;
import com.std.core.pojo.response.IdentifyOrderAlipayApplyRes;
import com.std.core.pojo.response.IdentifyOrderAlipayApplyResultRes;
import com.std.core.pojo.response.IdentifyOrderDetailRes;
import com.std.core.pojo.response.IdentifyOrderListRes;
import com.std.core.pojo.response.IdentifyOrderPageRes;
import com.std.core.pojo.response.IdentifySearchRes;
import java.util.List;

/**
 * 实名认证订单Service
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-07 14:45
 */
public interface IIdentifyOrderService {

    /**
     * 新增实名认证订单
     *
     * @param req 新增实名认证订单入参
     * @param operator 操作人
     */
    void create(IdentifyOrderCreateReq req, User operator);

    void create(IdentifyOrder req);

    /**
     * 新增实名认证定点(管理端)
     */
    void createByOss(IdentifyOrderCreateReq req, User operator);

    void remove(Long id);

    void modify(IdentifyOrder identifyOrder);

    IdentifyOrderAlipayApplyRes apply(IdentifyOrderAlipayApplyReq req, User operator);

    void applyOcr(IdentifyOrderFrontOcrReq req, User operator);

    void applyAuth(IdentifyOrderFrontTwoReq request, User operator);

    void checkImage(String idNo, String realName, String frontImage);

    /**
     * 详情查询实名认证订单
     *
     * @param id 主键ID
     * @return 实名认证订单详情数据
     */
    IdentifyOrder detail(Long id);

    /**
     * 分页查询实名认证订单
     *
     * @param req 分页查询实名认证订单入参
     * @return 实名认证订单分页数据
     */
    List<IdentifyOrder> page(IdentifyOrderPageReq req);

    /**
     * 列表查询实名认证订单
     *
     * @param req 列表查询实名认证订单入参
     * @return 实名认证订单列表数据
     */
    List<IdentifyOrder> list(IdentifyOrderListReq req);

    /**
     * 前端详情查询实名认证订单
     *
     * @param id 主键ID
     * @return 实名认证订单详情数据
     */
    IdentifyOrderDetailRes detailFront(Long id);

    IdentifyOrderDetailRes detailByUserId(Long userId);

    IdentifyOrder detailByUserIdOss(Long userId);

    IdentifySearchRes regFrontIdentifyInfo(String frontImage);

    /**
     * 前端分页查询实名认证订单
     *
     * @param req 分页查询实名认证订单入参
     * @return 实名认证订单分页数据
     */
    List<IdentifyOrderPageRes> pageFront(IdentifyOrderPageFrontReq req);

    /**
     * 前端列表查询实名认证订单
     *
     * @param req 列表查询实名认证订单入参
     * @return 实名认证订单列表数据
     */
    List<IdentifyOrderListRes> listFront(IdentifyOrderListFrontReq req);

    int selectCount(String idNo);

    Long selectCount(Long userId, String status);

    IdentifyOrderAlipayApplyResultRes checkResult(String certifyId, User user);

    IdentifyOrder selectLast(Long userId);

    /**
     * 用户发起人工审核实名
     */
    void createByFront(IdentifyOrderFrontCreateReq request, User operator);

    int selectApplyIngCount(Long userId);


}