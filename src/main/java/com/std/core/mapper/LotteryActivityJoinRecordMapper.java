package com.std.core.mapper;

import com.std.core.pojo.domain.LotteryActivityJoinRecord;
import com.std.core.pojo.response.LotteryActivityJoinRecordAllRes;
import com.std.core.pojo.response.LotteryActivityJoinRecordDetailRes;
import com.std.core.pojo.response.LotteryActivityJoinRecordListRes;

import java.util.List;

/**
 * 抽奖活动报名记录Mapper
 *
 * <AUTHOR> ycj
 * @since : 2022-06-02 11:03
 */
public interface LotteryActivityJoinRecordMapper {

    /**
     * 选择性插入
     *
     * @param record 抽奖活动报名记录
     * @return 影响行数
     */
    int insertSelective(LotteryActivityJoinRecord record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Long id);

    /**
     * 选择性更新
     *
     * @param record 抽奖活动报名记录
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(LotteryActivityJoinRecord record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return 抽奖活动报名记录
     */
    LotteryActivityJoinRecord selectByPrimaryKey(Long id);
    LotteryActivityJoinRecord selectForUpdate(Long id);
    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return 抽奖活动报名记录列表
     */
    List<LotteryActivityJoinRecord> selectByCondition(LotteryActivityJoinRecord condition);

    List<LotteryActivityJoinRecordListRes> selectByActivityLimit10(Long activityId);

    List<LotteryActivityJoinRecordDetailRes> selectJoinRecordDetailList(LotteryActivityJoinRecord condition);

    int updateLotterActivityEnd(LotteryActivityJoinRecord joinRecord);

    void updateLotterActivityMiss(LotteryActivityJoinRecord joinRecord);

    List<LotteryActivityJoinRecord> selectNoDealJoinRecord(Long activityId);


    void updateData(LotteryActivityJoinRecord joinRecord);

    Integer selectNoDealJoinRecordByActivity(Long activityId);

    Integer selectJoinCount(Long activityId);

    Integer selectUserJoinCount(Long activityId, Long userId);

    Integer selectUserWinCount(Long activityId, Long userId);

    List<LotteryActivityJoinRecordAllRes> selectByActivity(Long activityId);
}
