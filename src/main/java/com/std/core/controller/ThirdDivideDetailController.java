package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ThirdDivideDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ThirdDivideDetailListFrontReq;
import com.std.core.pojo.request.ThirdDivideDetailListReq;
import com.std.core.pojo.request.ThirdDivideDetailModifyReq;
import com.std.core.pojo.request.ThirdDivideDetailPageFrontReq;
import com.std.core.pojo.request.ThirdDivideDetailPageReq;
import com.std.core.pojo.response.ThirdDivideDetailDetailRes;
import com.std.core.pojo.response.ThirdDivideDetailListRes;
import com.std.core.pojo.response.ThirdDivideDetailPageRes;
import com.std.core.service.IThirdDivideDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 支付三方分账明细单Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-27 20:14
 */
@ApiVersion(1)
@RestController
@Api(value = "支付三方分账明细单管理", tags = "支付三方分账明细单管理")
@RequestMapping("{version}/third_divide_detail")
public class ThirdDivideDetailController extends BaseController {

    @Resource
    private IThirdDivideDetailService thirdDivideDetailService;


//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除支付三方分账明细单', NULL, '/core/v1/third_divide_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除支付三方分账明细单")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        thirdDivideDetailService.remove(id);
//
//        return new Result();
//    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改支付三方分账明细单', NULL, '/core/v1/third_divide_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改支付三方分账明细单")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ThirdDivideDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        thirdDivideDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询支付三方分账明细单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ThirdDivideDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdDivideDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询支付三方分账明细单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ThirdDivideDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdDivideDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdDivideDetail.class));

        return PageUtil.pageResult(thirdDivideDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询支付三方分账明细单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ThirdDivideDetail>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdDivideDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdDivideDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询支付三方分账明细单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ThirdDivideDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdDivideDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询支付三方分账明细单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ThirdDivideDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdDivideDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdDivideDetail.class));

        return PageUtil.pageResult(thirdDivideDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询支付三方分账明细单', NULL, '/core/v1/third_divide_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询支付三方分账明细单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ThirdDivideDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdDivideDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdDivideDetailService.listFront(request));
    }

}