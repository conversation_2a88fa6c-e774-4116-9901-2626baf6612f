package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.EntityUtils;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Cnavigate;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.BuyoutProductsReadReq;
import com.std.core.pojo.request.CollectionDetailExchangeXmetaReq;
import com.std.core.pojo.request.CollectionDetailListFrontReq;
import com.std.core.pojo.request.CollectionDetailModifyLockDatetimeReq;
import com.std.core.pojo.request.CollectionDetailPageCompanyReq;
import com.std.core.pojo.request.CollectionDetailPageFrontReq;
import com.std.core.pojo.request.CollectionDetailPageFrontU3dReq;
import com.std.core.pojo.request.CollectionDetailPageRecoveryReq;
import com.std.core.pojo.request.CollectionDetailPageReq;
import com.std.core.pojo.request.CollectionDetailRecordPageReq;
import com.std.core.pojo.request.CollectionDropRecordPageReq;
import com.std.core.pojo.request.TransferBatchCollectionDetailCompanyReq;
import com.std.core.pojo.request.TransferBatchCollectionDetailReq;
import com.std.core.pojo.request.TransferCollectionDetailReq;
import com.std.core.pojo.request.UserCollectionDetailReq;
import com.std.core.pojo.response.CollectionDetailDetailRes;
import com.std.core.pojo.response.CollectionDetailExchangeRes;
import com.std.core.pojo.response.CollectionDetailListFrontRes;
import com.std.core.pojo.response.CollectionDetailListFrontU3dRes;
import com.std.core.pojo.response.CollectionDetailPageRes;
import com.std.core.pojo.response.CollectionDetailRecordPageRes;
import com.std.core.pojo.response.CollectionDetailU3dOpenFlag;
import com.std.core.pojo.response.CollectionDetailUserPageRes;
import com.std.core.pojo.response.CollectionDetailXmetaExchangeCountRes;
import com.std.core.pojo.response.CollectionDropRecordPageRes;
import com.std.core.pojo.response.CollectionListU3dRes;
import com.std.core.pojo.response.CollectionTotalCountRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICnavigateService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.util.Page;
import com.std.core.util.RedisLock;
import com.std.core.util.SysConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 藏品Controller
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:42
 */
@ApiVersion(1)
@RestController
@Api(value = "藏品管理", tags = "藏品管理")
@Slf4j
@RequestMapping("{version}/collection_detail")
public class CollectionDetailController extends BaseController {

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @Resource
    private ICnavigateService cnavigateService;

    @ApiOperation(value = "front 分页查询用户某个作品的藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetail.class));

        return PageUtil.pageResult(collectionDetailService.pageFront(request));
    }

    @ApiOperation(value = "oss: 分页查询藏品")
    @ApiOperationSupport(order = 81)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailService.page(request));
    }

    @ApiOperation(value = "oss: 详情查询藏品")
    @ApiOperationSupport(order = 81)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionDetailService.detail(id));
    }

    @ApiOperation(value = "oss: 详情查询藏品")
    @ApiOperationSupport(order = 81)
    @PostMapping("/detail_company/{id}")
    public Result<CollectionDetail> detailCompany(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionDetailService.detail(id));
    }

    @ApiOperation(value = "front: 前端详情查询藏品(h5分享)")
    @ApiOperationSupport(order = 81)
    @PostMapping("/public/detail_front/{id}")
    public Result<CollectionDetailDetailRes> detail_front(@PathVariable("id") @Valid Long id) {
        return new Result<>(collectionDetailService.detailFront(id));
    }

    @ApiOperation(value = "front: 前端详情查询某个藏品")
    @ApiOperationSupport(order = 81)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionDetailDetailRes> myDetailFront(@RequestHeader(value = "Authorization", required = false) String token,
            @PathVariable("id") @Valid Long id) {
        return new Result<>(collectionDetailService.detailFront(id));
    }

    @ApiOperation(value = "front:我的藏品列表查")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/list_person_front")
    public Result<List<CollectionDetailListFrontRes>> listPersonFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailListFrontReq request) {
        User operator = getUserByToken(token);
        return new Result<>(collectionDetailService.collectionDetailPersonFront(request, operator));
    }

    @ApiOperation(value = "front:我的藏品分页查")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_person_front")
    public Result<List<CollectionDetailListFrontRes>> pagePersonFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageFrontReq request,
            @RequestHeader(value = "channelId", required = false) Long channelId) {
        User operator = getUserByToken(token);
        if (StringUtils.isNotBlank(request.getIsFilter()) && EBoolean.YES.getCode().equals(request.getIsFilter())
                && StringUtils.isNotBlank(request.getIsTransfer()) && EBoolean.YES.getCode().equals(request.getIsTransfer())) {
            request.setIsFilter(null);
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.collectionDetailPagePersonFront(channelId, request, operator));
    }

    @ApiOperation(value = "front:我的藏品分页查(u3d)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_person_front_u3d")
    public Result<List<CollectionDetailListFrontU3dRes>> pagePersonFrontU3d(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageFrontU3dReq request,
            @RequestHeader(value = "channelId", required = false) Long channelId) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.collectionDetailPagePersonFrontU3d(channelId, request, operator));
    }

    @ApiOperation(value = "front:我的藏品总数量")
    @ApiOperationSupport(order = 81)
    @PostMapping(value = "/my_collection_count")
    public Result<CollectionTotalCountRes> queryCollectionTotalCount(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailListFrontReq request,
            @RequestHeader(value = "channelId", required = false) Long channelId) {
        User operator = getUserByToken(token);

        return new Result(collectionDetailService.queryCollectionTotalCount(channelId, request, operator));
    }

    @ApiOperation(value = "oss: 分页查询流转记录")
    @ApiOperationSupport(order = 82)
    @PostMapping(value = "/page_detail_record")
    public Result<PageInfo<CollectionDetailRecordPageRes>> pageDetailRecord(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailRecordPageReq request) {
        User operator = getUserByToken(token);

        return PageUtil.pageResult(collectionDetailService.pageDetailRecord(request));
    }

    @ApiOperation(value = "oss: 分页查询空投记录")
    @ApiOperationSupport(order = 82)
    @PostMapping(value = "/page_drop_record")
    public Result<PageInfo<CollectionDropRecordPageRes>> dropRecord(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDropRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailService.pageDropRecord(request));
    }

    @ApiOperation(value = "oss: 分页查询空投记录")
    @ApiOperationSupport(order = 82)
    @PostMapping(value = "/page_drop_record_company")
    public Result<PageInfo<CollectionDropRecordPageRes>> dropRecordCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDropRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailService.pageDropRecord(request));
    }

    @ApiOperation(value = "front：分页查询用户时刻副本")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_user_collection_detail")
    public Result<PageInfo<CollectionDetailUserPageRes>> pageUserCollectionDetail(@RequestBody @Valid UserCollectionDetailReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetail.class));

        return PageUtil.pageResult(collectionDetailService.pageUserCollectionDetail(request));
    }

    @ApiOperation(value = "front:浏览时刻次数加1")
    @ApiOperationSupport(order = 84)
    @PostMapping("/public/read")
    public Result read(@RequestBody @Valid BuyoutProductsReadReq request) {
        collectionDetailService.read(request.getId());
        return new Result();
    }

    @ApiOperation(value = "front:转赠给其他人")
    @ApiOperationSupport(order = 84)
    @PostMapping("/transfer")
    public Result<OrderPayRes> transfer(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid TransferCollectionDetailReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        if (!EBigOrderPayType.ACCOUNT.getCode().equals(request.getPayType())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "暂不支持的支付方式");
        }
        String lockId = "transfer" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠进行中！");
        }
        try {
            TransferBatchCollectionDetailReq req = EntityUtils.copyData(request, TransferBatchCollectionDetailReq.class);
            List<Long> idList = new ArrayList<>();
            idList.add(request.getCollectionDetailId());
            req.setCollectionDetailIdList(idList);

            return new Result(collectionDetailService.transferBatch(req, operator, channelId));

        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    @ApiOperation(value = "front:批量转赠给其他人")
    @ApiOperationSupport(order = 84)
    @PostMapping("/batch_transfer")
    public Result<OrderPayRes> batchTransfer(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid TransferBatchCollectionDetailReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "transfer" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠进行中！");
        }
        ChannelMerchant channelMerchant = channelMerchantService.detailByFront(channelId);
        channelId = channelMerchant.getId();

        try {
            return new Result(collectionDetailService.transferBatch(request, operator, channelId));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    @ApiOperation(value = "front:(发行方)批量转赠给其他人")
    @ApiOperationSupport(order = 84)
    @PostMapping("/batch_transfer_company")
    public Result batchTransferCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid TransferBatchCollectionDetailCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        String lockId = "transfer" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠进行中！");
        }
        ChannelMerchant channelMerchant = channelMerchantService.detailByFront(null);
        Long channelId = channelMerchant.getId();

        try {
            collectionDetailService.transferBatchCompany(request, operator, channelId);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
        return new Result();
    }

    @ApiOperation(value = "oss:修改锁仓解锁时间")
    @ApiOperationSupport(order = 90)
    @PostMapping("/modify_lockdatetime")
    public Result modifyLockdatetime(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailModifyLockDatetimeReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionDetailService.modifyLockdatetime(request, operator);
        return new Result();
    }

    @ApiOperation(value = "oss:修改转赠锁定时间")
    @ApiOperationSupport(order = 90)
    @PostMapping("/modify_transform_limit_time")
    public Result modifyTransformLimitTime(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailModifyLockDatetimeReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionDetailService.modifyTransformLimitTime(request, operator);
        return new Result();
    }

    @ApiOperation(value = "oss:设置不可流转")
    @ApiOperationSupport(order = 90)
    @PostMapping("/set_up_no_second_transfer")
    public Result setUpNoSecondTransfer(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionDetailService.setUpNoSecondTransfer(request, operator);
        return new Result();
    }

    @ApiOperation(value = "oss:设置不可转赠")
    @ApiOperationSupport(order = 90)
    @PostMapping("/set_up_no_transfer")
    public Result setUpNoTransfer(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionDetailService.setUpNoTransfer(request, operator);
        return new Result();
    }

    @ApiOperation(value = "oss:分页查询机构藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<CollectionDetail>> pageCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.pageCompany(request, operator));
    }

    @ApiOperation(value = "oss:分页查询机构藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_company_author")
    public Result<PageInfo<CollectionDetail>> pageCompanyAuthor(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        request.setOwnerId(operator.getCompanyId());
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.pageCompany(request, operator));
    }

    @ApiOperation(value = "oss:分页查询清退藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_recovery")
    public Result<PageInfo<CollectionDetail>> pageRecovery(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageRecoveryReq request) {
        User operator = getUserByToken(token);

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.pageRecovery(request, operator));
    }

    @ApiOperation(value = "front:查询是否有资格进入(元宇宙)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/u3d/check_open_flag")
    public Result<CollectionDetailU3dOpenFlag> checkU3dOpenFlag(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        return new Result(collectionDetailService.selectU3dOpenFlag(operator));
    }

    @ApiOperation(value = "front:退出元宇宙")
    @ApiOperationSupport(order = 85)
    @PostMapping(value = "/u3d/back")
    public Result doU3dBack(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        collectionDetailService.doU3dBack(operator);
        return new Result();
    }

    @ApiOperation(value = "front:列表查询我的藏品(元宇宙)")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/u3d/query_collection_list")
    public Result<List<CollectionListU3dRes>> queryMyCollection(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);
        return new Result(collectionDetailService.selectMyCollectionList(operator));
    }

    @ApiOperation(value = "front:分页查询我的藏品(元宇宙)")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/u3d/query_collection_page")
    public Result<Page<CollectionListU3dRes>> queryMyCollectionPage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailPageFrontReq request) {

        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionDetailService.selectMyCollectionList(request, operator));
    }

    /********************************************xmeta 上架兑换权益卡****************************************************************/

    @ApiOperation(value = "front:查询xmeta可用的权益兑换卡数量")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/xmeta/get_available_count")
    public Result<CollectionDetailXmetaExchangeCountRes> getXmetaExchangeAvailableCount(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionDetailService.getXmetaExchangeAvailableCount(operator));
    }

    @ApiOperation(value = "front:xmeta权益卡兑换")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/xmeta/exchange")
    public Result<CollectionDetailExchangeRes> exchangeXmeta(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailExchangeXmetaReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "user_exchange_xmeta:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "xmeta权益卡兑换进行中，请稍后重试");
            }
            collectionDetailService.doExchangeXmeta(request, operator);
            Cnavigate cnavigate = cnavigateService.detail(SysConstants.XMETA_CNAV);
            return new Result(new CollectionDetailExchangeRes(cnavigate.getId(), cnavigate.getName(), cnavigate.getUrl()));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }
}