package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.UserRecoveryCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserRecoveryCollectionCreateReq;
import com.std.core.pojo.request.UserRecoveryCollectionListReq;
import com.std.core.pojo.request.UserRecoveryCollectionListFrontReq;
import com.std.core.pojo.request.UserRecoveryCollectionModifyReq;
import com.std.core.pojo.request.UserRecoveryCollectionPageReq;
import com.std.core.pojo.request.UserRecoveryCollectionPageFrontReq;
import com.std.core.pojo.response.UserRecoveryCollectionDetailRes;
import com.std.core.pojo.response.UserRecoveryCollectionListRes;
import com.std.core.pojo.response.UserRecoveryCollectionPageRes;
import com.std.core.service.IUserRecoveryCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 清退藏品Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-17 15:42
 */
@ApiVersion(1)
@RestController
@Api(value = "清退藏品管理", tags = "清退藏品管理")
@RequestMapping("{version}/user_recovery_collection")
public class UserRecoveryCollectionController extends BaseController {

    @Resource
    private IUserRecoveryCollectionService userRecoveryCollectionService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增清退藏品', NULL, '/core/v1/user_recovery_collection/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增清退藏品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionCreateReq request) {
        User operator = getUserByToken(token);
        userRecoveryCollectionService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除清退藏品', NULL, '/core/v1/user_recovery_collection/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除清退藏品")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userRecoveryCollectionService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改清退藏品', NULL, '/core/v1/user_recovery_collection/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改清退藏品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionModifyReq request) {
        User operator = getUserByToken(token);
        userRecoveryCollectionService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询清退藏品', NULL, '/core/v1/user_recovery_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询清退藏品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserRecoveryCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询清退藏品', NULL, '/core/v1/user_recovery_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询清退藏品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserRecoveryCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserRecoveryCollection.class));

        return PageUtil.pageResult(userRecoveryCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询清退藏品', NULL, '/core/v1/user_recovery_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询清退藏品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserRecoveryCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询清退藏品', NULL, '/core/v1/user_recovery_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询清退藏品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserRecoveryCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询清退藏品', NULL, '/core/v1/user_recovery_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询清退藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserRecoveryCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserRecoveryCollection.class));

        return PageUtil.pageResult(userRecoveryCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询清退藏品', NULL, '/core/v1/user_recovery_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询清退藏品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserRecoveryCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryCollectionService.listFront(request));
    }

}