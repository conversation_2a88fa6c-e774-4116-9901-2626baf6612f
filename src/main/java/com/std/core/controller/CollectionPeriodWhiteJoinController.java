package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionPeriodWhiteJoin;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionPeriodWhiteJoinPageFrontReq;
import com.std.core.pojo.response.CollectionPeriodWhiteJoinDetailRes;
import com.std.core.pojo.response.CollectionPeriodWhiteJoinPageRes;
import com.std.core.service.ICollectionPeriodWhiteJoinService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品期数白名单报名Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-12-25 16:23
 */
@ApiVersion(1)
@RestController
@Api(value = "作品期数白名单报名管理", tags = "作品期数白名单报名管理")
@RequestMapping("{version}/collection_period_white_join")
public class CollectionPeriodWhiteJoinController extends BaseController {

    @Resource
    private ICollectionPeriodWhiteJoinService collectionPeriodWhiteJoinService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除作品期数白名单报名")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionPeriodWhiteJoinService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改作品期数白名单报名")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionPeriodWhiteJoinModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionPeriodWhiteJoinService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询作品期数白名单报名")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionPeriodWhiteJoin> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodWhiteJoinService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/page', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "分页条件查询作品期数白名单报名")
//    @ApiOperationSupport(order = 50)
//    @PostMapping(value = "/page")
//    public Result<PageInfo<CollectionPeriodWhiteJoin>> page(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionPeriodWhiteJoinPageReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//                SqlUtil.parseSort(request.getSort(), CollectionPeriodWhiteJoin.class));
//
//        return PageUtil.pageResult(collectionPeriodWhiteJoinService.page(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/list', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "列表条件查询作品期数白名单报名")
//    @ApiOperationSupport(order = 60)
//    @PostMapping(value = "/list")
//    public Result<List<CollectionPeriodWhiteJoin>> list(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionPeriodWhiteJoinListReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(collectionPeriodWhiteJoinService.list(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询作品期数白名单报名")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionPeriodWhiteJoinDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodWhiteJoinService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询作品期数白名单报名")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionPeriodWhiteJoinPageRes>> pageFront(
            @RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody @Valid CollectionPeriodWhiteJoinPageFrontReq request) {
        User operator = null;
        if (null != token) {
            operator = getUserByToken(token);
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionPeriodWhiteJoin.class));

        return PageUtil.pageResult(collectionPeriodWhiteJoinService.pageFront(request));
    }

    @ApiOperation(value = "前端分页条件查询作品期数白名单报名(公开的)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<CollectionPeriodWhiteJoinPageRes>> pageFront(
            @RequestBody @Valid CollectionPeriodWhiteJoinPageFrontReq request) {
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionPeriodWhiteJoin.class));

        return PageUtil.pageResult(collectionPeriodWhiteJoinService.pageFront(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询作品期数白名单报名', NULL, '/core/v1/collection_period_white_join/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询作品期数白名单报名")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<CollectionPeriodWhiteJoinListRes>> listFront(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionPeriodWhiteJoinListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(collectionPeriodWhiteJoinService.listFront(request));
//    }

}