package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionDetailDayAllSnapshot;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionDetailDayAllSnapshotListFrontReq;
import com.std.core.pojo.request.CollectionDetailDayAllSnapshotListReq;
import com.std.core.pojo.request.CollectionDetailDayAllSnapshotModifyReq;
import com.std.core.pojo.request.CollectionDetailDayAllSnapshotPageFrontReq;
import com.std.core.pojo.request.CollectionDetailDayAllSnapshotPageReq;
import com.std.core.pojo.response.CollectionDetailDayAllSnapshotDetailRes;
import com.std.core.pojo.response.CollectionDetailDayAllSnapshotListRes;
import com.std.core.pojo.response.CollectionDetailDayAllSnapshotPageRes;
import com.std.core.service.ICollectionDetailDayAllSnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 某天共有的用户藏品清单Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-16 15:55
 */
@ApiVersion(1)
@RestController
@Api(value = "某天共有的用户藏品清单管理", tags = "某天共有的用户藏品清单管理")
@RequestMapping("{version}/collection_detail_day_all_snapshot")
public class CollectionDetailDayAllSnapshotController extends BaseController {

    @Resource
    private ICollectionDetailDayAllSnapshotService collectionDetailDayAllSnapshotService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除某天共有的用户藏品清单")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        collectionDetailDayAllSnapshotService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改某天共有的用户藏品清单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailDayAllSnapshotModifyReq request) {
        User operator = getUserByToken(token);
        collectionDetailDayAllSnapshotService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailDayAllSnapshot> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDayAllSnapshotService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailDayAllSnapshot>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailDayAllSnapshotPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailDayAllSnapshot.class));

        return PageUtil.pageResult(collectionDetailDayAllSnapshotService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionDetailDayAllSnapshot>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailDayAllSnapshotListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDayAllSnapshotService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionDetailDayAllSnapshotDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDayAllSnapshotService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionDetailDayAllSnapshotPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailDayAllSnapshotPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailDayAllSnapshot.class));

        return PageUtil.pageResult(collectionDetailDayAllSnapshotService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询某天共有的用户藏品清单', NULL, '/core/v1/collection_detail_day_all_snapshot/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询某天共有的用户藏品清单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionDetailDayAllSnapshotListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailDayAllSnapshotListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDayAllSnapshotService.listFront(request));
    }

}