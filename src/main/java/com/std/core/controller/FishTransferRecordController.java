package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.FishTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.FishTransferRecordCreateReq;
import com.std.core.pojo.request.FishTransferRecordListReq;
import com.std.core.pojo.request.FishTransferRecordListFrontReq;
import com.std.core.pojo.request.FishTransferRecordModifyReq;
import com.std.core.pojo.request.FishTransferRecordPageReq;
import com.std.core.pojo.request.FishTransferRecordPageFrontReq;
import com.std.core.pojo.response.FishTransferRecordDetailRes;
import com.std.core.pojo.response.FishTransferRecordListRes;
import com.std.core.pojo.response.FishTransferRecordPageRes;
import com.std.core.service.IFishTransferRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔光贝壳转增记录Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-02-28 14:50
 */
@ApiVersion(1)
@RestController
@Api(value = "渔光贝壳转增记录管理", tags = "渔光贝壳转增记录管理")
@RequestMapping("{version}/fish_transfer_record")
public class FishTransferRecordController extends BaseController {

    @Resource
    private IFishTransferRecordService fishTransferRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "渔光贝壳赠送")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordCreateReq request) {
        User operator = getUserByToken(token);

        String lockId = "shell_transfer:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠进行中，请稍后重试");
            }
            fishTransferRecordService.create(request, operator);
            return new Result();
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除渔光贝壳转增记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        fishTransferRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改渔光贝壳转增记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordModifyReq request) {
        User operator = getUserByToken(token);
        fishTransferRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FishTransferRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishTransferRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FishTransferRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishTransferRecord.class));

        return PageUtil.pageResult(fishTransferRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FishTransferRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishTransferRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<FishTransferRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishTransferRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<FishTransferRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishTransferRecord.class));

        return PageUtil.pageResult(fishTransferRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔光贝壳转增记录', NULL, '/core/v1/fish_transfer_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渔光贝壳转增记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FishTransferRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishTransferRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishTransferRecordService.listFront(request));
    }

}