package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.IntegralExchangeDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.IIntegralExchangeDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 兑换明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 05:56
 */
@ApiVersion(1)
@RestController
@Api(value = "兑换明细管理", tags = "兑换明细管理")
@RequestMapping("{version}/integral_exchange_detail")
public class IntegralExchangeDetailController extends BaseController {

    @Resource
    private IIntegralExchangeDetailService integralExchangeDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增兑换明细', NULL, '/core/v1/integral_exchange_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增兑换明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        integralExchangeDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除兑换明细', NULL, '/core/v1/integral_exchange_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除兑换明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        integralExchangeDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改兑换明细', NULL, '/core/v1/integral_exchange_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改兑换明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        integralExchangeDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询兑换明细', NULL, '/core/v1/integral_exchange_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询兑换明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<IntegralExchangeDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(integralExchangeDetailService.detail(id));
    }

    @ApiOperation(value = "查询兑换总数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_sum")
    public Result<List<IntegralExchangeDetailSumRes>> detailSum(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailSumReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(integralExchangeDetailService.detailSum(request));
    }

    @ApiOperation(value = "元气商场汇总统计")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_integral_activity_sum")
    public Result<List<IntegralActivitySumRes>> detailIntegralActivitySum(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralActivitySumReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(integralExchangeDetailService.detailIntegralActivitySum(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询兑换明细', NULL, '/core/v1/integral_exchange_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询兑换明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<IntegralExchangeDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(integralExchangeDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询兑换明细', NULL, '/core/v1/integral_exchange_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询兑换明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<IntegralExchangeDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(integralExchangeDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询兑换明细', NULL, '/core/v1/integral_exchange_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询兑换明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<IntegralExchangeDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询兑换明细', NULL, '/core/v1/integral_exchange_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询兑换明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<IntegralExchangeDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(integralExchangeDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询兑换明细', NULL, '/core/v1/integral_exchange_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询兑换明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<IntegralExchangeDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeDetailService.listFront(request));
    }

}