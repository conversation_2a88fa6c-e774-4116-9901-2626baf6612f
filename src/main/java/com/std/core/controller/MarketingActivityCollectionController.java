package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.MarketingActivityCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MarketingActivityCollectionCreateReq;
import com.std.core.pojo.request.MarketingActivityCollectionListReq;
import com.std.core.pojo.request.MarketingActivityCollectionListFrontReq;
import com.std.core.pojo.request.MarketingActivityCollectionModifyReq;
import com.std.core.pojo.request.MarketingActivityCollectionPageReq;
import com.std.core.pojo.request.MarketingActivityCollectionPageFrontReq;
import com.std.core.pojo.response.MarketingActivityCollectionDetailRes;
import com.std.core.pojo.response.MarketingActivityCollectionListRes;
import com.std.core.pojo.response.MarketingActivityCollectionPageRes;
import com.std.core.service.IMarketingActivityCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 营销活动关联藏品Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-10-25 13:54
 */
@ApiVersion(1)
@RestController
@Api(value = "营销活动关联藏品管理", tags = "营销活动关联藏品管理")
@RequestMapping("{version}/marketing_activity_collection")
public class MarketingActivityCollectionController extends BaseController {

    @Resource
    private IMarketingActivityCollectionService marketingActivityCollectionService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增营销活动关联藏品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionCreateReq request) {
        User operator = getUserByToken(token);
        marketingActivityCollectionService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除营销活动关联藏品")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        marketingActivityCollectionService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改营销活动关联藏品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionModifyReq request) {
        User operator = getUserByToken(token);
        marketingActivityCollectionService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询营销活动关联藏品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MarketingActivityCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(marketingActivityCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询营销活动关联藏品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MarketingActivityCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MarketingActivityCollection.class));

        return PageUtil.pageResult(marketingActivityCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询营销活动关联藏品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MarketingActivityCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(marketingActivityCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询营销活动关联藏品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MarketingActivityCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(marketingActivityCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询营销活动关联藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MarketingActivityCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MarketingActivityCollection.class));

        return PageUtil.pageResult(marketingActivityCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询营销活动关联藏品', NULL, '/core/v1/marketing_activity_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询营销活动关联藏品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MarketingActivityCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MarketingActivityCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(marketingActivityCollectionService.listFront(request));
    }

}