package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionRightsDetail;
import com.std.core.pojo.domain.TicketType;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionRightsDeatilTicketEnterMetaReq;
import com.std.core.pojo.request.CollectionRightsDetailCreateRightReq;
import com.std.core.pojo.request.CollectionRightsDetailListFrontReq;
import com.std.core.pojo.request.CollectionRightsDetailListReq;
import com.std.core.pojo.request.CollectionRightsDetailModifyRightReq;
import com.std.core.pojo.request.CollectionRightsDetailPageFrontReq;
import com.std.core.pojo.request.CollectionRightsDetailPageReq;
import com.std.core.pojo.request.CollectionRightsMyTicketPageFrontReq;
import com.std.core.pojo.request.TicketTypeListReq;
import com.std.core.pojo.response.CollectionRightsDeatilTicketEnterMetaRes;
import com.std.core.pojo.response.CollectionRightsDetailDetailRes;
import com.std.core.pojo.response.CollectionRightsDetailListRes;
import com.std.core.pojo.response.CollectionRightsDetailPageRes;
import com.std.core.pojo.response.CollectionRightsDetailTicketCheckRes;
import com.std.core.pojo.response.CollectionRightsMyTicketPageRes;
import com.std.core.service.ICollectionRightsDetailService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品权益明细表Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-06 18:03
 */
@ApiVersion(1)
@RestController
@Api(value = "作品权益明细表管理", tags = "作品权益明细表管理")
@RequestMapping("{version}/collection_rights_detail")
public class CollectionRightsDetailController extends BaseController {

    @Resource
    private ICollectionRightsDetailService collectionRightsDetailService;
    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增作品权益明细表', NULL, '/core/v1/collection_rights_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增作品权益明细表")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionRightsDetailCreateReq request) {
//        User operator = getUserByToken(token);
////        collectionRightsDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除作品权益明细表', NULL, '/core/v1/collection_rights_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除作品权益明细表")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionRightsDetailService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改作品权益明细表', NULL, '/core/v1/collection_rights_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改作品权益次数")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token,
//                         @RequestBody @Valid CollectionRightsDetailModifyReq request) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        collectionRightsDetailService.modify(request, operator);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "修改作品权益描述")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify_contnet")
//    public Result modify(@RequestHeader(value = "Authorization") String token,
//                         @RequestBody @Valid CollectionRightsDetailModifyContentReq request) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        collectionRightsDetailService.modifyContnet(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "新增作品权益")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/create_right")
    public Result createRight(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailCreateRightReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        String lockId = "collection_rights_detail:" + request.getCollectionId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "权益新增中，请稍后重试");
            }
            collectionRightsDetailService.createRight(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    @ApiOperation(value = "修改作品权益")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_right")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailModifyRightReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionRightsDetailService.modifyRight(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询作品权益明细表")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionRightsDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightsDetailService.detailOss(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询作品权益明细表")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionRightsDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionRightsDetail.class));

        return PageUtil.pageResult(collectionRightsDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询作品权益明细表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionRightsDetail>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightsDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询作品权益明细表")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionRightsDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightsDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询作品权益明细表")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionRightsDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionRightsDetail.class));

        return PageUtil.pageResult(collectionRightsDetailService.pageFront(request));
    }

    @ApiOperation(value = "检查用户是否需要选择门票")
    @ApiOperationSupport(order = 70)
    @PostMapping("/check_ticket")
    public Result<CollectionRightsDetailTicketCheckRes> checkTicket(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionRightsDetailService.checkTicket(operator));
    }

    @ApiOperation(value = "查询我的门票")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/my_ticket_page_front")
    public Result<PageInfo<CollectionRightsMyTicketPageRes>> myTicketpageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsMyTicketPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionRightsDetailService.myTicketpageFront(request, operator));
    }

    @ApiOperation(value = "进入元宇宙")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/enter_meta")
    public Result<CollectionRightsDeatilTicketEnterMetaRes> enterMeta(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDeatilTicketEnterMetaReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionRightsDetailService.enterMeta(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询作品权益明细表', NULL, '/core/v1/collection_rights_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询作品权益明细表")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionRightsDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightsDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightsDetailService.listFront(request, null));
    }


    @ApiOperation(value = "修改作品权益次数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/test")
    public Result modify() {

        collectionRightsDetailService.dealHistoryDate();
        return new Result();
    }


    @ApiOperation(value = "根据发行方查询门票类型")
    @ApiOperationSupport(order = 90)
    @PostMapping("/ticket_type_list")
    public Result<List<TicketType>> ticketTypeList(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid TicketTypeListReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionRightsDetailService.ticketTypeList(operator, request));
    }
}