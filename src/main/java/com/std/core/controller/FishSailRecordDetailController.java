package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.FishSailRecordDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.FishSailRecordDetailCreateReq;
import com.std.core.pojo.request.FishSailRecordDetailListReq;
import com.std.core.pojo.request.FishSailRecordDetailListFrontReq;
import com.std.core.pojo.request.FishSailRecordDetailModifyReq;
import com.std.core.pojo.request.FishSailRecordDetailPageReq;
import com.std.core.pojo.request.FishSailRecordDetailPageFrontReq;
import com.std.core.pojo.response.FishSailRecordDetailDetailRes;
import com.std.core.pojo.response.FishSailRecordDetailListRes;
import com.std.core.pojo.response.FishSailRecordDetailPageRes;
import com.std.core.service.IFishSailRecordDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔光捕鱼记录详情Controller
 *
 * <AUTHOR> ycj
 * @since : 2023-03-03 11:04
 */
@ApiVersion(1)
@RestController
@Api(value = "渔光捕鱼记录详情管理", tags = "渔光捕鱼记录详情管理")
@RequestMapping("{version}/fish_sail_record_detail")
public class FishSailRecordDetailController extends BaseController {

    @Resource
    private IFishSailRecordDetailService fishSailRecordDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增渔光捕鱼记录详情")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        fishSailRecordDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除渔光捕鱼记录详情")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        fishSailRecordDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改渔光捕鱼记录详情")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        fishSailRecordDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FishSailRecordDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishSailRecordDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FishSailRecordDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishSailRecordDetail.class));

        return PageUtil.pageResult(fishSailRecordDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FishSailRecordDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(fishSailRecordDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<FishSailRecordDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishSailRecordDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<FishSailRecordDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishSailRecordDetail.class));

        return PageUtil.pageResult(fishSailRecordDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔光捕鱼记录详情', NULL, '/core/v1/fish_sail_record_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渔光捕鱼记录详情")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FishSailRecordDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishSailRecordDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishSailRecordDetailService.listFront(request));
    }

}