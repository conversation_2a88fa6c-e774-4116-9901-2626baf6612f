package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.LotteryActivityCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LotteryActivityCollectionCreateReq;
import com.std.core.pojo.request.LotteryActivityCollectionListReq;
import com.std.core.pojo.request.LotteryActivityCollectionListFrontReq;
import com.std.core.pojo.request.LotteryActivityCollectionModifyReq;
import com.std.core.pojo.request.LotteryActivityCollectionPageReq;
import com.std.core.pojo.request.LotteryActivityCollectionPageFrontReq;
import com.std.core.pojo.response.LotteryActivityCollectionDetailRes;
import com.std.core.pojo.response.LotteryActivityCollectionListRes;
import com.std.core.pojo.response.LotteryActivityCollectionPageRes;
import com.std.core.service.ILotteryActivityCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抽奖资格Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-06-01 17:18
 */
@ApiVersion(1)
@RestController
@Api(value = "抽奖资格管理", tags = "抽奖资格管理")
@RequestMapping("{version}/lottery_activity_collection")
public class LotteryActivityCollectionController extends BaseController {

    @Resource
    private ILotteryActivityCollectionService lotteryActivityCollectionService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增抽奖资格', NULL, '/core/v1/lottery_activity_collection/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增抽奖资格")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionCreateReq request) {
        User operator = getUserByToken(token);
        lotteryActivityCollectionService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除抽奖资格', NULL, '/core/v1/lottery_activity_collection/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除抽奖资格")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        lotteryActivityCollectionService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改抽奖资格', NULL, '/core/v1/lottery_activity_collection/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改抽奖资格")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionModifyReq request) {
        User operator = getUserByToken(token);
        lotteryActivityCollectionService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询抽奖资格")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LotteryActivityCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询抽奖资格")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LotteryActivityCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), LotteryActivityCollection.class));

        return PageUtil.pageResult(lotteryActivityCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询抽奖资格")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LotteryActivityCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询抽奖资格")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<LotteryActivityCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询抽奖资格")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<LotteryActivityCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), LotteryActivityCollection.class));

        return PageUtil.pageResult(lotteryActivityCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询抽奖资格', NULL, '/core/v1/lottery_activity_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询抽奖资格")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<LotteryActivityCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityCollectionService.listFront(request));
    }

}