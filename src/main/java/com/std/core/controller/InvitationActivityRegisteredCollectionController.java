package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.InvitationActivityRegisteredCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionCreateReq;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionListReq;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionListFrontReq;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionModifyReq;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionPageReq;
import com.std.core.pojo.request.InvitationActivityRegisteredCollectionPageFrontReq;
import com.std.core.pojo.response.InvitationActivityRegisteredCollectionDetailRes;
import com.std.core.pojo.response.InvitationActivityRegisteredCollectionListRes;
import com.std.core.pojo.response.InvitationActivityRegisteredCollectionPageRes;
import com.std.core.service.IInvitationActivityRegisteredCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拉新活动注册赠送藏品Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-12 15:36
 */
@ApiVersion(1)
@RestController
@Api(value = "拉新活动注册赠送藏品管理", tags = "拉新活动注册赠送藏品管理")
@RequestMapping("{version}/invitation_activity_registered_collection")
public class InvitationActivityRegisteredCollectionController extends BaseController {

    @Resource
    private IInvitationActivityRegisteredCollectionService invitationActivityRegisteredCollectionService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增拉新活动注册赠送藏品")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionCreateReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityRegisteredCollectionService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除拉新活动注册赠送藏品")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        invitationActivityRegisteredCollectionService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改拉新活动注册赠送藏品")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionModifyReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityRegisteredCollectionService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityRegisteredCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityRegisteredCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityRegisteredCollection.class));

        return PageUtil.pageResult(invitationActivityRegisteredCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityRegisteredCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityRegisteredCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityRegisteredCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityRegisteredCollection.class));

        return PageUtil.pageResult(invitationActivityRegisteredCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询拉新活动注册赠送藏品', NULL, '/core/v1/invitation_activity_registered_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询拉新活动注册赠送藏品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityRegisteredCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredCollectionService.listFront(request));
    }

}