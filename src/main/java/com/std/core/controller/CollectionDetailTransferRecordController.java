package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionDetailTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionDetailTransferRecordListFrontReq;
import com.std.core.pojo.request.CollectionDetailTransferRecordListReq;
import com.std.core.pojo.request.CollectionDetailTransferRecordPageFrontReq;
import com.std.core.pojo.request.CollectionDetailTransferRecordPageReq;
import com.std.core.pojo.response.CollectionDetailTransferRecordDetailRes;
import com.std.core.pojo.response.CollectionDetailTransferRecordListRes;
import com.std.core.pojo.response.CollectionDetailTransferRecordPageRes;
import com.std.core.service.ICollectionDetailTransferRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 藏品转赠记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-11-04 22:09
 */
@ApiVersion(1)
@RestController
@Api(value = "藏品转赠记录管理", tags = "藏品转赠记录管理")
@RequestMapping("{version}/collection_detail_transfer_record")
public class CollectionDetailTransferRecordController extends BaseController {

    @Resource
    private ICollectionDetailTransferRecordService collectionDetailTransferRecordService;

//    @ApiOperation(value = "新增藏品转赠记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailTransferRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        collectionDetailTransferRecordService.create(request, operator);
//
//        return new Result();
//    }

//    @ApiOperation(value = "删除藏品转赠记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionDetailTransferRecordService.remove(id);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "修改藏品转赠记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailTransferRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionDetailTransferRecordService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "查询藏品转赠记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailTransferRecord> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferRecordService.detail(id));
    }

    @ApiOperation(value = "分页条件查询藏品转赠记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailTransferRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid CollectionDetailTransferRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailTransferRecordService.page(channelId, request));
    }

    @ApiOperation(value = "列表条件查询藏品转赠记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionDetailTransferRecord>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferRecordService.list(request));
    }

    @ApiOperation(value = "查询藏品转赠记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionDetailTransferRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferRecordService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询藏品转赠记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionDetailTransferRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailTransferRecordService.pageFront(operator));
    }

    @ApiOperation(value = "前端列表条件查询藏品转赠记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionDetailTransferRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferRecordService.listFront(request));
    }

}