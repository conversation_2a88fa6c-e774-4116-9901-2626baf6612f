package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserApplyInvoiceRecord;
import com.std.core.pojo.request.MyInvoiceOrderReq;
import com.std.core.pojo.request.UserApplyInvoiceRecordApplyReq;
import com.std.core.pojo.request.UserApplyInvoiceRecordListReq;
import com.std.core.pojo.request.UserApplyInvoiceRecordPageFrontReq;
import com.std.core.pojo.request.UserApplyInvoiceRecordPageReq;
import com.std.core.pojo.request.UserApplyInvoiceRecordSendReq;
import com.std.core.pojo.response.UserApplyInvoiceRecordApplyRes;
import com.std.core.pojo.response.UserApplyInvoiceRecordDetailRes;
import com.std.core.pojo.response.UserApplyInvoiceRecordPageRes;
import com.std.core.pojo.response.UserOrderInvoicePageRes;
import com.std.core.service.IConfigService;
import com.std.core.service.IUserApplyInvoiceRecordService;
import com.std.core.util.SysConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户申请开票记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-08-17 22:06
 */
@ApiVersion(1)
@RestController
@Api(value = "用户申请开票记录管理", tags = "用户申请开票记录管理")
@RequestMapping("{version}/user_apply_invoice_record")
public class UserApplyInvoiceRecordController extends BaseController {

    @Resource
    private IUserApplyInvoiceRecordService userApplyInvoiceRecordService;

    @Resource
    private IConfigService configService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "开票申请")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/apply")
    public Result<UserApplyInvoiceRecordApplyRes> apply(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordApplyReq request) {
        User operator = getUserByToken(token);
        userApplyInvoiceRecordService.apply(request, operator);

        return new Result(new UserApplyInvoiceRecordApplyRes(configService.getStringValue(SysConstants.INVOICE_APPLY_NOTE)));
    }

    @ApiOperation(value = "发送发票")
    @ApiOperationSupport(order = 40)
    @PostMapping("/send")
    public Result send(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordSendReq request) {
        User operator = getUserByToken(token);
        userApplyInvoiceRecordService.send(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户申请开票记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserApplyInvoiceRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userApplyInvoiceRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户申请开票记录(发行方)")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_company/{id}")
    public Result<UserApplyInvoiceRecordDetailRes> detailCompany(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userApplyInvoiceRecordService.detailCompany(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户申请开票记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserApplyInvoiceRecord>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userApplyInvoiceRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户申请开票记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserApplyInvoiceRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userApplyInvoiceRecordService.detailFront(id));
    }

    @ApiOperation(value = "front:分页查询我的待开发票订单", position = 80)
    @PostMapping(value = "/order/page_front")
    public Result<PageInfo<UserOrderInvoicePageRes>> myInvoiceOrderPage(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid MyInvoiceOrderReq request) {
        User operator = getUserByToken(token);
        request.setUserId(operator.getId());

        return PageUtil.pageResult(userApplyInvoiceRecordService.pageOrderFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户申请开票记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserApplyInvoiceRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserApplyInvoiceRecord.class));

        return PageUtil.pageResult(userApplyInvoiceRecordService.pageFront(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户申请开票记录(发行方自己)")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<UserApplyInvoiceRecord>> pageCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordPageReq request) {
        User operator = getUserByToken(token);
        request.setOpenCompanyId(operator.getCompanyId());
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserApplyInvoiceRecord.class));

        return PageUtil.pageResult(userApplyInvoiceRecordService.pageCompany(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户申请开票记录', NULL, '/core/v1/user_apply_invoice_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户申请开票记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserApplyInvoiceRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserApplyInvoiceRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserApplyInvoiceRecord.class));

        return PageUtil.pageResult(userApplyInvoiceRecordService.page(request));
    }
}