package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ThirdPaybackDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ThirdPaybackDetailListFrontReq;
import com.std.core.pojo.request.ThirdPaybackDetailListReq;
import com.std.core.pojo.request.ThirdPaybackDetailPageFrontReq;
import com.std.core.pojo.request.ThirdPaybackDetailPageReq;
import com.std.core.pojo.response.ThirdPaybackDetailDetailRes;
import com.std.core.pojo.response.ThirdPaybackDetailListRes;
import com.std.core.pojo.response.ThirdPaybackDetailPageRes;
import com.std.core.service.IThirdPaybackDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单三方退款明细单Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-03-30 14:34
 */
@ApiVersion(1)
@RestController
@Api(value = "订单三方退款明细单管理", tags = "订单三方退款明细单管理")
@RequestMapping("{version}/third_payback_detail")
public class ThirdPaybackDetailController extends BaseController {

    @Resource
    private IThirdPaybackDetailService thirdPaybackDetailService;


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询订单三方退款明细单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ThirdPaybackDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdPaybackDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询订单三方退款明细单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ThirdPaybackDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdPaybackDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdPaybackDetail.class));

        return PageUtil.pageResult(thirdPaybackDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询订单三方退款明细单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ThirdPaybackDetail>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdPaybackDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdPaybackDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询订单三方退款明细单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ThirdPaybackDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdPaybackDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询订单三方退款明细单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ThirdPaybackDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdPaybackDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdPaybackDetail.class));

        return PageUtil.pageResult(thirdPaybackDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询订单三方退款明细单', NULL, '/core/v1/third_payback_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询订单三方退款明细单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ThirdPaybackDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdPaybackDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdPaybackDetailService.listFront(request));
    }

}