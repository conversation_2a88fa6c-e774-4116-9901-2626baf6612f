package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CompanyApplySettleRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CompanyApplySettleRecordCreateReq;
import com.std.core.pojo.request.CompanyApplySettleRecordListReq;
import com.std.core.pojo.request.CompanyApplySettleRecordListFrontReq;
import com.std.core.pojo.request.CompanyApplySettleRecordModifyReq;
import com.std.core.pojo.request.CompanyApplySettleRecordPageReq;
import com.std.core.pojo.request.CompanyApplySettleRecordPageFrontReq;
import com.std.core.pojo.response.CompanyApplySettleRecordDetailRes;
import com.std.core.pojo.response.CompanyApplySettleRecordListRes;
import com.std.core.pojo.response.CompanyApplySettleRecordPageRes;
import com.std.core.service.ICompanyApplySettleRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 发行方申请结算记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-07 14:08
 */
@ApiVersion(1)
@RestController
@Api(value = "发行方申请结算记录管理", tags = "发行方申请结算记录管理")
@RequestMapping("{version}/company_apply_settle_record")
public class CompanyApplySettleRecordController extends BaseController {

    @Resource
    private ICompanyApplySettleRecordService companyApplySettleRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增发行方申请结算记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        String lockId = "company_apply_settle_record_create:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "申请发起中中，请稍后重试");
            }
            companyApplySettleRecordService.create(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除发行方申请结算记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        companyApplySettleRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "审核结算申请")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/audit")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordModifyReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        companyApplySettleRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询发行方申请结算记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CompanyApplySettleRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(companyApplySettleRecordService.detail(id));
    }

    @ApiOperation(value = "查询发行方申请结算记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<CompanyApplySettleRecord> detailCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.BP);

        return new Result<>(companyApplySettleRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询发行方申请结算记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CompanyApplySettleRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(companyApplySettleRecordService.page(request));
    }

    @ApiOperation(value = "分页条件查询发行方申请结算记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<CompanyApplySettleRecord>> pageCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordPageReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        request.setCompanyId(operator.getCompanyId());
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(companyApplySettleRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询发行方申请结算记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CompanyApplySettleRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(companyApplySettleRecordService.list(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询发行方申请结算记录")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<CompanyApplySettleRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(companyApplySettleRecordService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询发行方申请结算记录")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<CompanyApplySettleRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), CompanyApplySettleRecord.class));
//
//        return PageUtil.pageResult(companyApplySettleRecordService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询发行方申请结算记录', NULL, '/core/v1/company_apply_settle_record/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询发行方申请结算记录")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<CompanyApplySettleRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyApplySettleRecordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(companyApplySettleRecordService.listFront(request));
//    }

}