package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.IntegralExchange;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.IntegralExchangeListFrontReq;
import com.std.core.pojo.request.IntegralExchangeListReq;
import com.std.core.pojo.request.IntegralExchangePageFrontReq;
import com.std.core.pojo.request.IntegralExchangePageReq;
import com.std.core.pojo.response.IntegralExchangeAccountInfoRes;
import com.std.core.pojo.response.IntegralExchangeDetailRes;
import com.std.core.pojo.response.IntegralExchangeListRes;
import com.std.core.pojo.response.IntegralExchangePageRes;
import com.std.core.service.IIntegralExchangeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 兑换记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 05:55
 */
@ApiVersion(1)
@RestController
@Api(value = "兑换记录管理", tags = "兑换记录管理")
@RequestMapping("{version}/integral_exchange")
public class IntegralExchangeController extends BaseController {

    @Resource
    private IIntegralExchangeService integralExchangeService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增兑换记录', NULL, '/core/v1/integral_exchange/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增兑换记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeCreateReq request) {
//        User operator = getUserByToken(token);
//        integralExchangeService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除兑换记录', NULL, '/core/v1/integral_exchange/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除兑换记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        integralExchangeService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改兑换记录', NULL, '/core/v1/integral_exchange/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改兑换记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid IntegralExchangeModifyReq request) {
//        User operator = getUserByToken(token);
//        integralExchangeService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询兑换记录', NULL, '/core/v1/integral_exchange/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询兑换记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<IntegralExchange> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询兑换记录', NULL, '/core/v1/integral_exchange/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询兑换记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<IntegralExchange>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid IntegralExchangePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(integralExchangeService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询兑换记录', NULL, '/core/v1/integral_exchange/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询兑换记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<IntegralExchange>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid IntegralExchangeListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询兑换记录', NULL, '/core/v1/integral_exchange/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询兑换记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<IntegralExchangeDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeService.detailFront(id));
    }

    @ApiOperation(value = "查询元气值账户")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_integral_account")
    public Result<IntegralExchangeAccountInfoRes> detailIntegralAccount(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(integralExchangeService.detailIntegralAccount(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询兑换记录', NULL, '/core/v1/integral_exchange/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询兑换记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<IntegralExchangePageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid IntegralExchangePageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(integralExchangeService.pageFront(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询兑换记录', NULL, '/core/v1/integral_exchange/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询兑换记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<IntegralExchangeListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid IntegralExchangeListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(integralExchangeService.listFront(request));
    }

}