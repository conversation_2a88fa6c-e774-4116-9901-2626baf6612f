package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ShellInvitationUserRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ShellInvitationUserRecordCreateReq;
import com.std.core.pojo.request.ShellInvitationUserRecordListReq;
import com.std.core.pojo.request.ShellInvitationUserRecordListFrontReq;
import com.std.core.pojo.request.ShellInvitationUserRecordModifyReq;
import com.std.core.pojo.request.ShellInvitationUserRecordPageReq;
import com.std.core.pojo.request.ShellInvitationUserRecordPageFrontReq;
import com.std.core.pojo.response.ShellInvitationUserRecordDetailRes;
import com.std.core.pojo.response.ShellInvitationUserRecordListRes;
import com.std.core.pojo.response.ShellInvitationUserRecordPageRes;
import com.std.core.service.IShellInvitationUserRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户拉新记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2023-03-08 16:14
 */
@ApiVersion(1)
@RestController
@Api(value = "用户拉新记录管理", tags = "用户拉新记录管理")
@RequestMapping("{version}/shell_invitation_user_record")
public class ShellInvitationUserRecordController extends BaseController {

    @Resource
    private IShellInvitationUserRecordService shellInvitationUserRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增用户拉新记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        shellInvitationUserRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除用户拉新记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        shellInvitationUserRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改用户拉新记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        shellInvitationUserRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户拉新记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ShellInvitationUserRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(shellInvitationUserRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户拉新记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ShellInvitationUserRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ShellInvitationUserRecord.class));

        return PageUtil.pageResult(shellInvitationUserRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户拉新记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ShellInvitationUserRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(shellInvitationUserRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户拉新记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ShellInvitationUserRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(shellInvitationUserRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户拉新记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ShellInvitationUserRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ShellInvitationUserRecord.class));

        return PageUtil.pageResult(shellInvitationUserRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户拉新记录', NULL, '/core/v1/shell_invitation_user_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户拉新记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ShellInvitationUserRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellInvitationUserRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(shellInvitationUserRecordService.listFront(request));
    }

}