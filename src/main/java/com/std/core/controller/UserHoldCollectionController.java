package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.UserHoldCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserHoldCollectionCreateReq;
import com.std.core.pojo.request.UserHoldCollectionListReq;
import com.std.core.pojo.request.UserHoldCollectionListFrontReq;
import com.std.core.pojo.request.UserHoldCollectionModifyReq;
import com.std.core.pojo.request.UserHoldCollectionPageReq;
import com.std.core.pojo.request.UserHoldCollectionPageFrontReq;
import com.std.core.pojo.response.UserHoldCollectionDetailRes;
import com.std.core.pojo.response.UserHoldCollectionListRes;
import com.std.core.pojo.response.UserHoldCollectionPageRes;
import com.std.core.service.IUserHoldCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户持有藏品套数Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-04-20 20:47
 */
@ApiVersion(1)
@RestController
@Api(value = "用户持有藏品套数管理", tags = "用户持有藏品套数管理")
@RequestMapping("{version}/user_hold_collection")
public class UserHoldCollectionController extends BaseController {

    @Resource
    private IUserHoldCollectionService userHoldCollectionService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户持有藏品套数', NULL, '/core/v1/user_hold_collection/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增用户持有藏品套数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionCreateReq request) {
        User operator = getUserByToken(token);
        userHoldCollectionService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户持有藏品套数', NULL, '/core/v1/user_hold_collection/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除用户持有藏品套数")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userHoldCollectionService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户持有藏品套数', NULL, '/core/v1/user_hold_collection/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改用户持有藏品套数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionModifyReq request) {
        User operator = getUserByToken(token);
        userHoldCollectionService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户持有藏品套数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserHoldCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userHoldCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户持有藏品套数")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserHoldCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserHoldCollection.class));

        return PageUtil.pageResult(userHoldCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户持有藏品套数")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserHoldCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userHoldCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户持有藏品套数")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserHoldCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userHoldCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户持有藏品套数")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserHoldCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserHoldCollection.class));

        return PageUtil.pageResult(userHoldCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户持有藏品套数', NULL, '/core/v1/user_hold_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户持有藏品套数")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserHoldCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserHoldCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userHoldCollectionService.listFront(request));
    }

}