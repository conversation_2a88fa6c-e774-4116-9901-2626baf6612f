package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.PeriodAuctionDelayedRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordCreateReq;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordListReq;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordListFrontReq;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordModifyReq;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordPageReq;
import com.std.core.pojo.request.PeriodAuctionDelayedRecordPageFrontReq;
import com.std.core.pojo.response.PeriodAuctionDelayedRecordDetailRes;
import com.std.core.pojo.response.PeriodAuctionDelayedRecordListRes;
import com.std.core.pojo.response.PeriodAuctionDelayedRecordPageRes;
import com.std.core.service.IPeriodAuctionDelayedRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数竞拍延时记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 21:44
 */
@ApiVersion(1)
@RestController
@Api(value = "期数竞拍延时记录管理", tags = "期数竞拍延时记录管理")
@RequestMapping("{version}/period_auction_delayed_record")
public class PeriodAuctionDelayedRecordController extends BaseController {

    @Resource
    private IPeriodAuctionDelayedRecordService periodAuctionDelayedRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增期数竞拍延时记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        periodAuctionDelayedRecordService.create(request, operator);
//
//        return new Result();
//    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除期数竞拍延时记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        periodAuctionDelayedRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改期数竞拍延时记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        periodAuctionDelayedRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询期数竞拍延时记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodAuctionDelayedRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionDelayedRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数竞拍延时记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodAuctionDelayedRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodAuctionDelayedRecord.class));

        return PageUtil.pageResult(periodAuctionDelayedRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询期数竞拍延时记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<PeriodAuctionDelayedRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionDelayedRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询期数竞拍延时记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<PeriodAuctionDelayedRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionDelayedRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询期数竞拍延时记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<PeriodAuctionDelayedRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodAuctionDelayedRecord.class));

        return PageUtil.pageResult(periodAuctionDelayedRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询期数竞拍延时记录', NULL, '/core/v1/period_auction_delayed_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询期数竞拍延时记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<PeriodAuctionDelayedRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionDelayedRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionDelayedRecordService.listFront(request));
    }

}