package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EBigOrderPayType;
import com.std.core.enums.ECollectionPeriodStatus;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodJoinRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CollectionPeriodJoinMyRecordRes;
import com.std.core.pojo.response.CollectionPeriodJoinRecordListRes;
import com.std.core.pojo.response.CollectionPeriodJoinRecordPageRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.service.ICollectionPeriodJoinRecordService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数抽签区报名记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-02-22 18:51
 */
@ApiVersion(1)
@RestController
@Api(value = "期数抽签区报名记录管理", tags = "期数抽签区报名记录管理")
@RequestMapping("{version}/collection_period_join_record")
public class CollectionPeriodJoinRecordController extends BaseController {

    @Resource
    private ICollectionPeriodJoinRecordService collectionPeriodJoinRecordService;
    @Resource
    private ICollectionPeriodService collectionPeriodService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "新增期数抽签区报名记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<OrderPayRes> create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodJoinRecordCreateReq request) {
//        if (!EBigOrderPayType.ACCOUNT.getCode().equals(request.getPayType())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不支持的支付方式");
//        }
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "period_buy:" + request.getPeriodId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            }
            return new Result<>(collectionPeriodJoinRecordService.create(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

    @ApiOperation(value = "取消报名")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/cancel_registration")
    public Result cancelRegistration(@RequestHeader(value = "Authorization") String token,
                                      @RequestBody @Valid CollectionPeriodJoinRecordCancelRegistrationReq request) {
//        if (!EBigOrderPayType.ACCOUNT.getCode().equals(request.getPayType())) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不支持的支付方式");
//        }
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "period_cancel_registration:" + request.getId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "取消中请稍后");
            }
            collectionPeriodJoinRecordService.cancelRegistration(request, operator);
            return new Result();
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

    @ApiOperation(value = "查询期数抽签区报名记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionPeriodJoinRecord> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodJoinRecordService.detail(id));
    }

    @ApiOperation(value = "查询期数抽签区报名记录（历史记录）")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_history/{id}")
    public Result<CollectionPeriodJoinRecord> detailHistory(@RequestHeader(value = "Authorization") String token,
                                                     @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodJoinRecordService.detailHistory(id));
    }


    @ApiOperation(value = "分页条件查询期数抽签区报名记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionPeriodJoinRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodJoinRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        if (null != request.getPeriodId()){
            CollectionPeriod detailsimple = collectionPeriodService.detailsimple(request.getPeriodId());

            if (ECollectionPeriodStatus.COLLECTION_PERIOD_STATUS_2.getCode().equals(detailsimple.getStatus())){
                return PageUtil.pageResult(collectionPeriodJoinRecordService.pageHistory(request));
            }
        }

        return PageUtil.pageResult(collectionPeriodJoinRecordService.page(request));
    }

    @ApiOperation(value = "分页条件查询期数抽签区报名记录（历史记录）")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_history")
    public Result<PageInfo<CollectionPeriodJoinRecord>> pageHistory(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodJoinRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionPeriodJoinRecordService.pageHistory(request));
    }

    @ApiOperation(value = "列表条件查询期数抽签区报名记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionPeriodJoinRecord>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodJoinRecordListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodJoinRecordService.list(request));
    }

    @ApiOperation(value = "前端分页条件查询期数抽签区报名记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<CollectionPeriodJoinRecordPageRes>> pageFront(
            @RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody @Valid CollectionPeriodJoinRecordPageFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        if (null != request.getUserId() && StringUtils.isBlank(token)) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "token不能为空");
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionPeriodJoinRecordService.pageFront(request));
    }


    @ApiOperation(value = "列表条件查询期数抽签区报名记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/public/list_front")
    public Result<List<CollectionPeriodJoinRecordListRes>> listFront(@RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody @Valid CollectionPeriodJoinRecordListFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        return new Result<>(collectionPeriodJoinRecordService.listFront(request));
    }

    @ApiOperation(value = "我的报名记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/my_record_list")
    public Result<List<CollectionPeriodJoinMyRecordRes>> myRecordList(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodJoinRecordListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        request.setUserId(operator.getId());
        return new Result<>(collectionPeriodJoinRecordService.myRecordList(request));
    }
}