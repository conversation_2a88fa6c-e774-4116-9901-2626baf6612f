package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.StatTradeDataDay;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.StatTradeDataDayPageReq;
import com.std.core.service.IOperatorLogService;
import com.std.core.service.IScheduleService;
import com.std.core.service.IStatTradeDataDayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 每日统计交易数据Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-02-14 17:52
 */
@ApiVersion(1)
@RestController
@Api(value = "每日统计交易数据管理", tags = "每日统计交易数据管理")
@RequestMapping("{version}/stat_trade_data_day")
public class StatTradeDataDayController extends BaseController {

    @Resource
    private IStatTradeDataDayService statTradeDataDayService;

    @Resource
    private IScheduleService scheduleService;

    @Resource
    private IOperatorLogService operatorLogService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询每日统计交易数据', NULL, '/core/v1/stat_trade_data_day/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询每日统计交易数据")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<StatTradeDataDay>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid StatTradeDataDayPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), StatTradeDataDay.class));

        return PageUtil.pageResult(statTradeDataDayService.page(request));
    }

    @ApiOperation(value = "查询每日统计交易数据")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<StatTradeDataDay> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(statTradeDataDayService.detail(id));
    }

    @ApiOperation(value = "统计日常业务")
    @ApiOperationSupport(order = 40)
    @PostMapping("/doStatUserBizDataYesterday")
    public Result<StatTradeDataDay> doStatUserBizDataYesterday() {
        scheduleService.doStatUserBizDataYesterday();
        return new Result<>();
    }
}