package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.YaoMilletConfigRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.YaoMilletConfigRecordCreateReq;
import com.std.core.pojo.request.YaoMilletConfigRecordListReq;
import com.std.core.pojo.request.YaoMilletConfigRecordListFrontReq;
import com.std.core.pojo.request.YaoMilletConfigRecordModifyReq;
import com.std.core.pojo.request.YaoMilletConfigRecordPageReq;
import com.std.core.pojo.request.YaoMilletConfigRecordPageFrontReq;
import com.std.core.pojo.response.YaoMilletConfigRecordDetailRes;
import com.std.core.pojo.response.YaoMilletConfigRecordListRes;
import com.std.core.pojo.response.YaoMilletConfigRecordPageRes;
import com.std.core.service.IYaoMilletConfigRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元粟每日配置记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-14 15:08
 */
@ApiVersion(1)
@RestController
@Api(value = "元粟每日配置记录管理", tags = "元粟每日配置记录管理")
@RequestMapping("{version}/yao_millet_config_record")
public class YaoMilletConfigRecordController extends BaseController {

    @Resource
    private IYaoMilletConfigRecordService yaoMilletConfigRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增元粟每日配置记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordCreateReq request) {
        User operator = getUserByToken(token);
        yaoMilletConfigRecordService.create();

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元粟每日配置记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        yaoMilletConfigRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改元粟每日配置记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        yaoMilletConfigRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元粟每日配置记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<YaoMilletConfigRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletConfigRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元粟每日配置记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<YaoMilletConfigRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoMilletConfigRecord.class));

        return PageUtil.pageResult(yaoMilletConfigRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元粟每日配置记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<YaoMilletConfigRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletConfigRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元粟每日配置记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<YaoMilletConfigRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletConfigRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元粟每日配置记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<YaoMilletConfigRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoMilletConfigRecord.class));

        return PageUtil.pageResult(yaoMilletConfigRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元粟每日配置记录', NULL, '/core/v1/yao_millet_config_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元粟每日配置记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<YaoMilletConfigRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletConfigRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletConfigRecordService.listFront(request));
    }

}