package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.BusinessChannel;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.BusinessChannelDetailRes;
import com.std.core.pojo.response.BusinessChannelListRes;
import com.std.core.pojo.response.BusinessChannelPageRes;
import com.std.core.service.IBusinessChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 业务渠道关系表Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-02-17 16:06
 */
@ApiVersion(1)
@RestController
@Api(value = "业务渠道关系表管理", tags = "业务渠道关系表管理")
@RequestMapping("{version}/business_channel")
public class BusinessChannelController extends BaseController {

    @Resource
    private IBusinessChannelService businessChannelService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增业务渠道关系表', NULL, '/core/v1/business_channel/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增业务渠道关系表")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        businessChannelService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除业务渠道关系表', NULL, '/core/v1/business_channel/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除业务渠道关系表")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        businessChannelService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改业务渠道关系表', NULL, '/core/v1/business_channel/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改业务渠道关系表")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        businessChannelService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量开启/关闭")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_modify")
    public Result batchModify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelBatchModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        businessChannelService.batchModify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询业务渠道关系表', NULL, '/core/v1/business_channel/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询业务渠道关系表")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<BusinessChannel> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(businessChannelService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询业务渠道关系表', NULL, '/core/v1/business_channel/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询业务渠道关系表")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<BusinessChannel>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), BusinessChannel.class));

        return PageUtil.pageResult(businessChannelService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询业务渠道关系表', NULL, '/core/v1/business_channel/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询业务渠道关系表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<BusinessChannel>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(businessChannelService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询业务渠道关系表', NULL, '/core/v1/business_channel/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询业务渠道关系表")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<BusinessChannelDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(businessChannelService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询业务渠道关系表', NULL, '/core/v1/business_channel/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询业务渠道关系表")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<BusinessChannelPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), BusinessChannel.class));

        return PageUtil.pageResult(businessChannelService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询业务渠道关系表', NULL, '/core/v1/business_channel/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询业务渠道关系表")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<BusinessChannelListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BusinessChannelListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(businessChannelService.listFront(request));
    }

}