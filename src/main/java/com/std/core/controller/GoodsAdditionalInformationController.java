package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.GoodsAdditionalInformation;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.GoodsAdditionalInformationDetailRes;
import com.std.core.pojo.response.GoodsAdditionalInformationListRes;
import com.std.core.pojo.response.GoodsAdditionalInformationPageRes;
import com.std.core.service.IGoodsAdditionalInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 积分商品附加信息Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-25 23:13
 */
@ApiVersion(1)
@RestController
@Api(value = "积分商品附加信息管理", tags = "积分商品附加信息管理")
@RequestMapping("{version}/goods_additional_information")
public class GoodsAdditionalInformationController extends BaseController {

    @Resource
    private IGoodsAdditionalInformationService goodsAdditionalInformationService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增积分商品附加信息', NULL, '/core/v1/goods_additional_information/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增积分商品附加信息")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsAdditionalInformationService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量新增积分商品附加信息")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/batch_create")
    public Result batchCreate(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationBatchCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsAdditionalInformationService.batchCreate(request, operator);

        return new Result();
    }


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除积分商品附加信息', NULL, '/core/v1/goods_additional_information/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除积分商品附加信息")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        goodsAdditionalInformationService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改积分商品附加信息")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsAdditionalInformationService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改积分商品附加信息', NULL, '/core/v1/goods_additional_information/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "废弃积分商品附加信息")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/abandon")
    public Result abandon(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationAbandonReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsAdditionalInformationService.abandon(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询积分商品附加信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsAdditionalInformation> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsAdditionalInformationService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询积分商品附加信息")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsAdditionalInformation>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(goodsAdditionalInformationService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询积分商品附加信息")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsAdditionalInformation>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsAdditionalInformationService.list(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询积分商品附加信息")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<GoodsAdditionalInformationDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(goodsAdditionalInformationService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询积分商品附加信息")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<GoodsAdditionalInformationPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//                SqlUtil.parseSort(request.getSort(), GoodsAdditionalInformation.class));
//
//        return PageUtil.pageResult(goodsAdditionalInformationService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询积分商品附加信息', NULL, '/core/v1/goods_additional_information/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询积分商品附加信息")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<GoodsAdditionalInformationListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsAdditionalInformationListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(goodsAdditionalInformationService.listFront(request));
//    }

}