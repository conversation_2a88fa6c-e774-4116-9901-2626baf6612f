package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ChallengeOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChallengeOrderDetailCreateReq;
import com.std.core.pojo.request.ChallengeOrderDetailListReq;
import com.std.core.pojo.request.ChallengeOrderDetailListFrontReq;
import com.std.core.pojo.request.ChallengeOrderDetailModifyReq;
import com.std.core.pojo.request.ChallengeOrderDetailPageReq;
import com.std.core.pojo.request.ChallengeOrderDetailPageFrontReq;
import com.std.core.pojo.response.ChallengeOrderDetailDetailRes;
import com.std.core.pojo.response.ChallengeOrderDetailListRes;
import com.std.core.pojo.response.ChallengeOrderDetailPageRes;
import com.std.core.service.IChallengeOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 挑战兑换订单明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-12-29 15:45
 */
@ApiVersion(1)
@RestController
@Api(value = "挑战兑换订单明细管理", tags = "挑战兑换订单明细管理")
@RequestMapping("{version}/challenge_order_detail")
public class ChallengeOrderDetailController extends BaseController {

    @Resource
    private IChallengeOrderDetailService challengeOrderDetailService;

//    @ApiOperation(value = "新增挑战兑换订单明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        challengeOrderDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "删除挑战兑换订单明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        challengeOrderDetailService.remove(id);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "修改挑战兑换订单明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        challengeOrderDetailService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "查询挑战兑换订单明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChallengeOrderDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(challengeOrderDetailService.detail(id));
    }

    @ApiOperation(value = "分页条件查询挑战兑换订单明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChallengeOrderDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChallengeOrderDetail.class));

        return PageUtil.pageResult(challengeOrderDetailService.page(request));
    }

    @ApiOperation(value = "列表条件查询挑战兑换订单明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChallengeOrderDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeOrderDetailService.list(request));
    }

    @ApiOperation(value = "查询挑战兑换订单明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChallengeOrderDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(challengeOrderDetailService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询挑战兑换订单明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChallengeOrderDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChallengeOrderDetail.class));

        return PageUtil.pageResult(challengeOrderDetailService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询挑战兑换订单明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChallengeOrderDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeOrderDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeOrderDetailService.listFront(request));
    }

}