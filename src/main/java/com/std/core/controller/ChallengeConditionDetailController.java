package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ChallengeConditionDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChallengeConditionDetailCreateReq;
import com.std.core.pojo.request.ChallengeConditionDetailListReq;
import com.std.core.pojo.request.ChallengeConditionDetailListFrontReq;
import com.std.core.pojo.request.ChallengeConditionDetailModifyReq;
import com.std.core.pojo.request.ChallengeConditionDetailPageReq;
import com.std.core.pojo.request.ChallengeConditionDetailPageFrontReq;
import com.std.core.pojo.response.ChallengeConditionDetailDetailRes;
import com.std.core.pojo.response.ChallengeConditionDetailListRes;
import com.std.core.pojo.response.ChallengeConditionDetailPageRes;
import com.std.core.service.IChallengeConditionDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 挑战条件明细Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-05-26 11:13
 */
@ApiVersion(1)
@RestController
@Api(value = "挑战条件明细管理", tags = "挑战条件明细管理")
@RequestMapping("{version}/challenge_condition_detail")
public class ChallengeConditionDetailController extends BaseController {

    @Resource
    private IChallengeConditionDetailService challengeConditionDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增挑战条件明细', NULL, '/core/v1/challenge_condition_detail/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增挑战条件明细")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailCreateReq request) {
        User operator = getUserByToken(token);
        challengeConditionDetailService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除挑战条件明细', NULL, '/core/v1/challenge_condition_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除挑战条件明细")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        challengeConditionDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改挑战条件明细', NULL, '/core/v1/challenge_condition_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改挑战条件明细")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailModifyReq request) {
        User operator = getUserByToken(token);
        challengeConditionDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询挑战条件明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChallengeConditionDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询挑战条件明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChallengeConditionDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChallengeConditionDetail.class));

        return PageUtil.pageResult(challengeConditionDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询挑战条件明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChallengeConditionDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询挑战条件明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChallengeConditionDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询挑战条件明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChallengeConditionDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChallengeConditionDetail.class));

        return PageUtil.pageResult(challengeConditionDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询挑战条件明细', NULL, '/core/v1/challenge_condition_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询挑战条件明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChallengeConditionDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionDetailService.listFront(request));
    }

}