package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.UserSettleRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserSettleRecordCreateReq;
import com.std.core.pojo.request.UserSettleRecordListReq;
import com.std.core.pojo.request.UserSettleRecordListFrontReq;
import com.std.core.pojo.request.UserSettleRecordModifyReq;
import com.std.core.pojo.request.UserSettleRecordPageReq;
import com.std.core.pojo.request.UserSettleRecordPageFrontReq;
import com.std.core.pojo.response.UserSettleRecordDetailRes;
import com.std.core.pojo.response.UserSettleRecordListRes;
import com.std.core.pojo.response.UserSettleRecordPageRes;
import com.std.core.service.IUserSettleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户结算记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 15:32
 */
@ApiVersion(1)
@RestController
@Api(value = "用户结算记录管理", tags = "用户结算记录管理")
@RequestMapping("{version}/user_settle_record")
public class UserSettleRecordController extends BaseController {

    @Resource
    private IUserSettleRecordService userSettleRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户结算记录', NULL, '/core/v1/user_settle_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增用户结算记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        userSettleRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户结算记录', NULL, '/core/v1/user_settle_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除用户结算记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        userSettleRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户结算记录', NULL, '/core/v1/user_settle_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改用户结算记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        userSettleRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户结算记录', NULL, '/core/v1/user_settle_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户结算记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserSettleRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户结算记录', NULL, '/core/v1/user_settle_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户结算记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserSettleRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserSettleRecord.class));

        return PageUtil.pageResult(userSettleRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户结算记录', NULL, '/core/v1/user_settle_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户结算记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserSettleRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户结算记录', NULL, '/core/v1/user_settle_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户结算记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserSettleRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户结算记录', NULL, '/core/v1/user_settle_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户结算记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserSettleRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(userSettleRecordService.pageFront(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户结算记录', NULL, '/core/v1/user_settle_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户结算记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserSettleRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSettleRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userSettleRecordService.listFront(request));
    }

}