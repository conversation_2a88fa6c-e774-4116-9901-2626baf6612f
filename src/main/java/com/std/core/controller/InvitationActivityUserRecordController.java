package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.InvitationActivityUserRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityUserRecordCreateReq;
import com.std.core.pojo.request.InvitationActivityUserRecordListReq;
import com.std.core.pojo.request.InvitationActivityUserRecordListFrontReq;
import com.std.core.pojo.request.InvitationActivityUserRecordModifyReq;
import com.std.core.pojo.request.InvitationActivityUserRecordPageReq;
import com.std.core.pojo.request.InvitationActivityUserRecordPageFrontReq;
import com.std.core.pojo.response.InvitationActivityUserRecordDetailRes;
import com.std.core.pojo.response.InvitationActivityUserRecordListRes;
import com.std.core.pojo.response.InvitationActivityUserRecordPageRes;
import com.std.core.service.IInvitationActivityUserRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.ArrayList;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户拉新记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-12 11:16
 */
@ApiVersion(1)
@RestController
@Api(value = "用户拉新记录管理", tags = "用户拉新记录管理")
@RequestMapping("{version}/invitation_activity_user_record")
public class InvitationActivityUserRecordController extends BaseController {

    @Resource
    private IInvitationActivityUserRecordService invitationActivityUserRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增用户拉新记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityUserRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除用户拉新记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        invitationActivityUserRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改用户拉新记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityUserRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户拉新记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityUserRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(invitationActivityUserRecordService.detail(id));
    }

    @ApiOperation(value = "查询用户拉新记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/company_detail/{id}")
    public Result<InvitationActivityUserRecord> companyDetail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(invitationActivityUserRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户拉新记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityUserRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityUserRecord.class));

        return PageUtil.pageResult(invitationActivityUserRecordService.page(request, operator));
    }

    @ApiOperation(value = "分页条件查询用户拉新记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/company_page")
    public Result<PageInfo<InvitationActivityUserRecord>> companyPage(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityUserRecord.class));

        return PageUtil.pageResult(invitationActivityUserRecordService.page(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户拉新记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityUserRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户拉新记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityUserRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户拉新记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityUserRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityUserRecord.class));

        return PageUtil.pageResult(invitationActivityUserRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户拉新记录', NULL, '/core/v1/invitation_activity_user_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户拉新记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityUserRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityUserRecordListFrontReq request) {
        User operator = getUserByToken(token);
        List<InvitationActivityUserRecordListRes> resList = new ArrayList<>();


        resList = invitationActivityUserRecordService.listFront(request, operator);

        return new Result<>(resList);
    }

}