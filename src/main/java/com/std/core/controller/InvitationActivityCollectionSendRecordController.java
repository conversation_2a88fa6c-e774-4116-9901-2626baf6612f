package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.InvitationActivityCollectionSendRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordCreateReq;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordListReq;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordListFrontReq;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordModifyReq;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordPageReq;
import com.std.core.pojo.request.InvitationActivityCollectionSendRecordPageFrontReq;
import com.std.core.pojo.response.InvitationActivityCollectionSendRecordDetailRes;
import com.std.core.pojo.response.InvitationActivityCollectionSendRecordListRes;
import com.std.core.pojo.response.InvitationActivityCollectionSendRecordPageRes;
import com.std.core.service.IInvitationActivityCollectionSendRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拉新活动藏品分配记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-14 13:11
 */
@ApiVersion(1)
@RestController
@Api(value = "拉新活动藏品分配记录管理", tags = "拉新活动藏品分配记录管理")
@RequestMapping("{version}/invitation_activity_collection_send_record")
public class InvitationActivityCollectionSendRecordController extends BaseController {

    @Resource
    private IInvitationActivityCollectionSendRecordService invitationActivityCollectionSendRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增拉新活动藏品分配记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordCreateReq request) {
        User operator = getUserByToken(token);
        invitationActivityCollectionSendRecordService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除拉新活动藏品分配记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        invitationActivityCollectionSendRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改拉新活动藏品分配记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordModifyReq request) {
        User operator = getUserByToken(token);
        invitationActivityCollectionSendRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityCollectionSendRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityCollectionSendRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityCollectionSendRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityCollectionSendRecord.class));

        return PageUtil.pageResult(invitationActivityCollectionSendRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityCollectionSendRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityCollectionSendRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityCollectionSendRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityCollectionSendRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityCollectionSendRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityCollectionSendRecord.class));

        return PageUtil.pageResult(invitationActivityCollectionSendRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询拉新活动藏品分配记录', NULL, '/core/v1/invitation_activity_collection_send_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询拉新活动藏品分配记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityCollectionSendRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCollectionSendRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityCollectionSendRecordService.listFront(request));
    }

}