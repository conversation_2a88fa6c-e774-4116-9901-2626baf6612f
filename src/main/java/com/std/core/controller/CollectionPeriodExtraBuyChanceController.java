package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionPeriodExtraBuyChance;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChanceCreateReq;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChanceListReq;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChanceListFrontReq;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChanceModifyReq;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChancePageReq;
import com.std.core.pojo.request.CollectionPeriodExtraBuyChancePageFrontReq;
import com.std.core.pojo.response.CollectionPeriodExtraBuyChanceDetailRes;
import com.std.core.pojo.response.CollectionPeriodExtraBuyChanceListRes;
import com.std.core.pojo.response.CollectionPeriodExtraBuyChancePageRes;
import com.std.core.service.ICollectionPeriodExtraBuyChanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数额外购买机会Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-06-16 13:15
 */
@ApiVersion(1)
@RestController
@Api(value = "期数额外购买机会管理", tags = "期数额外购买机会管理")
@RequestMapping("{version}/collection_period_extra_buy_chance")
public class CollectionPeriodExtraBuyChanceController extends BaseController {

    @Resource
    private ICollectionPeriodExtraBuyChanceService collectionPeriodExtraBuyChanceService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增期数额外购买机会")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChanceCreateReq request) {
//        User operator = getUserByToken(token);
//        collectionPeriodExtraBuyChanceService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除期数额外购买机会")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionPeriodExtraBuyChanceService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改期数额外购买机会")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChanceModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionPeriodExtraBuyChanceService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询期数额外购买机会")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionPeriodExtraBuyChance> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodExtraBuyChanceService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数额外购买机会")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionPeriodExtraBuyChance>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChancePageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionPeriodExtraBuyChance.class));

        return PageUtil.pageResult(collectionPeriodExtraBuyChanceService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询期数额外购买机会")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionPeriodExtraBuyChance>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChanceListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodExtraBuyChanceService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询期数额外购买机会")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionPeriodExtraBuyChanceDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodExtraBuyChanceService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询期数额外购买机会")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionPeriodExtraBuyChancePageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChancePageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionPeriodExtraBuyChance.class));

        return PageUtil.pageResult(collectionPeriodExtraBuyChanceService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询期数额外购买机会', NULL, '/core/v1/collection_period_extra_buy_chance/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询期数额外购买机会")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionPeriodExtraBuyChanceListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodExtraBuyChanceListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodExtraBuyChanceService.listFront(request));
    }

}