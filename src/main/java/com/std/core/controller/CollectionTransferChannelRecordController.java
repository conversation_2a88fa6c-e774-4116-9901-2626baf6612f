package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionTransferChannelRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionTransferChannelRecordCreateReq;
import com.std.core.pojo.request.CollectionTransferChannelRecordListReq;
import com.std.core.pojo.request.CollectionTransferChannelRecordListFrontReq;
import com.std.core.pojo.request.CollectionTransferChannelRecordModifyReq;
import com.std.core.pojo.request.CollectionTransferChannelRecordPageReq;
import com.std.core.pojo.request.CollectionTransferChannelRecordPageFrontReq;
import com.std.core.pojo.response.CollectionTransferChannelRecordDetailRes;
import com.std.core.pojo.response.CollectionTransferChannelRecordListRes;
import com.std.core.pojo.response.CollectionTransferChannelRecordPageRes;
import com.std.core.service.ICollectionTransferChannelRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品转入渠道记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-08-16 18:22
 */
@ApiVersion(1)
@RestController
@Api(value = "作品转入渠道记录管理", tags = "作品转入渠道记录管理")
@RequestMapping("{version}/collection_transfer_channel_record")
public class CollectionTransferChannelRecordController extends BaseController {

    @Resource
    private ICollectionTransferChannelRecordService collectionTransferChannelRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增作品转入渠道记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        String lockId = "collection_transfer_channel_record_create:" + request.getCollectionId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "作品转入中，请稍后重试");
            }
            collectionTransferChannelRecordService.create(request, operator);

        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除作品转入渠道记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionTransferChannelRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "撤回已转入到三方的作品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionTransferChannelRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询作品转入渠道记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionTransferChannelRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionTransferChannelRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询作品转入渠道记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionTransferChannelRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionTransferChannelRecord.class));

        return PageUtil.pageResult(collectionTransferChannelRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询作品转入渠道记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionTransferChannelRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionTransferChannelRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询作品转入渠道记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionTransferChannelRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionTransferChannelRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询作品转入渠道记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionTransferChannelRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionTransferChannelRecord.class));

        return PageUtil.pageResult(collectionTransferChannelRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询作品转入渠道记录', NULL, '/core/v1/collection_transfer_channel_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询作品转入渠道记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionTransferChannelRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionTransferChannelRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionTransferChannelRecordService.listFront(request));
    }

}