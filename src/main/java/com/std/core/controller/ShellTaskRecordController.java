package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ShellTaskRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ShellTaskRecordCreateReq;
import com.std.core.pojo.request.ShellTaskRecordListReq;
import com.std.core.pojo.request.ShellTaskRecordListFrontReq;
import com.std.core.pojo.request.ShellTaskRecordModifyReq;
import com.std.core.pojo.request.ShellTaskRecordPageReq;
import com.std.core.pojo.request.ShellTaskRecordPageFrontReq;
import com.std.core.pojo.response.ShellTaskRecordDetailRes;
import com.std.core.pojo.response.ShellTaskRecordListRes;
import com.std.core.pojo.response.ShellTaskRecordPageRes;
import com.std.core.service.IShellTaskRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔贝周期任务记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2023-03-08 14:12
 */
@ApiVersion(1)
@RestController
@Api(value = "渔贝周期任务记录管理", tags = "渔贝周期任务记录管理")
@RequestMapping("{version}/shell_task_record")
public class ShellTaskRecordController extends BaseController {

    @Resource
    private IShellTaskRecordService shellTaskRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渔贝周期任务记录', NULL, '/core/v1/shell_task_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增渔贝周期任务记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordCreateReq request) {
        User operator = getUserByToken(token);
        shellTaskRecordService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渔贝周期任务记录', NULL, '/core/v1/shell_task_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除渔贝周期任务记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        shellTaskRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渔贝周期任务记录', NULL, '/core/v1/shell_task_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改渔贝周期任务记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordModifyReq request) {
        User operator = getUserByToken(token);
        shellTaskRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔贝周期任务记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ShellTaskRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(shellTaskRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔贝周期任务记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ShellTaskRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ShellTaskRecord.class));

        return PageUtil.pageResult(shellTaskRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔贝周期任务记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ShellTaskRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(shellTaskRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔贝周期任务记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ShellTaskRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(shellTaskRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渔贝周期任务记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ShellTaskRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ShellTaskRecord.class));

        return PageUtil.pageResult(shellTaskRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔贝周期任务记录', NULL, '/core/v1/shell_task_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渔贝周期任务记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ShellTaskRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ShellTaskRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(shellTaskRecordService.listFront(request));
    }

}