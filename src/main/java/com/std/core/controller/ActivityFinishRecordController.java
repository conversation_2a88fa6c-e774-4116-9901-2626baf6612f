package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ActivityFinishRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ActivityFinishRecordCreateReq;
import com.std.core.pojo.request.ActivityFinishRecordListReq;
import com.std.core.pojo.request.ActivityFinishRecordListFrontReq;
import com.std.core.pojo.request.ActivityFinishRecordModifyReq;
import com.std.core.pojo.request.ActivityFinishRecordPageReq;
import com.std.core.pojo.request.ActivityFinishRecordPageFrontReq;
import com.std.core.pojo.response.ActivityFinishRecordDetailRes;
import com.std.core.pojo.response.ActivityFinishRecordListRes;
import com.std.core.pojo.response.ActivityFinishRecordPageRes;
import com.std.core.service.IActivityFinishRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 赋能商城中奖记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-05 17:15
 */
@ApiVersion(1)
@RestController
@Api(value = "赋能商城中奖记录管理", tags = "赋能商城中奖记录管理")
@RequestMapping("{version}/activity_finish_record")
public class ActivityFinishRecordController extends BaseController {

    @Resource
    private IActivityFinishRecordService activityFinishRecordService;

    //    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增赋能商城中奖记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        activityFinishRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除赋能商城中奖记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        activityFinishRecordService.remove(id);
//
//        return new Result();
//    }
//
    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改赋能商城中奖记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordModifyReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        activityFinishRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询赋能商城中奖记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ActivityFinishRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(activityFinishRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询赋能商城中奖记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ActivityFinishRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(activityFinishRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询赋能商城中奖记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ActivityFinishRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityFinishRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询赋能商城中奖记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front")
    public Result<ActivityFinishRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(activityFinishRecordService.detailFront(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询赋能商城中奖记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ActivityFinishRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ActivityFinishRecord.class));

        return PageUtil.pageResult(activityFinishRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询赋能商城中奖记录', NULL, '/core/v1/activity_finish_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询赋能商城中奖记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ActivityFinishRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ActivityFinishRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(activityFinishRecordService.listFront(request));
    }

}