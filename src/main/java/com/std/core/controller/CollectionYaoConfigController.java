package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BasePageReq;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionYaoConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.ICollectionYaoConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品关联爻数Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 16:00
 */
@ApiVersion(1)
@RestController
@Api(value = "作品关联爻数管理", tags = "作品关联爻数管理")
@RequestMapping("{version}/collection_yao_config")
public class CollectionYaoConfigController extends BaseController {

    @Resource
    private ICollectionYaoConfigService collectionYaoConfigService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增作品关联爻数', NULL, '/core/v1/collection_yao_config/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增作品关联爻数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionYaoConfigService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigModifyReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        collectionYaoConfigService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除作品关联爻数', NULL, '/core/v1/collection_yao_config/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "数据清洗")
    @ApiOperationSupport(order = 20)
    @PostMapping("/deal_yao_date")
    public Result remove(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token,EUserKind.SYS);
        collectionYaoConfigService.dealYaoDate(operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改作品关联爻数', NULL, '/core/v1/collection_yao_config/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "批量开启关闭")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchModify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigBatchModifyReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        collectionYaoConfigService.batchModify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询作品关联爻数', NULL, '/core/v1/collection_yao_config/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询作品关联爻数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionYaoConfig> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(collectionYaoConfigService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询作品关联爻数', NULL, '/core/v1/collection_yao_config/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询作品关联爻数")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionYaoConfig>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionYaoConfig.class));

        return PageUtil.pageResult(collectionYaoConfigService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询作品关联爻数', NULL, '/core/v1/collection_yao_config/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询作品关联爻数")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionYaoConfig>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionYaoConfigService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询作品关联爻数', NULL, '/core/v1/collection_yao_config/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询作品关联爻数")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionYaoConfigDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionYaoConfigService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询作品关联爻数', NULL, '/core/v1/collection_yao_config/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "全部可转化爻藏品列表")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionYaoConfigPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigPageFrontReq request) {
        User operator = getUserByToken(token,EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionYaoConfigService.pageFront(request,operator));
    }

    @ApiOperation(value = "front:我的可转化爻藏品列表")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_yao_exchange")
    public Result<PageInfo<CollectionDetailPageYapExchangeRes>> pageYaoExchange(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BasePageReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionYaoConfigService.pageYaoExchange(operator));
    }


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询作品关联爻数', NULL, '/core/v1/collection_yao_config/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询作品关联爻数")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionYaoConfigListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionYaoConfigListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionYaoConfigService.listFront(request));
    }

}