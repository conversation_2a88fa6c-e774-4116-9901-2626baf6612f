package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.SettleAccountJour;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SettleAccountJourCreateReq;
import com.std.core.pojo.request.SettleAccountJourListReq;
import com.std.core.pojo.request.SettleAccountJourListFrontReq;
import com.std.core.pojo.request.SettleAccountJourModifyReq;
import com.std.core.pojo.request.SettleAccountJourPageReq;
import com.std.core.pojo.request.SettleAccountJourPageFrontReq;
import com.std.core.pojo.response.SettleAccountJourDetailRes;
import com.std.core.pojo.response.SettleAccountJourListRes;
import com.std.core.pojo.response.SettleAccountJourPageRes;
import com.std.core.service.ISettleAccountJourService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 结算账户流水Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-24 11:10
 */
@ApiVersion(1)
@RestController
@Api(value = "结算账户流水管理", tags = "结算账户流水管理")
@RequestMapping("{version}/settle_account_jour")
public class SettleAccountJourController extends BaseController {

    @Resource
    private ISettleAccountJourService settleAccountJourService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增结算账户流水', NULL, '/core/v1/settle_account_jour/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增结算账户流水")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourCreateReq request) {
//        User operator = getUserByToken(token);
//        settleAccountJourService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除结算账户流水', NULL, '/core/v1/settle_account_jour/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除结算账户流水")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        settleAccountJourService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改结算账户流水', NULL, '/core/v1/settle_account_jour/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改结算账户流水")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourModifyReq request) {
//        User operator = getUserByToken(token);
//        settleAccountJourService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询结算账户流水', NULL, '/core/v1/settle_account_jour/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询结算账户流水")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<SettleAccountJour> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(settleAccountJourService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询结算账户流水', NULL, '/core/v1/settle_account_jour/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询结算账户流水")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<SettleAccountJour>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(settleAccountJourService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询结算账户流水', NULL, '/core/v1/settle_account_jour/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询结算账户流水")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<SettleAccountJour>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(settleAccountJourService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询结算账户流水', NULL, '/core/v1/settle_account_jour/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询结算账户流水")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<SettleAccountJourDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(settleAccountJourService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询结算账户流水', NULL, '/core/v1/settle_account_jour/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询结算账户流水")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<SettleAccountJourPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), SettleAccountJour.class));

        return PageUtil.pageResult(settleAccountJourService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询结算账户流水', NULL, '/core/v1/settle_account_jour/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询结算账户流水")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<SettleAccountJourListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SettleAccountJourListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(settleAccountJourService.listFront(request));
    }

}