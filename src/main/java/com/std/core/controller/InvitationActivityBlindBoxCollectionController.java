package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.InvitationActivityBlindBoxCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionCreateReq;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionListReq;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionListFrontReq;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionModifyReq;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionPageReq;
import com.std.core.pojo.request.InvitationActivityBlindBoxCollectionPageFrontReq;
import com.std.core.pojo.response.InvitationActivityBlindBoxCollectionDetailRes;
import com.std.core.pojo.response.InvitationActivityBlindBoxCollectionListRes;
import com.std.core.pojo.response.InvitationActivityBlindBoxCollectionPageRes;
import com.std.core.service.IInvitationActivityBlindBoxCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拉新活动赠送盲盒Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-06-10 14:57
 */
@ApiVersion(1)
@RestController
@Api(value = "拉新活动赠送盲盒管理", tags = "拉新活动赠送盲盒管理")
@RequestMapping("{version}/invitation_activity_blind_box_collection")
public class InvitationActivityBlindBoxCollectionController extends BaseController {

    @Resource
    private IInvitationActivityBlindBoxCollectionService invitationActivityBlindBoxCollectionService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增拉新活动赠送盲盒")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionCreateReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityBlindBoxCollectionService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除拉新活动赠送盲盒")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        invitationActivityBlindBoxCollectionService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改拉新活动赠送盲盒")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionModifyReq request) {
//        User operator = getUserByToken(token);
//        invitationActivityBlindBoxCollectionService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityBlindBoxCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityBlindBoxCollectionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityBlindBoxCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityBlindBoxCollection.class));

        return PageUtil.pageResult(invitationActivityBlindBoxCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityBlindBoxCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityBlindBoxCollectionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityBlindBoxCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityBlindBoxCollectionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityBlindBoxCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), InvitationActivityBlindBoxCollection.class));

        return PageUtil.pageResult(invitationActivityBlindBoxCollectionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询拉新活动赠送盲盒', NULL, '/core/v1/invitation_activity_blind_box_collection/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询拉新活动赠送盲盒")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityBlindBoxCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityBlindBoxCollectionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityBlindBoxCollectionService.listFront(request));
    }

}