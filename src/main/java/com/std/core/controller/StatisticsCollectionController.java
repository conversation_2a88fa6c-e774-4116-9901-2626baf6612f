package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.StatisticsCollection;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.StatisticsCollectionCreateReq;
import com.std.core.pojo.request.StatisticsCollectionListReq;
import com.std.core.pojo.request.StatisticsCollectionListFrontReq;
import com.std.core.pojo.request.StatisticsCollectionModifyReq;
import com.std.core.pojo.request.StatisticsCollectionPageReq;
import com.std.core.pojo.request.StatisticsCollectionPageFrontReq;
import com.std.core.pojo.response.StatisticsCollectionDetailRes;
import com.std.core.pojo.response.StatisticsCollectionListRes;
import com.std.core.pojo.response.StatisticsCollectionPageRes;
import com.std.core.service.IStatisticsCollectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 每日流通藏品汇总Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-14 21:35
 */
@ApiVersion(1)
@RestController
@Api(value = "每日流通藏品汇总管理", tags = "每日流通藏品汇总管理")
@RequestMapping("{version}/statistics_collection")
public class StatisticsCollectionController extends BaseController {

    @Resource
    private IStatisticsCollectionService statisticsCollectionService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增每日流通藏品汇总', NULL, '/core/v1/statistics_collection/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增每日流通藏品汇总")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionCreateReq request) {
//        User operator = getUserByToken(token);
//        statisticsCollectionService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除每日流通藏品汇总', NULL, '/core/v1/statistics_collection/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除每日流通藏品汇总")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        statisticsCollectionService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改每日流通藏品汇总', NULL, '/core/v1/statistics_collection/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改每日流通藏品汇总")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionModifyReq request) {
//        User operator = getUserByToken(token);
//        statisticsCollectionService.modify(request, operator);
//
//        return new Result();
//    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/detail/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "查询每日流通藏品汇总")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/detail/{id}")
//    public Result<StatisticsCollection> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(statisticsCollectionService.detail(id));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询每日流通藏品汇总")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<StatisticsCollection>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), StatisticsCollection.class));

        return PageUtil.pageResult(statisticsCollectionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询每日流通藏品汇总")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<StatisticsCollection>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(statisticsCollectionService.list(request));
    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询每日流通藏品汇总")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<StatisticsCollectionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(statisticsCollectionService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询每日流通藏品汇总")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<StatisticsCollectionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), StatisticsCollection.class));
//
//        return PageUtil.pageResult(statisticsCollectionService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询每日流通藏品汇总', NULL, '/core/v1/statistics_collection/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询每日流通藏品汇总")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<StatisticsCollectionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsCollectionListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(statisticsCollectionService.listFront(request));
//    }

}