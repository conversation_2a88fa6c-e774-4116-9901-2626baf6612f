package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CompanyEntity;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CompanyEntityDetailRes;
import com.std.core.pojo.response.CompanyEntityListRes;
import com.std.core.pojo.response.CompanyEntityPageRes;
import com.std.core.service.ICompanyEntityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 实物Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-12-28 13:27
 */
@ApiVersion(1)
@RestController
@Api(value = "实物管理", tags = "实物管理")
@RequestMapping("{version}/company_entity")
public class CompanyEntityController extends BaseController {

    @Resource
    private ICompanyEntityService companyEntityService;

    @ApiOperation(value = "新增实物")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityCreateReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        companyEntityService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增实物（发行方）")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_company")
    public Result createCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityCompanyCreateReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        companyEntityService.create(request, operator);

        return new Result();
    }

//    @ApiOperation(value = "删除实物")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        companyEntityService.remove(id);
//
//        return new Result();
//    }

    @ApiOperation(value = "修改实物")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityModifyReq request) {
        User operator = getUserByToken(token);
        companyEntityService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改实物（发行方）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_company")
    public Result modifyCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityCompanyModifyReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        companyEntityService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "发起审核（平台方）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/start_review")
    public Result startReview(@RequestHeader(value = "Authorization") String token,
                                     @RequestBody @Valid ChallengeStartReviewReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        companyEntityService.startReview(request, operator);

        return new Result();
    }


    @ApiOperation(value = "发起审核（发行方）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/start_review_company")
    public Result startReviewCompany(@RequestHeader(value = "Authorization") String token,
                                     @RequestBody @Valid ChallengeStartReviewReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        companyEntityService.startReview(request, operator);

        return new Result();
    }

    @ApiOperation(value = "撤回审核（发行方）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/undo_review_company")
    public Result undoReviewCompany(@RequestHeader(value = "Authorization") String token,
                                    @RequestBody @Valid ChallengeStartReviewReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        companyEntityService.undoReview(request, operator);

        return new Result();
    }

    @ApiOperation(value = "oss:审批实物申请")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_audit")
    public Result batchAudit(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeBatchAuditReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        companyEntityService.batchAudit(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询实物")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CompanyEntity> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(companyEntityService.detail(id));
    }

    @ApiOperation(value = "查询实物（发行方）")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<CompanyEntity> detailCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.BP);

        return new Result<>(companyEntityService.detail(id));
    }

    @ApiOperation(value = "分页条件查询实物")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CompanyEntity>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(companyEntityService.page(request));
    }

    @ApiOperation(value = "分页条件查询实物（发行方）")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<CompanyEntity>> pageCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        request.setCompanyId(operator.getCompanyId());
        return PageUtil.pageResult(companyEntityService.page(request));
    }

    @ApiOperation(value = "列表条件查询实物")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CompanyEntity>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityListReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(companyEntityService.list(request));
    }

    @ApiOperation(value = "列表条件查询实物(发行方)")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list_company")
    public Result<List<CompanyEntity>> listCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityListReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        request.setCompanyId(operator.getCompanyId());
        return new Result<>(companyEntityService.list(request));
    }


    @ApiOperation(value = "查询实物")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CompanyEntityDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(companyEntityService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询实物")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CompanyEntityPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CompanyEntity.class));

        return PageUtil.pageResult(companyEntityService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询实物")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CompanyEntityListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CompanyEntityListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(companyEntityService.listFront(request));
    }

}