package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.UserRecoveryRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserRecoveryRecordCreateReq;
import com.std.core.pojo.request.UserRecoveryRecordListReq;
import com.std.core.pojo.request.UserRecoveryRecordListFrontReq;
import com.std.core.pojo.request.UserRecoveryRecordModifyReq;
import com.std.core.pojo.request.UserRecoveryRecordPageReq;
import com.std.core.pojo.request.UserRecoveryRecordPageFrontReq;
import com.std.core.pojo.response.UserRecoveryRecordDetailRes;
import com.std.core.pojo.response.UserRecoveryRecordListRes;
import com.std.core.pojo.response.UserRecoveryRecordPageRes;
import com.std.core.service.IUserRecoveryRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 平台清退用户记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-17 15:40
 */
@ApiVersion(1)
@RestController
@Api(value = "平台清退用户记录管理", tags = "平台清退用户记录管理")
@RequestMapping("{version}/user_recovery_record")
public class UserRecoveryRecordController extends BaseController {

    @Resource
    private IUserRecoveryRecordService userRecoveryRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增平台清退用户记录', NULL, '/core/v1/user_recovery_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增平台清退用户记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordCreateReq request) {
        User operator = getUserByToken(token);
        userRecoveryRecordService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除平台清退用户记录', NULL, '/core/v1/user_recovery_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除平台清退用户记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userRecoveryRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改平台清退用户记录', NULL, '/core/v1/user_recovery_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改平台清退用户记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordModifyReq request) {
        User operator = getUserByToken(token);
        userRecoveryRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询平台清退用户记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserRecoveryRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询平台清退用户记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserRecoveryRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserRecoveryRecord.class));

        return PageUtil.pageResult(userRecoveryRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询平台清退用户记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserRecoveryRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询平台清退用户记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserRecoveryRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询平台清退用户记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserRecoveryRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserRecoveryRecord.class));

        return PageUtil.pageResult(userRecoveryRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询平台清退用户记录', NULL, '/core/v1/user_recovery_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询平台清退用户记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserRecoveryRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserRecoveryRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userRecoveryRecordService.listFront(request));
    }

}