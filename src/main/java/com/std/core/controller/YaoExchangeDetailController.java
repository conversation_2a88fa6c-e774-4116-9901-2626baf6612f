package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.YaoExchangeDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.YaoExchangeDetailDetailRes;
import com.std.core.pojo.response.YaoExchangeDetailListRes;
import com.std.core.pojo.response.YaoExchangeDetailPageRes;
import com.std.core.pojo.response.YaoExchangeDetailSumPageRes;
import com.std.core.service.IYaoExchangeDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 爻转化订单明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 17:58
 */
@ApiVersion(1)
@RestController
@Api(value = "爻转化订单明细管理", tags = "爻转化订单明细管理")
@RequestMapping("{version}/yao_exchange_detail")
public class YaoExchangeDetailController extends BaseController {

    @Resource
    private IYaoExchangeDetailService yaoExchangeDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增爻转化订单明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        yaoExchangeDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除爻转化订单明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        yaoExchangeDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改爻转化订单明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        yaoExchangeDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询爻转化订单明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<YaoExchangeDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(yaoExchangeDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询爻转化订单明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<YaoExchangeDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), YaoExchangeDetail.class));

        return PageUtil.pageResult(yaoExchangeDetailService.page(request));
    }

    @ApiOperation(value = "爻转化参数情况")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/sum_page")
    public Result<PageInfo<YaoExchangeDetailSumPageRes>> sumPage(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailSumPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), YaoExchangeDetail.class));

        return PageUtil.pageResult(yaoExchangeDetailService.sumPage(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询爻转化订单明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<YaoExchangeDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoExchangeDetailService.list(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询爻转化订单明细")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<YaoExchangeDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeDetailService.detailFront(id));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询爻转化订单明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<YaoExchangeDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), YaoExchangeDetail.class));

        return PageUtil.pageResult(yaoExchangeDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询爻转化订单明细', NULL, '/core/v1/yao_exchange_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询爻转化订单明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<YaoExchangeDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoExchangeDetailService.listFront(request));
    }

}