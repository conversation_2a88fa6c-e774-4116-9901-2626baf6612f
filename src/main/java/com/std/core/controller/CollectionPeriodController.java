package com.std.core.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EBoolean;
import com.std.core.enums.ECategoryStatus;
import com.std.core.enums.ECategoryType;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.Category;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.CollectionPeriod;
import com.std.core.pojo.domain.CollectionPeriodInfo;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.*;
import com.std.core.service.ICategoryService;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.service.ICompanyService;
import com.std.core.util.RedisLock;
import com.std.core.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品期数Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@ApiVersion(1)
@RestController
@Api(value = "作品期数管理", tags = "作品期数管理")
@RequestMapping("{version}/collection_period")
@Slf4j
public class CollectionPeriodController extends BaseController {

    @Resource
    private ICollectionPeriodService collectionPeriodService;

    @Resource
    private ICategoryService categoryService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private ICompanyService companyService;

    @Resource
    private RedisUtil redisUtil;

    private static Long redisLockTime = 600L;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "新增版权区期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        if (StringUtils.isBlank(request.getWordFlag())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请设置口令");
        }
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改版权区作品期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        if (StringUtils.isBlank(request.getWordFlag())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请设置口令");
        }
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增版权区期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_company")
    public Result createCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        request.setWordFlag(EBoolean.NO.getCode());
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改版权区作品期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_company")
    public Result modifyCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        request.setWordFlag(EBoolean.NO.getCode());
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增版权区期数(发行方端)")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_company_platform")
    public Result createCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateCopyRightCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改版权区作品期数(发行方端)")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_company_platform")
    public Result modifyCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyCopyRightCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改作品期数序号")
    @ApiOperationSupport(order = 40)
    @PostMapping(value = "/modifyOrderNo")
    public Result modifyOrderNo(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodOrderNoReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改作品期数序号（发行方端）")
    @ApiOperationSupport(order = 40)
    @PostMapping(value = "/modifyOrderNo_company")
    public Result modifyOrderNoCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodOrderNoReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增衍生区作品期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/createDerive")
    public Result createDerive(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增衍生区作品期数(机构)")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/createDerive_company")
    public Result createDeriveCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增衍生区作品期数(发行方端)")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/createDerive_company_platform")
    public Result createDeriveCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateDeriveCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改衍生区作品期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modifyDerive")
    public Result modifyDerive(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改衍生区作品期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modifyDerive_company")
    public Result modifyDeriveCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改衍生区作品期数(发行方)")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modifyDerive_company_platform")
    public Result modifyDeriveCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyDeriveCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "提交期数申请（发行方端）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_submit_apply_for")
    public Result periodSubmitApplyFor(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        // 检查发行方
        companyService.checkCompany(operator);
        String lockId = "period_submit_apply_for";
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "提交超时，请重试");
            }
            collectionPeriodService.periodSubmitApplyFor(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    @ApiOperation(value = "审核期数申请")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_audit")
    public Result periodAudit(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAuditReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionPeriodService.periodAudit(request, operator);

        return new Result();
    }

    @ApiOperation(value = "代机构购买")
    @ApiOperationSupport(order = 35)
    @PostMapping(value = "/oss_buy")
    public Result ossBuy(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodOssBuyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.doBuyByCompany(request, operator);
        return new Result();
    }

//    @ApiOperation(value = "代机构购买")
//    @ApiOperationSupport(order = 35)
//    @PostMapping(value = "/oss_buy_company")
//    public Result ossBuyCompany(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid CollectionPeriodOssBuyReq request) {
//        User operator = getUserByToken(token, EUserKind.BP);
//        collectionPeriodService.doBuyByCompany(request, operator);
//        return new Result();
//    }

    @ApiOperation(value = "新增盲盒区期数")
    @ApiOperationSupport(order = 36)
    @PostMapping(value = "/create_blind_box_period")
    public Result createBlindBoxPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateBlindBoxReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        if (StringUtils.isBlank(request.getWordFlag())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请设置口令");
        }
        collectionPeriodService.createBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增盲盒区期数(机构)")
    @ApiOperationSupport(order = 36)
    @PostMapping(value = "/create_blind_box_period_company")
    public Result createBlindBoxPeriodCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateBlindBoxReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        request.setWordFlag(EBoolean.NO.getCode());
        collectionPeriodService.createBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增盲盒区期数(发行方端)")
    @ApiOperationSupport(order = 36)
    @PostMapping(value = "/create_blind_box_period_company_platform")
    public Result createBlindBoxPeriodCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateBlindBoxCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.createBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改盲盒区期数")
    @ApiOperationSupport(order = 37)
    @PostMapping(value = "/modify_blind_box_period")
    public Result modifyBlindBoxPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyBlindBoxReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        if (StringUtils.isBlank(request.getWordFlag())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "请设置口令");
        }
        collectionPeriodService.modifyBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改盲盒区期数")
    @ApiOperationSupport(order = 37)
    @PostMapping(value = "/modify_blind_box_period_company")
    public Result modifyBlindBoxPeriodCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyBlindBoxReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        request.setWordFlag(EBoolean.NO.getCode());
        collectionPeriodService.modifyBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改盲盒区期数（发行方端）")
    @ApiOperationSupport(order = 37)
    @PostMapping(value = "/modify_blind_box_period_company_platform")
    public Result modifyBlindBoxPeriodCompanyPlatform(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyBlindBoxCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.modifyBlindBoxPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增抽签区期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_draw_straws_period")
    public Result createDrawStrawsPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateDrawStrawsReq request) {
        User operator = getUserByToken(token);

        collectionPeriodService.createDrawStrawsPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改抽签区期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_draw_straws_period")
    public Result modifyDrawStrawsPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyDrawStrawsReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.modifyDrawStrawsPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增竞拍期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_period_auction_period")
    public Result createPeriodAuctionPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodCreateAuctionReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionPeriodService.createPeriodAuctionPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改竞拍期数")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/modify_period_auction_period")
    public Result modifyPeriodAuctionPeriod(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodModifyAuctionReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.modifyPeriodAuctionPeriod(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架作品期数")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_and_down")
    public Result batchUpAndDown(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodBatchUpAndDownReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionPeriodService.batchUpAndDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架作品期数(发行方端)")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_and_down_company")
    public Result batchUpAndDownCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodBatchUpAndDownReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.batchUpAndDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询作品期数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionPeriod> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodService.detail(id));
    }

    @ApiOperation(value = "查询作品期数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_audit/{id}")
    public Result<CollectionPeriodAuditRes> detailAudit(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodService.detailAudit(id));
    }

    @ApiOperation(value = "查询作品期数（发行方端）")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<CollectionPeriod> detailCompany(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionPeriodService.detail(id));
    }

    @ApiOperation(value = "分页条件查询作品期数")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionPeriod>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionPeriod.class));

        return PageUtil.pageResult(collectionPeriodService.page(request, operator));
    }

    @ApiOperation(value = "分页条件查询作品期数（发行方端）")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<CollectionPeriod>> pageCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionPeriod.class));
        request.setAuthorId(operator.getCompanyId());
        return PageUtil.pageResult(collectionPeriodService.page(request, operator));
    }

    @ApiOperation(value = "查询作品期数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/auction_detail/{id}")
    public Result<CollectionPeriodInfo> auctionDetail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionPeriodService.auctionDetail(id));
    }


    @ApiOperation(value = "竞拍作品期数oss分页查")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/auction_page")
    public Result<PageInfo<CollectionPeriodInfo>> auctionPage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAuctionPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionPeriodService.auctionPage(request));
    }

    @ApiOperation(value = "查询期数发售的类型")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/public/category/list")
    public Result<List<CollectionPeriodCategoryRes>> categoryList(@RequestBody @Valid CollectionPeriodLocationReq request) {
        Category category = new Category();
        category.setStatus(ECategoryStatus.ONE.getCode());
        category.setType(ECategoryType.ZERO.getCode());
        category.setOrderBy("t.order_no desc");

        String redisId = String.format(RedisKeyList.MT_CATEGORY_LIST_KEY, ECategoryStatus.ONE.getCode(), ECategoryType.ZERO.getCode());

        List<CollectionPeriodCategoryRes> resList = null;
        if (redisUtil.hasKey(redisId)) {
            JSONArray tableData = JSONArray.parseArray(JSON.toJSONString(redisUtil.get(redisId)));
            resList = JSONObject.parseArray(tableData.toJSONString(), CollectionPeriodCategoryRes.class);
        } else {
            resList = categoryService.list(category);
            redisUtil.set(redisId, resList, redisLockTime);
        }
        return new Result<>(resList);
    }

    @ApiOperation(value = "查询期数发售的发售板块")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/public/plate_category/list")
    public Result<List<CollectionPeriodCategoryRes>> plateCategoryList() {
        Category category = new Category();
        category.setStatus(ECategoryStatus.ONE.getCode());
        category.setType(ECategoryType.SEVEN.getCode());
        category.setOrderBy("t.order_no desc");

        return new Result<>(categoryService.list(category));
    }

    @ApiOperation(value = "查询期数发售的发售板块")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/public/plate_category/list_company")
    public Result<List<CollectionPeriodCategoryRes>> plateCategoryListCompany() {
        Category category = new Category();
        category.setStatus(ECategoryStatus.ONE.getCode());
        category.setType(ECategoryType.SEVEN.getCode());
        category.setOrderBy("t.order_no desc");

        List<CollectionPeriodCategoryRes> list = categoryService.list(category);
        for (CollectionPeriodCategoryRes res : list) {
            if (StringUtils.isBlank(res.getId())) {
                res.setId("0");
            }
        }
        return new Result<>(list);
    }

    @ApiOperation(value = "列表条件查询作品期数")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionPeriod>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodService.list(request, operator));
    }

    @ApiOperation(value = "列表查询发行方涉及到的渠道")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/channel_list")
    public Result<List<ChannelMerchant>> list(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionPeriodService.listChannelMerchant(operator));
    }

    @ApiOperation(value = "前端分页条件查询作品期数(公开)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<CollectionPeriodPageFrontRes>> pageFront
            (@RequestHeader(value = "Authorization", required = false) String token,
                    @RequestHeader(value = "channelId", required = false) Long channelId,
                    @RequestBody @Valid CollectionPeriodPageFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        if (null == channelId) {
            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
            if (null == channelMerchant) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的渠道");
            }
            channelId = channelMerchant.getId();
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionPeriodService.pageFront(request, operator, channelId));
    }

    @ApiOperation(value = "前端分页条件查询作品期数(公开)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/search_page_front")
    public Result<PageInfo<CollectionPeriodPageFrontRes>> searchPageFront
            (@RequestHeader(value = "Authorization", required = false) String token,
                    @RequestHeader(value = "channelId", required = false) Long channelId,
                    @RequestBody @Valid CollectionPeriodPageFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        if (null == channelId) {
            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
            if (null == channelMerchant) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的渠道");
            }
            channelId = channelMerchant.getId();
        }
        if (StringUtils.isNotBlank(request.getKeywords())) {
            String keywords = request.getKeywords();
            String replace = keywords.replace(",", "");
            request.setKeywords(replace);
        }

        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionPeriodService.pageFront(request, operator, channelId));
    }

    @ApiOperation(value = "前端板块列表查(含期数)(公开)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/plate_list_front")
    public Result<List<CollectionPeriodPlateListRes>> plateListFront() {
        ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
        if (null == channelMerchant) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的渠道");
        }

        return new Result<>(
                collectionPeriodService.listByPlate(channelMerchant.getId(), ECategoryType.SEVEN.getCode()));
    }

    @ApiOperation(value = "前端作品期数列表查(公开)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/list_front")
    public Result<List<CollectionPeriodListRes>> listFront
            (@RequestHeader(value = "Authorization", required = false) String token,
                    @RequestHeader(value = "channelId", required = false) Long channelId,
                    @RequestBody @Valid CollectionPeriodListFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        if (null == channelId) {
            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
            if (null == channelMerchant) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的渠道");
            }
            channelId = channelMerchant.getId();
        }
        return new Result<>(collectionPeriodService.listFront(request, operator, channelId));
    }

    @ApiOperation(value = "front:列表查询期数发行方页展示分类")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/public/company_category/list_front")
    public Result<List<CategoryListRes>> queryCategoryList(@RequestBody @Valid CollectionPeriodListFrontAuthorIdReq request) {
        return new Result<>(collectionPeriodService.listCompanyCategoryFront(request.getAuthorId()));
    }

    @ApiOperation(value = "前端作品期数列表查(h5)")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/list_front_h5")
    public Result<List<CollectionPeriodListRes>> listFrontH5
            (@RequestHeader(value = "Authorization", required = false) String token,
                    @RequestHeader(value = "channelId", required = false) Long channelId,
                    @RequestBody @Valid CollectionPeriodListFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        if (null == channelId) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "分发渠道不能为空");
        }

        return new Result<>(collectionPeriodService.listFront(request, operator, channelId));
    }

    @ApiOperation(value = "前端详情查询作品期数(公开)")
    @ApiOperationSupport(order = 90)
    @PostMapping("/public/detail_front/{id}")
    public Result<CollectionPeriodDetailFrontRes> publicDetailFront
            (@RequestHeader(value = "Authorization", required = false) String token,
                    @PathVariable("id") @Valid Long id) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }

        return new Result<>(collectionPeriodService.detailFront(id, operator));
    }

    @ApiOperation(value = "分页查询盲盒期数藏品信息")
    @ApiOperationSupport(order = 90)
    @PostMapping("/public/period_collection_page")
    public Result<PageInfo<CollectionPeriodCollectionPageRes>> periodCollectionPage
            (@RequestHeader(value = "Authorization", required = false) String token,
             @RequestBody @Valid CollectionPeriodCollectionPageReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }

        return PageUtil.pageResult(collectionPeriodService.periodCollectionPage(request));
    }


    /********************************************************************************************************************************/
    @ApiOperation(value = "新增衍生区并生成作品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_derive_company_platform")
    public Result createDerivePeriodAndCollection(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAndCollectionCreateDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改衍生区并生成作品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/modify_derive_company_platform")
    public Result modifyDerivePeriodAndCollection(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAndCollectionModifyDeriveReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增版权区并生成作品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_period_company_platform")
    public Result createPeriodAndCollection(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAndCollectionCreateCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改版权区并生成作品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/modify_period_company_platform")
    public Result modifyPeriodAndCollection(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAndCollectionModifyCopyRightReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增盲盒区期数(发行方端)")
    @ApiOperationSupport(order = 36)
    @PostMapping(value = "/create_blind_box_period_and_collection")
    public Result createBlindBoxPeriodAndCollectio(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodBlindBoxAndCollectionCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改盲盒区期数(发行方端)")
    @ApiOperationSupport(order = 36)
    @PostMapping(value = "/modify_blind_box_period_and_collection")
    public Result modifyBlindBoxPeriodAndCollectio(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodBlindBoxAndCollectionModifyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        collectionPeriodService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询作品期数（发行方端）")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_period_collection/{id}")
    public Result<CollectionPeriodAndCollectionDetailRes> detailPeriodAndCollection(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionPeriodService.detailPeriodAndCollection(id));
    }

    @ApiOperation(value = "提交期数申请（发行方端）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_submit_apply_for_company")
    public Result periodSubmitApplyForCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        // 检查发行方
        companyService.checkCompany(operator);
        String lockId = "period_submit_apply_for";
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "提交超时，请重试");
            }
            collectionPeriodService.periodSubmitApplyForCompany(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    @ApiOperation(value = "撤回期数申请（发行方端）")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_undo_apply_for_company")
    public Result periodUndoApplyForCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        // 检查发行方
        companyService.checkCompany(operator);
        String lockId = "period_submit_apply_for";
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "提交超时，请重试");
            }
            collectionPeriodService.periodUndoApplyForCompany(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    @ApiOperation(value = "审核期数申请")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_audit_company")
    public Result periodAuditCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAuditCompanyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionPeriodService.periodAuditCompany(request, operator);

        return new Result();
    }

    @ApiOperation(value = "审核期数申请")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/period_audit_company_oss")
    public Result periodAuditCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionPeriodAuditCompanyOssReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        collectionPeriodService.periodAuditCompanyOss(request, operator);

        return new Result();
    }
}