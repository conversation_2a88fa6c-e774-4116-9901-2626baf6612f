package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.MetaMilletTransferDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MetaMilletTransferDetailCreateReq;
import com.std.core.pojo.request.MetaMilletTransferDetailListReq;
import com.std.core.pojo.request.MetaMilletTransferDetailListFrontReq;
import com.std.core.pojo.request.MetaMilletTransferDetailModifyReq;
import com.std.core.pojo.request.MetaMilletTransferDetailPageReq;
import com.std.core.pojo.request.MetaMilletTransferDetailPageFrontReq;
import com.std.core.pojo.response.MetaMilletTransferDetailDetailRes;
import com.std.core.pojo.response.MetaMilletTransferDetailListRes;
import com.std.core.pojo.response.MetaMilletTransferDetailPageRes;
import com.std.core.service.IMetaMilletTransferDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元粟转赠明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:41
 */
@ApiVersion(1)
@RestController
@Api(value = "元粟转赠明细管理", tags = "元粟转赠明细管理")
@RequestMapping("{version}/meta_millet_transfer_detail")
public class MetaMilletTransferDetailController extends BaseController {

    @Resource
    private IMetaMilletTransferDetailService metaMilletTransferDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增元粟转赠明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        metaMilletTransferDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元粟转赠明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        metaMilletTransferDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改元粟转赠明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        metaMilletTransferDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元粟转赠明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MetaMilletTransferDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(metaMilletTransferDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元粟转赠明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MetaMilletTransferDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MetaMilletTransferDetail.class));

        return PageUtil.pageResult(metaMilletTransferDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元粟转赠明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MetaMilletTransferDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元粟转赠明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MetaMilletTransferDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元粟转赠明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MetaMilletTransferDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MetaMilletTransferDetail.class));

        return PageUtil.pageResult(metaMilletTransferDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元粟转赠明细', NULL, '/core/v1/meta_millet_transfer_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元粟转赠明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MetaMilletTransferDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferDetailService.listFront(request));
    }

}