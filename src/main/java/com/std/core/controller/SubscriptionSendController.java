package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.SubscriptionSend;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.SubscriptionSendCreateReq;
import com.std.core.pojo.request.SubscriptionSendListReq;
import com.std.core.pojo.request.SubscriptionSendListFrontReq;
import com.std.core.pojo.request.SubscriptionSendModifyReq;
import com.std.core.pojo.request.SubscriptionSendPageReq;
import com.std.core.pojo.request.SubscriptionSendPageFrontReq;
import com.std.core.pojo.response.SubscriptionSendDetailRes;
import com.std.core.pojo.response.SubscriptionSendListRes;
import com.std.core.pojo.response.SubscriptionSendPageRes;
import com.std.core.service.ISubscriptionSendService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订阅推送Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-08-20 11:57
 */
@ApiVersion(1)
@RestController
@Api(value = "订阅推送管理", tags = "订阅推送管理")
@RequestMapping("{version}/subscription_send")
public class SubscriptionSendController extends BaseController {

    @Resource
    private ISubscriptionSendService subscriptionSendService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增订阅推送', NULL, '/core/v1/subscription_send/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增订阅推送")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/subscription")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "subscription_send_subscription:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "订阅中，请稍后重试");
            }
            subscriptionSendService.create(request, operator);
            return new Result();
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除订阅推送', NULL, '/core/v1/subscription_send/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除订阅推送")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        subscriptionSendService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改订阅推送', NULL, '/core/v1/subscription_send/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改订阅推送")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendModifyReq request) {
//        User operator = getUserByToken(token);
//        subscriptionSendService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询订阅推送', NULL, '/core/v1/subscription_send/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询订阅推送")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<SubscriptionSend> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(subscriptionSendService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询订阅推送', NULL, '/core/v1/subscription_send/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询订阅推送")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<SubscriptionSend>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), SubscriptionSend.class));

        return PageUtil.pageResult(subscriptionSendService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询订阅推送', NULL, '/core/v1/subscription_send/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询订阅推送")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<SubscriptionSend>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(subscriptionSendService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询订阅推送', NULL, '/core/v1/subscription_send/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询订阅推送")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<SubscriptionSendDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(subscriptionSendService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询订阅推送', NULL, '/core/v1/subscription_send/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询订阅推送")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<SubscriptionSendPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), SubscriptionSend.class));

        return PageUtil.pageResult(subscriptionSendService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询订阅推送', NULL, '/core/v1/subscription_send/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询订阅推送")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<SubscriptionSendListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid SubscriptionSendListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(subscriptionSendService.listFront(request));
    }

}