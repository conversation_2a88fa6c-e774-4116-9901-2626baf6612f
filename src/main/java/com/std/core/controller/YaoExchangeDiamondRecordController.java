package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.YaoExchangeDiamondRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.YaoExchangeDiamondRecordCreateReq;
import com.std.core.pojo.request.YaoExchangeDiamondRecordListReq;
import com.std.core.pojo.request.YaoExchangeDiamondRecordListFrontReq;
import com.std.core.pojo.request.YaoExchangeDiamondRecordModifyReq;
import com.std.core.pojo.request.YaoExchangeDiamondRecordPageReq;
import com.std.core.pojo.request.YaoExchangeDiamondRecordPageFrontReq;
import com.std.core.pojo.response.YaoExchangeDiamondRecordDetailRes;
import com.std.core.pojo.response.YaoExchangeDiamondRecordListRes;
import com.std.core.pojo.response.YaoExchangeDiamondRecordPageRes;
import com.std.core.service.IYaoExchangeDiamondRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 钻石兑爻记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 14:48
 */
@ApiVersion(1)
@RestController
@Api(value = "钻石兑爻记录管理", tags = "钻石兑爻记录管理")
@RequestMapping("{version}/yao_exchange_diamond_record")
public class YaoExchangeDiamondRecordController extends BaseController {

    @Resource
    private IYaoExchangeDiamondRecordService yaoExchangeDiamondRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增钻石兑爻记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        yaoExchangeDiamondRecordService.create(request, operator);
//
//        return new Result();
//    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除钻石兑爻记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        yaoExchangeDiamondRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改钻石兑爻记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        yaoExchangeDiamondRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询钻石兑爻记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<YaoExchangeDiamondRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(yaoExchangeDiamondRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询钻石兑爻记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<YaoExchangeDiamondRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoExchangeDiamondRecord.class));

        return PageUtil.pageResult(yaoExchangeDiamondRecordService.page(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/list', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "列表条件查询钻石兑爻记录")
//    @ApiOperationSupport(order = 60)
//    @PostMapping(value = "/list")
//    public Result<List<YaoExchangeDiamondRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordListReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeDiamondRecordService.list(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询钻石兑爻记录")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<YaoExchangeDiamondRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeDiamondRecordService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询钻石兑爻记录")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<YaoExchangeDiamondRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), YaoExchangeDiamondRecord.class));
//
//        return PageUtil.pageResult(yaoExchangeDiamondRecordService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询钻石兑爻记录', NULL, '/core/v1/yao_exchange_diamond_record/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询钻石兑爻记录")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<YaoExchangeDiamondRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeDiamondRecordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeDiamondRecordService.listFront(request));
//    }

}