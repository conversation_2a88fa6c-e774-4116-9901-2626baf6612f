package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.InvitationActivityRegisteredSeries;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesCreateReq;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesListReq;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesListFrontReq;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesModifyReq;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesPageReq;
import com.std.core.pojo.request.InvitationActivityRegisteredSeriesPageFrontReq;
import com.std.core.pojo.response.InvitationActivityRegisteredSeriesDetailRes;
import com.std.core.pojo.response.InvitationActivityRegisteredSeriesListRes;
import com.std.core.pojo.response.InvitationActivityRegisteredSeriesPageRes;
import com.std.core.service.IInvitationActivityRegisteredSeriesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拉新活动注册赠送系列Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-12 15:32
 */
@ApiVersion(1)
@RestController
@Api(value = "拉新活动注册赠送系列管理", tags = "拉新活动注册赠送系列管理")
@RequestMapping("{version}/invitation_activity_registered_series")
public class InvitationActivityRegisteredSeriesController extends BaseController {

    @Resource
    private IInvitationActivityRegisteredSeriesService invitationActivityRegisteredSeriesService;

    //    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增拉新活动注册赠送系列")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityRegisteredSeriesService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增拉新活动注册赠送系列")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/company_create")
    public Result companyCreate(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityRegisteredSeriesService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除拉新活动注册赠送系列")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityRegisteredSeriesService.remove(id, operator);

        return new Result();
    }

    @ApiOperation(value = "删除拉新活动注册赠送系列")
    @ApiOperationSupport(order = 20)
    @PostMapping("/company_remove/{id}")
    public Result companyRemove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityRegisteredSeriesService.remove(id, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改拉新活动注册赠送系列")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityRegisteredSeriesService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改拉新活动注册赠送系列")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/company_modify")
    public Result companyModify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesModifyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityRegisteredSeriesService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "废除系列")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/abolish")
    public Result abolish(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityRegisteredSeriesService.abolish(request, operator);

        return new Result();
    }

    @ApiOperation(value = "废除系列")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/company_abolish")
    public Result companyAbolish(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityRegisteredSeriesService.abolish(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityRegisteredSeries> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(invitationActivityRegisteredSeriesService.detail(id));
    }

    @ApiOperation(value = "查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 40)
    @PostMapping("/company_detail/{id}")
    public Result<InvitationActivityRegisteredSeries> companyDetail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.BP);

        return new Result<>(invitationActivityRegisteredSeriesService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityRegisteredSeries>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(invitationActivityRegisteredSeriesService.page(request,operator));
    }

    @ApiOperation(value = "分页条件查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/company_page")
    public Result<PageInfo<InvitationActivityRegisteredSeries>> companyPage(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesPageReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(invitationActivityRegisteredSeriesService.page(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityRegisteredSeries>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredSeriesService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityRegisteredSeriesDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredSeriesService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityRegisteredSeriesPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityRegisteredSeries.class));

        return PageUtil.pageResult(invitationActivityRegisteredSeriesService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询拉新活动注册赠送系列', NULL, '/core/v1/invitation_activity_registered_series/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询拉新活动注册赠送系列")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityRegisteredSeriesListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityRegisteredSeriesListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityRegisteredSeriesService.listFront(request));
    }

}