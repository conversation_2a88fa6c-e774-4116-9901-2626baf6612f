package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserIdentifyStatus;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ChannelUser;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelCheckAuthReq;
import com.std.core.pojo.request.ChannelCheckMandateReq;
import com.std.core.pojo.request.ChannelUserListFrontReq;
import com.std.core.pojo.request.ChannelUserListReq;
import com.std.core.pojo.request.ChannelUserPageFrontReq;
import com.std.core.pojo.request.ChannelUserPageReq;
import com.std.core.pojo.response.ChannelCheckMandateRes;
import com.std.core.pojo.response.ChannelUserCreateRes;
import com.std.core.pojo.response.ChannelUserDetailRes;
import com.std.core.pojo.response.ChannelUserListRes;
import com.std.core.pojo.response.ChannelUserPageRes;
import com.std.core.service.IChannelUserService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渠道用户序号Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-20 14:21
 */
@ApiVersion(1)
@RestController
@Api(value = "渠道用户序号管理", tags = "渠道用户序号管理")
@RequestMapping("{version}/channel_user")
public class ChannelUserController extends BaseController {

    @Resource
    private IChannelUserService channelUserService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渠道用户序号', NULL, '/core/v1/channel_user/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "渠道授权")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/user_apply_authorization")
    public Result<ChannelUserCreateRes> create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelCheckMandateReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus())
                && !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }

        String lockId = "user_apply_authorization:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道授权中，请稍后重试");
            }

            return new Result<>(channelUserService.create(operator, request));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

    @ApiOperation(value = "渠道授权")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/user_apply_authorization_h5")
    public Result<ChannelUserCreateRes> createH5(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelCheckMandateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
//        EChannelSystemCode dataByBannerUrl = EChannelSystemCode.getDataByBannerUrl(request.getChannelUrl());

        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())
                && !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }

        String lockId = "user_apply_authorization:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道授权中，请稍后重试");
            }

            return new Result<>(channelUserService.create(operator, request));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

    }

//    @ApiOperation(value = "北文交所渠道授权")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/culturalchain/user_apply_authorization")
//    public Result<ChannelUserCreateRes> create(@RequestHeader(value = "Authorization") String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//        EChannelSystemCode dataByBannerUrl = EChannelSystemCode.CULTURALCHAIN;
//
//
//        String lockId = "user_apply_authorization:" + operator.getId();
//        Long time = System.currentTimeMillis() + metaLockTimeout;
//
//        try {
//            if (!redisLock.lock(lockId, String.valueOf(time))) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "渠道授权中，请稍后重试");
//            }
//
//            return new Result<>(channelUserService.create(operator, dataByBannerUrl.getCode()));
//        } finally {
//            redisLock.unlock(lockId, String.valueOf(time));
//        }
//
//    }

//    @ApiOperation(value = "一口价渠道授权")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/buyout_user_apply_authorization")
//    public Result<ChannelUserCreateRes> buyoutCreate(@RequestHeader(value = "Authorization") String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//
//        return new Result<>(channelUserService.create(operator, EChannelSystemCode.BUYOUT.getCode()));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渠道用户序号', NULL, '/core/v1/channel_user/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除渠道用户序号")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        channelUserService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渠道用户序号', NULL, '/core/v1/channel_user/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改渠道用户序号")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelUserModifyReq request) {
//        User operator = getUserByToken(token);
//        channelUserService.modify(request, operator);
//
//        return new Result();
//    }

//    @ApiOperation(value = "查询三方渠道用户是否授权")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/check_Mandate")
//    public Result<ChannelCheckMandateRes> checkMandate(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid ChannelCheckMandateReq request) {
//        User operator = getUserByToken(token, EUserKind.C);
//        EChannelSystemCode dataByBannerUrl = EChannelSystemCode.getDataByBannerUrl(request.getChannelUrl());
//        return new Result<>(channelUserService.checkMandate(operator, dataByBannerUrl.getCode()));
//    }

    @ApiOperation(value = "查询三方渠道用户是否授权")
    @ApiOperationSupport(order = 40)
    @PostMapping("/check_Mandate")
    public Result<ChannelCheckMandateRes> checkAuth(@RequestHeader(value = "Authorization", required = false) String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestBody @Valid ChannelCheckAuthReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        return new Result<>(channelUserService.checkAuth(operator, request.getNavigateId(), client));
    }

//    @ApiOperation(value = "查询北文交所用户是否授权")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/check_culturalchain")
//    public Result<ChannelCheckMandateRes> checkCulturalchain(@RequestHeader(value = "Authorization", required = false) String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//
//        return new Result<>(channelUserService.checkCulturalchainMandate(operator));
//    }

//    @ApiOperation(value = "查询一口价渠道用户是否授权")
//    @ApiOperationSupport(order = 40)
//    @PostMapping("/buyout_check_Mandate")
//    public Result<ChannelCheckMandateRes> buyoutCheckMandate(@RequestHeader(value = "Authorization") String token) {
//        User operator = getUserByToken(token, EUserKind.C);
//
//        return new Result<>(channelUserService.checkMandate(operator, EChannelSystemCode.BUYOUT.getCode()));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渠道用户序号', NULL, '/core/v1/channel_user/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渠道用户序号")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChannelUser> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(channelUserService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渠道用户序号', NULL, '/core/v1/channel_user/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渠道用户序号")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChannelUser>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelUserPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ChannelUser.class));

        return PageUtil.pageResult(channelUserService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渠道用户序号', NULL, '/core/v1/channel_user/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渠道用户序号")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChannelUser>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelUserListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(channelUserService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渠道用户序号', NULL, '/core/v1/channel_user/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渠道用户序号")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChannelUserDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(channelUserService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渠道用户序号', NULL, '/core/v1/channel_user/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渠道用户序号")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChannelUserPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelUserPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ChannelUser.class));

        return PageUtil.pageResult(channelUserService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渠道用户序号', NULL, '/core/v1/channel_user/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渠道用户序号")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChannelUserListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChannelUserListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(channelUserService.listFront(request));
    }

}