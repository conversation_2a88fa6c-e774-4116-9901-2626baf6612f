package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.StatisticsExceptUser;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.StatisticsExceptUserCreateReq;
import com.std.core.pojo.request.StatisticsExceptUserListReq;
import com.std.core.pojo.request.StatisticsExceptUserListFrontReq;
import com.std.core.pojo.request.StatisticsExceptUserModifyReq;
import com.std.core.pojo.request.StatisticsExceptUserPageReq;
import com.std.core.pojo.request.StatisticsExceptUserPageFrontReq;
import com.std.core.pojo.response.StatisticsExceptUserDetailRes;
import com.std.core.pojo.response.StatisticsExceptUserListRes;
import com.std.core.pojo.response.StatisticsExceptUserPageRes;
import com.std.core.service.IStatisticsExceptUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统计排除用户Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-08 15:22
 */
@ApiVersion(1)
@RestController
@Api(value = "统计排除用户管理", tags = "统计排除用户管理")
@RequestMapping("{version}/statistics_except_user")
public class StatisticsExceptUserController extends BaseController {

    @Resource
    private IStatisticsExceptUserService statisticsExceptUserService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增统计排除用户', NULL, '/core/v1/statistics_except_user/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增统计排除用户")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        statisticsExceptUserService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除统计排除用户', NULL, '/core/v1/statistics_except_user/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除统计排除用户")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove")
    public Result remove(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        statisticsExceptUserService.remove(request);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改统计排除用户', NULL, '/core/v1/statistics_except_user/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改统计排除用户")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserModifyReq request) {
        User operator = getUserByToken(token);
        statisticsExceptUserService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询统计排除用户', NULL, '/core/v1/statistics_except_user/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询统计排除用户")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<StatisticsExceptUser> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(statisticsExceptUserService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询统计排除用户', NULL, '/core/v1/statistics_except_user/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询统计排除用户")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<StatisticsExceptUser>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), StatisticsExceptUser.class));

        return PageUtil.pageResult(statisticsExceptUserService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询统计排除用户', NULL, '/core/v1/statistics_except_user/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询统计排除用户")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<StatisticsExceptUser>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(statisticsExceptUserService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询统计排除用户', NULL, '/core/v1/statistics_except_user/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询统计排除用户")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<StatisticsExceptUserDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(statisticsExceptUserService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询统计排除用户', NULL, '/core/v1/statistics_except_user/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询统计排除用户")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<StatisticsExceptUserPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), StatisticsExceptUser.class));

        return PageUtil.pageResult(statisticsExceptUserService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询统计排除用户', NULL, '/core/v1/statistics_except_user/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询统计排除用户")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<StatisticsExceptUserListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid StatisticsExceptUserListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(statisticsExceptUserService.listFront(request));
    }

}