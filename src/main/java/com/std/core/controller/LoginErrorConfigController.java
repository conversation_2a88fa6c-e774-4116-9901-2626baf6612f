package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.LoginErrorConfig;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.LoginErrorConfigDetailRes;
import com.std.core.pojo.response.LoginErrorConfigListRes;
import com.std.core.pojo.response.LoginErrorConfigPageRes;
import com.std.core.service.ILoginErrorConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录密码输入错误处理机制Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-14 09:42
 */
@ApiVersion(1)
@RestController
@Api(value = "登录密码输入错误处理机制管理", tags = "登录密码输入错误处理机制管理")
@RequestMapping("{version}/login_error_config")
public class LoginErrorConfigController extends BaseController {

    @Resource
    private ILoginErrorConfigService loginErrorConfigService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增登录密码输入错误处理机制")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        loginErrorConfigService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除登录密码输入错误处理机制")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        loginErrorConfigService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改登录密码输入错误处理机制")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        loginErrorConfigService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "登录密码输入错误处理机制批量上下架")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_up_down")
    public Result batchUpDown(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigBatchModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        loginErrorConfigService.batchUpDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询登录密码输入错误处理机制")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LoginErrorConfig> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(loginErrorConfigService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询登录密码输入错误处理机制")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LoginErrorConfig>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(loginErrorConfigService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询登录密码输入错误处理机制")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LoginErrorConfig>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(loginErrorConfigService.list(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询登录密码输入错误处理机制")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<LoginErrorConfigDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(loginErrorConfigService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询登录密码输入错误处理机制")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<LoginErrorConfigPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), LoginErrorConfig.class));
//
//        return PageUtil.pageResult(loginErrorConfigService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询登录密码输入错误处理机制', NULL, '/core/v1/login_error_config/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询登录密码输入错误处理机制")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<LoginErrorConfigListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorConfigListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(loginErrorConfigService.listFront(request));
//    }

}