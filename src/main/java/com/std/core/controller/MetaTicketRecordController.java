package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.MetaTicketRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MetaTicketRecordCreateReq;
import com.std.core.pojo.request.MetaTicketRecordListReq;
import com.std.core.pojo.request.MetaTicketRecordListFrontReq;
import com.std.core.pojo.request.MetaTicketRecordModifyReq;
import com.std.core.pojo.request.MetaTicketRecordPageReq;
import com.std.core.pojo.request.MetaTicketRecordPageFrontReq;
import com.std.core.pojo.response.MetaTicketRecordDetailRes;
import com.std.core.pojo.response.MetaTicketRecordListRes;
import com.std.core.pojo.response.MetaTicketRecordPageRes;
import com.std.core.service.IMetaTicketRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元宇宙门票使用记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-10-13 15:17
 */
@ApiVersion(1)
@RestController
@Api(value = "元宇宙门票使用记录管理", tags = "元宇宙门票使用记录管理")
@RequestMapping("{version}/meta_ticket_record")
public class MetaTicketRecordController extends BaseController {

    @Resource
    private IMetaTicketRecordService metaTicketRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增元宇宙门票使用记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        metaTicketRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元宇宙门票使用记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        metaTicketRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改元宇宙门票使用记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        metaTicketRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MetaTicketRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(metaTicketRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MetaTicketRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MetaTicketRecord.class));

        return PageUtil.pageResult(metaTicketRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MetaTicketRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaTicketRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MetaTicketRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(metaTicketRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MetaTicketRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), MetaTicketRecord.class));

        return PageUtil.pageResult(metaTicketRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元宇宙门票使用记录', NULL, '/core/v1/meta_ticket_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元宇宙门票使用记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MetaTicketRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaTicketRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaTicketRecordService.listFront(request));
    }

}