package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.LotteryActivityJoinRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LotteryActivityJoinRecordCreateReq;
import com.std.core.pojo.request.LotteryActivityJoinRecordListFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinRecordListReq;
import com.std.core.pojo.request.LotteryActivityJoinRecordPageFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinRecordPageReq;
import com.std.core.pojo.response.LotteryActivityJoinRecordAllRes;
import com.std.core.pojo.response.LotteryActivityJoinRecordDetailRes;
import com.std.core.pojo.response.LotteryActivityJoinRecordListRes;
import com.std.core.service.ILotteryActivityJoinRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抽奖活动报名记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-06-02 11:03
 */
@ApiVersion(1)
@RestController
@Api(value = "抽奖活动报名记录管理", tags = "抽奖活动报名记录管理")
@RequestMapping("{version}/lottery_activity_join_record")
public class LotteryActivityJoinRecordController extends BaseController {

    @Resource
    private ILotteryActivityJoinRecordService lotteryActivityJoinRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增抽奖活动报名记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinRecordCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "lottery_activity_join_record_join:" + request.getActivityId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            }
            lotteryActivityJoinRecordService.create(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询抽奖活动报名记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LotteryActivityJoinRecord> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(lotteryActivityJoinRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询抽奖活动报名记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LotteryActivityJoinRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(lotteryActivityJoinRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询抽奖活动报名记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LotteryActivityJoinRecord>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询抽奖活动报名记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<LotteryActivityJoinRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "我的报名记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<LotteryActivityJoinRecordDetailRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinRecordPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return PageUtil.pageResult(lotteryActivityJoinRecordService.pageFront(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "所有报名记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front_all")
    public Result<PageInfo<LotteryActivityJoinRecordAllRes>> pageFrontAll(
            @RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody @Valid LotteryActivityJoinRecordPageFrontReq request) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(lotteryActivityJoinRecordService.pageFrontAll(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询抽奖活动报名记录', NULL, '/core/v1/lottery_activity_join_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询抽奖活动报名记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<LotteryActivityJoinRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinRecordService.listFront(request));
    }

}