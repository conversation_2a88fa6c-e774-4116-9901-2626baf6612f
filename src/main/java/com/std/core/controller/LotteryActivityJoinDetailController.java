package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.LotteryActivityJoinDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LotteryActivityJoinDetailCreateReq;
import com.std.core.pojo.request.LotteryActivityJoinDetailListReq;
import com.std.core.pojo.request.LotteryActivityJoinDetailListFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinDetailModifyReq;
import com.std.core.pojo.request.LotteryActivityJoinDetailPageReq;
import com.std.core.pojo.request.LotteryActivityJoinDetailPageFrontReq;
import com.std.core.pojo.response.LotteryActivityJoinDetailDetailRes;
import com.std.core.pojo.response.LotteryActivityJoinDetailListRes;
import com.std.core.pojo.response.LotteryActivityJoinDetailPageRes;
import com.std.core.service.ILotteryActivityJoinDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抽奖活动报名详情Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-06-02 14:21
 */
@ApiVersion(1)
@RestController
@Api(value = "抽奖活动报名详情管理", tags = "抽奖活动报名详情管理")
@RequestMapping("{version}/lottery_activity_join_detail")
public class LotteryActivityJoinDetailController extends BaseController {

    @Resource
    private ILotteryActivityJoinDetailService lotteryActivityJoinDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增抽奖活动报名详情")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        lotteryActivityJoinDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除抽奖活动报名详情")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        lotteryActivityJoinDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改抽奖活动报名详情")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        lotteryActivityJoinDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询抽奖活动报名详情")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LotteryActivityJoinDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询抽奖活动报名详情")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LotteryActivityJoinDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(lotteryActivityJoinDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询抽奖活动报名详情")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LotteryActivityJoinDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询抽奖活动报名详情")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<LotteryActivityJoinDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询抽奖活动报名详情")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<LotteryActivityJoinDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), LotteryActivityJoinDetail.class));

        return PageUtil.pageResult(lotteryActivityJoinDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询抽奖活动报名详情', NULL, '/core/v1/lottery_activity_join_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询抽奖活动报名详情")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<LotteryActivityJoinDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LotteryActivityJoinDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinDetailService.listFront(request));
    }

}