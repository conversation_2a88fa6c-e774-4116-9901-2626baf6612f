package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.TransferOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.TransferOrderDetailCreateReq;
import com.std.core.pojo.request.TransferOrderDetailListReq;
import com.std.core.pojo.request.TransferOrderDetailListFrontReq;
import com.std.core.pojo.request.TransferOrderDetailModifyReq;
import com.std.core.pojo.request.TransferOrderDetailPageReq;
import com.std.core.pojo.request.TransferOrderDetailPageFrontReq;
import com.std.core.pojo.response.TransferOrderDetailDetailRes;
import com.std.core.pojo.response.TransferOrderDetailListRes;
import com.std.core.pojo.response.TransferOrderDetailPageRes;
import com.std.core.service.ITransferOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渠道系统藏品流转订单详情Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-21 10:51
 */
@ApiVersion(1)
@RestController
@Api(value = "渠道系统藏品流转订单详情管理", tags = "渠道系统藏品流转订单详情管理")
@RequestMapping("{version}/transfer_order_detail")
public class TransferOrderDetailController extends BaseController {

    @Resource
    private ITransferOrderDetailService transferOrderDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        transferOrderDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid TransferOrderDetailModifyReq request) {
        User operator = getUserByToken(token);
        transferOrderDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<TransferOrderDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(transferOrderDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<TransferOrderDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid TransferOrderDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), TransferOrderDetail.class));

        return PageUtil.pageResult(transferOrderDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<TransferOrderDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid TransferOrderDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(transferOrderDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<TransferOrderDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(transferOrderDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<TransferOrderDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid TransferOrderDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), TransferOrderDetail.class));

        return PageUtil.pageResult(transferOrderDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渠道系统藏品流转订单详情', NULL, '/core/v1/transfer_order_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渠道系统藏品流转订单详情")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<TransferOrderDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid TransferOrderDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(transferOrderDetailService.listFront(request));
    }

}