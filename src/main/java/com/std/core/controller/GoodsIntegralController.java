package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.GoodsIntegral;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsIntegralBatchCreateReq;
import com.std.core.pojo.request.GoodsIntegralBatchModifyReq;
import com.std.core.pojo.request.GoodsIntegralCashReq;
import com.std.core.pojo.request.GoodsIntegralCreateReq;
import com.std.core.pojo.request.GoodsIntegralListFrontReq;
import com.std.core.pojo.request.GoodsIntegralListReq;
import com.std.core.pojo.request.GoodsIntegralModifyOrderNoReq;
import com.std.core.pojo.request.GoodsIntegralModifyQuantityReq;
import com.std.core.pojo.request.GoodsIntegralModifyReq;
import com.std.core.pojo.request.GoodsIntegralPageFrontReq;
import com.std.core.pojo.request.GoodsIntegralPageReq;
import com.std.core.pojo.response.GoodsIntegralBatchCreateListRes;
import com.std.core.pojo.response.GoodsIntegralDetailRes;
import com.std.core.pojo.response.GoodsIntegralListRes;
import com.std.core.pojo.response.GoodsIntegralPageRes;
import com.std.core.pojo.response.GoodsIntegralTopDetailRes;
import com.std.core.service.IGoodsIntegralService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元气值商品Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-25 22:51
 */
@ApiVersion(1)
@RestController
@Api(value = "元气值商品管理", tags = "元气值商品管理")
@RequestMapping("{version}/goods_integral")
public class GoodsIntegralController extends BaseController {

    @Resource
    private IGoodsIntegralService goodsIntegralService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元气值商品', NULL, '/core/v1/goods_integral/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增元气值商品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsIntegralCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsIntegralService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量新增新增元气值商品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/batch_create")
    public Result<List<GoodsIntegralBatchCreateListRes>> create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralBatchCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(goodsIntegralService.batchCreate(request, operator));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元气值商品', NULL, '/core/v1/goods_integral/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元气值商品")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        goodsIntegralService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元气值商品', NULL, '/core/v1/goods_integral/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改元气值商品")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsIntegralModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsIntegralService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改元气值商品序号")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_order_no")
    public Result modifyOrderNo(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralModifyOrderNoReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsIntegralService.modifyOrderNo(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改库存")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_quantity")
    public Result modifyQuantity(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralModifyQuantityReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsIntegralService.modifyQuantity(request, operator);

        return new Result();
    }

    @ApiOperation(value = "批量上下架")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/batch_modify")
    public Result batchModify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralBatchModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        goodsIntegralService.batchModify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "元气值兑现")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/integral_cash")
    public Result integralCash(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsIntegralCashReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "integralCash" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        if (!redisLock.lock(lockId, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "系统繁忙请稍等，请稍后再试");
        }
        try {
            goodsIntegralService.integralCash(request, operator);
            return new Result();
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元气值商品', NULL, '/core/v1/goods_integral/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元气值商品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsIntegral> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(goodsIntegralService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元气值商品', NULL, '/core/v1/goods_integral/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元气值商品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsIntegral>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(goodsIntegralService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元气值商品', NULL, '/core/v1/goods_integral/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元气值商品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsIntegral>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(goodsIntegralService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元气值商品', NULL, '/core/v1/goods_integral/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元气值商品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_front/{id}")
    public Result<GoodsIntegralDetailRes> detailFront(@RequestHeader(value = "Authorization", required = false) String token,
            @PathVariable("id") @Valid Long id) {
        User operator = null;

        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token);
        }

        return new Result<>(goodsIntegralService.detailFront(id, operator));
    }

    @ApiOperation(value = "商城顶部：奖品解锁")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_front_top")
    public Result<GoodsIntegralTopDetailRes> detailFrontTop(@RequestHeader(value = "Authorization", required = false) String token) {
        User operator = null;

        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token);
        }

        return new Result<>(goodsIntegralService.detailFrontTop());
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元气值商品', NULL, '/core/v1/goods_integral/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元气值商品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/public/page_front")
    public Result<PageInfo<GoodsIntegralPageRes>> pageFront(@RequestHeader(value = "Authorization", required = false) String token,
            @RequestBody @Valid GoodsIntegralPageFrontReq request) {
        User operator = null;

        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token);
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(goodsIntegralService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元气值商品', NULL, '/core/v1/goods_integral/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元气值商品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<GoodsIntegralListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid GoodsIntegralListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(goodsIntegralService.listFront(request));
    }

}