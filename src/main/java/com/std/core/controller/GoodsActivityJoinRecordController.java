package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.GoodsActivityJoinRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.GoodsActivityJoinRecordCreateReq;
import com.std.core.pojo.request.GoodsActivityJoinRecordListReq;
import com.std.core.pojo.request.GoodsActivityJoinRecordListFrontReq;
import com.std.core.pojo.request.GoodsActivityJoinRecordModifyReq;
import com.std.core.pojo.request.GoodsActivityJoinRecordPageReq;
import com.std.core.pojo.request.GoodsActivityJoinRecordPageFrontReq;
import com.std.core.pojo.response.*;
import com.std.core.service.IGoodsActivityJoinRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 赋能商城参与记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 18:56
 */
@ApiVersion(1)
@RestController
@Api(value = "赋能商城参与记录管理", tags = "赋能商城参与记录管理")
@RequestMapping("{version}/goods_activity_join_record")
public class GoodsActivityJoinRecordController extends BaseController {

    @Resource
    private IGoodsActivityJoinRecordService goodsActivityJoinRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增赋能商城参与记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        goodsActivityJoinRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除赋能商城参与记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        goodsActivityJoinRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改赋能商城参与记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        goodsActivityJoinRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询赋能商城参与记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<GoodsActivityJoinRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(goodsActivityJoinRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询赋能商城活动参与总人数")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_sum")
    public Result<List<GoodsActivityJoinRecordDetailRes>> detailSum(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(goodsActivityJoinRecordService.detailSum(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询赋能商城参与记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<GoodsActivityJoinRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(goodsActivityJoinRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询赋能商城参与记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<GoodsActivityJoinRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordListReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(goodsActivityJoinRecordService.list(request));
    }

    //    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询赋能商城参与记录")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<GoodsActivityJoinRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(goodsActivityJoinRecordService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询赋能商城参与记录")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<GoodsActivityJoinRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), GoodsActivityJoinRecord.class));
//
//        return PageUtil.pageResult(goodsActivityJoinRecordService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询赋能商城参与记录', NULL, '/core/v1/goods_activity_join_record/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询赋能商城参与记录")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<GoodsActivityJoinRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid GoodsActivityJoinRecordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(goodsActivityJoinRecordService.listFront(request));
//    }
    @ApiOperation(value = "front:积分排行榜", position = 90)
    @PostMapping(value = "/public/integral_list")
    public Result<AccountIntegralRankListRes> integralList(@RequestHeader(value = "Authorization", required = false) String token) {
        User operator = null;

        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token);
        }
        return new Result<>(goodsActivityJoinRecordService.integralList(operator));
    }
}