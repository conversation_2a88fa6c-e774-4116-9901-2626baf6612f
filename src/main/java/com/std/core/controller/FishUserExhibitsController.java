package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.FishUserExhibits;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.FishUserExhibitsDetailRes;
import com.std.core.pojo.response.FishUserExhibitsListRes;
import com.std.core.pojo.response.FishUserExhibitsPageRes;
import com.std.core.service.IFishUserExhibitsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔光用户展品Controller
 *
 * <AUTHOR> ycj
 * @since : 2023-03-06 17:12
 */
@ApiVersion(1)
@RestController
@Api(value = "渔光用户展品管理", tags = "渔光用户展品管理")
@RequestMapping("{version}/fish_user_exhibits")
public class FishUserExhibitsController extends BaseController {

    @Resource
    private IFishUserExhibitsService fishUserExhibitsService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渔光用户展品', NULL, '/core/v1/fish_user_exhibits/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增渔光用户展品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        fishUserExhibitsService.create(request, operator);

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渔光用户展品', NULL, '/core/v1/fish_user_exhibits/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除渔光用户展品")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        fishUserExhibitsService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渔光用户展品', NULL, '/core/v1/fish_user_exhibits/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改渔光用户展品")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsModifyReq request) {
//        User operator = getUserByToken(token);
//        fishUserExhibitsService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔光用户展品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FishUserExhibits> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishUserExhibitsService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔光用户展品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FishUserExhibits>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishUserExhibits.class));

        return PageUtil.pageResult(fishUserExhibitsService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔光用户展品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FishUserExhibits>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishUserExhibitsService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔光用户展品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<FishUserExhibitsDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishUserExhibitsService.detailFront(id));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询渔光用户展品")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<FishUserExhibitsPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), FishUserExhibits.class));
//
//        return PageUtil.pageResult(fishUserExhibitsService.pageFront(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔光用户展品', NULL, '/core/v1/fish_user_exhibits/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "front:列表查询渔光用户展品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FishUserExhibitsListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishUserExhibitsService.listFront(request,operator));
    }

    @ApiOperation(value = "front:取下设置用户展品我的作品位置")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/cancel")
    public Result cancel(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishUserExhibitsCancelReq request) {
        User operator = getUserByToken(token);
        fishUserExhibitsService.cancel(request, operator);

        return new Result();
    }

}