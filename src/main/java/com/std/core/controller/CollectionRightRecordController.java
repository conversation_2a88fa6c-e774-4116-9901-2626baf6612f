package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionRightRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionRightRecordCreateReq;
import com.std.core.pojo.request.CollectionRightRecordListReq;
import com.std.core.pojo.request.CollectionRightRecordListFrontReq;
import com.std.core.pojo.request.CollectionRightRecordModifyReq;
import com.std.core.pojo.request.CollectionRightRecordPageReq;
import com.std.core.pojo.request.CollectionRightRecordPageFrontReq;
import com.std.core.pojo.response.CollectionRightRecordDetailRes;
import com.std.core.pojo.response.CollectionRightRecordListRes;
import com.std.core.pojo.response.CollectionRightRecordPageRes;
import com.std.core.service.ICollectionRightRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益使用明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-25 19:48
 */
@ApiVersion(1)
@RestController
@Api(value = "权益使用明细管理", tags = "权益使用明细管理")
@RequestMapping("{version}/collection_right_record")
public class CollectionRightRecordController extends BaseController {

    @Resource
    private ICollectionRightRecordService collectionRightRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增权益使用明细', NULL, '/core/v1/collection_right_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增权益使用明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        collectionRightRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除权益使用明细', NULL, '/core/v1/collection_right_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除权益使用明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionRightRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改权益使用明细', NULL, '/core/v1/collection_right_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改权益使用明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionRightRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询权益使用明细', NULL, '/core/v1/collection_right_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询权益使用明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionRightRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询权益使用明细', NULL, '/core/v1/collection_right_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询权益使用明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionRightRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionRightRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询权益使用明细', NULL, '/core/v1/collection_right_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询权益使用明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionRightRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询权益使用明细', NULL, '/core/v1/collection_right_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询权益使用明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionRightRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询权益使用明细', NULL, '/core/v1/collection_right_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询权益使用明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionRightRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionRightRecord.class));

        return PageUtil.pageResult(collectionRightRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询权益使用明细', NULL, '/core/v1/collection_right_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询权益使用明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionRightRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionRightRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightRecordService.listFront(request));
    }

}