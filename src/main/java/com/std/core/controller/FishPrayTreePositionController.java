package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.FishPrayTreePosition;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.FishPrayTreePositionCreateReq;
import com.std.core.pojo.request.FishPrayTreePositionListReq;
import com.std.core.pojo.request.FishPrayTreePositionListFrontReq;
import com.std.core.pojo.request.FishPrayTreePositionModifyReq;
import com.std.core.pojo.request.FishPrayTreePositionPageReq;
import com.std.core.pojo.request.FishPrayTreePositionPageFrontReq;
import com.std.core.pojo.response.FishPrayTreePositionDetailRes;
import com.std.core.pojo.response.FishPrayTreePositionListRes;
import com.std.core.pojo.response.FishPrayTreePositionPageRes;
import com.std.core.pojo.response.PrayScrollBarRes;
import com.std.core.service.IFishPrayTreePositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔光祈福树点位Controller
 *
 * <AUTHOR> wzh
 * @since : 2023-03-07 10:49
 */
@ApiVersion(1)
@RestController
@Api(value = "渔光祈福树点位管理", tags = "渔光祈福树点位管理")
@RequestMapping("{version}/fish_pray_tree_position")
public class FishPrayTreePositionController extends BaseController {

    @Resource
    private IFishPrayTreePositionService fishPrayTreePositionService;

//    @ApiOperation(value = "新增渔光祈福树点位")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionCreateReq request) {
//        User operator = getUserByToken(token);
//        fishPrayTreePositionService.create(request, operator);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "删除渔光祈福树点位")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        fishPrayTreePositionService.remove(id);
//
//        return new Result();
//    }
//
//    @ApiOperation(value = "修改渔光祈福树点位")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionModifyReq request) {
//        User operator = getUserByToken(token);
//        fishPrayTreePositionService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔光祈福树点位")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FishPrayTreePosition> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishPrayTreePositionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔光祈福树点位")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FishPrayTreePosition>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(fishPrayTreePositionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔光祈福树点位")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FishPrayTreePosition>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishPrayTreePositionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔光祈福树点位")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<FishPrayTreePositionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishPrayTreePositionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渔光祈福树点位")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<FishPrayTreePositionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), FishPrayTreePosition.class));

        return PageUtil.pageResult(fishPrayTreePositionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔光祈福树点位', NULL, '/core/v1/fish_pray_tree_position/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渔光祈福树点位")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FishPrayTreePositionListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPrayTreePositionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishPrayTreePositionService.listFront(request));
    }

    @ApiOperation(value = "祈福消息滚动条")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/scroll_bar")
    public Result<List<PrayScrollBarRes>> scrollBar(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token);

        return new Result<>(fishPrayTreePositionService.scrollBar());
    }

}