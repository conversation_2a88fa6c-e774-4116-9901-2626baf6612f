package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.MetaMilletTransferRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.MetaMilletTransferRecordCreateReq;
import com.std.core.pojo.request.MetaMilletTransferRecordListReq;
import com.std.core.pojo.request.MetaMilletTransferRecordListFrontReq;
import com.std.core.pojo.request.MetaMilletTransferRecordModifyReq;
import com.std.core.pojo.request.MetaMilletTransferRecordPageReq;
import com.std.core.pojo.request.MetaMilletTransferRecordPageFrontReq;
import com.std.core.pojo.response.MetaMilletTransferRecordDetailRes;
import com.std.core.pojo.response.MetaMilletTransferRecordListRes;
import com.std.core.pojo.response.MetaMilletTransferRecordPageRes;
import com.std.core.service.IMetaMilletTransferRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元粟转赠记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-11 14:42
 */
@ApiVersion(1)
@RestController
@Api(value = "元粟转赠记录管理", tags = "元粟转赠记录管理")
@RequestMapping("{version}/meta_millet_transfer_record")
public class MetaMilletTransferRecordController extends BaseController {

    @Resource
    private IMetaMilletTransferRecordService metaMilletTransferRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增元粟转赠记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "millet_transfer:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转赠进行中，请稍后重试");
            }
            metaMilletTransferRecordService.create(request, operator);
            return new Result();
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元粟转赠记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        metaMilletTransferRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改元粟转赠记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        metaMilletTransferRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元粟转赠记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<MetaMilletTransferRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(metaMilletTransferRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元粟转赠记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<MetaMilletTransferRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), MetaMilletTransferRecord.class));

        return PageUtil.pageResult(metaMilletTransferRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元粟转赠记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<MetaMilletTransferRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元粟转赠记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<MetaMilletTransferRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元粟转赠记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<MetaMilletTransferRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), MetaMilletTransferRecord.class));

        return PageUtil.pageResult(metaMilletTransferRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元粟转赠记录', NULL, '/core/v1/meta_millet_transfer_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元粟转赠记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<MetaMilletTransferRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid MetaMilletTransferRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(metaMilletTransferRecordService.listFront(request));
    }

}