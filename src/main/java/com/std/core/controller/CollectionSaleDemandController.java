package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionSaleDemand;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionSaleDemandCreateReq;
import com.std.core.pojo.request.CollectionSaleDemandListReq;
import com.std.core.pojo.request.CollectionSaleDemandListFrontReq;
import com.std.core.pojo.request.CollectionSaleDemandModifyReq;
import com.std.core.pojo.request.CollectionSaleDemandPageReq;
import com.std.core.pojo.request.CollectionSaleDemandPageFrontReq;
import com.std.core.pojo.response.CollectionSaleDemandDetailRes;
import com.std.core.pojo.response.CollectionSaleDemandListRes;
import com.std.core.pojo.response.CollectionSaleDemandPageRes;
import com.std.core.service.ICollectionSaleDemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数字藏品发售需求Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-05 11:37
 */
@ApiVersion(1)
@RestController
@Api(value = "数字藏品发售需求管理", tags = "数字藏品发售需求管理")
@RequestMapping("{version}/collection_sale_demand")
public class CollectionSaleDemandController extends BaseController {

    @Resource
    private ICollectionSaleDemandService collectionSaleDemandService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增数字藏品发售需求")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandCreateReq request) {
//        User operator = getUserByToken(token);
//        collectionSaleDemandService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除数字藏品发售需求")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionSaleDemandService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改数字藏品发售需求")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionSaleDemandService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询数字藏品发售需求")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionSaleDemand> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionSaleDemandService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询数字藏品发售需求")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionSaleDemand>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionSaleDemand.class));

        return PageUtil.pageResult(collectionSaleDemandService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询数字藏品发售需求")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionSaleDemand>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionSaleDemandService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询数字藏品发售需求")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionSaleDemandDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionSaleDemandService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询数字藏品发售需求")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionSaleDemandPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionSaleDemand.class));

        return PageUtil.pageResult(collectionSaleDemandService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询数字藏品发售需求', NULL, '/core/v1/collection_sale_demand/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询数字藏品发售需求")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionSaleDemandListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionSaleDemandListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionSaleDemandService.listFront(request));
    }

}