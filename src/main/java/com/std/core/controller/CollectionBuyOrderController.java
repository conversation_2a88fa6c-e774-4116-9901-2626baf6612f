package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserIdentifyStatus;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionBuyOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionBuyOrderAuctionCreateReq;
import com.std.core.pojo.request.CollectionBuyOrderCheckReq;
import com.std.core.pojo.request.CollectionBuyOrderCreateReq;
import com.std.core.pojo.request.CollectionBuyOrderListFrontReq;
import com.std.core.pojo.request.CollectionBuyOrderListReq;
import com.std.core.pojo.request.CollectionBuyOrderPageFrontReq;
import com.std.core.pojo.request.CollectionBuyOrderPageReq;
import com.std.core.pojo.response.CollectionBuyOrderAuctionPayRes;
import com.std.core.pojo.response.CollectionBuyOrderCollectionInfoRes;
import com.std.core.pojo.response.CollectionBuyOrderDetailRes;
import com.std.core.pojo.response.CollectionBuyOrderListRes;
import com.std.core.pojo.response.CollectionBuyOrderPageRes;
import com.std.core.pojo.response.OrderPayRes;
import com.std.core.service.ICollectionBuyOrderService;
import com.std.core.service.ICollectionPeriodService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 数字藏品型号购买订单Controller
 *
 * <AUTHOR> ycj
 * @since : 2021-11-04 16:01
 */
@Slf4j
@ApiVersion(1)
@RestController
@Api(value = "数字藏品购买订单", tags = "数字藏品购买订单")
@RequestMapping("{version}/collection_buy_order")
public class CollectionBuyOrderController extends BaseController {

    @Resource
    private ICollectionBuyOrderService collectionBuyOrderService;

    @Resource
    private ICollectionPeriodService collectionPeriodService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "校验购买藏品上限")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/check")
    public Result check(@RequestHeader(value = "Authorization") String token, @RequestBody CollectionBuyOrderCheckReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        collectionBuyOrderService.check(request, operator);

        return new Result();
    }

    @ApiOperation(value = "front:购买藏品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/buy")
//    @AccessLimit(seconds = 3, maxCount = 15)
    public Result<OrderPayRes> buy(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid CollectionBuyOrderCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus())
                && !EUserIdentifyStatus.IDENTIFY_6.getCode()
                .equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        if (null == channelId) {
            channelId = 1L;
        }
        String lockId = "user_buy:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买进行中，请稍后重试");
            }

            return new Result<>(collectionBuyOrderService.create(request, operator, client, channelId));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    @ApiOperation(value = "front:购买藏品")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/buy_h5")
//    @AccessLimit(seconds = 3, maxCount = 15)
    public Result<OrderPayRes> buyH5(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid CollectionBuyOrderCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        if (null == channelId) {
            channelId = 1L;
        }
        String lockId = "user_buy:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买进行中，请稍后重试");
            }

            return new Result<>(collectionBuyOrderService.create(request, operator, client, channelId));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    @ApiOperation(value = "front:一级市场购买盲盒")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/blind_box_buy")
    public Result<OrderPayRes> blindBoxBuy(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid CollectionBuyOrderCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) && !EUserIdentifyStatus.IDENTIFY_6.getCode()
                .equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        if (null == channelId) {
            channelId = 1L;
        }

        String lockId = "user_buy:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        OrderPayRes orderPayRes = null;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买进行中，请稍后重试");
            }

            orderPayRes = collectionBuyOrderService.blindBoxCreate(request, operator, client, channelId);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result<>(orderPayRes);
    }

    @ApiOperation(value = "front:一级市场购买盲盒")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/blind_box_buy_h5")
    public Result<OrderPayRes> blindBoxBuyH5(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestBody @Valid CollectionBuyOrderCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) &&
                !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus()) && !EUserIdentifyStatus.IDENTIFY_6.getCode()
                .equals(operator.getIdentifyStatus())) {
            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
        }
        if (null == channelId) {
            channelId = 1L;
        }

        String lockId = "user_buy:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        OrderPayRes orderPayRes = null;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "购买进行中，请稍后重试");
            }

            orderPayRes = collectionBuyOrderService.blindBoxCreate(request, operator, client, channelId);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result<>(orderPayRes);
    }

    @ApiOperation(value = "front:中拍支付")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/auction_buy")
    public Result<OrderPayRes> buy(@RequestHeader(value = "Authorization") String token,
            @RequestHeader(value = "client", required = false) String client,
            @RequestBody @Valid CollectionBuyOrderAuctionCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "auction_buy:" + request.getPeriodId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "中拍支付中");
            }
            return new Result<>(collectionBuyOrderService.auctionCreate(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

//    @ApiOperation(value = "front:打开盲盒")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/blindbox_create")
//    public Result<OrderPayRes> blindboxCreate(@RequestHeader(value = "Authorization") String token,
//                                              @RequestBody @Valid CollectionBlindBoxCreateReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result(collectionBuyOrderService.create(request, operator));
//    }

//    @ApiOperation(value = "删除数字藏品型号购买订单")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionBuyOrderService.remove(id);
//
//        return new Result();
//    }

//    @ApiOperation(value = "修改数字藏品型号购买订单")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionBuyOrderModifyReq request) {
//        User operator = getUserByToken(token);
//        collectionBuyOrderService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionBuyOrder> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionBuyOrderService.detail(id, operator));
    }

    @ApiOperation(value = "查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<CollectionBuyOrder> detailCompany(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(collectionBuyOrderService.detail(id, operator));
    }

    @ApiOperation(value = "分页条件查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionBuyOrder>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionBuyOrderPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionBuyOrder.class));

        return PageUtil.pageResult(collectionBuyOrderService.page(request, operator));
    }

    @ApiOperation(value = "分页条件查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<CollectionBuyOrder>> pageCompany(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionBuyOrderPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionBuyOrderService.page(request, operator));
    }

    @ApiOperation(value = "列表条件查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionBuyOrder>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionBuyOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionBuyOrderService.list(request));
    }

    @ApiOperation(value = "查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionBuyOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionBuyOrderService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionBuyOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionBuyOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionBuyOrder.class));

        return PageUtil.pageResult(collectionBuyOrderService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询数字藏品型号购买订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionBuyOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionBuyOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionBuyOrderService.listFront(request));
    }

    @ApiOperation(value = "前端根据订单号查询购买藏品的信息")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/detail_by_order_code")
    public Result<CollectionBuyOrderCollectionInfoRes> detailByOrderCode(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionBuyOrderService.detailByOrderCode(request, operator));
    }

    @ApiOperation(value = "竞拍多次付款订单列表")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/period_auction_pay_record")
    public Result<CollectionBuyOrderAuctionPayRes> periodAuctionPayRecord(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionBuyOrderService.periodAuctionPayRecord(request, operator));
    }

}