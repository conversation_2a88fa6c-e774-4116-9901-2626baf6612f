package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.UserAuthChannel;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.UserAuthChannelCreateReq;
import com.std.core.pojo.request.UserAuthChannelListReq;
import com.std.core.pojo.request.UserAuthChannelListFrontReq;
import com.std.core.pojo.request.UserAuthChannelModifyReq;
import com.std.core.pojo.request.UserAuthChannelPageReq;
import com.std.core.pojo.request.UserAuthChannelPageFrontReq;
import com.std.core.pojo.response.UserAuthChannelDetailRes;
import com.std.core.pojo.response.UserAuthChannelListRes;
import com.std.core.pojo.response.UserAuthChannelPageRes;
import com.std.core.service.IUserAuthChannelService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户三方渠道关联表Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-06-20 20:41
 */
@ApiVersion(1)
@RestController
@Api(value = "用户三方渠道关联表管理", tags = "用户三方渠道关联表管理")
@RequestMapping("{version}/user_auth_channel")
public class UserAuthChannelController extends BaseController {

    @Resource
    private IUserAuthChannelService userAuthChannelService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增用户三方渠道关联表")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelCreateReq request) {
        User operator = getUserByToken(token);
        userAuthChannelService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除用户三方渠道关联表")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        userAuthChannelService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改用户三方渠道关联表")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelModifyReq request) {
        User operator = getUserByToken(token);
        userAuthChannelService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户三方渠道关联表")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserAuthChannel> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userAuthChannelService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户三方渠道关联表")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserAuthChannel>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserAuthChannel.class));

        return PageUtil.pageResult(userAuthChannelService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户三方渠道关联表")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserAuthChannel>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userAuthChannelService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户三方渠道关联表")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserAuthChannelDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userAuthChannelService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户三方渠道关联表")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserAuthChannelPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), UserAuthChannel.class));

        return PageUtil.pageResult(userAuthChannelService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户三方渠道关联表', NULL, '/core/v1/user_auth_channel/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户三方渠道关联表")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserAuthChannelListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserAuthChannelListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userAuthChannelService.listFront(request));
    }

}