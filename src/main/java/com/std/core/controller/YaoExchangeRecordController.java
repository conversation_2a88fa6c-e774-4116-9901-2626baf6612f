package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.YaoExchangeRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.YaoExchangeRecordCreateReq;
import com.std.core.pojo.request.YaoExchangeRecordListReq;
import com.std.core.pojo.request.YaoExchangeRecordListFrontReq;
import com.std.core.pojo.request.YaoExchangeRecordModifyReq;
import com.std.core.pojo.request.YaoExchangeRecordPageReq;
import com.std.core.pojo.request.YaoExchangeRecordPageFrontReq;
import com.std.core.pojo.response.YaoExchangeRecordDetailRes;
import com.std.core.pojo.response.YaoExchangeRecordListRes;
import com.std.core.pojo.response.YaoExchangeRecordPageRes;
import com.std.core.service.IYaoExchangeRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 爻转化订单Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-10 17:57
 */
@ApiVersion(1)
@RestController
@Api(value = "爻转化订单管理", tags = "爻转化订单管理")
@RequestMapping("{version}/yao_exchange_record")
public class YaoExchangeRecordController extends BaseController {

    @Resource
    private IYaoExchangeRecordService yaoExchangeRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增爻转化订单', NULL, '/core/v1/yao_exchange_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增爻转化订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "yao_exchange_record_create:" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;

        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "转化进行中，请稍后重试");
            }
            yaoExchangeRecordService.create(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }


        return new Result();
    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除爻转化订单', NULL, '/core/v1/yao_exchange_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除爻转化订单")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        yaoExchangeRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改爻转化订单', NULL, '/core/v1/yao_exchange_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改爻转化订单")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        yaoExchangeRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询爻转化订单', NULL, '/core/v1/yao_exchange_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询爻转化订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<YaoExchangeRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(yaoExchangeRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询爻转化订单', NULL, '/core/v1/yao_exchange_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询爻转化订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<YaoExchangeRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoExchangeRecord.class));

        return PageUtil.pageResult(yaoExchangeRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询爻转化订单', NULL, '/core/v1/yao_exchange_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询爻转化订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<YaoExchangeRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoExchangeRecordService.list(request));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询爻转化订单', NULL, '/core/v1/yao_exchange_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询爻转化订单")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<YaoExchangeRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeRecordService.detailFront(id));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询爻转化订单', NULL, '/core/v1/yao_exchange_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询爻转化订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<YaoExchangeRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordPageFrontReq request) {
        User operator = getUserByToken(token,EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoExchangeRecord.class));

        return PageUtil.pageResult(yaoExchangeRecordService.pageFront(request,operator));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询爻转化订单', NULL, '/core/v1/yao_exchange_record/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询爻转化订单")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<YaoExchangeRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoExchangeRecordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(yaoExchangeRecordService.listFront(request));
//    }

}