package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.DateUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.domain.UserPaymentAccountInfo;
import com.std.core.pojo.request.UserPaymentAccountInfoCreateReq;
import com.std.core.pojo.request.UserPaymentAccountInfoListFrontReq;
import com.std.core.pojo.request.UserPaymentAccountInfoListReq;
import com.std.core.pojo.request.UserPaymentAccountInfoModifyReq;
import com.std.core.pojo.request.UserPaymentAccountInfoPageFrontReq;
import com.std.core.pojo.request.UserPaymentAccountInfoPageReq;
import com.std.core.pojo.request.UserPaymentAccountInfoReCalReq;
import com.std.core.pojo.response.UserPaymentAccountInfoDetailRes;
import com.std.core.pojo.response.UserPaymentAccountInfoListRes;
import com.std.core.pojo.response.UserPaymentAccountInfoPageRes;
import com.std.core.pojo.response.UserSettleAccountInfoRes;
import com.std.core.service.IUserPaymentAccountInfoService;
import com.std.core.service.IUserSettleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.Date;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户结算账户Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-19 19:10
 */
@Slf4j
@ApiVersion(1)
@RestController
@Api(value = "用户结算账户管理", tags = "用户结算账户管理")
@RequestMapping("{version}/user_payment_account_info")
public class UserPaymentAccountInfoController extends BaseController {

    @Resource
    private IUserPaymentAccountInfoService userPaymentAccountInfoService;

    @Resource
    private IUserSettleRecordService userSettleRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增用户结算账户', NULL, '/core/v1/user_payment_account_info/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增用户结算账户")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        userPaymentAccountInfoService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "申请账户短信重发")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/sms_resend")
    public Result smsResend(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        userPaymentAccountInfoService.smsResend(request.getId(), operator);

        return new Result();
    }

    @ApiOperation(value = "我的申请账户短信重发")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/my_sms_resend")
    public Result mySmsResend(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);
        UserPaymentAccountInfo userPaymentAccountInfo = userPaymentAccountInfoService.getApproveIng(operator);
        userPaymentAccountInfoService.smsResend(userPaymentAccountInfo.getId(), operator);

        return new Result();
    }

    @ApiOperation(value = "同步计算指定时间的数据")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/do_sync")
    public Result doSync(@RequestBody @Valid UserPaymentAccountInfoReCalReq request) {
        Date date = DateUtil.strToDate(request.getNewDate(), DateUtil.DATA_TIME_PATTERN_1);

        UserPaymentAccountInfo condition = new UserPaymentAccountInfo();
        condition.setUserIdList(request.getUserIdList());
        List<UserPaymentAccountInfo> list = userPaymentAccountInfoService.list(condition);
        for (UserPaymentAccountInfo userInfo : list) {
            try {
                userSettleRecordService.synchronousSettleRecord(userInfo, date);
            } catch (Exception e) {
                log.error("易宝每日结算记录同步错误，用户" + userInfo.getUserId() + "," + e.getMessage());
            }
        }

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除用户结算账户', NULL, '/core/v1/user_payment_account_info/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除用户结算账户")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        userPaymentAccountInfoService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改用户结算账户', NULL, '/core/v1/user_payment_account_info/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改用户结算账户")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserPaymentAccountInfoModifyReq request) {
//        User operator = getUserByToken(token);
//        userPaymentAccountInfoService.modify(request, operator);
//
//        return new Result();
//    }

    @ApiOperation(value = "开启关闭分账功能")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoModifyReq request) {
        User operator = getUserByToken(token);
        userPaymentAccountInfoService.modify(request, operator);

        return new Result();
    }


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户结算账户', NULL, '/core/v1/user_payment_account_info/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户结算账户")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<UserPaymentAccountInfo> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userPaymentAccountInfoService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户结算账户', NULL, '/core/v1/user_payment_account_info/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户结算账户")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<UserPaymentAccountInfo>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserPaymentAccountInfo.class));

        return PageUtil.pageResult(userPaymentAccountInfoService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户结算账户', NULL, '/core/v1/user_payment_account_info/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户结算账户")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<UserPaymentAccountInfo>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userPaymentAccountInfoService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户结算账户', NULL, '/core/v1/user_payment_account_info/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户结算账户")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<UserPaymentAccountInfoDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(userPaymentAccountInfoService.detailFront(id));
    }

    @ApiOperation(value = "查询用户的申请记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front_by_user")
    public Result<UserPaymentAccountInfoDetailRes> detailFrontByUser(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(userPaymentAccountInfoService.detailFrontByUser(operator));
    }

    @ApiOperation(value = "查询用户的结算账户")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_user_settle_account")
    public Result<UserSettleAccountInfoRes> detailUserSettleAccount(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(userPaymentAccountInfoService.detailUserSettleAccount(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户结算账户', NULL, '/core/v1/user_payment_account_info/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户结算账户")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<UserPaymentAccountInfoPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), UserPaymentAccountInfo.class));

        return PageUtil.pageResult(userPaymentAccountInfoService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户结算账户', NULL, '/core/v1/user_payment_account_info/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户结算账户")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<UserPaymentAccountInfoListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserPaymentAccountInfoListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(userPaymentAccountInfoService.listFront(request));
    }

}