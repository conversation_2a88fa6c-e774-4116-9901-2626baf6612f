package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionDetailModifyRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionDetailModifyRecordApproveReq;
import com.std.core.pojo.request.CollectionDetailModifyRecordPageReq;
import com.std.core.service.ICollectionDetailModifyRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 藏品修改记录Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-04-25 15:39
 */
@ApiVersion(1)
@RestController
@Api(value = "藏品修改记录管理", tags = "藏品修改记录管理")
@RequestMapping("{version}/collection_detail_modify_record")
public class CollectionDetailModifyRecordController extends BaseController {

    @Resource
    private ICollectionDetailModifyRecordService collectionDetailModifyRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '审核藏品修改名称', NULL, '/core/v1/collection_detail_modify_record/approve', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "审核藏品修改名称")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/approve")
    public Result approve(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailModifyRecordApproveReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionDetailModifyRecordService.approve(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询藏品修改记录', NULL, '/core/v1/collection_detail_modify_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询藏品修改记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailModifyRecord> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(collectionDetailModifyRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询藏品修改记录', NULL, '/core/v1/collection_detail_modify_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询藏品修改记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailModifyRecord>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailModifyRecordPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailModifyRecord.class));

        return PageUtil.pageResult(collectionDetailModifyRecordService.page(request));
    }
}