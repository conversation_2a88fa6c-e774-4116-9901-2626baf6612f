package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EClient;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.InvitationActivity;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityChannelReq;
import com.std.core.pojo.request.InvitationActivityCreateReq;
import com.std.core.pojo.request.InvitationActivityListFrontReq;
import com.std.core.pojo.request.InvitationActivityListReq;
import com.std.core.pojo.request.InvitationActivityModifyReq;
import com.std.core.pojo.request.InvitationActivityPageFrontReq;
import com.std.core.pojo.request.InvitationActivityPageReq;
import com.std.core.pojo.request.InvitationActivityUpAndDownReq;
import com.std.core.pojo.response.InvitationActivityBlindBoxAwardRes;
import com.std.core.pojo.response.InvitationActivityDetailRes;
import com.std.core.pojo.response.InvitationActivityListRes;
import com.std.core.pojo.response.InvitationActivityPageRes;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.IInvitationActivityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 拉新活动Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-11 21:00
 */
@ApiVersion(1)
@RestController
@Api(value = "拉新活动管理", tags = "拉新活动管理")
@RequestMapping("{version}/invitation_activity")
public class InvitationActivityController extends BaseController {

    @Resource
    private IInvitationActivityService invitationActivityService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增拉新活动', NULL, '/core/v1/invitation_activity/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增拉新活动")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增拉新活动")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/company_create")
    public Result companyCreate(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityService.create(request, operator);

        return new Result();
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除拉新活动', NULL, '/core/v1/invitation_activity/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除拉新活动")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        invitationActivityService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改拉新活动', NULL, '/core/v1/invitation_activity/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改拉新活动")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid InvitationActivityModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改拉新活动")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/company_modify")
    public Result companyModify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityModifyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "上下架活动")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_up_and_down")
    public Result modifyUpAndDown(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUpAndDownReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        invitationActivityService.modifyUpAndDown(request, operator);

        return new Result();
    }

    @ApiOperation(value = "上下架活动")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/company_modify_up_and_down")
    public Result companyModifyUpAndDown(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUpAndDownReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        invitationActivityService.modifyUpAndDown(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询拉新活动', NULL, '/core/v1/invitation_activity/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询拉新活动")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivity> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(invitationActivityService.detail(id, operator));
    }

    @ApiOperation(value = "查询拉新活动")
    @ApiOperationSupport(order = 40)
    @PostMapping("/company_detail/{id}")
    public Result<InvitationActivity> companyDetail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(invitationActivityService.detail(id, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询拉新活动', NULL, '/core/v1/invitation_activity/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询拉新活动")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivity>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return PageUtil.pageResult(invitationActivityService.page(request, operator));
    }

    @ApiOperation(value = "分页条件查询拉新活动")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/company_page")
    public Result<PageInfo<InvitationActivity>> companyPage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        return PageUtil.pageResult(invitationActivityService.page(request, operator));
    }


    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询拉新活动', NULL, '/core/v1/invitation_activity/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询拉新活动")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivity>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询拉新活动', NULL, '/core/v1/invitation_activity/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询拉新活动")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front")
    public Result<InvitationActivityDetailRes> detailFront(@RequestHeader(value = "channel", required = false) String channel,
            @RequestHeader(value = "client", required = false) String client,
            @RequestHeader(value = "version", required = false) String version,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @RequestHeader(value = "Authorization", required = false) String token) {

        if (StringUtils.isNotBlank(client)) {
            if (EClient.iOS.getCode().equals(client) || EClient.Android.getCode().equals(client)) {
                return new Result<>(new InvitationActivityDetailRes());
            }
        }

        User operator = getUserByToken(token, EUserKind.C);

        if (null == channelId) {
            ChannelMerchant channelMerchant = channelMerchantService.detailPlatChannel();
            if (null == channelMerchant) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "不正确的渠道");
            }
            channelId = channelMerchant.getId();
        }

        return new Result<>(invitationActivityService.detailFront(operator, channelId));
    }

    @ApiOperation(value = "前端详情查询拉新活动")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front_h5")
    public Result<InvitationActivityDetailRes> detailFrontH5(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityChannelReq request) {

        User operator = getUserByToken(token);

        return new Result<>(invitationActivityService.detailFront(operator, request.getChannelId()));
    }

    @ApiOperation(value = "查询盲盒详情页")
    @ApiOperationSupport(order = 70)
    @PostMapping("/public/detail_blind_box_award/{id}")
    public Result<InvitationActivityBlindBoxAwardRes> detailBlindBoxAward(
            @RequestHeader(value = "Authorization", required = false) String token,
            @PathVariable("id") @Valid Long id) {

        return new Result<>(invitationActivityService.detailBlindBoxAward(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询拉新活动', NULL, '/core/v1/invitation_activity/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询拉新活动")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivity.class));

        return PageUtil.pageResult(invitationActivityService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询拉新活动', NULL, '/core/v1/invitation_activity/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询拉新活动")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityService.listFront(request));
    }

    @ApiOperation(value = "列表查询发行方涉及到的渠道")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/channel_list")
    public Result<List<ChannelMerchant>> list(@RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(invitationActivityService.listChannelMerchant(operator));
    }
}