package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.PeriodChannelWord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodChannelWordCreateReq;
import com.std.core.pojo.request.PeriodChannelWordPageReq;
import com.std.core.service.IPeriodChannelWordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数作品机构口令Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-06-07 09:45
 */
@ApiVersion(1)
@RestController
@Api(value = "期数作品机构口令管理", tags = "期数作品机构口令管理")
@RequestMapping("{version}/period_channel_word")
public class PeriodChannelWordController extends BaseController {

    @Resource
    private IPeriodChannelWordService periodChannelWordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增期数作品机构口令', NULL, '/core/v1/period_channel_word/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "生成期数作品机构口令")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordCreateReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        periodChannelWordService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "生成期数作品机构口令")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_company")
    public Result createCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordCreateReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        periodChannelWordService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除期数作品机构口令', NULL, '/core/v1/period_channel_word/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "作废期数作品机构口令")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);
        periodChannelWordService.remove(id, operator);

        return new Result();
    }

    @ApiOperation(value = "作废期数作品机构口令")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove_company/{id}")
    public Result removeCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.BP);
        periodChannelWordService.remove(id, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改期数作品机构口令', NULL, '/core/v1/period_channel_word/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改期数作品机构口令")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordModifyReq request) {
//        User operator = getUserByToken(token);
//        periodChannelWordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询期数作品机构口令', NULL, '/core/v1/period_channel_word/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询期数作品机构口令")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodChannelWord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(periodChannelWordService.detail(id,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数作品机构口令', NULL, '/core/v1/period_channel_word/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数作品机构口令")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodChannelWord>> page(@RequestHeader(value = "Authorization") String token,
                                                    @RequestBody @Valid PeriodChannelWordPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), PeriodChannelWord.class));

        return PageUtil.pageResult(periodChannelWordService.page(request,operator));
    }

    @ApiOperation(value = "查询期数作品机构口令")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<PeriodChannelWord> detailCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.BP);

        return new Result<>(periodChannelWordService.detail(id,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数作品机构口令', NULL, '/core/v1/period_channel_word/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数作品机构口令")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<PeriodChannelWord>> pageCompany(@RequestHeader(value = "Authorization") String token,
                                                    @RequestBody @Valid PeriodChannelWordPageReq request) {
        User operator = getUserByToken(token,EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), PeriodChannelWord.class));

        return PageUtil.pageResult(periodChannelWordService.page(request,operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询期数作品机构口令', NULL, '/core/v1/period_channel_word/list', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "列表条件查询期数作品机构口令")
//    @ApiOperationSupport(order = 60)
//    @PostMapping(value = "/list")
//    public Result<List<PeriodChannelWord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordListReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(periodChannelWordService.list(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询期数作品机构口令', NULL, '/core/v1/period_channel_word/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询期数作品机构口令")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<PeriodChannelWordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(periodChannelWordService.detailFront(id));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询期数作品机构口令', NULL, '/core/v1/period_channel_word/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询期数作品机构口令")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<PeriodChannelWordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), PeriodChannelWord.class));
//
//        return PageUtil.pageResult(periodChannelWordService.pageFront(request));
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询期数作品机构口令', NULL, '/core/v1/period_channel_word/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询期数作品机构口令")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<PeriodChannelWordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodChannelWordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(periodChannelWordService.listFront(request));
//    }

}