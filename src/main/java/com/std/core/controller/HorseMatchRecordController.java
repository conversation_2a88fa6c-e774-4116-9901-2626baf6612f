package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.HorseMatchRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.HorseMatchRecordCreateReq;
import com.std.core.pojo.request.HorseMatchRecordListReq;
import com.std.core.pojo.request.HorseMatchRecordListFrontReq;
import com.std.core.pojo.request.HorseMatchRecordModifyReq;
import com.std.core.pojo.request.HorseMatchRecordPageReq;
import com.std.core.pojo.request.HorseMatchRecordPageFrontReq;
import com.std.core.pojo.response.HorseMatchRecordDetailRes;
import com.std.core.pojo.response.HorseMatchRecordListRes;
import com.std.core.pojo.response.HorseMatchRecordPageRes;
import com.std.core.service.IHorseMatchRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 比赛记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-30 16:43
 */
@ApiVersion(1)
@RestController
@Api(value = "比赛记录管理", tags = "比赛记录管理")
@RequestMapping("{version}/horse_match_record")
public class HorseMatchRecordController extends BaseController {

    @Resource
    private IHorseMatchRecordService horseMatchRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增比赛记录', NULL, '/core/v1/horse_match_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增比赛记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordCreateReq request) {
        User operator = getUserByToken(token);
        horseMatchRecordService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除比赛记录', NULL, '/core/v1/horse_match_record/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除比赛记录")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        horseMatchRecordService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改比赛记录', NULL, '/core/v1/horse_match_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改比赛记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordModifyReq request) {
        User operator = getUserByToken(token);
        horseMatchRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询比赛记录', NULL, '/core/v1/horse_match_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询比赛记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<HorseMatchRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(horseMatchRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询比赛记录', NULL, '/core/v1/horse_match_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询比赛记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<HorseMatchRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), HorseMatchRecord.class));

        return PageUtil.pageResult(horseMatchRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询比赛记录', NULL, '/core/v1/horse_match_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询比赛记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<HorseMatchRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(horseMatchRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询比赛记录', NULL, '/core/v1/horse_match_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询比赛记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<HorseMatchRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(horseMatchRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询比赛记录', NULL, '/core/v1/horse_match_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询比赛记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<HorseMatchRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordPageFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), HorseMatchRecord.class));

        return PageUtil.pageResult(horseMatchRecordService.pageFront(operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询比赛记录', NULL, '/core/v1/horse_match_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询比赛记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<HorseMatchRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid HorseMatchRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(horseMatchRecordService.listFront(request));
    }

}