package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ChannelSmsRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChannelSmsRecordCreateReq;
import com.std.core.pojo.request.ChannelSmsRecordListReq;
import com.std.core.pojo.request.ChannelSmsRecordListFrontReq;
import com.std.core.pojo.request.ChannelSmsRecordModifyReq;
import com.std.core.pojo.request.ChannelSmsRecordPageReq;
import com.std.core.pojo.request.ChannelSmsRecordPageFrontReq;
import com.std.core.pojo.response.ChannelSmsRecordDetailRes;
import com.std.core.pojo.response.ChannelSmsRecordListRes;
import com.std.core.pojo.response.ChannelSmsRecordPageRes;
import com.std.core.service.IChannelSmsRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 三方短信重发记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-05-05 10:21
 */
@ApiVersion(1)
@RestController
@Api(value = "三方短信重发记录管理", tags = "三方短信重发记录管理")
@RequestMapping("{version}/channel_sms_record")
public class ChannelSmsRecordController extends BaseController {

    @Resource
    private IChannelSmsRecordService channelSmsRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增三方短信重发记录', NULL, '/core/v1/channel_sms_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增三方短信重发记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        channelSmsRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除三方短信重发记录', NULL, '/core/v1/channel_sms_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除三方短信重发记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        channelSmsRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改三方短信重发记录', NULL, '/core/v1/channel_sms_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改三方短信重发记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        channelSmsRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询三方短信重发记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChannelSmsRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(channelSmsRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询三方短信重发记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChannelSmsRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChannelSmsRecord.class));

        return PageUtil.pageResult(channelSmsRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询三方短信重发记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChannelSmsRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(channelSmsRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询三方短信重发记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChannelSmsRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(channelSmsRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询三方短信重发记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChannelSmsRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ChannelSmsRecord.class));

        return PageUtil.pageResult(channelSmsRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询三方短信重发记录', NULL, '/core/v1/channel_sms_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询三方短信重发记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChannelSmsRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChannelSmsRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(channelSmsRecordService.listFront(request));
    }

}