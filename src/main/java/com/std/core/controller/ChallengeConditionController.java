package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ChallengeCondition;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ChallengeConditionCreateReq;
import com.std.core.pojo.request.ChallengeConditionListFrontReq;
import com.std.core.pojo.request.ChallengeConditionListReq;
import com.std.core.pojo.request.ChallengeConditionModifyReq;
import com.std.core.pojo.request.ChallengeConditionPageFrontReq;
import com.std.core.pojo.request.ChallengeConditionPageReq;
import com.std.core.pojo.response.ChallengeConditionDetailRes;
import com.std.core.pojo.response.ChallengeConditionListRes;
import com.std.core.pojo.response.ChallengeConditionPageRes;
import com.std.core.service.IChallengeConditionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 挑战条件Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-05-26 11:12
 */
@ApiVersion(1)
@RestController
@Api(value = "挑战条件管理", tags = "挑战条件管理")
@RequestMapping("{version}/challenge_condition")
public class ChallengeConditionController extends BaseController {

    @Resource
    private IChallengeConditionService challengeConditionService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增挑战条件', NULL, '/core/v1/challenge_condition/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增挑战条件")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        challengeConditionService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "新增挑战条件(发行方)")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create_company")
    public Result createCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionCreateReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        challengeConditionService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除挑战条件', NULL, '/core/v1/challenge_condition/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除挑战条件")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        challengeConditionService.remove(id,operator);

        return new Result();
    }

    @ApiOperation(value = "删除挑战条件(发行方)")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove_company/{id}")
    public Result removeCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);
        challengeConditionService.remove(id,operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改挑战条件', NULL, '/core/v1/challenge_condition/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改挑战条件")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        challengeConditionService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "修改挑战条件(发行方)")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify_company")
    public Result modifyCompany(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ChallengeConditionModifyReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        challengeConditionService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询挑战条件', NULL, '/core/v1/challenge_condition/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询挑战条件")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ChallengeCondition> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(challengeConditionService.detail(id));
    }

    @ApiOperation(value = "查询挑战条件(发行方)")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail_company/{id}")
    public Result<ChallengeCondition> detailCompany(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(challengeConditionService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询挑战条件', NULL, '/core/v1/challenge_condition/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询挑战条件")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ChallengeCondition>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChallengeConditionPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(challengeConditionService.page(request));
    }

    @ApiOperation(value = "分页条件查询挑战条件(发行方)")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<ChallengeCondition>> pageCompany(@RequestHeader(value = "Authorization") String token,
                                                     @RequestBody @Valid ChallengeConditionPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(challengeConditionService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询挑战条件', NULL, '/core/v1/challenge_condition/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询挑战条件")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ChallengeCondition>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChallengeConditionListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询挑战条件', NULL, '/core/v1/challenge_condition/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询挑战条件")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ChallengeConditionDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询挑战条件', NULL, '/core/v1/challenge_condition/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询挑战条件")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ChallengeConditionPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChallengeConditionPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ChallengeCondition.class));

        return PageUtil.pageResult(challengeConditionService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询挑战条件', NULL, '/core/v1/challenge_condition/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询挑战条件")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ChallengeConditionListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ChallengeConditionListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(challengeConditionService.listFront(request));
    }

}