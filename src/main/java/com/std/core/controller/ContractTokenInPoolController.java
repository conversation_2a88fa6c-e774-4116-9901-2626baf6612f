package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ContractTokenInPool;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ContractTokenInPoolCreateReq;
import com.std.core.pojo.request.ContractTokenInPoolListReq;
import com.std.core.pojo.request.ContractTokenInPoolListFrontReq;
import com.std.core.pojo.request.ContractTokenInPoolModifyReq;
import com.std.core.pojo.request.ContractTokenInPoolPageReq;
import com.std.core.pojo.request.ContractTokenInPoolPageFrontReq;
import com.std.core.pojo.response.ContractTokenInPoolDetailRes;
import com.std.core.pojo.response.ContractTokenInPoolListRes;
import com.std.core.pojo.response.ContractTokenInPoolPageRes;
import com.std.core.service.IContractTokenInPoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代币池信息Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-14 22:17
 */
@ApiVersion(1)
@RestController
@Api(value = "代币池信息管理", tags = "代币池信息管理")
@RequestMapping("{version}/contract_token_in_pool")
public class ContractTokenInPoolController extends BaseController {

    @Resource
    private IContractTokenInPoolService contractTokenInPoolService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增代币池信息', NULL, '/core/v1/contract_token_in_pool/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增代币池信息")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolCreateReq request) {
        User operator = getUserByToken(token);
        contractTokenInPoolService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除代币池信息', NULL, '/core/v1/contract_token_in_pool/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除代币池信息")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        contractTokenInPoolService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改代币池信息', NULL, '/core/v1/contract_token_in_pool/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改代币池信息")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolModifyReq request) {
        User operator = getUserByToken(token);
        contractTokenInPoolService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询代币池信息', NULL, '/core/v1/contract_token_in_pool/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询代币池信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ContractTokenInPool> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPoolService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询代币池信息")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ContractTokenInPool>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenInPool.class));

        return PageUtil.pageResult(contractTokenInPoolService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询代币池信息")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ContractTokenInPool>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPoolService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询代币池信息', NULL, '/core/v1/contract_token_in_pool/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询代币池信息")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ContractTokenInPoolDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPoolService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询代币池信息")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ContractTokenInPoolPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenInPool.class));

        return PageUtil.pageResult(contractTokenInPoolService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询代币池信息', NULL, '/core/v1/contract_token_in_pool/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询代币池信息")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ContractTokenInPoolListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenInPoolListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenInPoolService.listFront(request));
    }

}