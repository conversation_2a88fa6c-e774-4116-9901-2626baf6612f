package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionPeriodPriorityBuy;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyCreateReq;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyListReq;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyListFrontReq;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyModifyReq;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyPageReq;
import com.std.core.pojo.request.CollectionPeriodPriorityBuyPageFrontReq;
import com.std.core.pojo.response.CollectionPeriodPriorityBuyDetailRes;
import com.std.core.pojo.response.CollectionPeriodPriorityBuyListRes;
import com.std.core.pojo.response.CollectionPeriodPriorityBuyPageRes;
import com.std.core.service.ICollectionPeriodPriorityBuyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 作品期数优先购买权Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-01-24 16:47
 */
@ApiVersion(1)
@RestController
@Api(value = "作品期数优先购买权管理", tags = "作品期数优先购买权管理")
@RequestMapping("{version}/collection_period_priority_buy")
public class CollectionPeriodPriorityBuyController extends BaseController {

    @Resource
    private ICollectionPeriodPriorityBuyService collectionPeriodPriorityBuyService;

    @ApiOperation(value = "新增作品期数优先购买权")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyCreateReq request) {
        User operator = getUserByToken(token);
        collectionPeriodPriorityBuyService.create(request, operator);

        return new Result();
    }

    @ApiOperation(value = "删除作品期数优先购买权")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        collectionPeriodPriorityBuyService.remove(id);

        return new Result();
    }

    @ApiOperation(value = "修改作品期数优先购买权")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyModifyReq request) {
        User operator = getUserByToken(token);
        collectionPeriodPriorityBuyService.modify(request, operator);

        return new Result();
    }

    @ApiOperation(value = "查询作品期数优先购买权")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionPeriodPriorityBuy> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodPriorityBuyService.detail(id));
    }

    @ApiOperation(value = "分页条件查询作品期数优先购买权")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionPeriodPriorityBuy>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionPeriodPriorityBuy.class));

        return PageUtil.pageResult(collectionPeriodPriorityBuyService.page(request));
    }

    @ApiOperation(value = "列表条件查询作品期数优先购买权")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionPeriodPriorityBuy>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodPriorityBuyService.list(request));
    }

    @ApiOperation(value = "查询作品期数优先购买权")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionPeriodPriorityBuyDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodPriorityBuyService.detailFront(id));
    }

    @ApiOperation(value = "前端分页条件查询作品期数优先购买权")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionPeriodPriorityBuyPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionPeriodPriorityBuy.class));

        return PageUtil.pageResult(collectionPeriodPriorityBuyService.pageFront(request));
    }

    @ApiOperation(value = "前端列表条件查询作品期数优先购买权")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionPeriodPriorityBuyListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionPeriodPriorityBuyListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionPeriodPriorityBuyService.listFront(request));
    }

}