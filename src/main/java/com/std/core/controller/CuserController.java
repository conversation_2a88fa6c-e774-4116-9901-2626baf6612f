package com.std.core.controller;

import com.alibaba.fastjson.JSONObject;
import com.dingxianginc.ctu.client.CaptchaClient;
import com.dingxianginc.ctu.client.model.CaptchaResponse;
import com.github.pagehelper.PageHelper;
import com.std.common.base.BaseIdReq;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.config.DingXiangConfig;
import com.std.core.controller.base.BaseController;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EConfigType.AUTH;
import com.std.core.enums.EIdentifyOrderStatus;
import com.std.core.enums.EIdentifyStyle;
import com.std.core.enums.EUserIdentifyStatus;
import com.std.core.enums.EUserKind;
import com.std.core.enums.EUserNodeLevelType;
import com.std.core.enums.EUserStatus;
import com.std.core.pojo.domain.ChannelMerchant;
import com.std.core.pojo.domain.Cuser;
import com.std.core.pojo.domain.IdentifyConfig;
import com.std.core.pojo.domain.IdentifyOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CuserBatchResetReq;
import com.std.core.pojo.request.CuserListReq2;
import com.std.core.pojo.request.CuserLogoffReq;
import com.std.core.pojo.request.CuserModifyIdentifyStyleReq;
import com.std.core.pojo.request.CuserPageReq;
import com.std.core.pojo.request.ModifyUserMobileReq;
import com.std.core.pojo.request.UserChangeRefereeReq;
import com.std.core.pojo.request.UserChannelSettingCancelReq;
import com.std.core.pojo.request.UserChannelSettingReq;
import com.std.core.pojo.request.UserGetPhotoReq;
import com.std.core.pojo.request.UserGroupingSettingReq;
import com.std.core.pojo.request.UserLoginChannelReq;
import com.std.core.pojo.request.UserLoginCheckReq;
import com.std.core.pojo.request.UserLoginReq;
import com.std.core.pojo.request.UserLoginRes;
import com.std.core.pojo.request.UserModifyIdNoReq;
import com.std.core.pojo.request.UserModifyIntroduceReq;
import com.std.core.pojo.request.UserRegisterReq;
import com.std.core.pojo.request.UserStatusReq;
import com.std.core.pojo.request.UserSysCloseReq;
import com.std.core.pojo.request.XmlyBindMobileReq;
import com.std.core.pojo.request.XmlyCheckBindMobileReq;
import com.std.core.pojo.response.SmsUnreadMessagesRes;
import com.std.core.pojo.response.TeamUserRes;
import com.std.core.pojo.response.UserGetPhotoRes;
import com.std.core.pojo.response.UserHomeRes;
import com.std.core.pojo.response.UserMyPhotoRes;
import com.std.core.service.IChannelMerchantService;
import com.std.core.service.IConfigService;
import com.std.core.service.ICuserService;
import com.std.core.service.IIdentifyConfigService;
import com.std.core.service.IIdentifyOrderService;
import com.std.core.service.IMemberConfigService;
import com.std.core.service.IShellTaskConfigService;
import com.std.core.service.ISmsReadService;
import com.std.core.service.IUserService;
import com.std.core.service.IYaoTaskConfigService;
import com.std.core.util.DateUtil;
import com.std.core.util.PrivacyUtil;
import com.std.core.util.RedisLock;
import com.std.core.util.RedisUtil;
import com.std.core.util.SysConstants;
import com.std.core.websocket.WebSocketServer;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * C端用户Controller
 *
 * <AUTHOR> Leo
 * @since : 2020-05-18 20:09
 */
@ApiVersion(1)
@RestController
@Api(value = "C端用户管理", tags = "C端用户管理")
@RequestMapping("{version}/cuser")
@Slf4j
public class CuserController extends BaseController {

    @Resource
    private ICuserService cuserService;

    @Resource
    private WebSocketServer webSocketServer;

    @Resource
    private IUserService userService;

    @Resource
    private IIdentifyOrderService identifyOrderService;

    @Resource
    private IMemberConfigService memberConfigService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private IConfigService configService;

    @Resource
    private ISmsReadService smsReadService;

    @Resource
    private IIdentifyConfigService identifyConfigService;

    @Resource
    private IChannelMerchantService channelMerchantService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @Resource
    private DingXiangConfig dingXiangConfig;

    @Value("${spring.profiles.active}")
    private String profilesActive;

    @Resource
    private IYaoTaskConfigService yaoTaskConfigService;

    @Resource
    private IShellTaskConfigService shellTaskConfigService;

    @ApiOperation(value = "C端用户注册")
    @ApiOperationSupport(order = 1)
    @PostMapping("/public/register")
    public Result<JSONObject> register(@RequestBody @Valid UserRegisterReq request,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @ModelAttribute("ip") String ip) {
        if (null != channelId) {
            request.setChannelMerchantId(channelId);
        }
        return new Result<>(cuserService.register(request, ip));
    }

    @ApiOperation(value = "根据喜马拉雅uid获取是否已经绑定用户信息")
    @ApiOperationSupport(order = 1)
    @PostMapping("/public/doCheckBindMobile")
    public Result<JSONObject> doCheckBindMobile(@RequestBody @Valid XmlyCheckBindMobileReq request,
            @RequestHeader(value = "channelId") Long channelId,
            @ModelAttribute("ip") String ip) {
        request.setChannelId(channelId);
        return new Result<>(cuserService.doCheckBindMobile(request));
    }

    @ApiOperation(value = "根据uid绑定注册手机号")
    @ApiOperationSupport(order = 1)
    @PostMapping("/public/doBindMobile")
    public Result<JSONObject> doBindMobile(@RequestBody @Valid XmlyBindMobileReq request,
            @RequestHeader(value = "channelId") Long channelId,
            @ModelAttribute("ip") String ip) {
        request.setChannelId(channelId);
        return new Result<>(cuserService.doBindMobile(request, ip));
    }

    @ApiOperation(value = "C端用户登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/login")
    public Result<UserLoginRes> cLogin(
            @RequestBody @Valid UserLoginReq request,
            @RequestHeader(value = "channelId", required = false) Long channelId,
            @ModelAttribute("ip") String ip) {

        //顶象token验
        checkDingXiangToken(request.getThirdToken());

        String lockId = "login_C:" + request.getLoginName();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "登录中，请稍后");
            }
            return new Result<>(cuserService.login(request, channelId, ip));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }

    private void checkDingXiangToken(String token) {
        if (StringUtils.isBlank(token)) {
            return;
        }

        try {
            /**构造入参为appId和appSecret
             * appId和前端验证码的appId保持一致，appId可公开
             * appSecret为秘钥，请勿公开
             * token在前端完成验证后可以获取到，随业务请求发送到后台，token有效期为两分钟
             * ip 可选，提交业务参数的客户端ip
             **/
            String appId = dingXiangConfig.getAppId();
            String appSecret = dingXiangConfig.getAppSecret();
            CaptchaClient captchaClient = new CaptchaClient(appId, appSecret);
            captchaClient.setCaptchaUrl(dingXiangConfig.getApiServer() + "/api/tokenVerify");
            //指定服务器地址，saas可在控制台，应用管理页面最上方获取
            CaptchaResponse response = captchaClient.verifyToken(token);
            //CaptchaResponse response = captchaClient.verifyToken(token, ip);
            //针对一些token冒用的情况，业务方可以采集客户端ip随token一起提交到验证码服务，验证码服务除了判断token的合法性还会校验提交业务参数的客户端ip和验证码颁发token的客户端ip是否一致
            log.info("captchastatus[" + response.getCaptchaStatus() + "]");
            //确保验证状态是SERVER_SUCCESS，SDK中有容错机制，在网络出现异常的情况会返回通过
            log.info("客户端请求ip[" + response.getIp() + "]");
            //验证码服务采集到的客户端ip
            if (!response.getResult()) {
                /**token验证失败，业务系统可以直接阻断该次请求或者继续弹验证码**/
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无感校验失败，请重新再来");
            } else {
                /**token验证通过，继续其他流程**/
            }
        } catch (Exception e) {
            log.error("登录token:" + token);
            log.error("登录错误:" + e.getMessage());
            //throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无感校验失败，请稍后再试");
        }
    }

    @ApiOperation(value = "用户登录检查", position = 32)
    @PostMapping("/public/login_check")
    public Result<UserLoginRes> loginCheck(
            @RequestBody @Valid UserLoginCheckReq request, @ModelAttribute("ip") String ip) {

        return new Result<>(userService.loginCheck(request, EUserKind.C.getCode(), ip));

    }

    @ApiOperation(value = "重置登录状态", position = 32)
    @PostMapping("/login_failure_reset")
    public Result loginFailureReset(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid BaseIdReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        userService.loginFailureReset(request, operator);
        return new Result();

    }

    @ApiOperation(value = "用户未读消息查询")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/user_home_unread")
    public Result<UserHomeRes> userHomeUnread(
            @RequestHeader(value = "Authorization", required = false) String token) {
        User operator = null;
        if (StringUtils.isNotBlank(token)) {
            operator = getUserByToken(token, EUserKind.C);
        }

        return new Result<>(cuserService.getUserUnRead(operator));
    }

    @ApiOperation(value = "C端用户登录")
    @ApiOperationSupport(order = 2)
    @PostMapping("/public/login_channel")
    public Result<JSONObject> cLoginChannel(
            @RequestBody @Valid UserLoginChannelReq request,
            @RequestHeader(value = "channelId") Long channelId,
            @ModelAttribute("ip") String ip) {

        return new Result<>(cuserService.loginChannel(request, channelId, ip));
    }

    @ApiOperation(value = "根据手机号获取昵称头像")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/get_user_photo")
    public Result<UserGetPhotoRes> getUserPhotoByMobile(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserGetPhotoReq req) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(cuserService.getUserPhotoByMobile(req.getMobile()));
    }

    @ApiOperation(value = "根据手机号获取昵称头像")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/public/check_user_by_mobile")
    public Result<UserGetPhotoRes> checkUserByMobile(
            @RequestBody @Valid UserGetPhotoReq req) {

        return new Result<>(cuserService.checkUserByMobile(req));
    }

    @ApiOperation(value = "C端个人详细信息查询")
    @ApiOperationSupport(order = 3)
    @PostMapping("/my")
    public Result<Cuser> detailByToken(@RequestHeader(value = "Authorization") @Valid String token,
            @RequestHeader(value = "channelId", required = false) @Valid Long channelId) {
        User user = getUserByToken(token, EUserKind.C);

        Map<String, String> faceConfigMap = configService.getConfigsMap(AUTH.FACE.getCode());
        user.setRemainVerfityCount(Integer.valueOf(faceConfigMap.get(SysConstants.FACE_PERSON_TIME)));
        user.setNextFaceFlag(EBoolean.YES.getCode());
        if (StringUtils.isNotBlank(user.getIdKind()) && StringUtils.isNotBlank(user.getRealName()) && StringUtils
                .isNotBlank(user.getIdNo())) {
            if (!EUserIdentifyStatus.IDENTIFY_6.getCode().equals(user.getIdentifyStatus())) {
                user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_1.getCode());
                user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_1.getCode());
            } else {
                user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_6.getCode());
                user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_1.getCode());
            }

        } else {

            IdentifyOrder identifyOrder = identifyOrderService.selectLast(user.getId());
            if (null == identifyOrder) {
                user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_0.getCode());
                user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_0.getCode());
            } else if (EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_0.getCode().equals(identifyOrder.getStatus())
                    || EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_2.getCode().equals(identifyOrder.getStatus())
                    || EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_6.getCode().equals(identifyOrder.getStatus())) {
                //如果次数超限制，状态改成待人工认证
                int userIngCount = identifyOrderService.selectApplyIngCount(user.getId());
                user.setRemainVerfityCount(user.getRemainVerfityCount() - userIngCount);
                if (user.getRemainVerfityCount() <= 0) {
                    user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_2.getCode());
                    user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_2.getCode());
                    user.setFaceFailNote(faceConfigMap.get(SysConstants.FACE_FAIL_NOTE));
                    user.setFaceFailOpenManualFlag(faceConfigMap.get(SysConstants.FACE_FAIL_OPEN_MANUAL_FLAG));
                } else {
                    if (EIdentifyStyle.FACE.getCode().equals(user.getIdentifyStyle())) {
                        user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_0.getCode());
                        user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_0.getCode());
                        if (null != identifyOrder.getNextDatetime() && new Date().before(identifyOrder.getNextDatetime())) {

                            IdentifyConfig config = identifyConfigService.detailByConfig(identifyOrder.getOrderNo());
                            if (null != config) {
                                user.setFaceFailNote(config.getPrompt());
                            } else {
                                String s = DateUtil
                                        .dateToStr(identifyOrder.getNextDatetime(), com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1);
                                user.setFaceFailNote("您必须等待至" + s + "后才能再次实名认证");
                            }
                            long times = (identifyOrder.getNextDatetime().getTime() - System.currentTimeMillis()) / 1000 * 1000;

                            user.setNextTime(times);

                            if (times > 0) {
                                user.setNextFaceFlag(EBoolean.NO.getCode());
                            }
                        }
                    } else {
                        user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_2.getCode());
                        user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_2.getCode());
                    }
                }
            } else if (EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_5.getCode().equals(identifyOrder.getStatus())) {
                //人工认证失败，剩余人脸认证为0，认证方式=人工认证
                user.setRemainVerfityCount(0);
                user.setIdentifyStyle(EIdentifyStyle.MANUAL.getCode());
                user.setIdentifyStatus(EUserIdentifyStatus.IDENTIFY_5.getCode());
                user.setIdentifyStatusH5(EUserIdentifyStatus.IDENTIFY_5.getCode());
            } else {
                //其他情况就是人工认证或者人脸通过的情况
                user.setIdentifyStatus(identifyOrder.getStatus());
                user.setIdentifyStatusH5(identifyOrder.getStatus());
                user.setIdNo(identifyOrder.getIdNo());
            }
        }

        try {
            //做最后登录
            String userLoginKey = SysConstants.CURRDAY_LOGIN.concat(user.getId().toString());
            String time = redisUtil.getString(userLoginKey);
            boolean isNeedCache = false;
            if (StringUtils.isBlank(time)) {

                isNeedCache = true;
            } else {
                Long cacheTime = Long.valueOf(time);
                Long firstTime = DateUtil.getCurrDayFirstTime().getTime();
                if (cacheTime.compareTo(firstTime) < 0) {
                    isNeedCache = true;
                }
            }
            if (isNeedCache) {
                redisUtil.set(userLoginKey, System.currentTimeMillis(), 24 * 60 * 60);
                //更新登录时间
                user.setLastLoginDatetime(new Date());
                userService.updateLogintime(user);
            }
        } catch (Exception e) {
        }

        Cuser cuser = new Cuser();
        cuser.setId(user.getId());
        cuser.setIdentifyStyle(user.getIdentifyStyle());
        cuser.setIdentifyStatus(user.getIdentifyStatus());
        cuser.setIdentifyStatusH5(user.getIdentifyStatusH5());
        cuser.setFaceFailNote(user.getFaceFailNote());
        cuser.setFaceFailOpenManualFlag(user.getFaceFailOpenManualFlag());
        cuser.setRemainVerfityCount(user.getRemainVerfityCount());
        cuser.setNextTime(user.getNextTime());
        cuser.setNextFaceFlag(user.getNextFaceFlag());
        cuser.setPhoto(user.getPhoto());
        cuser.setLoginName(user.getLoginName());
        cuser.setNickname(user.getNickname());
        cuser.setInviteCode(user.getInviteCode());
        cuser.setTradePwdFlag(user.getTradePwdFlag());
        cuser.setRealName(user.getRealName());
        cuser.setIdNo(user.getIdNo());
        cuser.setUserId(user.getId());
        cuser.setMobile(user.getMobile());
//        MemberConfig memberConfig = memberConfigService.getMemberConfig(cuser.getUserId());
//        cuser.setMemberName(memberConfig.getName());
//        cuser.setMemberLevel(memberConfig.getLevel());
        //cuser.setBlockAddress(user.getBlockAddress());
        cuser.setIntroduce(user.getIntroduce());

        cuser.setYaoFlag(user.getYaoFlag());
//        if (EUserYaoFlag.E_USER_YAO_FLAG_2.getCode().equals(user.getYaoFlag())) {
//            cuser.setYaoFlag(EUserYaoFlag.E_USER_YAO_FLAG_0.getCode());
//        } else if (EUserYaoFlag.E_USER_YAO_FLAG_0.getCode().equals(user.getYaoFlag())) {
//            Date date = DateUtil.strToDate("2022-11-24 20:30:00", com.std.common.utils.DateUtil.DATA_TIME_PATTERN_1);
//            if (user.getRegisterDatetime().before(date)) {
//                cuser.setYaoFlag(EUserYaoFlag.E_USER_YAO_FLAG_1.getCode());
//            }
//        }

        //人脸认证成功之后，身份证信息获取隐藏
        if (StringUtils.isNotBlank(cuser.getIdNo()) && EIdentifyOrderStatus.IDENTIFY_ORDER_STATUS_1.getCode()
                .equals(cuser.getIdentifyStatus())) {
            cuser.setIdNo(cuser.getIdNo().substring(0, 4) + "********" + cuser.getIdNo().substring(cuser.getIdNo().length() - 6));
        }

        if (StringUtils.isBlank(cuser.getIntroduce())) {
            cuser.setIntroduce(configService.getString(RedisKeyList.MT_CONFIG_OBJECT_KEY, SysConstants.USER_DEFAULT_INTRODUCE));
        }

        // 获取商户渠道商
        ChannelMerchant channelMerchant = channelMerchantService.detailByFront(channelId);
        SmsUnreadMessagesRes myUnreadCount = smsReadService.getMyUnreadExists(user, channelMerchant);
        cuser.setSmsUnreadMessagesRes(myUnreadCount);

        // 每日登录获得渔贝
        shellTaskConfigService.create(user);

        return new Result<>(cuser);
    }

    @ApiOperation(value = "C端个人头像和昵称")
    @ApiOperationSupport(order = 3)
    @PostMapping("/my_photo")
    public Result<UserMyPhotoRes> myPhoto(@RequestHeader(value = "Authorization") @Valid String token) {
        User user = getUserByToken(token, EUserKind.C);
        UserMyPhotoRes userNew = new UserMyPhotoRes();
        userNew.setNickname(user.getNickname());
        userNew.setPhoto(user.getPhoto());
        userNew.setId(user.getId());
        return new Result<>(userNew);
    }

    @ApiOperation(value = "设置状态")
    @ApiOperationSupport(order = 5)
    @PostMapping("/set_up_status")
    public Result setUpstatus(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserStatusReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);

        cuserService.modifyStatus(request, operator, ip);
        return new Result<>();
    }


    @ApiOperation(value = "用户注销")
    @ApiOperationSupport(order = 5)
    @PostMapping("/logoff")
    public Result logoff(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CuserLogoffReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        cuserService.logoff(operator, request);
        return new Result<>();
    }

    @ApiOperation(value = "系统注销")
    @ApiOperationSupport(order = 5)
    @PostMapping("/sys_close")
    public Result doSysClose(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid UserSysCloseReq request,
            @ModelAttribute("ip") String ip) {
        User operator = getUserByToken(token, EUserKind.SYS);

        for (Long id : request.getIdList()) {
            cuserService.doSysClose(id, request.getRemark(), operator, ip);
        }
        return new Result<>();
    }

    @ApiOperation(value = "批量重置实名信息")
    @ApiOperationSupport(order = 5)
    @PostMapping("/batch_reset")
    public Result batchReset(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CuserBatchResetReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        cuserService.batchReset(operator, request);
        return new Result<>();
    }

    @ApiOperation(value = "批量清退用户")
    @ApiOperationSupport(order = 5)
    @PostMapping("/batch_recovery")
    public Result batchRecovery(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CuserBatchResetReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        for (Long id : request.getIdList()) {
            Cuser cuser = cuserService.detail(id);
            User user = userService.detailSimple(cuser.getUserId());

            if (EUserStatus.NORMAL.getCode().equals(user.getStatus())) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户" + user.getLoginName() + "账户未注销，不能清退");
            }

            try {
                cuserService.batchRecovery(operator, user);
            } catch (Exception e) {
                log.error("清退失败,原因：" + e.getMessage());
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户" + user.getLoginName() + "清退失败");
            }

        }
        return new Result<>();
    }

    @ApiOperation(value = "实名修改成人工实名方式(批量)")
    @ApiOperationSupport(order = 5)
    @PostMapping("/modify_identify_style")
    public Result modifyIdentifyStyle(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserModifyIdentifyStyleReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        cuserService.batchModifyIdentifyStyle(operator, request);
        return new Result<>();
    }

    @ApiOperation(value = "分页条件查询C端用户（OSS）")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/page")
    public Result<PageInfo<Cuser>> page(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        request.setNodeLevelType(EUserNodeLevelType.MEMBER.getCode());
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));

        return PageUtil.pageResult(cuserService.page(request));
    }

    @ApiOperation(value = "分页条件查询C端用户（OSS）")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/page_company")
    public Result<PageInfo<Cuser>> pageCompany(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);

        request.setNodeLevelType(EUserNodeLevelType.MEMBER.getCode());
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));

        return PageUtil.pageResult(cuserService.page(request));
    }

    @ApiOperation(value = "下拉框模糊查询")
    @ApiOperationSupport(order = 24)
    @PostMapping(value = "/vague_deatil")
    public Result<List<Cuser>> vagueDeatil(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(cuserService.vagueDeatil(request));
    }

    @ApiOperation(value = "C端用户详情（OSS）")
    @ApiOperationSupport(order = 25)
    @PostMapping(value = "/detail/{id}")
    public Result<Cuser> detail(
            @RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);
        Cuser cuser = cuserService.detail(id);
        cuser.setLoginName(PrivacyUtil.encryptPhoneNo(cuser.getLoginName()));
        cuser.setMobile(PrivacyUtil.encryptPhoneNo(cuser.getMobile()));
        cuser.setIdNo(PrivacyUtil.encryptIdNo(cuser.getIdNo()));

        return new Result<>(cuser);
    }

    @ApiOperation(value = "前端用户退出")
    @ApiOperationSupport(order = 30)
    @GetMapping(value = "/logOut")
    public Result<Object> logOut(
            @RequestHeader(value = "Authorization") String token) {
        User operator = getUserByToken(token, EUserKind.C);
        webSocketServer.onClose();

        redisUtil.del(operator.getId().toString());

        return new Result<>();
    }

    @ApiOperation(value = "修改个人介绍")
    @ApiOperationSupport(order = 31)
    @GetMapping(value = "/modify_introduce")
    public Result modifyIntroduce(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyIntroduceReq request) {
        User operator = getUserByToken(token, EUserKind.C);
        userService.modifyIntroduce(request, operator);

        return new Result<>();
    }

    @ApiOperation(value = "修改身份证号")
    @ApiOperationSupport(order = 31)
    @PostMapping(value = "/modify_id_no")
    public Result modifyIdNo(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserModifyIdNoReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        userService.modifyIdNo(request, operator);

        return new Result<>();
    }

    @ApiOperation(value = "重置推荐", position = 220)
    @PostMapping(value = "/change_refer_user")
    public Result changeReferUser(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserChangeRefereeReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.changeReferUser(request);

        return new Result();
    }

    @ApiOperation(value = "平台分页团队查询")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/team_page")
    public Result<PageInfo<TeamUserRes>> teamPage(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(
                request.getPageNum(),
                request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), User.class));
        return PageUtil.pageResult(cuserService.teamPage(request));
    }

    @ApiOperation(value = "平台分页团队查询-下级人员")
    @ApiOperationSupport(order = 22)
    @PostMapping(value = "/team_page_next")
    public Result<List<TeamUserRes>> teamPageNext(
            @RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CuserListReq2 request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        CuserPageReq request2 = new CuserPageReq();
        request2.setUserReferee(request.getUserReferee());

        return new Result<>(cuserService.teamPage(request2));
    }

//
//    @ApiOperation(value = "社区查询")
//    @ApiOperationSupport(order = 22)
//    @PostMapping(value = "/community")
//    public Result<CommunityUserRes> community(
//            @RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid BaseIdReq req) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        return new Result<>(cuserService.communityRef(req));
//    }

//
//    @ApiOperation(value = "社区查询-下级")
//    @ApiOperationSupport(order = 22)
//    @PostMapping(value = "/community_page")
//    public Result<List<CommunityUserRes>> communityPage(
//            @RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid BaseIdReq req) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        return new Result<>(cuserService.communityRef2(req));
//    }
//
//    @ApiOperation(value = "平台社区查询-社区人员")
//    @ApiOperationSupport(order = 22)
//    @PostMapping(value = "/community_page_next")
//    public Result<List<CommunityUserRes>> communityPageNext(
//            @RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid BaseIdReq req) {
//        User operator = getUserByToken(token, EUserKind.SYS);
//
//        return new Result<>(cuserService.communityPage(req));
//    }
//

    @ApiOperation(value = "设置用户组(OSS)")
    @ApiOperationSupport(order = 200)
    @PostMapping(value = "/user_grouping_setting")
    public Result userGroupingSetting(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserGroupingSettingReq req) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.userGroupingSetting(req, operator);
        return new Result();
    }

    @ApiOperation(value = "设置用户渠道商(OSS)")
    @ApiOperationSupport(order = 210)
    @PostMapping(value = "/user_channel_setting")
    public Result userChannelSetting(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserChannelSettingReq req) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.userChannelSetting(req, operator);
        return new Result();
    }

    @ApiOperation(value = "取消渠道商配置(oss)")
    @ApiOperationSupport(order = 211)
    @PostMapping(value = "/cancel_channel_setting")
    public Result cancelChannelSetting(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid UserChannelSettingCancelReq req) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.cancelChannelSetting(req, operator);
        return new Result();
    }

    @ApiOperation(value = "修改用户手机号(OSS)")
    @ApiOperationSupport(order = 212)
    @PostMapping(value = "/modify_mobile")
    public Result modifyMobile(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ModifyUserMobileReq req) {
        User operator = getUserByToken(token, EUserKind.SYS);
        cuserService.modifyMobile(req, operator);
        return new Result();
    }
}