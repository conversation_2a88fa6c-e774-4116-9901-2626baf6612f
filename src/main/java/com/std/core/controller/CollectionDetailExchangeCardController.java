package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionDetailExchangeCard;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionDetailExchangeCardPageReq;
import com.std.core.service.ICollectionDetailExchangeCardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 藏品型号兑换Xmeta上架卡Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-12-13 17:32
 */
@ApiVersion(1)
@RestController
@Api(value = "藏品型号兑换Xmeta上架卡管理", tags = "藏品型号兑换Xmeta上架卡管理")
@RequestMapping("{version}/collection_detail_exchange_card")
public class CollectionDetailExchangeCardController extends BaseController {

    @Resource
    private ICollectionDetailExchangeCardService collectionDetailExchangeCardService;

    @ApiOperation(value = "分页条件查询藏品型号兑换Xmeta上架卡")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailExchangeCard>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailExchangeCardPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailExchangeCard.class));

        return PageUtil.pageResult(collectionDetailExchangeCardService.page(request));
    }

    @ApiOperation(value = "查询藏品型号兑换Xmeta上架卡")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailExchangeCard> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailExchangeCardService.detail(id));
    }

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询藏品型号兑换Xmeta上架卡', NULL, '/core/v1/collection_detail_exchange_card/list', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "列表条件查询藏品型号兑换Xmeta上架卡")
//    @ApiOperationSupport(order = 60)
//    @PostMapping(value = "/list")
//    public Result<List<CollectionDetailExchangeCard>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailExchangeCardListReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(collectionDetailExchangeCardService.list(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询藏品型号兑换Xmeta上架卡', NULL, '/core/v1/collection_detail_exchange_card/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询藏品型号兑换Xmeta上架卡")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<CollectionDetailExchangeCardDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(collectionDetailExchangeCardService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询藏品型号兑换Xmeta上架卡', NULL, '/core/v1/collection_detail_exchange_card/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询藏品型号兑换Xmeta上架卡")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<CollectionDetailExchangeCardPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailExchangeCardPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), CollectionDetailExchangeCard.class));
//
//        return PageUtil.pageResult(collectionDetailExchangeCardService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询藏品型号兑换Xmeta上架卡', NULL, '/core/v1/collection_detail_exchange_card/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询藏品型号兑换Xmeta上架卡")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<CollectionDetailExchangeCardListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailExchangeCardListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(collectionDetailExchangeCardService.listFront(request));
//    }

}