package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.PeriodDiscountDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodDiscountDetailCreateReq;
import com.std.core.pojo.request.PeriodDiscountDetailListReq;
import com.std.core.pojo.request.PeriodDiscountDetailListFrontReq;
import com.std.core.pojo.request.PeriodDiscountDetailModifyReq;
import com.std.core.pojo.request.PeriodDiscountDetailPageReq;
import com.std.core.pojo.request.PeriodDiscountDetailPageFrontReq;
import com.std.core.pojo.response.PeriodDiscountDetailDetailRes;
import com.std.core.pojo.response.PeriodDiscountDetailListRes;
import com.std.core.pojo.response.PeriodDiscountDetailPageRes;
import com.std.core.service.IPeriodDiscountDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 一级市场折扣明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-28 13:48
 */
@ApiVersion(1)
@RestController
@Api(value = "一级市场折扣明细管理", tags = "一级市场折扣明细管理")
@RequestMapping("{version}/period_discount_detail")
public class PeriodDiscountDetailController extends BaseController {

    @Resource
    private IPeriodDiscountDetailService periodDiscountDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增一级市场折扣明细', NULL, '/core/v1/period_discount_detail/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增一级市场折扣明细")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailCreateReq request) {
        User operator = getUserByToken(token);
        periodDiscountDetailService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除一级市场折扣明细', NULL, '/core/v1/period_discount_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除一级市场折扣明细")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        periodDiscountDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改一级市场折扣明细', NULL, '/core/v1/period_discount_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改一级市场折扣明细")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailModifyReq request) {
        User operator = getUserByToken(token);
        periodDiscountDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询一级市场折扣明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodDiscountDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodDiscountDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询一级市场折扣明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodDiscountDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodDiscountDetail.class));

        return PageUtil.pageResult(periodDiscountDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询一级市场折扣明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<PeriodDiscountDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodDiscountDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询一级市场折扣明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<PeriodDiscountDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodDiscountDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询一级市场折扣明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<PeriodDiscountDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodDiscountDetail.class));

        return PageUtil.pageResult(periodDiscountDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询一级市场折扣明细', NULL, '/core/v1/period_discount_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询一级市场折扣明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<PeriodDiscountDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodDiscountDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodDiscountDetailService.listFront(request));
    }

}