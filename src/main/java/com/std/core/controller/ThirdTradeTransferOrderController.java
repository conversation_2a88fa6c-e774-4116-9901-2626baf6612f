package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ThirdTradeTransferOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ThirdTradeTransferOrderListFrontReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderListReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderPageFrontReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderPageReq;
import com.std.core.pojo.response.ThirdTradeTransferOrderDetailRes;
import com.std.core.pojo.response.ThirdTradeTransferOrderListRes;
import com.std.core.pojo.response.ThirdTradeTransferOrderPageRes;
import com.std.core.service.IThirdTradeTransferOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 三方系统交易划转订单Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-11-18 17:30
 */
@ApiVersion(1)
@RestController
@Api(value = "三方系统交易划转订单管理", tags = "三方系统交易划转订单管理")
@RequestMapping("{version}/third_trade_transfer_order")
public class ThirdTradeTransferOrderController extends BaseController {

    @Resource
    private IThirdTradeTransferOrderService thirdTradeTransferOrderService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询三方系统交易划转订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ThirdTradeTransferOrder> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ThirdTradeTransferOrder>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdTradeTransferOrder.class));

        return PageUtil.pageResult(thirdTradeTransferOrderService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ThirdTradeTransferOrder>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询三方系统交易划转订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ThirdTradeTransferOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ThirdTradeTransferOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdTradeTransferOrder.class));

        return PageUtil.pageResult(thirdTradeTransferOrderService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ThirdTradeTransferOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderService.listFront(request));
    }

}