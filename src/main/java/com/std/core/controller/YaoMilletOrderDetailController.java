package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.YaoMilletOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.YaoMilletOrderDetailCreateReq;
import com.std.core.pojo.request.YaoMilletOrderDetailListReq;
import com.std.core.pojo.request.YaoMilletOrderDetailListFrontReq;
import com.std.core.pojo.request.YaoMilletOrderDetailModifyReq;
import com.std.core.pojo.request.YaoMilletOrderDetailPageReq;
import com.std.core.pojo.request.YaoMilletOrderDetailPageFrontReq;
import com.std.core.pojo.response.YaoMilletOrderDetailDetailRes;
import com.std.core.pojo.response.YaoMilletOrderDetailListRes;
import com.std.core.pojo.response.YaoMilletOrderDetailPageRes;
import com.std.core.service.IYaoMilletOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 元粟合成订单明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-11-12 09:51
 */
@ApiVersion(1)
@RestController
@Api(value = "元粟合成订单明细管理", tags = "元粟合成订单明细管理")
@RequestMapping("{version}/yao_millet_order_detail")
public class YaoMilletOrderDetailController extends BaseController {

    @Resource
    private IYaoMilletOrderDetailService yaoMilletOrderDetailService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增元粟合成订单明细")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailCreateReq request) {
//        User operator = getUserByToken(token);
//        yaoMilletOrderDetailService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除元粟合成订单明细")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        yaoMilletOrderDetailService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改元粟合成订单明细")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailModifyReq request) {
//        User operator = getUserByToken(token);
//        yaoMilletOrderDetailService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询元粟合成订单明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<YaoMilletOrderDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(yaoMilletOrderDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询元粟合成订单明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<YaoMilletOrderDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoMilletOrderDetail.class));

        return PageUtil.pageResult(yaoMilletOrderDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询元粟合成订单明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<YaoMilletOrderDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletOrderDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询元粟合成订单明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<YaoMilletOrderDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletOrderDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询元粟合成订单明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<YaoMilletOrderDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), YaoMilletOrderDetail.class));

        return PageUtil.pageResult(yaoMilletOrderDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询元粟合成订单明细', NULL, '/core/v1/yao_millet_order_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询元粟合成订单明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<YaoMilletOrderDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid YaoMilletOrderDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(yaoMilletOrderDetailService.listFront(request));
    }

}