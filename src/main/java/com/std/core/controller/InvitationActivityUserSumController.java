package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.InvitationActivityUserSum;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.InvitationActivityUserSumListFrontReq;
import com.std.core.pojo.request.InvitationActivityUserSumListReq;
import com.std.core.pojo.request.InvitationActivityUserSumPageFrontReq;
import com.std.core.pojo.request.InvitationActivityUserSumPageReq;
import com.std.core.pojo.response.InvitationActivityUserSumDetailRes;
import com.std.core.pojo.response.InvitationActivityUserSumListRes;
import com.std.core.pojo.response.InvitationActivityUserSumPageRes;
import com.std.core.pojo.response.InvitationActivityUserSumRankRes;
import com.std.core.service.IInvitationActivityUserSumService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户拉新信息Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-12 14:54
 */
@ApiVersion(1)
@RestController
@Api(value = "用户拉新信息管理", tags = "用户拉新信息管理")
@RequestMapping("{version}/invitation_activity_user_sum")
public class InvitationActivityUserSumController extends BaseController {

    @Resource
    private IInvitationActivityUserSumService invitationActivityUserSumService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询用户拉新信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<InvitationActivityUserSum> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(invitationActivityUserSumService.detail(id));
    }

    @ApiOperation(value = "查询用户拉新信息")
    @ApiOperationSupport(order = 40)
    @PostMapping("/company_detail/{id}")
    public Result<InvitationActivityUserSum> companyDetail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.BP);

        return new Result<>(invitationActivityUserSumService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询用户拉新信息")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<InvitationActivityUserSum>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUserSumPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(invitationActivityUserSumService.page(request, operator));
    }

    @ApiOperation(value = "分页条件查询用户拉新信息")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/company_page")
    public Result<PageInfo<InvitationActivityUserSum>> companypage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUserSumPageReq request) {
        User operator = getUserByToken(token, EUserKind.BP);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityUserSum.class));

        return PageUtil.pageResult(invitationActivityUserSumService.page(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询用户拉新信息")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<InvitationActivityUserSum>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUserSumListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserSumService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询用户拉新信息")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<InvitationActivityUserSumDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserSumService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询用户拉新信息")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<InvitationActivityUserSumPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUserSumPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), InvitationActivityUserSum.class));

        return PageUtil.pageResult(invitationActivityUserSumService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询用户拉新信息', NULL, '/core/v1/invitation_activity_user_sum/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询用户拉新信息")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<InvitationActivityUserSumListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid InvitationActivityUserSumListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserSumService.listFront(request));
    }

    @ApiOperation(value = "前端查询某个活动排行情况")
    @ApiOperationSupport(order = 100)
    @PostMapping(value = "/rank/detail/{id}")
    public Result<InvitationActivityUserSumRankRes> rankDetail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(invitationActivityUserSumService.detailRank(operator, id));
    }
}