package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.ContractTokenExportDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ContractTokenExportDetailCreateReq;
import com.std.core.pojo.request.ContractTokenExportDetailListReq;
import com.std.core.pojo.request.ContractTokenExportDetailListFrontReq;
import com.std.core.pojo.request.ContractTokenExportDetailModifyReq;
import com.std.core.pojo.request.ContractTokenExportDetailPageReq;
import com.std.core.pojo.request.ContractTokenExportDetailPageFrontReq;
import com.std.core.pojo.response.ContractTokenExportDetailDetailRes;
import com.std.core.pojo.response.ContractTokenExportDetailListRes;
import com.std.core.pojo.response.ContractTokenExportDetailPageRes;
import com.std.core.service.IContractTokenExportDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代币导出明细Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-21 22:20
 */
@ApiVersion(1)
@RestController
@Api(value = "代币导出明细管理", tags = "代币导出明细管理")
@RequestMapping("{version}/contract_token_export_detail")
public class ContractTokenExportDetailController extends BaseController {

    @Resource
    private IContractTokenExportDetailService contractTokenExportDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增代币导出明细', NULL, '/core/v1/contract_token_export_detail/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增代币导出明细")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailCreateReq request) {
        User operator = getUserByToken(token);
        contractTokenExportDetailService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除代币导出明细', NULL, '/core/v1/contract_token_export_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除代币导出明细")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        contractTokenExportDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改代币导出明细', NULL, '/core/v1/contract_token_export_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改代币导出明细")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailModifyReq request) {
        User operator = getUserByToken(token);
        contractTokenExportDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询代币导出明细")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ContractTokenExportDetail> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询代币导出明细")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ContractTokenExportDetail>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenExportDetail.class));

        return PageUtil.pageResult(contractTokenExportDetailService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询代币导出明细")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ContractTokenExportDetail>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询代币导出明细")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ContractTokenExportDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询代币导出明细")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ContractTokenExportDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), ContractTokenExportDetail.class));

        return PageUtil.pageResult(contractTokenExportDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询代币导出明细', NULL, '/core/v1/contract_token_export_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询代币导出明细")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ContractTokenExportDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ContractTokenExportDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportDetailService.listFront(request));
    }

}