package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionDetailTransferDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.*;
import com.std.core.pojo.response.CollectionDetailTransferDetailDetailRes;
import com.std.core.pojo.response.CollectionDetailTransferDetailListRes;
import com.std.core.pojo.response.CollectionDetailTransferDetailOssPageRes;
import com.std.core.pojo.response.CollectionDetailTransferDetailPageRes;
import com.std.core.service.ICollectionDetailTransferDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 转赠订单藏品Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-02-23 20:21
 */
@ApiVersion(1)
@RestController
@Api(value = "转赠订单藏品管理", tags = "转赠订单藏品管理")
@RequestMapping("{version}/collection_detail_transfer_detail")
public class CollectionDetailTransferDetailController extends BaseController {

    @Resource
    private ICollectionDetailTransferDetailService collectionDetailTransferDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询转赠订单藏品")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailTransferDetail> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询转赠订单藏品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailTransferDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferDetailPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailTransferDetail.class));

        return PageUtil.pageResult(collectionDetailTransferDetailService.page(request));
    }

    @ApiOperation(value = "分页条件查询转赠订单藏品")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/oss_page")
    public Result<PageInfo<CollectionDetailTransferDetailOssPageRes>> ossPage(@RequestHeader(value = "Authorization") String token,
                                                                              @RequestBody @Valid CollectionDetailTransferDetailOssPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize());

        return PageUtil.pageResult(collectionDetailTransferDetailService.ossPage(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询转赠订单藏品")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionDetailTransferDetail>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询转赠订单藏品")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionDetailTransferDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailTransferDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询转赠订单藏品")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionDetailTransferDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionDetailTransferDetail.class));

        return PageUtil.pageResult(collectionDetailTransferDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询转赠订单藏品', NULL, '/core/v1/collection_detail_transfer_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询转赠订单藏品")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionDetailTransferDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionDetailTransferDetailListFrontReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        return new Result<>(collectionDetailTransferDetailService.listFront(request));
    }

}