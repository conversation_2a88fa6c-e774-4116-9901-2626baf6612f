package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.LotteryActivityJoinOrder;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LotteryActivityJoinOrderAddAddressReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderListFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderListReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderPageFrontReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderPageReq;
import com.std.core.pojo.request.LotteryActivityJoinOrderSendLogisticsReq;
import com.std.core.pojo.response.LotteryActivityJoinOrderDetailRes;
import com.std.core.pojo.response.LotteryActivityJoinOrderListRes;
import com.std.core.pojo.response.LotteryActivityJoinOrderPageRes;
import com.std.core.service.ILotteryActivityJoinOrderService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 抽奖活动用户参与订单Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-08 11:46
 */
@ApiVersion(1)
@RestController
@Api(value = "抽奖活动用户参与订单管理", tags = "抽奖活动用户参与订单管理")
@RequestMapping("{version}/lottery_activity_join_order")
public class LotteryActivityJoinOrderController extends BaseController {

    @Resource
    private ILotteryActivityJoinOrderService lotteryActivityJoinOrderService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "添加发货地址")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/add_address")
    public Result addAddress(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderAddAddressReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "lottery_activity_join_order_add_address:" + request.getId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            }
            lotteryActivityJoinOrderService.addAddress(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    @ApiOperation(value = "发货")
    @ApiOperationSupport(order = 20)
    @PostMapping(value = "/send_logistics")
    public Result sendLogistics(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderSendLogisticsReq request) {

        User operator = getUserByToken(token, EUserKind.SYS);

        String lockId = "lottery_activity_join_order_send:" + request.getId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "已锁定，请稍后再试");
            }
            lotteryActivityJoinOrderService.sendLogistics(request, operator);
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LotteryActivityJoinOrder> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinOrderService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LotteryActivityJoinOrder>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), LotteryActivityJoinOrder.class));

        return PageUtil.pageResult(lotteryActivityJoinOrderService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LotteryActivityJoinOrder>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinOrderService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<LotteryActivityJoinOrderDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinOrderService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<LotteryActivityJoinOrderPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), LotteryActivityJoinOrder.class));

        return PageUtil.pageResult(lotteryActivityJoinOrderService.pageFront(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询抽奖活动用户参与订单', NULL, '/core/v1/lottery_activity_join_order/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询抽奖活动用户参与订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<LotteryActivityJoinOrderListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid LotteryActivityJoinOrderListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(lotteryActivityJoinOrderService.listFront(request));
    }
}