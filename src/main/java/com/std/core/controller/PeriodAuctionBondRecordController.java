package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.PeriodAuctionBondRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.PeriodAuctionBondRecordCreateReq;
import com.std.core.pojo.request.PeriodAuctionBondRecordListReq;
import com.std.core.pojo.request.PeriodAuctionBondRecordListFrontReq;
import com.std.core.pojo.request.PeriodAuctionBondRecordModifyReq;
import com.std.core.pojo.request.PeriodAuctionBondRecordPageReq;
import com.std.core.pojo.request.PeriodAuctionBondRecordPageFrontReq;
import com.std.core.pojo.response.PeriodAuctionBondRecordDetailRes;
import com.std.core.pojo.response.PeriodAuctionBondRecordListRes;
import com.std.core.pojo.response.PeriodAuctionBondRecordPageRes;
import com.std.core.service.IPeriodAuctionBondRecordService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 期数竞拍保证金记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-03-29 19:23
 */
@ApiVersion(1)
@RestController
@Api(value = "期数竞拍保证金记录管理", tags = "期数竞拍保证金记录管理")
@RequestMapping("{version}/period_auction_bond_record")
public class PeriodAuctionBondRecordController extends BaseController {

    @Resource
    private IPeriodAuctionBondRecordService periodAuctionBondRecordService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "缴纳保证金")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordCreateReq request) {

        User operator = getUserByToken(token, EUserKind.C);

        String lockId = "period_auction_bond_record_create:" + request.getPeriodId() + ":" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        try {
            if (!redisLock.lock(lockId, String.valueOf(time))) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "保证金缴纳中，请稍等");
            }
            return new Result<>(periodAuctionBondRecordService.create(request, operator));
        } finally {
            redisLock.unlock(lockId, String.valueOf(time));
        }
    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除期数竞拍保证金记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        periodAuctionBondRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改期数竞拍保证金记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        periodAuctionBondRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<PeriodAuctionBondRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionBondRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<PeriodAuctionBondRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodAuctionBondRecord.class));

        return PageUtil.pageResult(periodAuctionBondRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<PeriodAuctionBondRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionBondRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<PeriodAuctionBondRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionBondRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<PeriodAuctionBondRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), PeriodAuctionBondRecord.class));

        return PageUtil.pageResult(periodAuctionBondRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询期数竞拍保证金记录', NULL, '/core/v1/period_auction_bond_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询期数竞拍保证金记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<PeriodAuctionBondRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid PeriodAuctionBondRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(periodAuctionBondRecordService.listFront(request));
    }

}