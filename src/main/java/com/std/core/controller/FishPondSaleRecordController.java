package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.FishPondSaleRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.FishPondSaleRecordCreateReq;
import com.std.core.pojo.request.FishPondSaleRecordListReq;
import com.std.core.pojo.request.FishPondSaleRecordListFrontReq;
import com.std.core.pojo.request.FishPondSaleRecordModifyReq;
import com.std.core.pojo.request.FishPondSaleRecordPageReq;
import com.std.core.pojo.request.FishPondSaleRecordPageFrontReq;
import com.std.core.pojo.response.FishPondSaleRecordCreateRes;
import com.std.core.pojo.response.FishPondSaleRecordDetailRes;
import com.std.core.pojo.response.FishPondSaleRecordListRes;
import com.std.core.pojo.response.FishPondSaleRecordPageRes;
import com.std.core.service.IFishPondSaleRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;

import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 渔光鱼货出售记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2023-03-06 14:56
 */
@ApiVersion(1)
@RestController
@Api(value = "渔光鱼货出售记录管理", tags = "渔光鱼货出售记录管理")
@RequestMapping("{version}/fish_pond_sale_record")
public class FishPondSaleRecordController extends BaseController {

    @Resource
    private IFishPondSaleRecordService fishPondSaleRecordService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "出售鱼货")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<FishPondSaleRecordCreateRes> create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);


        return new Result<>(fishPondSaleRecordService.create(request, operator));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除渔光鱼货出售记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        fishPondSaleRecordService.remove(id);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/modify', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "修改渔光鱼货出售记录")
//    @ApiOperationSupport(order = 30)
//    @PostMapping(value = "/modify")
//    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordModifyReq request) {
//        User operator = getUserByToken(token);
//        fishPondSaleRecordService.modify(request, operator);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<FishPondSaleRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token,EUserKind.SYS);

        return new Result<>(fishPondSaleRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<FishPondSaleRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordPageReq request) {
        User operator = getUserByToken(token,EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), FishPondSaleRecord.class));

        return PageUtil.pageResult(fishPondSaleRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<FishPondSaleRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishPondSaleRecordService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<FishPondSaleRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(fishPondSaleRecordService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<FishPondSaleRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), FishPondSaleRecord.class));

        return PageUtil.pageResult(fishPondSaleRecordService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询渔光鱼货出售记录', NULL, '/core/v1/fish_pond_sale_record/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询渔光鱼货出售记录")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<FishPondSaleRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid FishPondSaleRecordListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(fishPondSaleRecordService.listFront(request));
    }

}