package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.CollectionRightCompany;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionRightCompanyCreatePeriodListReq;
import com.std.core.pojo.request.CollectionRightCompanyCreatePeriodPageReq;
import com.std.core.pojo.request.CollectionRightCompanyCreateReq;
import com.std.core.pojo.request.CollectionRightCompanyListFrontReq;
import com.std.core.pojo.request.CollectionRightCompanyListReq;
import com.std.core.pojo.request.CollectionRightCompanyModifyReq;
import com.std.core.pojo.request.CollectionRightCompanyPageFrontReq;
import com.std.core.pojo.request.CollectionRightCompanyPageReq;
import com.std.core.pojo.response.CollectionRightCompanyDetailRes;
import com.std.core.pojo.response.CollectionRightCompanyListRes;
import com.std.core.pojo.response.CollectionRightCompanyPageRes;
import com.std.core.service.ICollectionRightCompanyService;
import com.std.core.util.SysConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 权益所属发行方Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-25 10:41
 */
@ApiVersion(1)
@RestController
@Api(value = "权益所属发行方管理", tags = "权益所属发行方管理")
@RequestMapping("{version}/collection_right_company")
public class CollectionRightCompanyController extends BaseController {

    @Resource
    private ICollectionRightCompanyService collectionRightCompanyService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增权益所属发行方', NULL, '/core/v1/collection_right_company/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增权益所属发行方")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyCreateReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionRightCompanyService.create(request, operator);

        return new Result();
    }

    //
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除权益所属发行方', NULL, '/core/v1/collection_right_company/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除权益所属发行方")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        collectionRightCompanyService.remove(id);
//
//        return new Result();
//    }
//
    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改权益所属发行方', NULL, '/core/v1/collection_right_company/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "开启/关闭权益")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyModifyReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        collectionRightCompanyService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询权益所属发行方', NULL, '/core/v1/collection_right_company/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询权益所属发行方")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionRightCompany> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightCompanyService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询权益所属发行方', NULL, '/core/v1/collection_right_company/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询权益所属发行方")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionRightCompany>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionRightCompany.class));

        return PageUtil.pageResult(collectionRightCompanyService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询权益所属发行方', NULL, '/core/v1/collection_right_company/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询权益所属发行方")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionRightCompany>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightCompanyService.list(request));
    }

    @ApiOperation(value = "新增期数查询权益")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/create_period_list")
    public Result<List<CollectionRightCompany>> createPeriodList(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyCreatePeriodListReq request) {
        User operator = getUserByToken(token);
        Long companyId = null;
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {
            companyId = SysConstants.COMPANY_DEFAULT_ID;
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权访问");
        }

        return new Result<>(collectionRightCompanyService.list(request, companyId));
    }

    @ApiOperation(value = "新增期数查询权益")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/create_period_page")
    public Result<PageInfo<CollectionRightCompany>> createPeriodPage(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyCreatePeriodPageReq request) {
        User operator = getUserByToken(token);
        Long companyId = null;
        if (EUserKind.SYS.getCode().equals(operator.getKind())) {
            companyId = null;
        } else if (EUserKind.BP.getCode().equals(operator.getKind())) {
            companyId = operator.getCompanyId();
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "无权访问");
        }
        PageHelper.startPage(request.getPageNum(), request.getPageSize());
        return PageUtil.pageResult(collectionRightCompanyService.page(request, companyId));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询权益所属发行方', NULL, '/core/v1/collection_right_company/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询权益所属发行方")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionRightCompanyDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightCompanyService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询权益所属发行方', NULL, '/core/v1/collection_right_company/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询权益所属发行方")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionRightCompanyPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), CollectionRightCompany.class));

        return PageUtil.pageResult(collectionRightCompanyService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询权益所属发行方', NULL, '/core/v1/collection_right_company/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询权益所属发行方")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionRightCompanyListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid CollectionRightCompanyListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionRightCompanyService.listFront(request));
    }

}