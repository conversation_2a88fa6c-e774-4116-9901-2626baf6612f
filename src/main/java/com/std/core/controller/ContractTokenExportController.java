package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ContractTokenExport;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ContractTokenBackReq;
import com.std.core.pojo.request.ContractTokenExportApproveReq;
import com.std.core.pojo.request.ContractTokenExportBroadcastFailBackReq;
import com.std.core.pojo.request.ContractTokenExportBroadcastReq;
import com.std.core.pojo.request.ContractTokenExportDetailFrontReq;
import com.std.core.pojo.request.ContractTokenExportPageFrontReq;
import com.std.core.pojo.request.ContractTokenExportPageReq;
import com.std.core.pojo.response.ContractTokenExportDetailRes;
import com.std.core.pojo.response.ContractTokenExportPageRes;
import com.std.core.service.IContractTokenExportService;
import com.std.core.util.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代币导出记录Controller
 *
 * <AUTHOR> xieyj
 * @since : 2021-08-24 13:59
 */
@ApiVersion(1)
@RestController
@Api(value = "代币导出记录管理", tags = "代币导出记录管理")
@RequestMapping("{version}/contract_token_export")
public class ContractTokenExportController extends BaseController {

    @Resource
    private IContractTokenExportService contractTokenExportService;

    @Resource
    private RedisLock redisLock;

    @Value("${meta.lock.timeout}")
    private Long metaLockTimeout;

    @ApiOperation(value = "front:新增代币导出记录")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result<OrderPayRes> create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportCreateReq request) {
        User operator = getUserByToken(token, EUserKind.C);

        String id = "contractToken" + operator.getId();
        Long time = System.currentTimeMillis() + metaLockTimeout;
        if (!redisLock.lock(id, String.valueOf(time))) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "操作频繁，请稍后再试");
        }

        try {
            ContractTokenExportBatchCreateReq req = new ContractTokenExportBatchCreateReq();
            EntityUtils.copyData(request, req);
            List<Long> idList = new ArrayList<>();
            idList.add(request.getCollectionDetailId());
            req.setCollectionDetailIdList(idList);
            return new Result<>(contractTokenExportService.createBatch(req, operator));
        } finally {
            redisLock.unlock(id, String.valueOf(time));
        }
    }

//    @ApiOperation(value = "front:批量新增代币导出记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/batch_export")
//    public Result<OrderPayRes> batchExport(@RequestHeader(value = "Authorization") String token,
//            @RequestBody @Valid ContractTokenExportBatchCreateReq request) {
//        User operator = getUserByToken(token, EUserKind.C);
//        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
//                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus())) {
//            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
//        }
//        String id = "contractToken" + operator.getId();
//        Long time = System.currentTimeMillis() + metaLockTimeout;
//        if (!redisLock.lock(id, String.valueOf(time))) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "操作频繁，请稍后再试");
//        }
//
//        try {
//            return new Result<>(contractTokenExportService.createBatch(request, operator));
//        } finally {
//            redisLock.unlock(id, String.valueOf(time));
//        }
//    }

//    @ApiOperation(value = "front:批量新增代币导出记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/batch_export_h5")
//    public Result<OrderPayRes> batchExportH5(@RequestHeader(value = "Authorization") String token,
//                                           @RequestBody @Valid ContractTokenExportBatchCreateReq request) {
//        User operator = getUserByToken(token, EUserKind.C);
//        if (!EUserIdentifyStatus.IDENTIFY_1.getCode().equals(operator.getIdentifyStatus()) &&
//                !EUserIdentifyStatus.IDENTIFY_4.getCode().equals(operator.getIdentifyStatus()) &&
//                !EUserIdentifyStatus.IDENTIFY_6.getCode().equals(operator.getIdentifyStatus())) {
//            throw new com.std.common.exception.BizException(com.std.common.enums.ECommonErrorCode.BIZ_DEFAULT.getCode(), "请先完成实名认证");
//        }
//        String id = "contractToken" + operator.getId();
//        Long time = System.currentTimeMillis() + metaLockTimeout;
//        if (!redisLock.lock(id, String.valueOf(time))) {
//            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "操作频繁，请稍后再试");
//        }
//
//        try {
//            return new Result<>(contractTokenExportService.createBatch(request, operator));
//        } finally {
//            redisLock.unlock(id, String.valueOf(time));
//        }
//    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "oss:代币导出审核", position = 12)
    @PostMapping(value = "/token_export_approve")
    public Result tokenExportApprove(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportApproveReq request) {
        User operator = getUserByToken(token);
        contractTokenExportService.approve(request, operator);

        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "oss:代币导出审核通过打回", position = 12)
    @PostMapping(value = "/token_back")
    public Result tokenBack(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenBackReq request) {
        User operator = getUserByToken(token);
        contractTokenExportService.tokenBack(request, operator);

        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "oss:代币导出广播", position = 13)
    @PostMapping(value = "/token_export_broadcast")
    public Result tokenExportBroadcast(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportBroadcastReq request) {
        User operator = getUserByToken(token);
        contractTokenExportService.broadcast(request, operator);
        return new Result();
    }

    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "oss:代币导出广播失败打回", position = 14)
    @PostMapping(value = "/token_export_broadcast_fail_back")
    public Result tokenExportBroadcastFailBack(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportBroadcastFailBackReq request) {
        User operator = getUserByToken(token);
        contractTokenExportService.broadcastFailBack(request, operator);
        return new Result();
    }

    @ApiOperation(value = "查询代币导出记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ContractTokenExport> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(contractTokenExportService.detail(id));
    }

    @ApiOperation(value = "分页条件查询代币导出记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ContractTokenExport>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ContractTokenExport.class));
        return PageUtil.pageResult(contractTokenExportService.page(request));
    }

    @ApiOperation(value = "前端详情查询代币导出记录")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ContractTokenExportPageRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportService.detailFront(id));
    }

    @ApiOperation(value = "前端详情查询代币导出藏品明细列表")
    @ApiOperationSupport(order = 71)
    @PostMapping("/detail_collection_list")
    public Result<List<ContractTokenExportDetailRes>> detailCollectionList(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportDetailFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(contractTokenExportService.detailCollectionListFront(request.getId()));
    }

    @ApiOperation(value = "前端分页条件查询代币导出记录")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ContractTokenExportPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ContractTokenExportPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ContractTokenExport.class));
        return PageUtil.pageResult(contractTokenExportService.pageFront(request, operator));
    }

}