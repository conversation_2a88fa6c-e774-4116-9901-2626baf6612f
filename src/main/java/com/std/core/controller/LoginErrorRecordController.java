package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.LoginErrorRecord;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.LoginErrorRecordCreateReq;
import com.std.core.pojo.request.LoginErrorRecordListReq;
import com.std.core.pojo.request.LoginErrorRecordListFrontReq;
import com.std.core.pojo.request.LoginErrorRecordModifyReq;
import com.std.core.pojo.request.LoginErrorRecordPageReq;
import com.std.core.pojo.request.LoginErrorRecordPageFrontReq;
import com.std.core.pojo.response.LoginErrorRecordDetailRes;
import com.std.core.pojo.response.LoginErrorRecordListRes;
import com.std.core.pojo.response.LoginErrorRecordPageRes;
import com.std.core.service.ILoginErrorRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 登录密码输入错误记录Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-07-14 10:17
 */
@ApiVersion(1)
@RestController
@Api(value = "登录密码输入错误记录管理", tags = "登录密码输入错误记录管理")
@RequestMapping("{version}/login_error_record")
public class LoginErrorRecordController extends BaseController {

    @Resource
    private ILoginErrorRecordService loginErrorRecordService;

//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增登录密码输入错误记录', NULL, '/core/v1/login_error_record/create', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "新增登录密码输入错误记录")
//    @ApiOperationSupport(order = 10)
//    @PostMapping(value = "/create")
//    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordCreateReq request) {
//        User operator = getUserByToken(token);
//        loginErrorRecordService.create(request, operator);
//
//        return new Result();
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除登录密码输入错误记录', NULL, '/core/v1/login_error_record/remove/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "删除登录密码输入错误记录")
//    @ApiOperationSupport(order = 20)
//    @PostMapping("/remove/{id}")
//    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//        loginErrorRecordService.remove(id);
//
//        return new Result();
//    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改登录密码输入错误记录', NULL, '/core/v1/login_error_record/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改登录密码输入错误记录")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordModifyReq request) {
        User operator = getUserByToken(token);
        loginErrorRecordService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询登录密码输入错误记录")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<LoginErrorRecord> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(loginErrorRecordService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询登录密码输入错误记录")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<LoginErrorRecord>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), LoginErrorRecord.class));

        return PageUtil.pageResult(loginErrorRecordService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询登录密码输入错误记录")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<LoginErrorRecord>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(loginErrorRecordService.list(request));
    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/detail_front/{id}', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端详情查询登录密码输入错误记录")
//    @ApiOperationSupport(order = 70)
//    @PostMapping("/detail_front/{id}")
//    public Result<LoginErrorRecordDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(loginErrorRecordService.detailFront(id));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/page_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端分页条件查询登录密码输入错误记录")
//    @ApiOperationSupport(order = 80)
//    @PostMapping(value = "/page_front")
//    public Result<PageInfo<LoginErrorRecordPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordPageFrontReq request) {
//        User operator = getUserByToken(token);
//        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
//        SqlUtil.parseSort(request.getSort(), LoginErrorRecord.class));
//
//        return PageUtil.pageResult(loginErrorRecordService.pageFront(request));
//    }
//
//    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询登录密码输入错误记录', NULL, '/core/v1/login_error_record/list_front', NULL, NULL, 'pre', NULL);
//    @ApiOperation(value = "前端列表条件查询登录密码输入错误记录")
//    @ApiOperationSupport(order = 90)
//    @PostMapping(value = "/list_front")
//    public Result<List<LoginErrorRecordListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid LoginErrorRecordListFrontReq request) {
//        User operator = getUserByToken(token);
//
//        return new Result<>(loginErrorRecordService.listFront(request));
//    }

}