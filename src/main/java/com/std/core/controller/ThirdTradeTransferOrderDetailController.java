package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageInfo;
import com.std.common.page.PageUtil;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.ThirdTradeTransferOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailCreateReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailListFrontReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailListReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailModifyReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailPageFrontReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailPageRelationOrderReq;
import com.std.core.pojo.request.ThirdTradeTransferOrderDetailPageReq;
import com.std.core.pojo.response.ThirdTradeTransferOrderDetailDetailRes;
import com.std.core.pojo.response.ThirdTradeTransferOrderDetailListRes;
import com.std.core.pojo.response.ThirdTradeTransferOrderDetailPageRes;
import com.std.core.service.IThirdTradeTransferOrderDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 三方系统交易划转订单Controller
 *
 * <AUTHOR> xieyj
 * @since : 2022-11-18 17:34
 */
@ApiVersion(1)
@RestController
@Api(value = "三方系统交易划转订单管理", tags = "三方系统交易划转订单管理")
@RequestMapping("{version}/third_trade_transfer_order_detail")
public class ThirdTradeTransferOrderDetailController extends BaseController {

    @Resource
    private IThirdTradeTransferOrderDetailService thirdTradeTransferOrderDetailService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增三方系统交易划转订单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailCreateReq request) {
        User operator = getUserByToken(token);
        thirdTradeTransferOrderDetailService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除三方系统交易划转订单")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        thirdTradeTransferOrderDetailService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改三方系统交易划转订单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailModifyReq request) {
        User operator = getUserByToken(token);
        thirdTradeTransferOrderDetailService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询三方系统交易划转订单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<ThirdTradeTransferOrderDetail> detail(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderDetailService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<ThirdTradeTransferOrderDetail>> page(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailPageReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdTradeTransferOrderDetail.class));
        return PageUtil.pageResult(thirdTradeTransferOrderDetailService.page(request));
    }

    @ApiOperation(value = "分页条件查询三方系统交易划转订单明细(关联订单)")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page_relation_order")
    public Result<PageInfo<ThirdTradeTransferOrderDetail>> pageRelationOrder(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailPageRelationOrderReq request) {
        User operator = getUserByToken(token, EUserKind.SYS);

        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdTradeTransferOrderDetail.class));
        return PageUtil.pageResult(thirdTradeTransferOrderDetailService.pageRelationOrder(request));
    }

    @ApiOperation(value = "详情查询三方系统交易划转订单明细(关联订单)")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/detail_relation_order/{id}")
    public Result<ThirdTradeTransferOrderDetail> detailRelationOrder(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token, EUserKind.SYS);

        return new Result<>(thirdTradeTransferOrderDetailService.detailRelationOrder(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<ThirdTradeTransferOrderDetail>> list(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderDetailService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询三方系统交易划转订单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<ThirdTradeTransferOrderDetailDetailRes> detailFront(@RequestHeader(value = "Authorization") String token,
            @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderDetailService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<ThirdTradeTransferOrderDetailPageRes>> pageFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
                SqlUtil.parseSort(request.getSort(), ThirdTradeTransferOrderDetail.class));

        return PageUtil.pageResult(thirdTradeTransferOrderDetailService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询三方系统交易划转订单', NULL, '/core/v1/third_trade_transfer_order_detail/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询三方系统交易划转订单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<ThirdTradeTransferOrderDetailListRes>> listFront(@RequestHeader(value = "Authorization") String token,
            @RequestBody @Valid ThirdTradeTransferOrderDetailListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(thirdTradeTransferOrderDetailService.listFront(request));
    }

}