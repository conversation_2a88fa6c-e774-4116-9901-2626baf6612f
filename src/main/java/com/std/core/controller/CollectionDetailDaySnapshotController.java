package com.std.core.controller;

import com.github.pagehelper.PageHelper;
import com.std.common.base.Result;
import com.std.common.page.PageUtil;
import com.std.common.page.PageInfo;
import com.std.common.utils.SqlUtil;
import com.std.common.version.ApiVersion;
import com.std.core.controller.base.BaseController;
import com.std.core.pojo.domain.CollectionDetailDaySnapshot;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.request.CollectionDetailDaySnapshotCreateReq;
import com.std.core.pojo.request.CollectionDetailDaySnapshotListReq;
import com.std.core.pojo.request.CollectionDetailDaySnapshotListFrontReq;
import com.std.core.pojo.request.CollectionDetailDaySnapshotModifyReq;
import com.std.core.pojo.request.CollectionDetailDaySnapshotPageReq;
import com.std.core.pojo.request.CollectionDetailDaySnapshotPageFrontReq;
import com.std.core.pojo.response.CollectionDetailDaySnapshotDetailRes;
import com.std.core.pojo.response.CollectionDetailDaySnapshotListRes;
import com.std.core.pojo.response.CollectionDetailDaySnapshotPageRes;
import com.std.core.service.ICollectionDetailDaySnapshotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiOperationSupport;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 当天有的用户藏品清单Controller
 *
 * <AUTHOR> ycj
 * @since : 2022-04-16 15:56
 */
@ApiVersion(1)
@RestController
@Api(value = "当天有的用户藏品清单管理", tags = "当天有的用户藏品清单管理")
@RequestMapping("{version}/collection_detail_day_snapshot")
public class CollectionDetailDaySnapshotController extends BaseController {

    @Resource
    private ICollectionDetailDaySnapshotService collectionDetailDaySnapshotService;

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '新增当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/create', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "新增当天有的用户藏品清单")
    @ApiOperationSupport(order = 10)
    @PostMapping(value = "/create")
    public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotCreateReq request) {
        User operator = getUserByToken(token);
        collectionDetailDaySnapshotService.create(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '删除当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/remove/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "删除当天有的用户藏品清单")
    @ApiOperationSupport(order = 20)
    @PostMapping("/remove/{id}")
    public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);
        collectionDetailDaySnapshotService.remove(id);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('operate', '修改当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/modify', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "修改当天有的用户藏品清单")
    @ApiOperationSupport(order = 30)
    @PostMapping(value = "/modify")
    public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotModifyReq request) {
        User operator = getUserByToken(token);
        collectionDetailDaySnapshotService.modify(request, operator);

        return new Result();
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/detail/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 40)
    @PostMapping("/detail/{id}")
    public Result<CollectionDetailDaySnapshot> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDaySnapshotService.detail(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '分页条件查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/page', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "分页条件查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 50)
    @PostMapping(value = "/page")
    public Result<PageInfo<CollectionDetailDaySnapshot>> page(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotPageReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionDetailDaySnapshot.class));

        return PageUtil.pageResult(collectionDetailDaySnapshotService.page(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '列表条件查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/list', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "列表条件查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 60)
    @PostMapping(value = "/list")
    public Result<List<CollectionDetailDaySnapshot>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotListReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDaySnapshotService.list(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端详情查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/detail_front/{id}', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端详情查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 70)
    @PostMapping("/detail_front/{id}")
    public Result<CollectionDetailDaySnapshotDetailRes> detailFront(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid Long id) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDaySnapshotService.detailFront(id));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端分页条件查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/page_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端分页条件查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 80)
    @PostMapping(value = "/page_front")
    public Result<PageInfo<CollectionDetailDaySnapshotPageRes>> pageFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotPageFrontReq request) {
        User operator = getUserByToken(token);
        PageHelper.startPage(request.getPageNum(), request.getPageSize(),
        SqlUtil.parseSort(request.getSort(), CollectionDetailDaySnapshot.class));

        return PageUtil.pageResult(collectionDetailDaySnapshotService.pageFront(request));
    }

    // INSERT INTO  `tsys_action` (`type`, `name`, `code`, `url`, `input`, `output`, `status`, `remark`) VALUES ('query', '前端列表条件查询当天有的用户藏品清单', NULL, '/core/v1/collection_detail_day_snapshot/list_front', NULL, NULL, 'pre', NULL);
    @ApiOperation(value = "前端列表条件查询当天有的用户藏品清单")
    @ApiOperationSupport(order = 90)
    @PostMapping(value = "/list_front")
    public Result<List<CollectionDetailDaySnapshotListRes>> listFront(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid CollectionDetailDaySnapshotListFrontReq request) {
        User operator = getUserByToken(token);

        return new Result<>(collectionDetailDaySnapshotService.listFront(request));
    }

}