package com.std.core.pojo.response;

import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR> ycj
 * @since : 2022-01-02 20:46
 */
@Data
public class ExchangeCodeDetailRes {

    /**
     * 编号
     */
    @ApiModelProperty(name = "id", value = "编号", position = 10)
    private Long id;

    /**
     * 兑换码
     */
    @ApiModelProperty(name = "exchangeNo", value = "兑换码", position = 20)
    private Integer exchangeNo;

    /**
     * 作品id
     */
    @ApiModelProperty(name = "collectionId", value = "作品id", position = 30)
    private Long collectionId;

    /**
     * 数量
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 40)
    private Integer quantity;

    /**
     * 状态(0=待使用，1=已使用，2=已作废)
     */
    @ApiModelProperty(name = "status", value = "状态(0=待使用，1=已使用，2=已作废)", position = 50)
    private String status;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creator", value = "创建人", position = 60)
    private Long creator;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "creatorName", value = "创建人名称", position = 70)
    private String creatorName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createTime", value = "创建时间", position = 80)
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 90)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 100)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateTime", value = "更新时间", position = 110)
    private Date updateTime;

    /**
     * 兑换用户id
     */
    @ApiModelProperty(name = "exchangeUserId", value = "兑换用户id", position = 120)
    private Long exchangeUserId;

    /**
     * 兑换时间
     */
    @ApiModelProperty(name = "exchangeTime", value = "兑换时间", position = 130)
    private Date exchangeTime;

}
