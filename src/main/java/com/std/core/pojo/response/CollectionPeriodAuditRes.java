package com.std.core.pojo.response;

import com.std.common.base.BaseDo;
import com.std.core.enums.ECollectionPeriodStatus;
import com.std.core.pojo.domain.*;
import com.std.core.pojo.response.CollectionPeriodCollectionPageRes;
import com.std.core.pojo.response.CollectionPeriodDropRes;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 作品期数
 *
 * <AUTHOR> ycj
 * @since : 2021-11-03 20:27
 */
@Data
public class CollectionPeriodAuditRes extends BaseDo {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "id序号", position = 10)
    private Long id;

    /**
     * 分类(0=版权区 1=衍生区)
     */
    @ApiModelProperty(name = "category", value = "分类(0=版权区 1=衍生区)", position = 21)
    private String category;

    /**
     * 板块类别
     */
    @ApiModelProperty(name = "plateCategory", value = "板块类别", position = 21)
    private String plateCategory;

    /**
     * 名称(宝藏计划展示)
     */
    @ApiModelProperty(name = "name", value = "名称，(0=版权区自取 1=衍生区取藏品名称)", position = 21)
    private String name;

    /**
     * 期数缩列图
     */
    @ApiModelProperty(name = "planPic", value = "期数缩列图", position = 22)
    private String planPic;

    /**
     * 期数标语(宝藏计划副标题展示)
     */
    @ApiModelProperty(name = "introduce", value = "期数标语", position = 22)
    private String introduce;


    /**
     * 封面图(宝藏计划副标题展示)
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面图", position = 80)
    private String coverFileUrl;

    /**
     * 文件类型 0:图片,1:音频,2:视频
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 60)
    private String fileType;

    /**
     * 标签
     */
    @ApiModelProperty(name = "tags", value = "标签", position = 93)
    private String tags;

    /**
     * 作品介绍
     */
    @ApiModelProperty(name = "content", value = "作品介绍", position = 70)
    private String content;

//    /**
//     * 创作人id
//     */
//    @ApiModelProperty(name = "authorId", value = "创作人id", position = 20)
//    private Long authorId;

    /**
     * 创作人id集合
     */
    @ApiModelProperty(name = "authorIds", value = "创作人id集合", position = 20)
    private Long authorIds;

    /**
     * 开始发售时间
     */
    @ApiModelProperty(name = "startSellDate", value = "开始发售时间", position = 30)
    private Date startSellDate;

    /**
     * 公布中签时间
     */
    @ApiModelProperty(name = "endSellDate", value = "公布中签时间", position = 110)
    private Date endSellDate;

    /**
     * 开始发售时间起
     */
    @ApiModelProperty(name = "startSellDateStart", value = "开始发售时间起", position = 35)
    private Date startSellDateStart;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数量", position = 40)
    private Integer totalQuantity;

    /**
     * 剩余数量
     */
    @ApiModelProperty(name = "remainQuantity", value = "剩余数量", position = 50)
    private Integer remainQuantity;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 60)
    private BigDecimal price;

    /**
     * 单人最大购买份数
     */
    @ApiModelProperty(name = "buyMax", value = "单人最大购买份数", position = 50)
    private Integer buyMax;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 60)
    private Integer orderNo;

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "status", value = "状态 collection_period_status ", position = 70)
    private String status;

    /**
     * 售卖状态 {0:正常,1:已售罄}
     */
    @ApiModelProperty(name = "soldStatus", value = "售卖状态collection_period_sold_status", position = 70)
    private String soldStatus;

    /**
     * 开始状态 {0:待开始,1:售卖中,2:已结束}
     */
    @ApiModelProperty(name = "startStatus", value = "开始状态collection_period_start_status", position = 70)
    private String startStatus;

    /**
     * 锁仓时间(小时)
     */
    @ApiModelProperty(name = "lockTime", value = "锁仓时间(小时)", position = 70)
    private Integer lockTime;

    /**
     * 权益内容
     */
    @ApiModelProperty(name = "rightContent", value = "权益内容", position = 93)
    private String rightContent;

    /**
     * 权益类型0=文字 1=图片 2=暂无权益
     */
    @ApiModelProperty(name = "rightType", value = "权益类型0=文字 1=图片 2=暂无权益", position = 93)
    private String rightType;

    /**
     * 更新人
     */
    @ApiModelProperty(name = "updater", value = "更新人", position = 80)
    private Long updater;

    /**
     * 更新人名称
     */
    @ApiModelProperty(name = "updaterName", value = "更新人名称", position = 90)
    private String updaterName;

    /**
     * 更新时间
     */
    @ApiModelProperty(name = "updateDatetime", value = "更新时间", position = 100)
    private Date updateDatetime;

    /**
     * 优先购数量叠加标志
     */
    @ApiModelProperty(name = "priorityAddQuantityFlag", value = "优先购数量叠加标志", position = 110)
    private String priorityAddQuantityFlag;

    /**
     * 优先购买数量
     */
    @ApiModelProperty(name = "priorityNumber", value = "优先购买数量", position = 120)
    private Integer priorityNumber;

    /**
     * 优先购抽签参数
     */
    @ApiModelProperty(name = "priorityDrawParam", value = "优先购抽签参数", position = 130)
    private Integer priorityDrawParam;

    /**
     * 抽签参数
     */
    @ApiModelProperty(name = "drawParam", value = "抽签参数", position = 140)
    private Integer drawParam;

    /**
     * 玩法介绍
     */
    @ApiModelProperty(name = "drawNote", value = "玩法介绍", position = 150)
    private String drawNote;

    @ApiModelProperty(name = "transformLimitTime", value = "转赠限制(小时)")
    private Integer transformLimitTime;

    @ApiModelProperty(name = "wordFlag", value = "口令标志(0=不是口令 1=是口令)")
    private String wordFlag;

    @ApiModelProperty(name = "createType", value = "创建类型 0:平台代创,1:发行方创建")
    private String createType;

    /**** Properties ****/

    /**
     * 状态 {0:待上架,1:已上架,2:已下架}
     */
    @ApiModelProperty(name = "statusName", value = "状态 {0:待上架,1:已上架,2:已下架}")
    private String statusName;

    public String getStatusName() {
        if (StringUtils.isNotBlank(status)) {
            statusName = ECollectionPeriodStatus.getCollectionPeriodStatus(status).getValue();
        }

        return statusName;
    }

    /**
     * 作品介绍类型
     */
    @ApiModelProperty(name = "contentType", value = "作品介绍类型0=文字 1=图片,期数里就是图片", position = 70)
    private String contentType = "1";


    /**
     * 售卖藏品列表
     */
    @ApiModelProperty(name = "sellCollectionList", value = "售卖藏品列表", position = 95)
    private List<CollectionPeriodAuditCollectionPageRes> sellCollectionList;

    /**
     * 仅展示藏品列表
     */
    @ApiModelProperty(name = "showCollectionList", value = "仅展示藏品列表", position = 95)
    private List<CollectionPeriodAuditCollectionPageRes> showCollectionList;

    /**
     * 标签
     */
    @ApiModelProperty(name = "tagList", value = "标签", position = 93)
    private List<String> tagList;

    @ApiModelProperty(name = "collectionPeriodPriorityBuyList", value = "优先购买作品列表", position = 36)
    private List<CollectionPeriodPriorityBuy> collectionPeriodPriorityBuyList;

    /**
     * 折扣藏品列表
     */
    @ApiModelProperty(name = "discountCollectionList", value = "折扣藏品列表", position = 200)
    private List<PeriodDiscountDetail> discountCollectionList;

    /**
     * 空投作品列表
     */
    @ApiModelProperty(name = "collectionPeriodDropList", value = "空投作品列表", position = 100)
    private List<CollectionPeriodDropRes> collectionPeriodDropList;

    /**
     * 期数口令列表
     */
    @ApiModelProperty(name = "periodChannelWordList", value = "期数口令列表", position = 36)
    private List<PeriodChannelWord> periodChannelWordList;

    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 36)
    private Long channelId;

    @ApiModelProperty(name = "channelName", value = "分发渠道名称", position = 36)
    private String channelName;

    @ApiModelProperty(name = "approveOpinion", value = "审核意见")
    private String approveOpinion;

    /************************************************************发行方信息***************************************************************/
    /**
     * 作者id
     */
    @ApiModelProperty(name = "authorId", value = "作者id", position = 90)
    private Long authorId;
    /**
     * 作者名称
     */
    @ApiModelProperty(name = "author", value = "作者名称", position = 91)
    private String author;

    /**
     * 作者头像
     */
    @ApiModelProperty(name = "authorPic", value = "作者头像", position = 92)
    private String authorPic;

    /**
     * 合约Id
     */
    @ApiModelProperty(name = "contractId", value = "合约Id", position = 121)
    private Long contractId;
}
