package com.std.core.pojo.response;

import com.std.core.pojo.domain.*;
import com.std.core.pojo.request.CollectionRightsDetailCreateAutoReq;
import com.std.core.pojo.request.CollectionSaleDemandCompanyCreateReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 返回藏品列表
 */
@Data
public class CollectionPeriodCollectionRes {

    /**
     * id
     */
    @ApiModelProperty(name = "id", value = "藏品id", position = 10)
    private Long id;
    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 30)
    private String name;

    @ApiModelProperty(name = "category", value = "分类(0=版权区 1=衍生区)", required = true, position = 30)
    private String category;
    /**
     * 购买方式
     */
    @ApiModelProperty(name = "buyType", value = "购买方式 数据字典collection_buy_type", position = 40)
    private String buyType;
    /**
     * 板块类别
     */
    @ApiModelProperty(name = "plateCategory", value = "板块类别", position = 21)
    private String plateCategory;

    /**
     * 级别类型 0;普通 1:稀有
     */
    @ApiModelProperty(name = "levelType", value = "级别类型 0;普通 1:稀有", position = 50)
    private String levelType;

    /**
     * 封面
     */
    @ApiModelProperty(name = "coverFileUrl", value = "封面", position = 80)
    private String coverFileUrl;

    /**
     * 内容类型 0:文字,1:图片
     */
    @ApiModelProperty(name = "contentType", value = "内容类型 0:文字,1:图片", position = 60)
    private String contentType;

    /**
     * 介绍
     */
    @ApiModelProperty(name = "content", value = "介绍", position = 70)
    private String content;

    /**
     * 出品方
     */
    @ApiModelProperty(name = "producedId", value = "出品方", position = 20)
    private Long producedId;

    /**
     * 文件类型 0:图片,1:音频,2:视频
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 60)
    private String fileType;

    @ApiModelProperty(name = "fileList", value = "文件列表", position = 141)
    private List<UploadFile> fileList;

    /**
     * 标签
     */
    @ApiModelProperty(name = "tagList", value = "标签", position = 93)
    private List<String> tagList;

    @ApiModelProperty(name = "transformLimitTime", value = "转赠限制(小时)", position = 93)
    private Integer transformLimitTime;

    @ApiModelProperty(name = "singleMaxQuantity", value = "单人拥有最大数量")
    private Integer singleMaxQuantity;

    @ApiModelProperty(name = "collectionSaleDemandCreateReq", value = "数字藏品发行需求")
    @Valid
    @NotNull(message = "数字藏品发行需求不能为空")
    private CollectionSaleDemand collectionSaleDemand;

    @ApiModelProperty(name = "rightsDetailList", value = "作品权益")
    private List<CollectionRightsDetail> rightsDetailList;

    /**
     * 价格
     */
    @ApiModelProperty(name = "price", value = "价格", position = 60)
    private BigDecimal price;

    /**
     * id
     */
    @ApiModelProperty(name = "quantity", value = "数量", position = 10)
    private Integer quantity;

    /**
     * 状态 0:即将开启 1:售卖中 2:已售罄
     */
    @ApiModelProperty(name = "status", value = "0:即将开启 1:售卖中 2:已售罄", position = 120)
    private String status;

    /**
     * 序号
     */
    @ApiModelProperty(name = "orderNo", value = "序号", position = 40)
    private Integer orderNo;

    private BigDecimal rate;



    /**
     * 类型 0:自创作品,1:外部导入,2:自创头像作品
     */
    @ApiModelProperty(name = "type", value = "类型 0:自创作品,1:外部导入 2:自创头像作品 3:平台代创", position = 40)
    private String type;

    @ApiModelProperty(name = "producedPic", value = "出品方pic", position = 20)
    private String producedPic;

    @ApiModelProperty(name = "producedName", value = "出品方名称", position = 20)
    private String producedName;

    /**
     * 发行方
     */
    @ApiModelProperty(name = "authorId", value = "发行方", position = 20)
    private Long authorId;

    @ApiModelProperty(name = "divideAuthorId", value = "分账主体", position = 20)
    private Long divideAuthorId;

    /**
     * 标签
     */
    @ApiModelProperty(name = "tags", value = "标签", position = 93)
    private String tags;

    /**
     * 权益内容
     */
    @ApiModelProperty(name = "rightContent", value = "权益内容", position = 93)
    private String rightContent;

    /**
     * 权益类型0=文字 1=图片 2=暂无权益
     */
    @ApiModelProperty(name = "rightType", value = "权益类型0=文字 1=图片 2=暂无权益 3=列表展示", position = 93)
    private String rightType;

    /**
     * 总数量
     */
    @ApiModelProperty(name = "totalQuantity", value = "总数量", position = 100)
    private Integer totalQuantity;

    /**
     * 流通总量
     */
    @ApiModelProperty(name = "marketQuantity", value = "流通总量", position = 100)
    private Integer marketQuantity;

    /**
     * 剩余数量
     */
    @ApiModelProperty(name = "remainQuantity", value = "剩余数量", position = 110)
    private Integer remainQuantity;

    /**
     * 平台剩余数量
     */
    @ApiModelProperty(name = "platRemainQuantity", value = "平台剩余数量", position = 110)
    private Integer platRemainQuantity;

    /**
     * 链类型
     */
    @ApiModelProperty(name = "chainType", value = "链类型", position = 120)
    private String chainType;

    /**
     * 合约Id
     */
    @ApiModelProperty(name = "contractId", value = "合约Id", position = 121)
    private Long contractId;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 180)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 作者名称
     */
    @ApiModelProperty(name = "author", value = "作者名称", position = 91)
    private String author;

    @ApiModelProperty(name = "divideAuthor", value = "分账机构名称", position = 91)
    private String divideAuthor;

    /**
     * 作者头像
     */
    @ApiModelProperty(name = "authorPic", value = "作者头像", position = 92)
    private String authorPic;

    @ApiModelProperty(name = "isDeduction", value = "余额抵扣 ", position = 140)
    private String deduction;

    /**
     * 使用标识 0:未使用,1:使用
     */
    @ApiModelProperty(name = "useFlag", value = "使用标识 0:未使用,1:使用", position = 65)
    private String useFlag;

    /**
     * 使用用途 0:期数
     */
    @ApiModelProperty(name = "useWay", value = "使用用途 0:期数", position = 65)
    private String useWay;
}
