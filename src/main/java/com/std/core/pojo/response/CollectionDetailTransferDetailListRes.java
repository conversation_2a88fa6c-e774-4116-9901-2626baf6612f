package com.std.core.pojo.response;

import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR> ycj
 * @since : 2022-02-23 20:21
 */
@Data
public class CollectionDetailTransferDetailListRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 20)
    private Long collectionDetailId;

    /**
     * tokenId
     */
    @ApiModelProperty(name = "tokenId", value = "tokenId", position = 50)
    private String tokenId;

    /**
     * 名称
     */
    @ApiModelProperty(name = "name", value = "名称", position = 30)
    private String name;

    /**
     * 级别类型 0;普通 1:稀有
     */
    @ApiModelProperty(name = "levelType", value = "级别类型 0;普通 1:稀有", position = 50)
    private String levelType;

    /**
     * 文件类型 0:图片,1:音频,2:视频
     */
    @ApiModelProperty(name = "fileType", value = "文件类型 0:图片,1:音频,2:视频,3:3d文件", position = 60)
    private String fileType;

    /**
     * 模糊文件地址
     */
    @ApiModelProperty(name = "coverFileUrl", value = "模糊文件地址", position = 80)
    private String coverFileUrl;


}
