package com.std.core.pojo.response;

import com.std.core.enums.ETransferOrderPayStatus;
import com.std.core.enums.ETransferOrderPayType;
import com.std.core.enums.ETransferOrderType;
import com.std.core.pojo.domain.User;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR> ycj
 * @since : 2022-03-21 10:50
 */
@Data
public class TransferOrderRes {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 用户序号
     */
    @ApiModelProperty(name = "userId", value = "用户序号", position = 20)
    private Long userId;

    /**
     * 用户
     */
    private User user;

    /**
     * 竞拍用户
     */
    @ApiModelProperty(name = "channelUserId", value = "竞拍用户", position = 30)
    private Long channelUserId;

    /**
     * 渠道编号
     */
    @ApiModelProperty(name = "channelCode", value = "渠道编号", position = 40)
    private String channelCode;

    /**
     * 渠道编号
     */
    @ApiModelProperty(name = "channelCode", value = "渠道编号", position = 40)
    private String channelName;

    @ApiModelProperty(name = "mobile", value = "转入人手机号", position = 40)
    private String mobile;


    /**
     * 类型 {0:转出,1:转入}
     */
    @ApiModelProperty(name = "type", value = "类型 {0:转出,1:转入}", position = 50)
    private String type;

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payType", value = "支付方式 {0:余额支付,1:支付宝,2:微信}", position = 80)
    private String payType;

    /**
     * 支付订单号
     */
    @ApiModelProperty(name = "payOrderCode", value = "支付订单号", position = 90)
    private String payOrderCode;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatus", value = "支付状态 {0:待支付,1:已支付,2:支付失败}", position = 100)
    private String payStatus;

    /**
     * 支付时间
     */
    @ApiModelProperty(name = "payDatetime", value = "支付时间", position = 110)
    private Date payDatetime;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 150)
    private Date createDatetime;

    /**** Properties ****/

    /**
     * 类型 {0:转出,1:转入}
     */
    @ApiModelProperty(name = "typeName", value = "类型 {0:转出,1:转入}")
    private String typeName;

    /**
     * 支付方式 {0:余额支付,1:支付宝,2:微信}
     */
    @ApiModelProperty(name = "payTypeName", value = "支付方式 {0:余额支付,1:支付宝,2:微信}")
    private String payTypeName;

    /**
     * 支付状态 {0:待支付,1:已支付,2:支付失败}
     */
    @ApiModelProperty(name = "payStatusName", value = "支付状态 {0:待支付,1:已支付,2:支付失败}")
    private String payStatusName;

    public String getTypeName() {
        if (StringUtils.isNotBlank(type)) {
            typeName = ETransferOrderType.getTransferOrderType(type).getValue();
        }

        return typeName;
    }

    public String getPayTypeName() {
        if (StringUtils.isNotBlank(payType)) {
            payTypeName = ETransferOrderPayType.getTransferOrderPayType(payType).getValue();
        }

        return payTypeName;
    }

    public String getPayStatusName() {
        if (StringUtils.isNotBlank(payStatus)) {
            payStatusName = ETransferOrderPayStatus.getTransferOrderPayStatus(payStatus).getValue();
        }

        return payStatusName;
    }

    @ApiModelProperty(name = "collectionId",value = "collectionId")
    private Long collectionId;

    @ApiModelProperty(name = "collectionName",value = "藏品名称")
    private String collectionName;

    @ApiModelProperty(name = "collectionDetailId",value = "藏品id")
    private Long collectionDetailId;

    @ApiModelProperty(name = "price",value = "上架金额")
    private BigDecimal price;

}
