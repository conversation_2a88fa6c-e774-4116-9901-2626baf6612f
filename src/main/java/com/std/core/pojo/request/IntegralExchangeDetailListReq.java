package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 列表查询兑换明细
 *
 * <AUTHOR> ycj
 * @since : 2022-04-26 05:56
 */
@Data
public class IntegralExchangeDetailListReq extends BaseListReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 兑换序号
     */
    @ApiModelProperty(name = "exchangeId", value = "兑换序号", position = 20)
    private Long exchangeId;

    /**
     * 藏品序号
     */
    @ApiModelProperty(name = "collectionDetailId", value = "藏品序号", position = 30)
    private Long collectionDetailId;

    /**
     * 积分兑换单价
     */
    @ApiModelProperty(name = "integralPrice", value = "积分兑换单价", position = 40)
    private BigDecimal integralPrice;

}
