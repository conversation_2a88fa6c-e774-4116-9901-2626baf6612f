package com.std.core.pojo.request;

import com.std.common.base.BaseListReq;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 列表查询宝藏计划
 *
 * <AUTHOR> ycj
 * @since : 2021-11-02 13:31
 */
@Data
public class TreasurePlanCompanyListReq extends BaseListReq {

    @ApiModelProperty(name = "planDate", value = "查询时间（yyyy-MM）", position = 36)
    @NotBlank(message = "查询时间不能为空")
    private String planDate;

    @ApiModelProperty(name = "planDate", value = "板块类别", position = 36)
//    @NotBlank(message = "板块类别不能为空")
    private String plateCategory;

    @ApiModelProperty(name = "channelId", value = "分发渠道", position = 36)
    private Long channelId;
}
