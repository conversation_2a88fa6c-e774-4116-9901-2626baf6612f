package com.std.core.pojo.request;

import com.std.common.base.BasePageReq;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * 分页查询作品转入渠道记录
 *
 * <AUTHOR> ycj
 * @since : 2022-08-16 18:22
 */
@Data
public class CollectionTransferChannelRecordPageReq extends BasePageReq {

    /**
     * 序号
     */
    @ApiModelProperty(name = "id", value = "序号", position = 10)
    private Long id;

    /**
     * 作品序号
     */
    @ApiModelProperty(name = "collectionId", value = "作品序号", position = 20)
    private Long collectionId;

    /**
     * 渠道编号
     */
    @ApiModelProperty(name = "channelBizCode", value = "渠道编号", position = 30)
    private String channelBizCode;

    /**
     * 创建人
     */
    @ApiModelProperty(name = "creater", value = "创建人", position = 40)
    private Long creater;

    /**
     * 创建人名称
     */
    @ApiModelProperty(name = "createrName", value = "创建人名称", position = 50)
    private String createrName;

    /**
     * 创建时间
     */
    @ApiModelProperty(name = "createDatetime", value = "创建时间", position = 60)
    private Date createDatetime;

}
