package com.std.core.pojo.request;

import com.std.core.util.EnumValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 购买盲盒
 *
 * <AUTHOR> xieyj
 * @since : 2021-07-08 09:36
 */
@Data
public class CollectionBlindBoxBuyReq {

    /**
     * 支付方式 0:余额支付,1:支付宝,2:微信
     */
    @ApiModelProperty(name = "payType", value = "支付方式 0:余额支付,1:支付宝,2:微信", position = 20)
    @NotBlank(message = "支付方式不能为空")
    @EnumValue(strValues = {"0","7"}, message = "请选择其他支付方式")
    private String payType;

    @ApiModelProperty(name = "pwd", value = "支付密码", position = 39)
    private String pwd;

    @ApiModelProperty(name = "wxAppId", value = "安卓需要", position = 50)
    private String wxAppId;

    @ApiModelProperty(name = "bindCardId",value = "绑定的卡宾id")
    private Long bindCardId;

    @ApiModelProperty(name = "redirectUrl", value = "回调地址")
    private String redirectUrl;
}
