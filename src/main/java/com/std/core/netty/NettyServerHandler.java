package com.std.core.netty;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.define.RedisKeyList;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EHorseMatchUpdateFlag;
import com.std.core.enums.EHorseScene;
import com.std.core.enums.EUserKind;
import com.std.core.pojo.domain.HorseChannelUser;
import com.std.core.pojo.domain.HorseConfig;
import com.std.core.pojo.domain.HorseMatch;
import com.std.core.pojo.domain.HorseMatchLog;
import com.std.core.pojo.domain.HorseUserChannel;
import com.std.core.pojo.domain.User;
import com.std.core.pojo.response.HorseMatchCreateRes;
import com.std.core.pojo.response.HorseMatchRoomRes;
import com.std.core.pojo.response.HorseMatchRoomUserRes;
import com.std.core.pojo.response.HorseMatchStartRes;
import com.std.core.service.IHorseConfigService;
import com.std.core.service.IHorseMatchLogService;
import com.std.core.service.IHorseMatchRecordService;
import com.std.core.service.IHorseMatchService;
import com.std.core.service.IUserService;
import com.std.core.util.RedisUtil;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.handler.timeout.IdleState;
import io.netty.handler.timeout.IdleStateEvent;
import io.netty.util.AttributeKey;
import io.netty.util.CharsetUtil;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@ChannelHandler.Sharable
public class NettyServerHandler extends ChannelInboundHandlerAdapter {

    @Autowired
    protected RedisUtil redisUtil;

    @Autowired
    private IUserService userService;

    @Autowired
    private IHorseConfigService horseConfigService;

    @Autowired
    private IHorseMatchService horseMatchService;

    @Autowired
    private IHorseMatchRecordService horseMatchRecordService;

    @Resource
    private IHorseMatchLogService horseMatchLogService;

    // 场景-用户集合
    private static final ConcurrentHashMap<String, Set<Long>> sceneUserMap = new ConcurrentHashMap<>();
    private static final ConcurrentHashMap<Long, HorseUserChannel> userChannelMap = new ConcurrentHashMap<>();

    @Override
    public void channelActive(ChannelHandlerContext ctx) throws Exception {
        System.out.println("新的客户端链接地址：" + ctx.channel().remoteAddress());
//        String str = "hello";
//
    }

    /**
     * 读取客户端的信息
     */
    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        try {
            System.out.print("ctx = " + ctx.channel().remoteAddress() + " \t");
            String message = msg.toString();
            System.out.println("客户端发送的消息是 ： " + message);

            if (StringUtils.isBlank(message)) {
                return;
            }

            //去掉分隔符
            message = message.replace("|", "");

            System.out.println("客户端消息处理后是 ： " + message);

            SocketMessage webSocketMessage = null;

            if (message.contains("JoinScene")) {
                webSocketMessage = JSON.parseObject(message, SocketMessage.class);
//            //绑定用户
                online(ctx.channel(), webSocketMessage.getData().toString());

            } else if (message.contains("MsgPing")) {
                SocketMessage webSocketMess = new SocketMessage();
                webSocketMess.setRequestCode("MsgPong");
                webSocketMess.setData(System.currentTimeMillis());
                sendMsg(ctx.channel(), JSON.toJSONString(webSocketMess));
            } else {
                sendMsg(ctx.channel(), message);
            }
        } catch (Exception e) {
            log.error("netty连接错误：{}", e);
        }

    }


    public void sendMsg(Channel channel, String msg) {
        if (!channel.isActive()) {
            log.info("连接已被关闭");
//            userChannelMap.remove(getUserId(channel));
            return;
        }

        if (!msg.contains("MsgPong")) {
            log.info("推送消息，用户：" + channel + ",内容：" + msg);
        }

        channel.writeAndFlush(Unpooled.copiedBuffer(msg + "|", CharsetUtil.UTF_8));
    }

    /**
     * 读完成
     */
    @Override
    public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
//        ctx.writeAndFlush(Unpooled.copiedBuffer("hello,客户端～～",CharsetUtil.UTF_8));
    }

    /**
     * 发生异常，关闭
     */
    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) throws Exception {
        System.out.println(cause.getMessage());
        log.error("netty报错:" + cause.getMessage());
//        ctx.close();

    }


    /**
     * 判断一个通道是否有用户在使用 可做信息转发时判断该通道是否合法
     */
    public boolean hasUser(Channel channel) {
        AttributeKey<String> key = AttributeKey.valueOf("user");
        return (channel.hasAttr(key) || channel.attr(key).get() != null);
        //netty移除了这个map的remove方法,这里的判断谨慎一点
    }


    /**
     * 判断一个通道是否有用户在使用 可做信息转发时判断该通道是否合法
     */
    public String getUserId(Channel channel) {
        AttributeKey<String> key = AttributeKey.valueOf("user");
        if (hasUser(channel)) {
            return channel.attr(key).get();
        }
        return null;
    }

    public HorseUserChannel getUserChannel(Long userId) {
        HorseUserChannel userChannel = userChannelMap.get(userId);
        return userChannel;
    }

    /**
     * 上线一个用户
     */
    public void online(Channel channel, String data) {
        //先判断用户是否在系统中登录?
        //这部分代码个人实现,参考上面redis中的验证

        HorseChannelUser channelUser = JSONObject.parseObject(data, HorseChannelUser.class);

        getUser(channelUser.getUserId());

        try {
            EHorseScene.getHorseScene(channelUser.getSceneId());
        } catch (Exception e) {
            log.info("加入socket失败，场景错误");
            return;
        }
        AttributeKey<String> key = AttributeKey.valueOf("user");
        channel.attr(key).set(channelUser.getUserId().toString());

        HorseUserChannel userChannel = new HorseUserChannel();
        userChannel.setSceneId(channelUser.getSceneId());
        userChannel.setChannel(channel);
        userChannelMap.put(channelUser.getUserId(), userChannel);
//
//        Set<Long> userIdSet = sceneUserMap.get(channelUser.getSceneId());
//        if (null == userIdSet) {
//            userIdSet = new LinkedHashSet<>();
//        }
//        userIdSet.add(channelUser.getUserId());
//        sceneUserMap.put(channelUser.getSceneId(), userIdSet);

    }

    public User getUser(Long userId) {
        String userCacheKey = String.format(RedisKeyList.MT_USER_INFO_KEY, userId.toString());
        User user;
        if (redisUtil.hasKey(userCacheKey)) {
            user = JSON.parseObject(JSON.toJSONString(redisUtil.get(userCacheKey)), User.class);
        } else {
            try {
                user = userService.detail(userId, EUserKind.C);
            } catch (Exception e) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "用户不存在");
            }

//            if (EUserKind.BP.getCode().equals(user.getKind()) && null == user.getCompanyId()) {
//                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "发行方用户异常");
//            }
            if (null != user) {
                redisUtil.set(userCacheKey, user);
            }
        }

        return user;
    }

    /**
     * 根据用户id获取该用户的通道
     */
    public Channel getChannelByUserId(Long userId) {
        HorseUserChannel userChannel = userChannelMap.get(userId);
        return userChannel.getChannel();
    }


    /**
     * 根据场景推送
     */
    public void sendMsgByScene(String sceneId, String requestCode, HorseMatchCreateRes data) {
        Set<Long> userList = sceneUserMap.get(sceneId);

        if (null != userList) {
            for (Long userId : userList) {
                HorseUserChannel userChannel = userChannelMap.get(userId);
                SocketMessage webSocketMess = new SocketMessage();
                webSocketMess.setRequestCode(requestCode);
                SocketMessage socketMessage = new SocketMessage();
                socketMessage.setData(data);
                webSocketMess.setData(socketMessage);
                if (null == userChannel || null == userChannel.getChannel()) {
                    continue;
                }
                try {
                    sendMsg(userChannel.getChannel(), JSON.toJSONString(webSocketMess));
                } catch (Exception e) {
                    log.error("推送大厅用户错误：" + e.getMessage());
                }

            }
        }


    }

    public void createOrJoinRoom(String sceneId, Long horseId, String readyFlag, Long userId) {
        Set<Long> userIdList = sceneUserMap.get(sceneId);

        if (null == userIdList || CollectionUtils.isEmpty(userIdList)) {
            userIdList = new LinkedHashSet<>();
        }

        userIdList.add(userId);
        sceneUserMap.put(sceneId, userIdList);

        HorseUserChannel userChannel = userChannelMap.get(userId);
        if (null == userChannel || null == userChannel.getChannel()) {
            userChannel = new HorseUserChannel();
        }

        // 先将用户从老的场景剔除
        if (null != userChannel.getSceneId()) {
            Set<Long> userIdSet = sceneUserMap.get(userChannel.getSceneId());
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                userIdSet.remove(userId);
            }
        }

        userChannel.setSceneId(sceneId);
        userChannel.setHorseId(horseId);
        userChannel.setReadyFlag(readyFlag);
        userChannelMap.put(userId, userChannel);


    }

    public Set<Long> detailRoom(String sceneId) {
        Set<Long> userIdSet = sceneUserMap.get(sceneId);

        return userIdSet;
    }

    public void sendMsgUserByRoom(HorseMatch room, Long operator) {

//        IdleStateHandler handler = new IdleStateHandler(0,3);
//        Set<Long> userIdSet = sceneUserMap.get(room.getId().toString());
        // 房间里的人从数据库取
        List<HorseMatchLog> horseMatchLogList = horseMatchLogService.detailByMatch(room.getId());

        if (null == horseMatchLogList || CollectionUtils.isEmpty(horseMatchLogList)) {
            return;
        }

        HorseUserChannel userChannel = userChannelMap.get(operator);
        HorseMatchRoomUserRes roomUserRes = new HorseMatchRoomUserRes();
        User user = getUser(operator);
        roomUserRes.setNickname(user.getNickname());
        roomUserRes.setUserId(operator.toString());
        HorseConfig horseConfig = horseConfigService.detail(userChannel.getHorseId());
        roomUserRes.setHorsePhoto(horseConfig.getPic());
        roomUserRes.setReadyFlag(userChannel.getReadyFlag());
        roomUserRes.setHomeOwnerId(room.getUserId().toString());
        if (operator.equals(room.getUserId())) {
            roomUserRes.setHomeOwnerFlag(EBoolean.YES.getCode());
        } else {
            roomUserRes.setHomeOwnerFlag(EBoolean.NO.getCode());
        }

        roomUserRes.setGameStartFlag(false);
        for (HorseMatchLog horseMatchLog : horseMatchLogList) {

            // 自己不用推
            if (horseMatchLog.getUserId().equals(operator)) {
                continue;
            }
            try {
                HorseUserChannel horseUserChannel = userChannelMap.get(horseMatchLog.getUserId());
                SocketMessage webSocketMess = new SocketMessage();
                webSocketMess.setRequestCode(EHorseMatchUpdateFlag.UpdateRoom.getCode());
                SocketMessage socketMessage = new SocketMessage();
                socketMessage.setData(roomUserRes);
                webSocketMess.setData(socketMessage);
                if (null == userChannel || null == userChannel.getChannel()) {
                    continue;
                }
                sendMsg(horseUserChannel.getChannel(), JSON.toJSONString(webSocketMess));
            } catch (Exception e) {
                log.error("进入房间推送报错");
            }

        }

    }

    public HorseMatchRoomRes sendMsgByRoom(Long operator, String sceneId) {

        getUser(operator);

        Set<Long> userIdSet = sceneUserMap.get(sceneId);

        if (null == userIdSet || CollectionUtils.isEmpty(userIdSet)) {
            userIdSet = new LinkedHashSet<>();
        }

        HorseMatch horseMatch = horseMatchService.detail(sceneId);
        HorseMatchRoomRes roomRes = new HorseMatchRoomRes();
        BeanUtils.copyProperties(horseMatch, roomRes);
        roomRes.setNumber(userIdSet.size());
        roomRes.setUserId(horseMatch.getUserId().toString());
        List<HorseMatchRoomUserRes> resList = new LinkedList<>();
        for (Long userId : userIdSet) {
            HorseUserChannel userChannel = userChannelMap.get(userId);
            HorseMatchRoomUserRes roomUserRes = new HorseMatchRoomUserRes();
            User user = getUser(userId);
            roomUserRes.setUserId(userId.toString());
            roomUserRes.setNickname(user.getNickname());
            HorseConfig horseConfig = horseConfigService.detail(userChannel.getHorseId());
            roomUserRes.setHorsePhoto(horseConfig.getPic());
            roomUserRes.setReadyFlag(userChannel.getReadyFlag());

            if (userId.equals(horseMatch.getUserId())) {
                roomUserRes.setHomeOwnerFlag(EBoolean.YES.getCode());
            } else {
                roomUserRes.setHomeOwnerFlag(EBoolean.NO.getCode());
            }
            roomUserRes.setHomeOwnerId(horseMatch.getUserId().toString());
            resList.add(roomUserRes);
        }

        roomRes.setRoomUserResList(resList);

        HorseUserChannel userChannel = userChannelMap.get(operator);

        SocketMessage webSocketMess = new SocketMessage();
        webSocketMess.setRequestCode(EHorseMatchUpdateFlag.JoinRoom.getCode());
        SocketMessage socketMessage = new SocketMessage();
        socketMessage.setData(roomRes);
        webSocketMess.setData(socketMessage);
        if (null != userChannel.getChannel()) {
            sendMsg(userChannel.getChannel(), JSON.toJSONString(webSocketMess));
        }

        return roomRes;
    }


    public void sendMsgMatchStart(String sceneId, HorseMatchStartRes startRes) {
        Set<Long> userIdList = sceneUserMap.get(sceneId);
        for (Long userId : userIdList) {
            HorseUserChannel userChannel = userChannelMap.get(userId);
            userChannel.setLoadFinishFlag(EBoolean.NO.getCode());
            SocketMessage webSocketMess = new SocketMessage();
            SocketMessage message = new SocketMessage();
            message.setData(startRes);
            webSocketMess.setData(message);
            webSocketMess.setRequestCode(EHorseMatchUpdateFlag.RoomStart.getCode());
            sendMsg(userChannel.getChannel(), JSON.toJSONString(webSocketMess));
        }
    }

    public void removeBySceneUserMap(String sceneId) {
        sceneUserMap.remove(sceneId);
    }


    /**
     * 超时断开连接
     */
    public void userTimeOut(ChannelHandlerContext ctx) {
        String operator = getUserId(ctx.channel());

        if (null == operator) {
            return;
        }
        Long userId = Long.valueOf(operator);
        HorseUserChannel userChannel = userChannelMap.get(userId);
        if (null == userChannel) {
            return;
        }

//        Set<Long> userIdList = sceneUserMap.get(userChannel.getSceneId());
//        userIdList.remove(userId);
//
//        sceneUserMap.put(userChannel.getSceneId(), userIdList);
        userChannelMap.remove(userId);
    }

    public Object getSceneUserMapAll() {

        return sceneUserMap;
    }

    public Object getUserChannelMapAll() {
        return userChannelMap;
    }


    public void putUserChannelMap(Long userId, HorseUserChannel userChannel) {
        userChannelMap.put(userId, userChannel);
    }

    public void sendMsgUserByRoomChangeHorse(HorseMatch room, Long operator) {

//        IdleStateHandler handler = new IdleStateHandler(0,3);
//        Set<Long> userIdSet = sceneUserMap.get(room.getId().toString());
        // 房间里的人从数据库取
        List<HorseMatchLog> horseMatchLogList = horseMatchLogService.detailByMatch(room.getId());

        if (null == horseMatchLogList || CollectionUtils.isEmpty(horseMatchLogList)) {
            return;
        }

        HorseUserChannel userChannel = userChannelMap.get(operator);
        HorseMatchRoomUserRes roomUserRes = new HorseMatchRoomUserRes();
        User user = getUser(operator);
        roomUserRes.setNickname(user.getNickname());
        roomUserRes.setUserId(operator.toString());
        HorseConfig horseConfig = horseConfigService.detail(userChannel.getHorseId());
        roomUserRes.setHorsePhoto(horseConfig.getPic());
        roomUserRes.setReadyFlag(userChannel.getReadyFlag());
        roomUserRes.setHomeOwnerId(room.getUserId().toString());
        if (operator.equals(room.getUserId())) {
            roomUserRes.setHomeOwnerFlag(EBoolean.YES.getCode());
        } else {
            roomUserRes.setHomeOwnerFlag(EBoolean.NO.getCode());
        }

        roomUserRes.setGameStartFlag(false);
        for (HorseMatchLog matchLog : horseMatchLogList) {

//            // 自己不用推
//            if (userId.equals(operator)) {
//                continue;
//            }
            try {
                HorseUserChannel horseUserChannel = userChannelMap.get(matchLog.getUserId());
                SocketMessage webSocketMess = new SocketMessage();
                webSocketMess.setRequestCode(EHorseMatchUpdateFlag.UpdateRoom.getCode());
                SocketMessage socketMessage = new SocketMessage();
                socketMessage.setData(roomUserRes);
                webSocketMess.setData(socketMessage);
                if (null == userChannel || null == userChannel.getChannel()) {
                    continue;
                }
                sendMsg(horseUserChannel.getChannel(), JSON.toJSONString(webSocketMess));
            } catch (Exception e) {
                log.error("进入房间推送报错");
            }

        }
    }

    @Override
    public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
        if (evt instanceof IdleStateEvent) {
            IdleState state = ((IdleStateEvent) evt).state();
            if (state == IdleState.WRITER_IDLE) {
                log.info("写超时,连接关闭开始");
                // write heartbeat to server
                userTimeOut(ctx);
                ctx.close();
                log.info("写超时,连接关闭");
            }
        } else {
            super.userEventTriggered(ctx, evt);
        }
    }
}
