package com.std.core.enums;

import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import java.util.HashMap;
import java.util.Map;

/**
* 发行方渠道关联表Enum
*
* <AUTHOR> ycj
* @since : 2022-06-14 11:34
*/
public enum ECompanyChannelStatus {

// 数据字典执行SQL
// INSERT INTO "public"."tsys_dict"(key, value, remark) VALUES ('company_channel.status', '{"0":"废弃","1":"启用"}', '发行方渠道关联表状态');

    /**
    * 废弃
    */
    COMPANY_CHANNEL_STATUS_0("0", "废弃"),

    /**
    * 启用
    */
    COMPANY_CHANNEL_STATUS_1("1", "启用"),

    ;

    private String code;
    private String value;

    ECompanyChannelStatus(String code, String value) {
        this.code = code;
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public static Map<String, ECompanyChannelStatus> getCompanyChannelStatusResultMap() {
        Map<String, ECompanyChannelStatus> map = new HashMap<String, ECompanyChannelStatus>();
        for (ECompanyChannelStatus type : ECompanyChannelStatus.values()) {
        map.put(type.getCode(), type);
    }

    return map;
    }

    public static ECompanyChannelStatus getCompanyChannelStatus(String code) {
        Map<String, ECompanyChannelStatus> map = getCompanyChannelStatusResultMap();
        ECompanyChannelStatus result = map.get(code);
        if (result == null) {
            throw new BizException(ECommonErrorCode.E500001.getCode(),
            ECommonErrorCode.E500001.getValue(), "ECompanyChannelStatus=" + code);
        }

        return result;
    }

}
