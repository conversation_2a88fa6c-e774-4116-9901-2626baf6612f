package com.xmeta.opensdk.component;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;


@Component
@ConfigurationProperties(prefix = "xmeta.method")
@Data
public class InvokingMethodProperties {

    /**
     * 一级平台向Xmeta上传商品档案
     */
    private String archiveGoodsTransfer;

    /**
     * Xmeta向一级平台验证用户信息
     */
    private String userVerify;

    /**
     * #Xmeta向一级平台获取藏品档案下未锁定藏品数量
     */
    private String archiveGoodsCount;

    /**
     * Xmeta向一级平台获取用户未锁定藏品信息列表
     */
    private String goodsList;

    /**
     * Xmeta向一级平台验证用户藏品
     */
    private String goodsVerify;

    /**
     * XMeta向一级平台发起上架下架藏品
     */
    private String goodsLock;

    /**
     * Xmeta向一级平台发起转赠操作
     */
    private String goodsTransfer;

    /**
     * Xmeta向一级平台转赠结果查询
     */
    private String goodsTransferConfirm;

    /**
     * Xmeta向一级平台发起买家接收地址校验
     */
    private String buyerReceiveVerify;

    /**
     * Xmeta向一级平台发起买家接收地址校验
     */
    private String userInfo;
}
