package com.xmeta.opensdk.model.dto;

import java.util.List;
import lombok.Data;


@Data
public class GoodsTransferDTO {

    private String sourcePhone;

    private String targetPhone;

    private String tranNo;

    private String sourceWalletHash;

    private String targetWalletHash;

    private List<GoodsTransferList> goodsList;

    @Data
    public static class GoodsTransferList {

        private String goodsId;

        private String goodsHash;

        private String goodsContract;

        //藏品价格
        private String goodsPrice;

        //用户降费
        private String reduceCost;

        //平台返佣
        private String rebate;

    }


}
