package com.xmeta.opensdk.model.vo;

import java.util.List;
import lombok.Data;


@Data
public class ArchiveTransferVO {

    private String appId;

    private List<ArchiveTransferList> archiveList;

    @Data
    public static class ArchiveTransferList {

        //档案id （必填）
        private String archiveId;

        //档案藏品发布数量 （必填）
        private Integer publishCount;

        //档案图片地址（必填）
        private List<String> archiveImage;

        //档案名（必填）
        private String archiveName;

        //发行时间（必填）
        private String issueTime;

        //档案描述（必填）
        private String archiveDescription;

        //发行方(非必填)
        private String issuer;

        //发行方简介(非必填)
        private String issuerDescription;

        //版权方(非必填)
        private String copyRight;

        //版权方简介(非必填)
        private String copyRightDescription;

        //版权认证方(非必填)
        private String copyRightAuth;

        //版权认证方简介(非必填)
        private String copyRightAuthDescription;

        //技术支持(非必填)
        private String support;

        //备注(非必填)
        private String remark;

        //介绍(非必填)
        private String introduce;

        //作者名称(非必填)
        private String authorName;

        //作者简介(非必填)
        private String authorDescription;

        //系列名(必填)
        private String seriesTitle;

        //系列描述（非必填）
        private String seriesDescription;

        //系统图片地址（非必填）
        private List<String> seriesImage;
    }


}
