package com.xmeta.opensdk.model.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
public class GoodsTransferConfirmVO {

    private Integer status;

    private String tranNo;

    private List<GoodsTransferConfirmList> goodsList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GoodsTransferConfirmList {

        private String goodsId;

        private String goodsStatus;

    }
}
