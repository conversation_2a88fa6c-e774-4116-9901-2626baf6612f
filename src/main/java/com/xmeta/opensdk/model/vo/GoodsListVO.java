package com.xmeta.opensdk.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;


@Data
public class GoodsListVO {

    private List<String> goodsImage;

    @JSONField(serialize = false)
    private String coverFileUrl;

    private String archiveId;

    @JSONField(serialize = false)
    private String totalQuantity;

    private String goodsNo;

    private String goodsId;

    private String goodsName;

    private String goodsDescription;

    private String goodsHash;

    private String goodsContract;

    private Integer canTransfer;

    private String canTransferTime;


}
