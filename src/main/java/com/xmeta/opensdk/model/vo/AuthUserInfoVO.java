package com.xmeta.opensdk.model.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthUserInfoVO {

    /**
     * 手机号(必填)
     */
    private String phone;

    /**
     * 昵称(必填)
     */
    private String nickName;

    /**
     * headImg
     */
    private String headImg;

    /**
     * 用户编号(必填)
     */
    private String userId;

    /**
     * 身份证号 （非必填，已实名则必填，减少实名操作）
     */
    private String identityNumber;

    /**
     * 真实姓名（非必填，已实名则必填，减少实名操作）
     */
    private String realName;

    /**
     * 钱包地址(必填)
     */
    private String walletHash;


}
