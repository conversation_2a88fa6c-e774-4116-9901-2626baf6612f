package com.xmeta.opensdk.model.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
public class GoodsTransferVO {

    private Integer status;

    private String tranNo;

    private List<GoodsTransferVOList> goodsList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class GoodsTransferVOList {

        private String goodsId;

        private String goodsHash;

        private String goodsContract;

        private String goodsStatus;
    }

}
