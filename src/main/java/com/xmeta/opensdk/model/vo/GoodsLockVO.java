package com.xmeta.opensdk.model.vo;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsLockVO {

    private String walletHash;

    private List<GoodsLockVOList> goodsList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class GoodsLockVOList {

        private String goodsId;

        private Integer status;

    }

}
