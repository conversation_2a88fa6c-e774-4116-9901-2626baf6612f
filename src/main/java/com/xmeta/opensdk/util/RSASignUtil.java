package com.xmeta.opensdk.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * RSA加密解密工具类
 *
 */
@Slf4j
@Component
public class RSASignUtil {

    private RSASignUtil(){}


    /**
     * 参数签名拼接
     *
     * @param paramsMap 参数map
     * @return
     */
    public static String splicingParamString(Map<String, String> paramsMap) {
        return Signer.getSignCheckContent(paramsMap);
    }


    /**
     * 签名加密
     *
     * @param privateKey 秘钥
     * @param encodeStr        加密字符串
     * @return
     */
    public static String sign(String privateKey, String encodeStr) {
        Signer signer = new Signer();
        return signer.sign(encodeStr, privateKey);

    }


    /**
     *
     * 校验签名是否相等
     *
     * @param publicKey xmeta公钥
     * @param selfStr 待校验字符串
     * @param signStr 签名字符串
     * @return
     */
    public static boolean verify(String publicKey, String selfStr, String signStr) {
        return Signer.verify(selfStr, signStr, publicKey);
    }

}
