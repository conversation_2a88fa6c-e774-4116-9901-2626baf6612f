package com.xmeta.opensdk.util;

import lombok.extern.slf4j.Slf4j;

/**
 * aes工具类
 */
@Slf4j

public class AESUtil {

    private AESUtil(){}


    /**
     * 加密
     *
     * @param key aes秘钥
     * @param str 加密字符串
     * @return
     */
    public static String encode(String key, String str) {
        return AES.encrypt(str, key);

    }

    /**
     * 解密
     *
     * @param key aes秘钥
     * @param str 解密字符串
     * @return
     */
    public static String decode(String key, String str) {
        return AES.decrypt(str, key);
    }



}
