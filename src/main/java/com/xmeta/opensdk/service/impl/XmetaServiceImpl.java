package com.xmeta.opensdk.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.json.JSONUtil;
import com.std.common.enums.ECommonErrorCode;
import com.std.common.exception.BizException;
import com.std.core.enums.EBizErrorCode;
import com.std.core.enums.EBoolean;
import com.std.core.enums.EChannelSystemCode;
import com.std.core.enums.ECollectionDetailBuyChannel;
import com.std.core.enums.ECollectionDetailOwnerType;
import com.std.core.enums.ECollectionDetailRecordTradeType;
import com.std.core.enums.ECollectionDetailStatus;
import com.std.core.enums.EGoodsLockResultStatus;
import com.std.core.enums.EGoodsLockStatus;
import com.std.core.enums.EGoodsStatus;
import com.std.core.enums.EGoodsTransferResultStatus;
import com.std.core.enums.EPublishXmetaStyle;
import com.std.core.enums.EThirdTradeTransferOrderStatus;
import com.std.core.enums.EUserKind;
import com.std.core.mapper.CollectionDetailMapper;
import com.std.core.pojo.domain.ChannelTempCode;
import com.std.core.pojo.domain.ChannelUser;
import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.CollectionDetail;
import com.std.core.pojo.domain.CollectionDetailRecord;
import com.std.core.pojo.domain.ThirdTradeTransferOrder;
import com.std.core.pojo.domain.ThirdTradeTransferOrderDetail;
import com.std.core.pojo.domain.User;
import com.std.core.service.IChannelTempCodeService;
import com.std.core.service.IChannelUserService;
import com.std.core.service.ICollectionDetailExchangeCardService;
import com.std.core.service.ICollectionDetailRecordService;
import com.std.core.service.ICollectionDetailService;
import com.std.core.service.ICollectionService;
import com.std.core.service.IThirdTradeTransferOrderDetailService;
import com.std.core.service.IThirdTradeTransferOrderService;
import com.std.core.service.IUserService;
import com.std.core.util.DateUtil;
import com.xmeta.opensdk.component.InvokingMethodProperties;
import com.xmeta.opensdk.component.InvokingProperties;
import com.xmeta.opensdk.component.RestTemplateComponent;
import com.xmeta.opensdk.constant.BaseParameterConstant;
import com.xmeta.opensdk.enums.RespEnum;
import com.xmeta.opensdk.model.dto.AuthUserInfoDTO;
import com.xmeta.opensdk.model.dto.CommonReq;
import com.xmeta.opensdk.model.dto.GoodsCountDTO;
import com.xmeta.opensdk.model.dto.GoodsListDTO;
import com.xmeta.opensdk.model.dto.GoodsLockDTO;
import com.xmeta.opensdk.model.dto.GoodsLockDTO.GoodsLockList;
import com.xmeta.opensdk.model.dto.GoodsTransferConfirmDTO;
import com.xmeta.opensdk.model.dto.GoodsTransferDTO;
import com.xmeta.opensdk.model.dto.GoodsTransferDTO.GoodsTransferList;
import com.xmeta.opensdk.model.dto.GoodsVerifyDTO;
import com.xmeta.opensdk.model.dto.GoodsVerifyDTO.GoodsVerifyList;
import com.xmeta.opensdk.model.dto.ReceiveVerifyDTO;
import com.xmeta.opensdk.model.dto.SyncUserInfoDTO;
import com.xmeta.opensdk.model.vo.ArchiveTransferVO;
import com.xmeta.opensdk.model.vo.ArchiveTransferVO.ArchiveTransferList;
import com.xmeta.opensdk.model.vo.AuthUserInfoVO;
import com.xmeta.opensdk.model.vo.CommonResp;
import com.xmeta.opensdk.model.vo.GoodsCountDetailVO;
import com.xmeta.opensdk.model.vo.GoodsCountVO;
import com.xmeta.opensdk.model.vo.GoodsListDetailVO;
import com.xmeta.opensdk.model.vo.GoodsListVO;
import com.xmeta.opensdk.model.vo.GoodsLockVO;
import com.xmeta.opensdk.model.vo.GoodsLockVO.GoodsLockVOList;
import com.xmeta.opensdk.model.vo.GoodsTransferConfirmVO;
import com.xmeta.opensdk.model.vo.GoodsTransferConfirmVO.GoodsTransferConfirmList;
import com.xmeta.opensdk.model.vo.GoodsTransferVO;
import com.xmeta.opensdk.model.vo.GoodsTransferVO.GoodsTransferVOList;
import com.xmeta.opensdk.model.vo.GoodsVerifyVO;
import com.xmeta.opensdk.model.vo.ReceiveVerifyVO;
import com.xmeta.opensdk.model.vo.SyncUserInfoVO;
import com.xmeta.opensdk.service.IXmetaService;
import com.xmeta.opensdk.util.AESUtil;
import com.xmeta.opensdk.util.RSASignUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class XmetaServiceImpl implements IXmetaService {

    @Resource
    private InvokingProperties invokingProperties;

    @Resource
    private InvokingMethodProperties invokingMethodProperties;

    @Resource
    private RestTemplateComponent restTemplateComponent;

    @Resource
    private IUserService userService;

    @Resource
    private IChannelTempCodeService channelTempCodeService;

    @Resource
    private IChannelUserService channelUserService;

    @Resource
    private ICollectionService collectionService;

    @Resource
    private ICollectionDetailService collectionDetailService;

    @Resource
    private IThirdTradeTransferOrderService thirdTradeTransferOrderService;

    @Resource
    private IThirdTradeTransferOrderDetailService thirdTradeTransferOrderDetailService;

    @Resource
    private CollectionDetailMapper collectionDetailMapper;

    @Resource
    private ICollectionDetailRecordService collectionDetailRecordService;

    @Resource
    private ICollectionDetailExchangeCardService collectionDetailExchangeCardService;

    @Override
    public CommonResp syncUser(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        SyncUserInfoDTO syncUserInfoDTO = JSONUtil.toBean(decode, SyncUserInfoDTO.class);
        //业务处理
        User user = userService.detailBrief(syncUserInfoDTO.getPhone(), EUserKind.C.getCode());
        if (null == user) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "手机号不存在");
        }

        //aes加密返回objEncrypt
        //封装响应参数
        // todo 不授权是否需要产生授权用户记录
        ChannelUser channelUser = channelUserService.detailByUserId(user.getId(), EChannelSystemCode.XMETA.getCode());
        SyncUserInfoVO syncUserInfoVO = new SyncUserInfoVO(user.getBlockAddress(), channelUser.getChannelUserId());
        //sign签名返回
        return getCommonResp(syncUserInfoVO, invokingMethodProperties.getUserVerify());
    }

    @Override
    public CommonResp getUserInfoByCode(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        AuthUserInfoDTO authUserInfoDTO = JSONUtil.toBean(decode, AuthUserInfoDTO.class);
        //业务处理
        ChannelTempCode channelTempCode = channelTempCodeService.detail(authUserInfoDTO.getCode());
        User user = userService.detailSimpleInfo(channelTempCode.getUserId());

        //aes加密返回objEncrypt
        //封装响应参数
        AuthUserInfoVO authUserInfoVO = new AuthUserInfoVO(user.getLoginName(), user.getNickname(), user.getPhoto(),
                channelTempCode.getChannelUserId(), user.getIdNo(), user.getRealName(), user.getBlockAddress());

        //sign签名返回
        return getCommonResp(authUserInfoVO, invokingMethodProperties.getUserInfo());
    }

    @Override
    public CommonResp goodsCount(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        GoodsCountDTO goodsCountDTO = JSONUtil.toBean(decode, GoodsCountDTO.class);
        User user = userService.detailBrief(goodsCountDTO.getPhone(), EUserKind.C.getCode());
        if (null == user) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "手机号不存在");
        }

        //TODO 业务处理
        //封装响应参数
        GoodsCountDetailVO goodsCountDetailVO = new GoodsCountDetailVO();
        List<GoodsCountVO> goodsCountVOList = collectionService.selectMyHavePublishXmetaCollectionCount(user);
        goodsCountDetailVO.setArchiveList(goodsCountVOList);
        return getCommonResp(goodsCountDetailVO, invokingMethodProperties.getArchiveGoodsCount());
    }

    @Override
    public CommonResp goodsList(CommonReq commonReq) {

        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        GoodsListDTO goodsListDTO = JSONUtil.toBean(decode, GoodsListDTO.class);
        User user = getUser(goodsListDTO.getPhone(), goodsListDTO.getWalletHash());

        //TODO 业务处理
        GoodsListDetailVO goodsListDetailVO = new GoodsListDetailVO();
        List<GoodsListVO> goodsList = collectionDetailService.selectMyHavePublishXmetaCollectionDetail(user, goodsListDTO);
        if (CollectionUtils.isEmpty(goodsList)) {
            goodsListDetailVO.setTotal(0);
            goodsListDetailVO.setGoodsList(new ArrayList<>());
        } else {
            goodsListDetailVO.setTotal(goodsList.size());
            goodsListDetailVO.setGoodsList(goodsList);
            for (GoodsListVO goodsListVO : goodsList) {
                //初始化藏品编号前缀0
                int totalLength = goodsListVO.getTotalQuantity().length();
                int currentLenght = goodsListVO.getGoodsNo().length();
                String preZero = "";
                if (totalLength - currentLenght > 0) {
                    int total = totalLength - currentLenght;
                    for (int i = 0; i < total; i++) {
                        preZero = preZero + "0";
                    }
                }
                goodsListVO.setGoodsNo(preZero + goodsListVO.getGoodsNo());
            }
        }

        //sign签名返回
        return getCommonResp(goodsListDetailVO, invokingMethodProperties.getGoodsList());
    }

    /**
     * 手机号和钱包地址一定要一致
     */
    private User getUser(String phone, String walletHash) {
        User phoneUser = null;
        if (StringUtils.isNotBlank(phone)) {
            phoneUser = userService.detailBrief(phone, EUserKind.C.getCode());
            if (null == phoneUser) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "phone用户不存在");
            }
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "phone不能为空");
        }

        //有钱包地址用户信息一定要跟手机号保持一致，否则报错
        User walletUser = null;
        if (StringUtils.isNotBlank(walletHash)) {
            walletUser = userService.detailByBlockAddress(walletHash);
            if (null == walletUser) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "walletHash用户不存在");
            }
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "walletHash不能为空");
        }

        if (!phoneUser.getId().equals(walletUser.getId())) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "phone和walletHash用户不对应");
        }

        return phoneUser;
    }

    @Override
    public CommonResp goodsVerify(CommonReq commonReq) {
        String decode = verifyReq(commonReq);

        //取出objEncrypt中的数据
        GoodsVerifyDTO goodsVerifyDTO = JSONUtil.toBean(decode, GoodsVerifyDTO.class);
        //TODO 业务处理
        User user = getUser(goodsVerifyDTO.getPhone(), goodsVerifyDTO.getWalletHash());
        List<GoodsVerifyVO> goodsVerifyVOList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(goodsVerifyDTO.getGoodsList())) {
            for (GoodsVerifyList goodsVerify : goodsVerifyDTO.getGoodsList()) {
                GoodsVerifyVO goodsVerifyVO = new GoodsVerifyVO(goodsVerify.getGoodsId(), EGoodsStatus.NOT.getCode());
                CollectionDetail collectionDetail = collectionDetailService.detailSimple(Long.valueOf(goodsVerify.getGoodsId()));
                if (ECollectionDetailOwnerType.CUSER.getCode().equals(collectionDetail.getOwnerType()) && user.getId()
                        .equals(collectionDetail.getOwnerId()) && ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode()
                        .equals(collectionDetail.getStatus())) {
                    goodsVerifyVO.setGoodsStatus(EGoodsStatus.FULL.getCode());
                }
                goodsVerifyVOList.add(goodsVerifyVO);
            }
        }

        //sign签名返回
        return getCommonResp(goodsVerifyVOList, invokingMethodProperties.getGoodsVerify());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResp goodsLock(CommonReq commonReq) {

        String decode = verifyReq(commonReq);

        //取出objEncrypt中的数据
        GoodsLockDTO goodsLockDTO = JSONUtil.toBean(decode, GoodsLockDTO.class);
        User user = userService.detailByBlockAddress(goodsLockDTO.getWalletHash());

        //封装响应参数
        List<GoodsLockVOList> goodsLockVOLists = new ArrayList<>();
        GoodsLockVO goodsLockResult = new GoodsLockVO(user.getBlockAddress(), goodsLockVOLists);
        for (GoodsLockList goodsLock : goodsLockDTO.getGoodsList()) {
            GoodsLockVOList goodsLockVO = new GoodsLockVOList(goodsLock.getGoodsId(), EGoodsLockResultStatus.FAILURE.getCode());
            CollectionDetail collectionDetail = collectionDetailService.detailForUpdate(new Long(goodsLock.getGoodsId()));
            Collection collection = collectionService.detailSimple(collectionDetail.getCollectionId());

            if (collectionDetail.getLockTime() > 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品可流转，不能在XMeta秒转");
            }
            //藏品拥有者和当前钱包地址用户相同才可进行上下架操作
            if (user.getId().equals(collectionDetail.getOwnerId())
                    && ECollectionDetailOwnerType.CUSER.getCode().equals(collectionDetail.getOwnerType())) {
                //上下架状态 0上架 1下架
                if (EGoodsLockStatus.PUTON.getCode().equals(goodsLock.getStatus())) {
                    //判断是否上架Xmeta
                    if (!EBoolean.YES.getCode().equals(collection.getPublishXmeta())) {
                        throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前作品未上架");
                    }

                    if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode().equals(collectionDetail.getStatus())) {
                        collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_19.getCode());
                        collectionDetailService.modify(collectionDetail);
                        goodsLockVO.setStatus(EGoodsLockResultStatus.SUCCESS.getCode());
                    }

                    //权益卡上架需要把对应的卡状态变更为已使用
                    if (EPublishXmetaStyle.RIGHT_CARD.getCode().equals(collection.getPublishXmetaStyle())) {
                        collectionDetailExchangeCardService.doHandleUsed(collectionDetail.getId());
                    }


                } else if (EGoodsLockStatus.PUTOFF.getCode().equals(goodsLock.getStatus())) {
                    if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_19.getCode().equals(collectionDetail.getStatus())) {
                        collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
                        collectionDetailService.modify(collectionDetail);
                        goodsLockVO.setStatus(EGoodsLockResultStatus.SUCCESS.getCode());
                    }
                }
            } else {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品不属于您，无法上下架操作");
            }

            goodsLockVOLists.add(goodsLockVO);
        }

        //sign签名返回
        return getCommonResp(goodsLockResult, invokingMethodProperties.getGoodsLock());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResp goodsTransfer(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        GoodsTransferDTO goodsTransferDTO = JSONUtil.toBean(decode, GoodsTransferDTO.class);

        //业务处理:
        //1)订单是否已存在
        ThirdTradeTransferOrder thirdTradeTransferOrder = thirdTradeTransferOrderService.detailUnCheck(goodsTransferDTO.getTranNo());
        if (null != thirdTradeTransferOrder) {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "交易订单已存在，无需再次回调");
        }
        //2)根据walletHash获取用户信息,判断当前藏品是否xmeta售卖中，且是他的
        User sourceUser = userService.detailByBlockAddress(goodsTransferDTO.getSourceWalletHash());

        //封装响应参数
        List<GoodsTransferVO.GoodsTransferVOList> goodsTransferVOLists = new ArrayList<>();
        GoodsTransferVO goodsTransferVO = new GoodsTransferVO();
        goodsTransferVO.setTranNo(goodsTransferDTO.getTranNo());
        goodsTransferVO.setGoodsList(goodsTransferVOLists);

        List<CollectionDetail> collectionDetailList = new ArrayList<>();
        boolean goodsTransferResult = true;
        for (GoodsTransferList goodsTransfer : goodsTransferDTO.getGoodsList()) {
            GoodsTransferVOList goodsTransferVOList = new GoodsTransferVOList(goodsTransfer.getGoodsId(), goodsTransfer.getGoodsHash(),
                    goodsTransfer.getGoodsContract(), EGoodsStatus.NOT.getCode());
            CollectionDetail collectionDetail = collectionDetailService.detailSimple(Long.valueOf(goodsTransfer.getGoodsId()));
            if (collectionDetail.getLockTime() > 0) {
                throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "当前藏品可流转，不能在XMeta秒转");
            }
            //判断藏品所属目标用户，且藏品状态为XMeta售卖中才可返回成功
            if (sourceUser.getId().equals(collectionDetail.getOwnerId())
                    && ECollectionDetailOwnerType.CUSER.getCode().equals(collectionDetail.getOwnerType())
                    && ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_19.getCode().equals(collectionDetail.getStatus())) {
                goodsTransferVOList.setGoodsStatus(EGoodsStatus.FULL.getCode());
                collectionDetail.setGoodsPrice(new BigDecimal(goodsTransfer.getGoodsPrice()));
                collectionDetail.setReduceCost(new BigDecimal(goodsTransfer.getReduceCost()));
                collectionDetail.setRebate(new BigDecimal(goodsTransfer.getRebate()));
            } else {
                goodsTransferResult = false;
            }

            goodsTransferVOLists.add(goodsTransferVOList);
            collectionDetailList.add(collectionDetail);
        }
        // 转赠结果处理
        if (goodsTransferResult) {
            goodsTransferVO.setStatus(EGoodsTransferResultStatus.TRANFERING.getCode());
        } else {
            goodsTransferVO.setStatus(EGoodsTransferResultStatus.FAILURE.getCode());
        }
        //@TODO 转赠结果成功怎么处理？

        //3)验证目标walletHash是否真实
        User targetUser = userService.detailByBlockAddress(goodsTransferDTO.getTargetWalletHash());
        //4)落地外部交易划转订单和明细
        thirdTradeTransferOrderService.create(goodsTransferDTO, sourceUser, targetUser, collectionDetailList);

        //sign签名返回
        return getCommonResp(goodsTransferVO, invokingMethodProperties.getGoodsTransfer());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResp goodsTransferConfirm(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        GoodsTransferConfirmDTO goodsTransferConfirmDTO = JSONUtil.toBean(decode, GoodsTransferConfirmDTO.class);
        //业务处理:
        //1)查询订单是否存在，且是待确认状态
        //2)交易订单状态更改为划转成功，处理转赠明细，将藏品划转给目标用户
        //3)处理响应逻辑为划转成功
        ThirdTradeTransferOrder thirdTradeTransferOrder = thirdTradeTransferOrderService.detail(goodsTransferConfirmDTO.getTranNo());
        thirdTradeTransferOrder = thirdTradeTransferOrderService.selectForUpdate(thirdTradeTransferOrder.getId());
        List<ThirdTradeTransferOrderDetail> detailList = thirdTradeTransferOrderDetailService.list(thirdTradeTransferOrder.getId());

        //封装参数
        GoodsTransferConfirmVO goodsTransferConfirmVO = new GoodsTransferConfirmVO();
        goodsTransferConfirmVO.setTranNo(goodsTransferConfirmDTO.getTranNo());
        List<GoodsTransferConfirmList> goodsTransferConfirmLists = new ArrayList<>();
        goodsTransferConfirmVO.setGoodsList(goodsTransferConfirmLists);

        //订单状态处理
        if (EThirdTradeTransferOrderStatus.SUCCESS.getCode().equals(thirdTradeTransferOrder.getStatus())) {
            //订单已交易成功
            goodsTransferConfirmVO.setStatus(EGoodsTransferResultStatus.SUCCESS.getCode());
            for (ThirdTradeTransferOrderDetail thirdTradeTransferOrderDetail : detailList) {
                GoodsTransferConfirmList goodsTransferConfirmList = new GoodsTransferConfirmList(
                        thirdTradeTransferOrderDetail.getCollectionDetailId().toString(), EGoodsStatus.FULL.getCode());
                goodsTransferConfirmLists.add(goodsTransferConfirmList);
            }
        } else if (EThirdTradeTransferOrderStatus.FAILURE.getCode().equals(thirdTradeTransferOrder.getStatus())) {
            //订单已交易失败
            goodsTransferConfirmVO.setStatus(EGoodsTransferResultStatus.FAILURE.getCode());
            for (ThirdTradeTransferOrderDetail thirdTradeTransferOrderDetail : detailList) {
                GoodsTransferConfirmList goodsTransferConfirmList = new GoodsTransferConfirmList(
                        thirdTradeTransferOrderDetail.getCollectionDetailId().toString(), EGoodsStatus.NOT.getCode());
                goodsTransferConfirmLists.add(goodsTransferConfirmList);
            }

        } else if (EThirdTradeTransferOrderStatus.TO_CONFIRM.getCode().equals(thirdTradeTransferOrder.getStatus())) {
            //首次订单处理，数据封装
            boolean goodsTransferResult = true;
            List<CollectionDetail> collectionDetailList = new ArrayList<>();
            for (ThirdTradeTransferOrderDetail thirdTradeTransferOrderDetail : detailList) {
                GoodsTransferConfirmList goodsTransferConfirmDetail = null;
                CollectionDetail collectionDetail = collectionDetailService
                        .detailForUpdate(thirdTradeTransferOrderDetail.getCollectionDetailId());
                if (ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_19.getCode().equals(collectionDetail.getStatus())
                        && collectionDetail.getOwnerId().equals(thirdTradeTransferOrder.getSourceUserId())) {
                    collectionDetail.setStatus(ECollectionDetailStatus.COLLECTION_DETAIL_STATUS_0.getCode());
                    //临时传参
                    collectionDetail.setRefId(thirdTradeTransferOrderDetail.getId());

                    collectionDetail.setOwnerType(ECollectionDetailOwnerType.CUSER.getCode());
                    collectionDetail.setOwnerId(thirdTradeTransferOrder.getTargetUserId());
                    collectionDetail.setBuyChannel(ECollectionDetailBuyChannel.XMETA_TRADE.getCode());
                    collectionDetail.setBuyPrice(thirdTradeTransferOrderDetail.getPrice());
                    collectionDetail.setBuyDatetime(new Date());
                    collectionDetail.setUpdateTime(new Date().getTime());
                    collectionDetailList.add(collectionDetail);

                    goodsTransferConfirmDetail = new GoodsTransferConfirmList(
                            thirdTradeTransferOrderDetail.getCollectionDetailId().toString(),
                            EGoodsStatus.FULL.getCode());
                } else {
                    goodsTransferConfirmDetail = new GoodsTransferConfirmList(
                            thirdTradeTransferOrderDetail.getCollectionDetailId().toString(),
                            EGoodsStatus.NOT.getCode());
                    goodsTransferResult = false;
                }

                goodsTransferConfirmLists.add(goodsTransferConfirmDetail);
            }

            if (goodsTransferResult) {
                for (CollectionDetail collectionDetail : collectionDetailList) {
                    //之前的藏品流转状态变更
                    CollectionDetailRecord collectionDetailRecord = new CollectionDetailRecord();
                    collectionDetailRecord.setCollectionDetailId(collectionDetail.getId());
                    collectionDetailRecord.setStatus(EBoolean.NO.getCode());
                    collectionDetailRecordService.modify(collectionDetailRecord);

                    //藏品流转记录
                    collectionDetailRecordService.create(collectionDetail.getId(), ECollectionDetailOwnerType.CUSER.getCode(),
                            thirdTradeTransferOrder.getSourceUserId(), ECollectionDetailOwnerType.CUSER.getCode(),
                            thirdTradeTransferOrder.getTargetUserId(), collectionDetail.getBuyPrice(),
                            ECollectionDetailRecordTradeType.COLLECTION_DETAIL_RECORD_TRADE_TYPE_27.getCode(),
                            collectionDetail.getRefId());
                }
                collectionDetailMapper.updateTradeTransforOwner(collectionDetailList);

                //订单处理
                thirdTradeTransferOrderService.doTransfer(thirdTradeTransferOrder, EThirdTradeTransferOrderStatus.SUCCESS.getCode());
                goodsTransferConfirmVO.setStatus(EGoodsTransferResultStatus.SUCCESS.getCode());
            } else {
                thirdTradeTransferOrderService.doTransfer(thirdTradeTransferOrder, EThirdTradeTransferOrderStatus.FAILURE.getCode());
                goodsTransferConfirmVO.setStatus(EGoodsTransferResultStatus.FAILURE.getCode());
            }
        } else {
            throw new BizException(ECommonErrorCode.BIZ_DEFAULT.getCode(), "订单状态不支持");
        }

        //sign签名返回
        return getCommonResp(goodsTransferConfirmVO, invokingMethodProperties.getGoodsTransferConfirm());
    }

    @Override
    public CommonResp receiveVerify(CommonReq commonReq) {
        String decode = verifyReq(commonReq);
        //取出objEncrypt中的数据
        ReceiveVerifyDTO request = JSONUtil.toBean(decode, ReceiveVerifyDTO.class);
        User user = userService.detailUnCheckByBlockAddress(request.getWalletHash());
        //封装响应参数
        ReceiveVerifyVO receiveVerifyVO = null;
        if (null != user) {
            receiveVerifyVO = new ReceiveVerifyVO(EBoolean.YES.getCode(), "钱包地址存在");
        } else {
            receiveVerifyVO = new ReceiveVerifyVO(EBoolean.NO.getCode(), "钱包地址不存在");
        }

        //sign签名返回
        return getCommonResp(receiveVerifyVO, invokingMethodProperties.getBuyerReceiveVerify());
    }

    @Override
    public void archiveTransfer(List<Collection> collectionList, User operator) {
        if (CollectionUtils.isEmpty(collectionList)) {
            return;
        }

        //业务处理
        //封装请求参数
        ArchiveTransferVO archiveTransferVO = new ArchiveTransferVO();
        List<ArchiveTransferList> archiveTransferList = new ArrayList<>();
        for (Collection collection : collectionList) {
            ArchiveTransferList archiveTransfer = new ArchiveTransferList();
            archiveTransfer.setArchiveId(collection.getId().toString());
            archiveTransfer.setPublishCount(collection.getMarketQuantity());
            List<String> archiveImageList = new ArrayList<>();
            archiveImageList.add(collection.getCoverFileUrl());
            archiveTransfer.setArchiveImage(archiveImageList);
            archiveTransfer.setArchiveName(collection.getName());
            archiveTransfer.setIssueTime(DateUtil.getDate(collection.getCreateDatetime(), DateUtil.DATA_TIME_PATTERN_1));
            archiveTransfer.setArchiveDescription("<img src='" + collection.getContent() + "'>");
            archiveTransfer.setSeriesTitle(collection.getSerialName());

            archiveTransferList.add(archiveTransfer);
        }

        archiveTransferVO.setArchiveList(archiveTransferList);
        archiveTransferVO.setAppId(invokingProperties.getAppId());
        String objEncrypt = JSONUtil.toJsonStr(archiveTransferVO);
        objEncrypt = AESUtil.encode(invokingProperties.getAesKey(), objEncrypt);

        CommonReq commonReq = new CommonReq();
        commonReq.setObjEncrypt(objEncrypt);
        commonReq.setAppId(invokingProperties.getAppId());
        commonReq.setMethod(invokingMethodProperties.getArchiveGoodsTransfer());
        commonReq.setNonce(IdUtil.simpleUUID());
        commonReq.setTimestamp(String.valueOf(System.currentTimeMillis()));
        commonReq.setVersion(invokingProperties.getVersion());
        String s = encodeSign(commonReq);
        commonReq.setSign(s);

        Object o = restTemplateComponent.postEntity(invokingProperties.getUrl(), JSONUtil.toJsonStr(commonReq));
        CommonResp resultResp = JSONUtil.toBean(JSONUtil.toJsonStr(o), CommonResp.class);
        String decode = verifyResp(resultResp);
        //取出objEncrypt中的数据
//        List<ArchiveTransferDTO> archiveTransferDTOList = JSONUtil.toList(JSONUtil.toJsonStr(decode), ArchiveTransferDTO.class);
//        for (ArchiveTransferDTO archiveTransferDTO : archiveTransferDTOList) {
//            if (EBoolean.NO.getCode().equals(collection.getPublishXmeta())) {
//
//            }
//        }

        for (Collection collection : collectionList) {
            collection.setPublishXmeta(EBoolean.YES.getCode());
            if (EUserKind.C.getCode().equals(operator.getKind())) {
                collection.setPublishXmetaStyle(EPublishXmetaStyle.RIGHT_CARD.getCode());
            } else {
                collection.setPublishXmetaStyle(EPublishXmetaStyle.MANUAL.getCode());
            }
            collection.setUpdateDatetime(new Date());
            collection.setUpdater(operator.getId());
            collection.setUpdaterName(operator.getLoginName());
            collectionService.modify(collection);
        }

    }

    @NotNull
    private String verifyReq(CommonReq commonReq) {
        //校验签名
        if (!verifyReqSign(commonReq)) {
            //签名不匹配
            throw new BizException(EBizErrorCode.UNDONE.getCode(), "签名不匹配");
        }

        //解密objEncrypt
        if (!verifyObjEncrypt(invokingProperties.getAesKey(), commonReq.getObjEncrypt())) {
            //解密错误
            throw new BizException(EBizErrorCode.UNDONE.getCode(), "解密错误");
        }

        return AESUtil.decode(invokingProperties.getAesKey(), commonReq.getObjEncrypt());
    }

    @NotNull
    private String verifyResp(CommonResp commonResp) {
        //校验签名
        if (!verifyRespSign(commonResp)) {
            //签名不匹配
            throw new BizException(EBizErrorCode.UNDONE.getCode(), "签名不匹配");
        }

        //解密objEncrypt
        if (!verifyObjEncrypt(invokingProperties.getAesKey(), commonResp.getObjEncrypt())) {
            //解密错误
            throw new BizException(EBizErrorCode.UNDONE.getCode(), "解密错误");
        }

        String objEncrypt = AESUtil.decode(invokingProperties.getAesKey(), commonResp.getObjEncrypt());
        log.info("objEncrypt:[{}]", objEncrypt);
        return objEncrypt;
    }

    @NotNull
    private CommonResp getCommonResp(Object obj, String method) {
        String objEncrypt = JSONUtil.toJsonStr(obj);
        objEncrypt = AESUtil.encode(invokingProperties.getAesKey(), objEncrypt);

        CommonResp commonResp = new CommonResp();
        commonResp.setMethod(method);
        commonResp.setTimestamp(String.valueOf(System.currentTimeMillis()));
        commonResp.setNonce(IdUtil.simpleUUID());
        commonResp.setCode(RespEnum.SUCCESS.getK());
        commonResp.setMsg(RespEnum.SUCCESS.getV());
        commonResp.setObjEncrypt(objEncrypt);
        commonResp.setAppId(invokingProperties.getAppId());
        commonResp.setVersion(invokingProperties.getVersion());
        String s = encodeSign(commonResp);
        commonResp.setSign(s);
        return commonResp;
    }

    /**
     *
     */
    private String encodeSign(CommonResp commonResp) {
        Map<String, String> encodeSign = new HashMap<>(16);
        encodeSign.put(BaseParameterConstant.APPID, commonResp.getAppId());
        encodeSign.put(BaseParameterConstant.METHOD, commonResp.getMethod());
        encodeSign.put(BaseParameterConstant.NONCE, commonResp.getNonce());
        encodeSign.put(BaseParameterConstant.OBJ_ENCRYPT, commonResp.getObjEncrypt());
        encodeSign.put(BaseParameterConstant.TIMESTAMP, commonResp.getTimestamp());
        encodeSign.put(BaseParameterConstant.VERSION, commonResp.getVersion());

        encodeSign.put(BaseParameterConstant.CODE, RespEnum.SUCCESS.getK().toString());
        encodeSign.put(BaseParameterConstant.MSG, RespEnum.SUCCESS.getV());
        String s = RSASignUtil.splicingParamString(encodeSign);
        return RSASignUtil.sign(invokingProperties.getAppPrivateKey(), s);
    }

    /**
     * 校验请求签名
     *
     * @param commonResp 公共返回参数
     * @return 校验成功失败
     */
    private boolean verifyRespSign(CommonResp commonResp) {
        Map<String, String> signVerifyMap = new HashMap<>(16);
        signVerifyMap.put(BaseParameterConstant.APPID, commonResp.getAppId());
        signVerifyMap.put(BaseParameterConstant.METHOD, commonResp.getMethod());
        signVerifyMap.put(BaseParameterConstant.NONCE, commonResp.getNonce());
        signVerifyMap.put(BaseParameterConstant.OBJ_ENCRYPT, commonResp.getObjEncrypt());
        signVerifyMap.put(BaseParameterConstant.TIMESTAMP, commonResp.getTimestamp());
        signVerifyMap.put(BaseParameterConstant.VERSION, commonResp.getVersion());
        signVerifyMap.put(BaseParameterConstant.CODE, RespEnum.SUCCESS.getK().toString());
        signVerifyMap.put(BaseParameterConstant.MSG, RespEnum.SUCCESS.getV());

        String selfSign = RSASignUtil.splicingParamString(signVerifyMap);

        return RSASignUtil.verify(invokingProperties.getPublicKey(), selfSign, commonResp.getSign());
    }

    /**
     *
     */
    private String encodeSign(CommonReq commonReq) {
        Map<String, String> encodeSign = new HashMap<>(16);
        encodeSign.put(BaseParameterConstant.APPID, commonReq.getAppId());
        encodeSign.put(BaseParameterConstant.METHOD, commonReq.getMethod());
        encodeSign.put(BaseParameterConstant.NONCE, commonReq.getNonce());
        encodeSign.put(BaseParameterConstant.OBJ_ENCRYPT, commonReq.getObjEncrypt());
        encodeSign.put(BaseParameterConstant.TIMESTAMP, commonReq.getTimestamp());
        encodeSign.put(BaseParameterConstant.VERSION, commonReq.getVersion());
        String s = RSASignUtil.splicingParamString(encodeSign);
        return RSASignUtil.sign(invokingProperties.getAppPrivateKey(), s);
    }

    /**
     * 校验请求签名
     *
     * @param commonReq 公共请求
     * @return 校验成功失败
     */
    private boolean verifyReqSign(CommonReq commonReq) {
        Map<String, String> signVerifyMap = new HashMap<>(16);
        signVerifyMap.put(BaseParameterConstant.APPID, commonReq.getAppId());
        signVerifyMap.put(BaseParameterConstant.METHOD, commonReq.getMethod());
        signVerifyMap.put(BaseParameterConstant.NONCE, commonReq.getNonce());
        signVerifyMap.put(BaseParameterConstant.OBJ_ENCRYPT, commonReq.getObjEncrypt());
        signVerifyMap.put(BaseParameterConstant.TIMESTAMP, commonReq.getTimestamp());
        signVerifyMap.put(BaseParameterConstant.VERSION, commonReq.getVersion());
        String selfSign = RSASignUtil.splicingParamString(signVerifyMap);

        return RSASignUtil.verify(invokingProperties.getPublicKey(), selfSign, commonReq.getSign());
    }

    /**
     * 校验objEncrypt
     *
     * @param aesKey aesKey
     * @param objEncrypt 请求objEncrypt
     * @return 成功失败
     */
    private boolean verifyObjEncrypt(String aesKey, String objEncrypt) {
        try {
            AESUtil.decode(aesKey, objEncrypt);
        } catch (Exception e) {
            //解密出错
            return false;
        }
        return true;
    }
}
