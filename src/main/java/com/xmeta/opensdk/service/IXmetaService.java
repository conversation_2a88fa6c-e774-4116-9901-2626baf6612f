package com.xmeta.opensdk.service;


import com.std.core.pojo.domain.Collection;
import com.std.core.pojo.domain.User;
import com.xmeta.opensdk.model.dto.CommonReq;
import com.xmeta.opensdk.model.vo.CommonResp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/24
 */
public interface IXmetaService {

    /**
     * Xmeta向一级平台验证用户信息
     */
    CommonResp syncUser(CommonReq commonReq);

    /**
     * Xmeta向一级平台根据code获取用户信息（页面嵌入一级平台）
     */
    CommonResp getUserInfoByCode(CommonReq commonReq);

    /**
     * Xmeta向一级平台获取藏品档案下未锁定藏品数量
     */
    CommonResp goodsCount(CommonReq commonReq);

    /**
     * Xmeta向一级平台获取用户未锁定藏品信息列表
     */
    CommonResp goodsList(CommonReq commonReq);

    /**
     * Xmeta向一级平台验证用户藏品
     */
    CommonResp goodsVerify(CommonReq commonReq);

    /**
     * Xmeta向一级平台发起转赠操作
     */
    CommonResp goodsTransfer(CommonReq commonReq);

    /**
     * Xmeta向一级平台转赠结果查询
     */
    CommonResp goodsTransferConfirm(CommonReq commonReq);

    /**
     * XMeta向一级平台发起上架下架藏品
     */
    CommonResp goodsLock(CommonReq commonReq);

    /**
     * XMeta向一级平台发起买家接收地址校验
     */
    CommonResp receiveVerify(CommonReq commonReq);

    /**
     * 一级平台向Xmeta上传商品档案
     */
    void archiveTransfer(List<Collection> collectionList, User operator);
}
