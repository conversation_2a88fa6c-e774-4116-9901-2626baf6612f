####### server #######
server.port=6903
###### system.code #######
system.code=CL-META
####### dev datasource #######
spring.datasource.url=**************************************************************************************************************************************************************
spring.datasource.username=ygzc_dev
spring.datasource.password=Uabypk9WFCHFTy0n
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
####### eureka #######
eureka.client.serviceUrl.defaultZone=***********************************/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetchRegistry=true
####### sms #######
sms_url=http://**************:7902/std-sms/api
###### logger #######
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.std.core.mapper=INFO
####### swagger #######
swagger.host:api.dev.ygzcmeta.com
swagger.enable=true
##### AliOSS  #######
oss.endpoint=sts.cn-hangzhou.aliyuncs.com
oss.accessKeyId=LTAI5tKpVvey4X9o7NLy23mf
oss.accessKeySecret=******************************
oss.roleArn=acs:ram::****************:role/ygzcrole
oss.bucket=fish-meta
oss.bucket.endpoint=https://oss-accelerate.aliyuncs.com
oss.bucket.ossEndpoint=oss-accelerate
oss.bucket.filePath=https://fish-meta.oss-accelerate.aliyuncs.com
###### bsn #######
bsn.plat.code=MTWH
bsn.key=6PmOpkC8mlqkwejkrqwellkqjkeq
bsn.base.url=https://ddc.wanlinsl.com/api
bsn.host.address=iaa1dprgyz3zp402cmr889ejz2wj5uf8n76w23c50y
###### yeepay.config #######
yeepay.bank.parentMerchantNo=***********
yeepay.bank.merchantNo=***********
yeepay.bank.notifyUrl=http://************:6903/callback/public/yeepay_bank
yeepay.bank.fundProcessType=DELAY_SETTLE
yeepay.bank.appKey=app_***********
yeepay.bank.url=https://cash.yeepay.com/cashier/std
yeepay.bank.certType=RSA2048
yeepay.bank.priKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDAV0xBSqQsUQ5AfW6ACEaY53zWScsIa1moVYl+gWx3nPkeGjXASoYNAOyDyKopHTDLhjmDpIahHPUmyjcAEolHXerTFBMcliUWBaUx3pd3GNpS9tEQVLLPprGVzeAZvle4o/nbQBqLSNBF2eZUevFl2MXX28wDM1G3OKRtiMkOsOlE2A/qdAgAGAdGDX4l+GOkt1uIpG0Z6T+nuJZyvbAdlMGJPploQgtPr688XD7tQXUTTWnGcra8XTnpwI2wu7sjP87/P5yMJDdGknpXA3Ggq7r6rcMLf0CHTclMpPkysE/WJZL+N5tvSmCJNY+mDRX0nFaTAGT/mUmnwAdVq537AgMBAAECggEACUbspaWM/5o1PsUOBFzh0tQvgEJ06YYMAAOIKzLg4XAPz/ThrNGrX4sw3TZbhroitV5W/dN8rt6vsFxGhNlgB7Iu9llchp9zUHmbOymKUrM/LIs9EDWVnlBi9l/EtFkq7/vwvDN+VEOB0CyvYFnBtPDcsgmm8GyUP2sa+zGCyROH7f8ZBVdZT1Nf2EOQRF8FyvBp4QBHrpR0nH3RnsktSE0gdSGeOYPUf5FnZFAr+NMwVbRkT5GOfmhYseWCJUhZkEEaBcKCuGHXSoPM8be1GGhd9+KNgEszPN7o1rltyH/W8RFBAClG3G0Ykb4Q9S4YCVCKAt9lVLgSHovHUTSM+QKBgQDTBVhNDgyHSaSzHSkDiSdeuKGs2XPvflcVUVYeeKX0sUH6/E+K672r+wBML9tlG6oI04u1X4bAifAqr1la9dp3yEkHfXVBerOW4VKl9KeT7jy3BpI54wX8sUEgdk8qJTwpe1EWkzErncO4N26OBgvwc+BFaLdkH4rJztZKVIX4XwKBgQDpVqi9PL+51bywbunH9eD13Jl2hthSSn3nNBegqhgM8DfTkYtAcdKGF2LGxExZIW6C7x2frC0mtgr50/Ak0TE1s1kTPbfaEIDucCzXa3UmK8u3E5G+3WxDlmG4LGcZUs8kvhqyPaZiZg9IcaKBZxEAnuVszyRMwDI6T3x/dKUv5QKBgGbkHUj9B6jThKX9Gulpb7PkzJguMzRcYvO3BAm5hBnKcESDz/kJtyLrMRr03sHu+jpxg5Z9LIQm/d6iPyx14f8zvkATsR+rR3PTC+x1XVxo9lu7rYLiKiIq/3lnlnQTUcFcKo6aWIhO1Ya2KIbBdgLZTUoqo/kC1DZQTvZRPj17AoGBANT8uieKBQOvGn39r71LLZ2eUCJFsb1jq4rYHN0VhcCBoKOozW/0tZKaVBpU4c/V2VcAxoySD+Cu+FPUZfZlUMLQw3TxuBj/SYDbucKknplKDrg8z6Nl38XNxcLkHPObh+Lk999MsMxOlpKszwXJO1bSs15fsaWxJefd2ClLIuEVAoGBAI7l72Tm38YlQUqJKLYlJGFJvt4cVn6+CQ8AcLteLJ4H/SRNNgstRWi8D5r5rshcYtYj0FeVEQ0/4Vd9zecCoLUKaNVyfZ0cD6Tla3ctl6kDZ5SqFY3NlQqDu/hfForeaQocO3ZvSctd6frVQzy9OM4WXMmgW+jndH91dY3BYHbE
yeepay.bank.redirectUrl=http://front.dev.ygzcmeta.com/pay/return
yeepay.bank.micro.noticeUrl=http://************:6903/callback/public/yeepay_micro_notice
#channel merchant
yeepay.bank.micro.filePath=/mnt/www/ygzc/file
yeepay.bank.merchant.notify.url=http://************:6903/callback/public/yeepay_user_micro_notice
###### chuanglan.config #######
chuanglan.appId=IY9hHNrK
chuanglan.appKey=bREIfjXu
chuanglan.url=https://api.253.com/open/carriers/carriers-auth
##### REDIS  #######
spring.redis.database=3
spring.redis.host=************
spring.redis.port=6379
spring.redis.password=v2Vp5xP0Ieq73uBx
spring.redis.max-active=600
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.max-wait=60000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.max-idle=300
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.min-idle=0
spring.redis.timeout=6000
spring.devtools.add-properties=false
meta.lock.timeout=30000
###ææé¾éç½®### https://explorer.testnet.bianjie.ai/
avata.url=https://stage.apis.avata.bianjie.ai
avata.apiKey=82V380z360Y6V0g1j4N7G2B439w6X2cZ
avata.apiSecret=w24300Z3t0m6K0L174X7m214H9H6R2EU
avata.systemAddress=iaa1vhn4y28t4xjy3nntf7en5c594yjet7aq0kz3ps
###################TO_DET###################
##### AliOCR #######
ocr.accessKeyId=LTAI5t5cyb64jvKj4L3EKU9W
ocr.accessKeySecret=******************************
ocr.endpoint=ocr-api.cn-hangzhou.aliyuncs.com
###### dingxiang #######
dingxiang.appId=78d86f92f4b64f25bf65800f83e711f3
dingxiang.appSecret=c8cb69aa3119be06014f4c05efd46d7d
dingxiang.apiServer=https://dxvip.dingxiang-inc.com
###### alipay #######
alipay.providerid=2088341304406111
alipay.appid=2021003107627080
alipay.privatekey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC8ytCre3u4aK5dEJ+j8EUEytf3Eocd6eEReP+UDnm1pT+axOHhlG9fc6mucm8l9P+dBEHox3ymItgv6vEZbJeM9qQTbq0ophR7wosccTavMNka8tZN9PNT4R6q2bGSeDKWtNFwaHR6D3ma2MIe5Q3npWWlU42jmmKPE8PGs4eM+p+e3HvdvrHY00Vw9SSboW5IYrWQthbex0lZrS2n/WtqfQe9+IBWtk/TBqR+hLElQ1+3sJjSXpRQFSFSpyRR6oq2vqJvUuQ6jT7vw/Mn68aQANlJ6x1L4KhaaGYXrE71kbgmWBEvALW8JTI1lVntXMC3duW81CxROgnzOzFQBFlHAgMBAAECggEAcr5yBUeP8oztLPMlk+Y8jKUcjbZdGPlFKfF4pOwHXTeKaO6wuwIUjtclkjlLBK7bjPtBc9GLGQ6khrssvl8Z3TsqjgT+iW4nlPCwzCCjM+dkjNR7G/Z8zcZNdVnKa1NSq/ZKNR/Ha0hsiIils1FUGi6UkN8vfvgeXweMPgBvqma7506UHF2yWV1wZjAY04qRoRUwRiYF5tkoMlbVW4YTHrs8unZw2IAj3q3J92jxYWg0U/r6X/VjUw2V+wUHBoDct2UF071+tMZ64oBHBQtaunV56ZAzp+4l1/Ln3GZl9ZIwi2aV09Q6T8fQgUMAKsWBPsU5qD5bvXa9hXS8ZWDkwQKBgQD3w9x/WE4jRJQSfN1Ze/C3OTKlq9yEhmZES8uCVSD4yuGvTE9cr7eKd5yqYKC4ARILEiHrB+jj8aZ3vmQZWYwtRsPE37whYHXDKZ7NDrhn+GqqbYs5RONX0vTVv/zDOQPOfHVBITKM4uPceoLf2wZsD2Ka7bk4YbCPpq4qDOb8iwKBgQDDES0ouFRw0Lagf2AkW8YSJRk5hPhYAhvsP9AJF1nO/K4kOFzraPsx9IUe2IL/ZiYwHU89/mQvuSzyOdpzHb+suBr3hWMfscy6FXWoCzNEpCF9/wPs6+G38FXLlAIAegVqX4tLfPQQqEsnS5RyefSQKzMjmEu4k75dlPtAk83BtQKBgQCXzb9ZnucxIKGAulR8sT2JZVgsvaH86MPb8HyePD5ZcSTZ3O17IoZYJnUsxstyMeW2NsPlVxFQKA0QrOxzw7PpQcRM6bBA2vMMQoHzraM6wK/KABgfXJ/WfYBZPqpFirdUWlkVMJviSlnGCqIJOg8zQjHuLt0fOGuPeFaZmRIlhQKBgEOtp+9i1Nk3cMWKq/6xYK1W9cOBIPDX+nwjP7iHw38PKBQlAggjKlQQtEcZc4nNFR7IbH/jzTrJkpCQBfy+q4vvbFvIibWraI5/d4zQJf+AuqYOXnxK8stwtTOmrHMh4tMfRyLvvXYyDbEaheyZ54JFmr6UzmDB0eCCGhIztcWpAoGASVKj5CJw3hkDgoqp6xuX9s8E1wOBOt0lDxOqlE1oswznyynqo3iy7ENfdtcSaFK2re1CTIlp7WFwa/yoLwFheZShnwbzxFLDbZ+Mcu9UCqjiwneK4AcaNsqAS7N9KyH7zym/FNjeIDJT5yJhPCKBiKrEOWrp9vggB0q/mlcUm9I=
alipay.publickey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlb0/vF2jsLfCQzrs/Fuds+U4ubUzlgN3mqMTU97NSkiw5yo+sajLkW5vEGa4YRRfnGO7QDMh9H+9vF5DcEqHogQZ1w0sH5t180URqJSngcxPQFK5/nzwWqGtk0uYxs4Xq+NAX4einMvLgubfRj1QHytMDWzqqo5iplbjBZTnBsjTN4cJ4c5FISprFC9uTTDFVepgaCdhA4V25RA+ffA35kPdl0V9O1tqumYo2vmJKTYAXEE3BlGyHsW3LfoTUgkFODZZZ8ZHFMpdsykJYIUsEw/WMvYDyOuKRLk5M2i+RNh438/SDu+YgSXIm4B37QmLfXXq6cvY9rAiaO86Z4teQQIDAQAB
alipay.notifyurl=http://api.metahz.com/callback/public/alipay
alipay.returnurl=http://front.metahz.com/pay/paySuccess
alipay.signtype=RSA2
alipay.gateway=https://openapi.alipay.com/gateway.do
alipay.format=json
alipay.charset=UTF-8
###### wechat #######
wechat.h5.appid=wx1c9837d9ba02c433
wechat.h5.secret=890955560ec0ad6d91ceb8263242b8ec
wechat.appid=wxe251665319847d00
wechat.backurl=http://api.dev.metahz.com/callback/public/wechat
wechat.merchant.id=1400666002
wechat.merchant.privatekey=cd3c5e63fa3b2599216e1da5d3c9e66d
wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
###### adapay #######
adapay.appid=app_511dc9b5-63d1-4e5d-9668-0582d29ef447
adapay.backurl=http://************:6903/callback/public/adapay
adapay.merchant.key=1400666002
adapay.api.key=api_live_4a8d8286-e01d-4854-8e89-dcf847ffec17
adapay.mock.api.key=api_test_bc402229-801c-49d2-b995-028833c7cae3
adapay.rsa.private.key=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANV9tuQ8CZmHbgjludGO/3WG42FFllyKlA1gCiYt6Vp9Vg5GiNw6Oey/lbC/82MtGq3dMvZYZTVWt4eocqeZnb0ddXJVLZtCYoy4YNwWXi2JWZc87+dmNxoIjKR4ANCFr719p1W+UerbUsi9B9C7woAHoqhFtIQbB8kxVIZEOC7zAgMBAAECgYBe/MMuA0RmKeqcTNIDWjNxMCXk9pgy7nl3Bf8eA5lq6I8sZNep3MI/AvLwJEd/Hedb6iotjyDgvYeE9T6mMWQLm3CJtvd7XddjLwdo2jrSrbPqa6AAPdghsMsGfI1lhbmqcwCUhkl9uUqJA9TpG9/79jreEuqeWp1+eaIkRWvx6QJBAPJCYU9LymoxYrLEpTQP6dqWoPIT9vr36TNjNpp/tNjw7gpIBdYurD65OoX29Gd7zrM2aVHSSSlS14v38AZALFcCQQDhmZ4u7t4vua8z2Ku03/pMpV/O0cGSuYlWxn23QxbjkAXr2jqFbh/ZthhHC4yzk4Xp2/8yvQ5BNMEe7ODEEHDFAkAH1yAhGdnWL/z6viR+l9lAqslQrFa87pMMh7R3sZRfxQRfLs+Ji/8lFIeRWFm2k6ov4J3t+PlHLhgtvnt1KFSHAkBxBMR6PrCQuDVNg/6BXrPGMhMNSYfwOYLUNhxE8xdEaaKNxYYL0l+icdc3wFF8pSsxPUVb5dp+UC9vjrwLfTEBAkEApOGgdRb8XO3bYKb/5B4qySMLoriaHkJcNlu82r+79tew1nPUpVupg+PXTjDDua5uqUIiLHmbbpKy/+WHeSYjXg==
###### babyPay #######
babyPay.backurl=http://************:6903/callback/public/adapay
babyPay.merchant.no=************
babyPay.pwd=123456
babyPay.url=https://test-api.huishouqian.com/api/acquiring
babyPay.pfxpath=/************_pri.pfx
babyPay.publicpath=/MANDAO_************_pub.cer
###### babyPayBank #######
babyPayBank.merchant.id=*********
babyPayBank.terminal.id=*********
babyPayBank.aes.key=4f66405c4f66405c
babyPayBank.baopay.url=https://vgw.baofoo.com/cutpayment/protocol/backTransRequest
babyPayBank.card.info.url=https://vgw.baofoo.com/biztransfer/product/bankcard/v1/bin/info
babyPayBank.pri.key.pass=*********_286941
babyPayBank.pfxpath=/bfkey_*********@@*********.pfx
babyPayBank.cerpath=/bfkey_*********@@*********.cer
babyPayBank.backurl=http://************:6903/callback/public/baopay_bank
###### channleAction.System #######
channleAction.System.url=http://47.110.55.234:1818/api/core/v1/auction_system
channleAction.System.user.apply.authorization=/public/authorization
channleAction.System.user.collection.info=/public/user_collection_info
channleAction.System.collection.pass.on=/public/pass_on
channleAction.System.roll.out.callback=/public/roll_out_callback
channleAction.System.get.token=/public/get_token
###### xmly #######
xmly.app.key=62bf6d9bc639fc4dfc8bfa2c653aa861
xmly.app.secret=a0bcb65ca8e5fb105bc8f940ae3e2411
xmly.http.url=https://api.ximalaya.com
xmly.grant.type=js_client_credentials
xmly.device.id=app_jssdk_device
xmly.redirect.uri=http://channel.front.dev.metahz.com/home
xmly.h5.domain.uri=http://channel.front.dev.metahz.com
###### RongCloud #######
rongcloud.appKey=bmdehs6pba19s
rongcloud.appSecret=dWf7mCkOgnd
##### xmeta  #######
#å¼æ¾å¹³å°rsa2å¬é¥
xmeta.publicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvTLWk0CizPqKYPqF4SsoVsiIcUjQIkJSR8Nb8Z2ytaBwk1j6epBPPuCmVlcMK68p0U/HVp/ziE+utVXvFbcUofGSszt/nA1l4y+PlqujUdrPsGdLITFGhhFLvSRRc/jSpjUvfRKShDQntcx87Ec7Et+CQgX1PXR1MUGlkGVp08xvj5iNndQfXcBQ/AUvnmA1EI5Z2GxIcrHe4z5qth+DPZH6qg/EywTgoebVycmofM/OgMhYBFiEoVZnb5GavAOqG6Tt3tVClyiBAG/aE96HMibJdSYCqByh+Q/N1SCD2ppCKXR13dJdTK2XJCSpNuIcVQtWgo/g+GHrJaBlzhp0nQIDAQAB
#ä¸çº§å¹³å°èªèº«çrsa2å¬é¥
xmeta.appPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtrFW2hjJ7mP0LUIL/i8B2D1IKjVNEfe8kq6oZTIKMmwJee9erKSsjqbpDXJDMPNTxDJ8HDwzQDfzYuIj2nA/wRxuNRFmQ/yCdbXW8T6N5HxwZKfA5aJC1jzTHYdw5/EsSZDS9SYmKnyrxZGE5WrC4xLHJpxi66J6M9fcrLdSnIMoZKElOOaMpVf3wxeLa3XB85JFOGSSoHFkB+uA+6v4K8yvv1l+F9hGzOjj79xPZ0EXkzOTfrqD593G4YGwJWD9DAMiVcIFm0qCMxAc60mXRuIwokzUqu7zxR1+UQ87Bfrqy93CX6O8nY5NzIu4HzF2OU7edZX3lPieRSwn3DPFhQIDAQAB
#ä¸çº§å¹³å°èªèº«çrsa2ç§é¥
xmeta.appPrivateKey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2sVbaGMnuY/QtQgv+LwHYPUgqNU0R97ySrqhlMgoybAl5716spKyOpukNckMw81PEMnwcPDNAN/Ni4iPacD/BHG41EWZD/IJ1tdbxPo3kfHBkp8DlokLWPNMdh3Dn8SxJkNL1JiYqfKvFkYTlasLjEscmnGLronoz19yst1KcgyhkoSU45oylV/fDF4trdcHzkkU4ZJKgcWQH64D7q/grzK+/WX4X2EbM6OPv3E9nQReTM5N+uoPn3cbhgbAlYP0MAyJVwgWbSoIzEBzrSZdG4jCiTNSq7vPFHX5RDzsF+urL3cJfo7ydjk3Mi7gfMXY5Tt51lfeU+J5FLCfcM8WFAgMBAAECggEAfRpnTSYYcWSE+tD1gXQTIU6lsjGZuyoYOaHcsp9XmDufKZ6fCD4j4/OsN4b9N/IIdJvVnUgkGF+4DQiqYJTpM4YSeciWbQvnElAZBk49wC7ukB7H2vgIKMiyENCEom/mUeIYc8NR81o4DiyeArPfDLv5IBmLK6yxXfr9pFBNA9sop/OCYSzBp0v3K3SGyYMcnyDtsr4emCnfbyXo9QtlQ/RNUiNXHaNHDu7U3TcVKxWSm1I2IhuQvK+0flh9xRcVAgjg6SXtFOhhgj3GzV1g5Se0tE8ywvZjc4c/z93C8ru4j7quysW/On6gxQnpwn8Hv2mumSPpWdoB39IoVdlY6QKBgQD+MnUJMkxuDPE6J+PgT3+j9PpEKdRLnCtOvNwazsVhVzE98bsdaqAFJJq6BmTrGxs5U88nltl0JcBkw97+b1RKymaXlVdc816wI7848i3B/nP+sl1uoebfCnqnpkzurlHKKAOCeBnZA20Vm5EsknsM/vbjmolX6ZITBGsOctBnBwKBgQC3/Q1rDM1IaG4NDGOsw2VrdVC6ra6PVbqxod9sxo0b4e9fArMZoQhzNEpyFUyG7u8+udlC3ejfJ93UfN6rSli2RArNQLnBCwJFQABCXLqBA+ItQnRzxTwFv/irhjsQ7zi1ygqzDj7MBNYTFKiL0jW7GCHYdCa5WhU8VBeE8zrgEwKBgQCb3mC5hOgsyy7X1EH894JzDPKw/V06B8befxwQLB4H9CkzrJhqD0mUHb/qtaUILRTicCukQPGSMNvqbdi895MeUUHbTS3Gx0yWgTTuPEImpFqIIOL9xbUFqy1Ow8CE0stARIzyYaan1Vv8VeoNBL0DWQwWwFnbWFPD6X+j2m9D3QKBgC1ZyKbsXuVMKGQ6+46oEyWroxyAlxhpjz0do07SmhGXXUNUDQVyNfz/lKIOMfDIkFwrvyvGOxJWZ01rAg/CoZ87olX5cE/vAL5FCktRQ/9tD6EMUlv+786TBR5yVbjU3mugw4ZjKoNrZcEKRIlCZVAaoK0T5D+nvrhCGhIBSi/VAoGAY5JUjjg9j4pIoYxYZ5pjK9vQm867msLjECakNQDc7rMuzkqaeZLAmt95/ABZxOjAwevyc1/+LwBmrATyfLd6MKXq9OyggS0u7SJK04BEibIFPAUQZDzHWnWsBzl8XT0j2ezn8BIbhjcBHMpi88ShbCXgkd6Md7iQ/cyYDydYJfU=
#å¼æ¾å¹³å°èµäºçaes
xmeta.aesKey=EtwaF6+cDYHezmrPIlfvyQ==
#å¼æ¾å¹³å°èµäºçappId
xmeta.appId=0a429c7cd0c94885b241f77f9c997dba
#å¼æ¾å¹³å°ççæ¬å·
xmeta.version=1.2.0
#å¼æ¾å¹³å°çapiè¯·æ±å°å
xmeta.url=https://dev-open-api.x-metash.cn/gateway/forward/
#å¼æ¾å¹³å°çææå°å
xmeta.authUrl=https://dev-nft-mall-ui.x-metash.cn/excenter
##### xmeta method  #######
#ä¸çº§å¹³å°åXmetaä¸ä¼ ååæ¡£æ¡
xmeta.method.archiveGoodsTransfer=xmeta.mall.archive.goods.transfer
#Xmetaåä¸çº§å¹³å°éªè¯ç¨æ·ä¿¡æ¯
xmeta.method.userVerify=xmeta.mall.user.verify
#Xmetaåä¸çº§å¹³å°è·åèåæ¡£æ¡ä¸æªéå®èåæ°é
xmeta.method.archiveGoodsCount=xmeta.mall.archive.goodsCount
#Xmetaåä¸çº§å¹³å°è·åç¨æ·æªéå®èåä¿¡æ¯åè¡¨
xmeta.method.goodsList=xmeta.mall.goods.list
#Xmetaåä¸çº§å¹³å°éªè¯ç¨æ·èå
xmeta.method.goodsVerify=xmeta.mall.goods.verify
#Xmetaåä¸çº§å¹³å°åèµ·ä¸æ¶ä¸æ¶èå
xmeta.method.goodsLock=xmeta.mall.goods.lock
#Xmetaåä¸çº§å¹³å°åèµ·è½¬èµ æä½
xmeta.method.goodsTransfer=xmeta.mall.goods.transfer
#Xmetaåä¸çº§å¹³å°è½¬èµ ç»ææ¥è¯¢
xmeta.method.goodsTransferConfirm=xmeta.mall.goods.transfer.confirm
#Xmetaåä¸çº§å¹³å°åèµ·ä¹°å®¶æ¥æ¶å°åæ ¡éª
xmeta.method.buyerReceiveVerify=xmeta.mall.buyer.receive.verify
#Xmetaåä¸çº§å¹³å°åèµ·ä¹°å®¶æ¥æ¶å°åæ ¡éª
xmeta.method.userInfo=xmeta.mall.user.info
###################TO_DET###################
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://************:6904/xxl-job-admin
### xxl-job, access token
xxl.job.accessToken=
### xxl-job executor appname
xxl.job.executor.appname=meta-job-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=6309
### xxl-job executor log-path
xxl.job.executor.logpath=/mnt/www/chain_play/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30
### \u706B\u5E01\u4EA4\u6613\u5206\u5E03\u5F0F\u9501\u8D85\u65F6\u65F6\u95F4\uFF08\u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF09
#-------------------------------------------------------------------------------
# Nacos\u914D\u7F6E
#-------------------------------------------------------------------------------
#nacos:
#config:
#server-addr: ***************:8848
#type: yaml
#namespace: a3029897-dd3d-4205-aae5-28e6b0e425dc
#bootstrap:
#enable: true
#log-enable: true
#context-path: nacos
#data-id: goldMqProducer
#auto-refresh: true
#####mq###
#spring.rabbitmq.host=***************
#spring.rabbitmq.port=5672
#spring.rabbitmq.username=admin
#spring.rabbitmq.password=ASD123
#####jpush###
jpush.customer.appKey=a9911b8b1b8f78e79e56b4e3
jpush.customer.masterSecret=44cfeafad95aca26a3618aba
jpush.apns.production=0
##-----------?????-------------
#schedule.doPeriodStartTrigger=1
#schedule.doPeriodEndTrigger=1
#schedule.doFirstMarketOrderDesignCollecction=1
#schedule.doSynPayRecordTrigger=1
#schedule.doStatUserBizData=1
#schedule.doJpushSend=1
#schedule.doUnFreezeTrigger=1
#schedule.doFirstMarketUnFreezeTrigger=1
#schedule.doPayBackTrigger=1
#schedule.doQueryPayBackTrigger=1
#schedule.doDivideTrigger=1
#schedule.doUserDivideTrigger=1
#schedule.doCheckDivideResultTrigger=1
#schedule.doInvitationActivityTrigger=1
#schedule.doCollectionStatisticsTrigger=1
#schedule.doSynchronousYeepaySettleRecordTrigger=1
#schedule.doPeriodSubscriptionJpushSendTrigger=1
#schedule.doCompanySmsSendTrigger=1
#schedule.doPublishNft=0
#schedule.doSyncNftTrade=0
#schedule.doFishSailFishing=1
#schedule.doFishSailFishingEnd=1
#schedule.doFishUserLeaveMeta=1
#
schedule.doPeriodStartTrigger=0
schedule.doPeriodEndTrigger=0
schedule.doFirstMarketOrderDesignCollecction=0
schedule.doSynPayRecordTrigger=0
schedule.doStatUserBizData=0
schedule.doJpushSend=0
schedule.doUnFreezeTrigger=0
schedule.doFirstMarketUnFreezeTrigger=0
schedule.doPayBackTrigger=0
schedule.doQueryPayBackTrigger=0
schedule.doDivideTrigger=0
schedule.doUserDivideTrigger=0
schedule.doCheckDivideResultTrigger=0
schedule.doInvitationActivityTrigger=0
schedule.doCollectionStatisticsTrigger=0
schedule.doSynchronousYeepaySettleRecordTrigger=0
schedule.doPeriodSubscriptionJpushSendTrigger=0
schedule.doCompanySmsSendTrigger=0
schedule.doPublishNft=0
schedule.doSyncNftTrade=0
schedule.doFishSailFishing=0
schedule.doFishSailFishingEnd=0
schedule.doFishUserLeaveMeta=0
#æ ç¨åè½å®æ¶ä»»å¡
schedule.doPeriodDrawStrawsEndTrigger=0
schedule.doDealDrawStrawsJoinRecordTrigger=0
schedule.doPayCancelBuyoutProducts=0
schedule.doBuyProductsTimeoutDownTrigger=0
schedule.doAuctionStartTrigger=0
schedule.doPayCancelAuctionProducts=0
schedule.doSynAuctionBondRefundTrigger=0
schedule.doChallengeFinalDeal=0
schedule.doChallengeFinal=0
schedule.doChangeStatusChangeTrigger=0
schedule.doIncomeSettleTrigger=0
schedule.doStatisticDailyTrigger=0
schedule.doBlindSellFinal=0
schedule.doBlindSellFinalDeal=0
schedule.doSendUpgradeInfoTrigger=0
schedule.doPeriodJoinRecordMigration=0
schedule.doBaopaySmsCodeFailure=0
schedule.doCollectionDetailPassOn=0
schedule.doPeriodAuctionCancelFreezeTrigger=0
schedule.doPeriodAuctionEndFreezeTrigger=0
schedule.doPeriodAuctionBondReturnFreezeTrigger=0
schedule.doPeriodAuctionDefaultFreezeTrigger=0
schedule.doPeriodAuctionDividesTrigger=0
schedule.doIntegralGoodsDistributionTrigger=0
schedule.doIntegralGoodsEndTrigger=0
schedule.doIntegralGoodsActivityCloseTrigger=0
schedule.doMetaPitSellerEndTrigger=0
schedule.doDegressionAuctionTrigger=0
schedule.doLotteryActivityEndTrigger=0
schedule.doCulturalchainDealRecordTrigger=0
schedule.doChipCollectionRecordDistributionTrigger=0
schedule.doMetaRobotCalculationRecordTrigger=0
schedule.doMetaTicketRecordEndTrigger=0
schedule.doYaoMilletConfigCreate=0

