{"Read Me": {"使用说明": "key为异常代码，value为异常提示", "使用规则": "key采用9位，前4位用于区分类型，后5位用于区分具体异常", "返回字段": "key:errorCode value:errorMsg"}, "##通用异常": "通用异常（errorCode ）", "500003": "The record for %s's ID=%s does not exist", "##系统权限业务异常": "系统基础功能业务异常(AUTH*****)", "AUTH00001": "User password error ", "AUTH00002": "The login account already exists ", "AUTH00003": "The login name does not exist ", "AUTH00004": "Sorry, you have been logged off, please contact the administrator!", "AUTH00005": "The phone number already exists ", "AUTH00006": "Mailbox already exists ", "AUTH00007": "Please set the password first.", "AUTH00008": "Qniuyun picture parameters abnormal ", "AUTH00010": "The phone number doesn't exist.", "AUTH00011": "account close", "AUTH00012": "account close by system", "##系统核心业务异常": "系统基础功能业务异常(CORE*****)", "CORE00000": "【%s】", "CORE00001": "Insufficient account available balance ", "CORE00002": "The withdrawal amount shall be greater than the handling fee ", "CORE00003": "The order is not in the auditable state ", "CORE00004": "The order is not in the state of payment ", "CORE00005": "The recharge amount shall not be less than 0", "CORE00006": "Account number abnormal ", "CORE00007": "This thawing will cause the frozen amount of the account to be less than 0", "CORE00008": "The user has not set the transaction password ", "CORE00009": "Transaction password error ", "CORE00010": "The phone number doesn't exist.", "CORE00011": "The phone number already exists ", "CORE00012": "Login password error ", "CORE00013": "Sorry, you have been logged off, please contact the administrator!", "CORE00014": "The minimum withdrawal amount is: %s", "CORE00015": "The maximum withdrawal amount is: %s", "CORE00016": "The withdrawal step size is: %s", "CORE00017": "The component module requested an exception, check the url configuration: [%s】", "CORE00018": "Wrong type of card ", "CORE00019": "The payment must not be less than 0", "CORE00020": "No refunds for orders not paid ", "##业务核心业务异常": "业务基础功能业务异常(AIS*****)", "AIS00001": "The current data is not in an operational state ", "AIS00002": "Unsupported operations ", "AIS00003": "Error receiving file batch ", "AIS00004": "Case %s has no shipping address and cannot be shipped ", "AIS00005": "Operation failed, material does not exist ", "AIS00006": "Operation failed, operation number error ", "AIS00007": "Operation failed, operation amount error ", "AIS00008": "If the operation fails, the credit amount is reduced to be greater than the credit amount granted ", "AIS00009": "Operation failed, please enter the correct batch and the number of sets per batch ", "AIS00010": "Operation failed, please complete all projects before submitting ", "AIS00011": "Operation failed, the hold batch exists ", "AIS00012": "The operation failed, the information of the case [%s] batch is wrong, the current batch is [%s]", "AIS00013": "Operation failed, all batches have been shipped ", "AIS00014": "The operation failed, the treatment plan has not been developed.", "AIS00015": "Operation failed, the case has been fully worn ", "AIS00016": "Operation failure, wrong braces, currently should wear the [%s] ", "AIS00017": "Operation failed, please deliver the silicon rubber film before submitting.", "AIS00018": "Operation failed, please fill in the information problem list ", "AIS00019": "Operation failed, the problem item already exists ", "AIS00020": "Operation failed, please fill in the complete address information ", "AIS00021": "Operation failed, please fill in the number ", "AIS00022": "Operation failed, please fill in the conditional amount ", "AIS00023": "Operation failed, validity error ", "AIS00024": "If the operation fails, it can only be issued to the patient.", "AIS00025": "Failed operations, unable to establish a relationship with oneself ", "AIS00026": "Operation failed, the product has been removed from the shelf ", "AIS00027": "Operation failed, coupon cannot be used ", "AIS00028": "Operation failed, coupon did not reach the reduced amount ", "AIS00029": "Operation failed, the user has an order ", "AIS00030": "If the operation fails, only the patient can place the order.", "AIS00031": "Alipay payment is abnormal, please contact the customer service ", "AIS00032": "Please log in WeChat before you pay ", "AIS00033": "Operation failed, the braces have not been received ", "AIS00034": "Operation failed, please complete the logistics receipt first.", "AIS00035": "Operation failed, please complete all braces ", "AIS00036": "Operation failed, the current batch has been worn out, please ship again.", "AIS00037": "Operation failed, the maximum length of patient name was %s", "GOLD0001": "Operation failed, please bind apikey"}