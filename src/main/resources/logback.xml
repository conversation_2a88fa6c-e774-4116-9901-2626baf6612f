<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="false" scanPeriod="30 seconds">

    <!-- 输出到控制台 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%level] - %m%n</pattern>
        </encoder>
    </appender>


    <!-- 按日期滚动日志 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志存放位置 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--            <fileNamePattern>/var/log/www/app/meta-core-biz/%d{yyyy-MM-dd}.log</fileNamePattern>-->
            <fileNamePattern>logs/%d{yyyy-MM-dd}.log</fileNamePattern>


            <!-- 保存30天历史 -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss} [%level] - %m%n</pattern>
        </encoder>

        <!--    <triggeringPolicy-->
        <!--            class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
        <!--      <maxFileSize>2048MB</maxFileSize>-->
        <!--    </triggeringPolicy>-->
    </appender>

    <!-- 配置好前面对应的appender -->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>