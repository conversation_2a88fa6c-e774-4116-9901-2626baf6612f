####### \u73AF\u5883 #######
spring.profiles.active=test
#\u8BF7\u6C42\u5904\u7406\u7684\u8D85\u65F6\u65F6\u95F4
ribbon.ReadTimeout:60000
#\u8BF7\u6C42\u8FDE\u63A5\u7684\u8D85\u65F6\u65F6\u95F4
ribbon.ConnectTimeout:60000
####### \u5FAE\u670D\u52A1\u8FDE\u63A5\u8D85\u65F6\u65F6\u95F4 #######
spring.application.name=chain-play-core
eureka.instance.preferIpAddress=true
hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=30000
####### mapper #######
mybatis.type-aliases-package=com.std.core.domain
mybatis.mapper-locations=classpath:mapper/*.xml
mapper.identity=mysql
###### system #######
default.role.c=300000000000000000
default.role.cps=400000000000000000
default.role.bp=490080063174483969
default.role.company.bp=572517123390447616
default.role.registered.company.bp=584566916442759168
#####\u90AE\u4EF6\u914D\u7F6E(\u53D1\u4EF6\u4EBA\uFF09######
spring.jackson.date-format=yyyy-MM-dd
spring.jackson.time-zone=GMT+8
spring.jackson.serialization.write-dates-as-timestamps=false
# http
http.maxTotal=200
http.maxPerRouter=100
http.connectionTimeout=3000
http.connectionRequestTimeout=2000
http.readTime=15000
