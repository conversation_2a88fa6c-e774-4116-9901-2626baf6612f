<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodChannelWordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodChannelWord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="word" jdbcType="VARCHAR" property="word"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.period_id
        , t.channel_id
        , t.word
        , t.status
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null and periodId != '' ">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="channelId != null and channelId != '' ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="word != null and word != '' ">
                AND binary t.word = #{word, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodChannelWord">
        insert into nft_period_channel_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="periodId != null">
                period_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="word != null and word != '' ">
                word,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=BIGINT},
            </if>
            <if test="word != null and word != '' ">
                #{word,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_period_channel_word
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.periodId != null">
                    period_id,
                </if>
                <if test="item.channelId != null">
                    channel_id,
                </if>
                <if test="item.word != null and item.word != '' ">
                    word,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.updater != null ">
                    updater,
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    updater_name,
                </if>
                <if test="item.updateDatetime != null ">
                    update_datetime,
                </if>
                <if test="item.remark != null and item.remark != '' ">
                    remark,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.periodId != null">
                    #{item.periodId,jdbcType=BIGINT},
                </if>
                <if test="item.channelId != null">
                    #{item.channelId,jdbcType=BIGINT},
                </if>
                <if test="item.word != null and item.word != '' ">
                    #{item.word,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updater != null">
                    #{item.updater,jdbcType=BIGINT},
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    #{item.updaterName,jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    #{item.updateDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != '' ">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_period_channel_word
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByPeriodId">
        delete
        from nft_period_channel_word
        where period_id = #{periodId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodChannelWord">
        update nft_period_channel_word
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId,jdbcType=BIGINT},
            </if>
            <if test="word != null and word != '' ">
                word = #{word,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_channel_word t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodChannelWord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_channel_word t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>