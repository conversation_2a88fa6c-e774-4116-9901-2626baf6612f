<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.DropRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.DropRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_kind" jdbcType="VARCHAR" property="createrKind"/>
        <result column="creatr_name" jdbcType="VARCHAR" property="creatrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.order_id
        , t.collection_id
        , t.type
        , t.status
        , t.mobile
        , t.user_id
        , t.remark
        , t.creater
        , t.creater_kind
        , t.creatr_name
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile like concat('%', #{mobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrKind != null and createrKind != '' ">
                AND t.creater_kind = #{createrKind, jdbcType=VARCHAR}
            </if>
            <if test="creatrName != null and creatrName != '' ">
                AND t.creatr_name = #{creatrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.DropRecord">
        insert into nft_drop_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="orderId != null ">
                order_id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="mobile != null and mobile != '' ">
                mobile,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrKind != null and createrKind != '' ">
                creater_kind,
              </if>
              <if test="creatrName != null and creatrName != '' ">
                creatr_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrKind != null and createrKind != '' ">
                #{createrKind,jdbcType=VARCHAR},
            </if>
            <if test="creatrName != null and creatrName != '' ">
                #{creatrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_drop_record
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.orderId != null ">
                    order_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.type != null and item.type != '' ">
                    type,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.mobile != null and item.mobile != '' ">
                    mobile,
                </if>
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.remark != null and item.remark != '' ">
                    remark,
                </if>
                <if test="item.creater != null ">
                    creater,
                </if>
                <if test="item.createrKind != null and item.createrKind != '' ">
                    creater_kind,
                </if>
                <if test="item.creatrName != null and item.creatrName != '' ">
                    creatr_name,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.orderId != null">
                    #{item.orderId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.type != null and item.type != '' ">
                    #{item.type,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.mobile != null and item.mobile != '' ">
                    #{item.mobile,jdbcType=VARCHAR},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.remark != null and item.remark != '' ">
                    #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.creater != null">
                    #{item.creater,jdbcType=BIGINT},
                </if>
                <if test="item.createrKind != null and item.createrKind != '' ">
                    #{item.createrKind,jdbcType=VARCHAR},
                </if>
                <if test="item.creatrName != null and item.creatrName != '' ">
                    #{item.creatrName,jdbcType=VARCHAR},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_drop_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.DropRecord">
        update nft_drop_record
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrKind != null and createrKind != '' ">
                creater_kind = #{createrKind,jdbcType=VARCHAR},
            </if>
            <if test="creatrName != null and creatrName != '' ">
                creatr_name = #{creatrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_drop_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.DropRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_drop_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>