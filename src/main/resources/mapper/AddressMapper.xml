<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.AddressMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Address">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="is_default" jdbcType="VARCHAR" property="isDefault"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="province_id" jdbcType="BIGINT" property="provinceId"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="city_id" jdbcType="BIGINT" property="cityId"/>
        <result column="county" jdbcType="VARCHAR" property="county"/>
        <result column="county_id" jdbcType="BIGINT" property="countyId"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="post_time" jdbcType="TIMESTAMP" property="postTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.name
        , t.user_type
        , t.is_default
        , t.province
        , t.province_id
        , t.city
        , t.city_id
        , t.county
        , t.county_id
        , t.address
        , t.phone
        , t.status
        , t.post_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userType != null and userType != ''  ">
                AND t.user_type = #{userType, jdbcType=VARCHAR}
            </if>
            <if test="isDefault != null and isDefault != ''  ">
                AND t.is_default = #{isDefault, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''  ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="province != null and province != '' ">
                AND t.province= #{province, jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != '' ">
                AND t.city= #{city, jdbcType=VARCHAR}
            </if>
            <if test="county != null and county != '' ">
                AND t.county= #{county, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Address">
        insert into tstd_address
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="userType != null and userType != '' ">
                user_type,
            </if>
            <if test="isDefault != null and isDefault != '' ">
                is_default,
            </if>
            <if test="province != null and province != '' ">
                province,
            </if>
            <if test="provinceId != null ">
                province_id,
            </if>
            <if test="city != null and city != '' ">
                city,
            </if>
            <if test="cityId != null ">
                city_id,
            </if>
            <if test="county != null and county != '' ">
                county,
            </if>
            <if test="countyId != null ">
                county_id,
            </if>
            <if test="address != null and address != '' ">
                address,
            </if>
            <if test="phone != null and phone != '' ">
                phone,
            </if>
            <if test="status != null and status != ''  ">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="userType != null and userType != '' ">
                #{userType,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null and isDefault != '' ">
                #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="city != null and city != '' ">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=BIGINT},
            </if>
            <if test="county != null and county != '' ">
                #{county,jdbcType=VARCHAR},
            </if>
            <if test="countyId != null">
                #{countyId,jdbcType=BIGINT},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != '' ">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_address
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Address">
        update tstd_address
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="userType != null and userType != '' ">
                user_type = #{userType,jdbcType=VARCHAR},
            </if>
            <if test="isDefault != null and isDefault != '' ">
                is_default = #{isDefault,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                province_id = #{provinceId,jdbcType=BIGINT},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                city_id = #{cityId,jdbcType=BIGINT},
            </if>
            <if test="county != null and county != '' ">
                county = #{county,jdbcType=VARCHAR},
            </if>
            <if test="countyId != null">
                county_id = #{countyId,jdbcType=BIGINT},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="phone != null and phone != '' ">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_address t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Address"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_address t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="select" parameterType="com.std.core.pojo.domain.Address"
            resultMap="BaseResultMap">
        select
        t.id
        , t.user_id
        , t.name
        , t.user_type
        , t.is_default
        , t.province
        , t.province_id
        , t.city
        , t.city_id
        , t.county
        , t.county_id
        , t.address
        , t.phone
        , t.status
        , t.post_time
        from tstd_address t
        left join tsys_user tu on t.user_id=tu.id
        <include refid="where_condition"/>
        and tu.kind="C"
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectUserDefaultAddr" resultType="com.std.core.pojo.domain.Address">
        select
        <include refid="Base_Column_List"/>
        from tstd_address t
        left join tsys_user u on t.user_id = u.id
        where t.user_id = #{userId,jdbcType=BIGINT}
        and t.is_default = #{isDefault,jdbcType=VARCHAR}
    </select>
</mapper>