<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityUserRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivityUserRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_type" jdbcType="VARCHAR" property="activityType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_referee" jdbcType="BIGINT" property="userReferee"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="activityName" jdbcType="VARCHAR" property="activityName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.channel_id
        , t.activity_id
        , t.activity_type
        , t.user_id
        , t.user_referee
        , t.status
        , t.create_datetime
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="activityType != null and activityType != '' ">
                AND t.activity_type = #{activityType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userRefereeKeywords != null and userRefereeKeywords != '' ">
                AND t.user_referee in (select id from (select id from tsys_user where
                mobile like concat('%', #{userRefereeKeywords, jdbcType=VARCHAR},'%') OR
                id_no like concat('%', #{userRefereeKeywords, jdbcType=VARCHAR},'%') OR
                real_name like concat('%', #{userRefereeKeywords, jdbcType=VARCHAR},'%'))a
                )
            </if>
            <if test="userReferee != null">
                AND t.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND ta.company_id = #{companyId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivityUserRecord"
            useGeneratedKeys="true" keyProperty="id">
        insert into lxa_invitation_activity_user_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="activityType != null and activityType != '' ">
                activity_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="userReferee != null ">
                user_referee,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityType != null and activityType != '' ">
                #{activityType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userReferee != null">
                #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from lxa_invitation_activity_user_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivityUserRecord">
        update lxa_invitation_activity_user_record
        <set>
            <if test="channelId != null">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityType != null and activityType != '' ">
                activity_type = #{activityType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userReferee != null">
                user_referee = #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateStatus">
        update lxa_invitation_activity_user_record
        <set>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivityUserRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_record t
        where t.user_id = #{userId,jdbcType=BIGINT}
        and t.activity_type=#{activityType}
        and t.channel_id=#{channelId}
        and t.status='0';
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_record t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name activityName
        from lxa_invitation_activity_user_record t
        inner join lxa_invitation_activity ta on ta.id=t.activity_id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.InvitationActivityUserRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name activityName
        from lxa_invitation_activity_user_record t
        inner join lxa_invitation_activity ta on ta.id=t.activity_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>