<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityRegisteredCollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivityRegisteredCollection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="series_id" jdbcType="BIGINT" property="seriesId"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.series_id
        , t.activity_id
        , t.collection_id
        , t.collection_name
        , t.cover_file_url
        , t.quantity
        , t.total_quantity
        , t.remain_quantity
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="seriesId != null">
                AND t.series_id = #{seriesId, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name = #{collectionName, jdbcType=VARCHAR}
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                AND t.cover_file_url = #{coverFileUrl, jdbcType=VARCHAR}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivityRegisteredCollection">
        insert into lxa_invitation_activity_registered_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="seriesId != null ">
                series_id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="collectionName != null and collectionName != '' ">
                collection_name,
              </if>
              <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
              </if>
            <if test="quantity != null">
                quantity ,
            </if>
              <if test="totalQuantity != null ">
                total_quantity,
              </if>
              <if test="remainQuantity != null ">
                remain_quantity,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="seriesId != null">
                #{seriesId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity, jdbcType=INTEGER},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from lxa_invitation_activity_registered_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByActivity">
        delete from lxa_invitation_activity_registered_collection
        where activity_id = #{activityId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteBySeries">
        delete from lxa_invitation_activity_registered_collection
        where series_id = #{seriesId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivityRegisteredCollection">
        update lxa_invitation_activity_registered_collection
        <set>
            <if test="seriesId != null">
                series_id = #{seriesId,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity, jdbcType=INTEGER},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_registered_collection t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivityRegisteredCollection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_registered_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_registered_collection t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>