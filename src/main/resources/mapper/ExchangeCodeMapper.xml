<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ExchangeCodeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ExchangeCode">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="exchange_no" jdbcType="VARCHAR" property="exchangeNo"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>

        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="exchange_user_id" jdbcType="BIGINT" property="exchangeUserId"/>
        <result column="exchange_time" jdbcType="TIMESTAMP" property="exchangeTime"/>

        <result column="export_batch_id" jdbcType="INTEGER" property="exportBatchId"/>
        <result column="export_flag" jdbcType="VARCHAR" property="exportFlag"/>
        <result column="export_time" jdbcType="TIMESTAMP" property="exportTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.exchange_no
        , t.collection_id
        , t.company_id
        , t.quantity
        , t.lock_time
        , t.type
        , t.status
        , t.creator
        , t.creator_name
        , t.create_time
        , t.updater
        , t.updater_name
        , t.update_time
        , t.exchange_user_id
        , t.exchange_time
        , t.export_batch_id
        , t.export_flag
        , t.export_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="exchangeNo != null and exchangeNo != '' ">
                AND binary t.exchange_no = #{exchangeNo, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creator != null">
                AND t.creator = #{creator, jdbcType=BIGINT}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="exchangeUserId != null">
                AND t.exchange_user_id = #{exchangeUserId, jdbcType=BIGINT}
            </if>
            <if test="exchangeTime != null">
                AND t.exchange_time = #{exchangeTime, jdbcType=TIMESTAMP}
            </if>
            <if test="exportBatchId != null">
                AND t.export_batch_id = #{exportBatchId, jdbcType=INTEGER}
            </if>
            <if test="exportFlag != null and exportFlag != '' ">
                AND t.export_flag = #{exportFlag, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ExchangeCode">
        insert into nft_exchange_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="exchangeNo != null and exchangeNo != ''  ">
                exchange_no,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="lockTime != null ">
                lock_time,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="creator != null ">
                creator,
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="exchangeUserId != null ">
                exchange_user_id,
            </if>
            <if test="exchangeTime != null ">
                exchange_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="exchangeNo != null and exchangeNo != ''  ">
                #{exchangeNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId, jdbcType=BIGINT},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="lockTime != null ">
                #{lockTime,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="exchangeUserId != null">
                #{exchangeUserId,jdbcType=BIGINT},
            </if>
            <if test="exchangeTime != null">
                #{exchangeTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_exchange_code
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ExchangeCode">
        update nft_exchange_code
        <set>
            <if test="exchangeNo != null and exchangeNo != '' ">
                exchange_no = #{exchangeNo,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                creator = #{creator,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="exchangeUserId != null">
                exchange_user_id = #{exchangeUserId,jdbcType=BIGINT},
            </if>
            <if test="exchangeTime != null">
                exchange_time = #{exchangeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="exportBatchId != null">
                export_batch_id = #{exportBatchId,jdbcType=INTEGER},
            </if>
            <if test="exportFlag != null and exportFlag != '' ">
                export_flag = #{exportFlag, jdbcType=VARCHAR},
            </if>
            <if test="exportTime != null">
                export_time = #{exportTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_exchange_code t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_exchange_code t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ExchangeCode"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_exchange_code t
        <if test="keywords != null and keywords != '' ">
        INNER JOIN tsys_user tu on t.exchange_user_id = tu.id
        </if>
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectExportByCondition" parameterType="com.std.core.pojo.domain.ExchangeCode"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_exchange_code t where export_flag = #{exportFlag} and collection_id = #{collectionId} order by id limit #{exportSize}
    </select>

    <select id="selectCountByCondition" parameterType="com.std.core.pojo.domain.ExchangeCode"
            resultType="java.lang.Long">
        select count(1) from nft_exchange_code t
        <include refid="where_condition"/>
    </select>

    <select id="selectMaxBatchIdByCondition" parameterType="com.std.core.pojo.domain.ExchangeCode"
            resultType="java.lang.Integer">
        select ifnull(max(export_batch_id),0) from nft_exchange_code t
        <include refid="where_condition"/>
    </select>


</mapper>