<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.DictMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Dict">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="parent_key" jdbcType="VARCHAR" property="parentKey"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="group_no" jdbcType="INTEGER" property="groupNo"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.parent_key
        , t.key
        , t.value
        , t.group_no
        , t.order_no
        , t.updater
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="parentKey != null and parentKey != '' ">
                AND t.parent_key = #{parentKey, jdbcType=VARCHAR}
            </if>
            <if test="key != null and key != '' ">
                AND t.key = #{key, jdbcType=VARCHAR}
            </if>
            <if test="value != null and value != '' ">
                AND t.value = #{value, jdbcType=VARCHAR}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>

            <if test="parentKeyList != null and parentKeyList.size() != 0 ">
                AND t.parent_key in
                <foreach item="item" index="index" collection="parentKeyList" open="(" separator=","
                  close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Dict">
        insert into tsys_dict
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="parentKey != null and parentKey != '' ">
                parent_key,
            </if>
            <if test="key != null and key != '' ">
                `key`,
            </if>
            <if test="value != null and value != '' ">
                `value`,
            </if>
            <if test="groupNo != null ">
                group_no,
            </if>
            <if test="orderNo != null and orderNo != '' ">
                order_no,
            </if>
            <if test="updater != null and updater != '' ">
                updater,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="parentKey != null and parentKey != '' ">
                #{parentKey,jdbcType=VARCHAR},
            </if>
            <if test="key != null and key != '' ">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null and value != '' ">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="groupNo != null">
                #{groupNo,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null and orderNo != '' ">
                #{orderNo,jdbcType=BIGINT},
            </if>
            <if test="updater != null and updater != '' ">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tsys_dict
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Dict">
        update tsys_dict
        <set>
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="parentKey != null and parentKey != '' ">
                parent_key = #{parentKey,jdbcType=VARCHAR},
            </if>
            <if test="key != null and key != '' ">
                `key` = #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null and value != '' ">
                `value` = #{value,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo != '' ">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
      resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_dict t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Dict"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_dict t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>