<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserPaymentAccountInfoMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserPaymentAccountInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="divide_flag" jdbcType="VARCHAR" property="divideFlag"/>
        <result column="plat_divide_rate" jdbcType="DECIMAL" property="platDivideRate"/>
        <result column="legal_licence_type" jdbcType="VARCHAR" property="legalLicenceType"/>
        <result column="legal_licence_no" jdbcType="VARCHAR" property="legalLicenceNo"/>
        <result column="legal_licence_front_url" jdbcType="VARCHAR" property="legalLicenceFrontUrl"/>
        <result column="legal_licence_back_url" jdbcType="VARCHAR" property="legalLicenceBackUrl"/>
        <result column="legal_real_name" jdbcType="VARCHAR" property="legalRealName"/>
        <result column="legal_mobile" jdbcType="VARCHAR" property="legalMobile"/>
        <result column="settle_card_no" jdbcType="VARCHAR" property="settleCardNo"/>
        <result column="settle_bank_code" jdbcType="VARCHAR" property="settleBankCode"/>
        <result column="settle_bank_name" jdbcType="VARCHAR" property="settleBankName"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="request_no" jdbcType="VARCHAR" property="requestNo"/>
        <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.name
        , t.short_name
        , t.introduce
        , t.divide_flag
        , t.plat_divide_rate
        , t.legal_licence_type
        , t.legal_licence_no
        , t.legal_licence_front_url
        , t.legal_licence_back_url
        , t.legal_real_name
        , t.legal_mobile
        , t.settle_card_no
        , t.settle_bank_code
        , t.settle_bank_name
        , t.province
        , t.city
        , t.district
        , t.address
        , t.request_no
        , t.merchant_no
        , t.status
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userIdList != null and userIdList.size > 0 ">
                AND t.user_id in
                <foreach item="item" index="index" collection="userIdList" open="(" separator=","
                        close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="shortName != null and shortName != '' ">
                AND t.short_name = #{shortName, jdbcType=VARCHAR}
            </if>
            <if test="introduce != null and introduce != '' ">
                AND t.introduce = #{introduce, jdbcType=VARCHAR}
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                AND t.divide_flag = #{divideFlag, jdbcType=VARCHAR}
            </if>
            <if test="platDivideRate != null">
                AND t.plat_divide_rate = #{platDivideRate, jdbcType=DECIMAL}
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                AND t.legal_licence_type = #{legalLicenceType, jdbcType=VARCHAR}
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                AND t.legal_licence_no = #{legalLicenceNo, jdbcType=VARCHAR}
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                AND t.legal_licence_front_url = #{legalLicenceFrontUrl, jdbcType=VARCHAR}
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                AND t.legal_licence_back_url = #{legalLicenceBackUrl, jdbcType=VARCHAR}
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                AND t.legal_real_name = #{legalRealName, jdbcType=VARCHAR}
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                AND t.legal_mobile = #{legalMobile, jdbcType=VARCHAR}
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                AND t.settle_card_no = #{settleCardNo, jdbcType=VARCHAR}
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                AND t.settle_bank_code = #{settleBankCode, jdbcType=VARCHAR}
            </if>
            <if test="settleBankName != null and settleBankName != '' ">
                AND t.settle_bank_name = #{settleBankName, jdbcType=VARCHAR}
            </if>
            <if test="province != null and province != '' ">
                AND t.province = #{province, jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != '' ">
                AND t.city = #{city, jdbcType=VARCHAR}
            </if>
            <if test="district != null and district != '' ">
                AND t.district = #{district, jdbcType=VARCHAR}
            </if>
            <if test="address != null and address != '' ">
                AND t.address = #{address, jdbcType=VARCHAR}
            </if>
            <if test="requestNo != null and requestNo != '' ">
                AND t.request_no = #{requestNo, jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                AND t.merchant_no = #{merchantNo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="shortName != null and shortName != '' ">
                AND t.short_name like concat('%',#{shortName, jdbcType=VARCHAR},'%')
            </if>
            <if test="introduce != null and introduce != '' ">
                AND t.introduce = #{introduce, jdbcType=VARCHAR}
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                AND t.divide_flag = #{divideFlag, jdbcType=VARCHAR}
            </if>
            <if test="platDivideRate != null">
                AND t.plat_divide_rate = #{platDivideRate, jdbcType=DECIMAL}
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                AND t.legal_licence_type = #{legalLicenceType, jdbcType=VARCHAR}
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                AND t.legal_licence_no like concat('%',#{legalLicenceNo, jdbcType=VARCHAR},'%')
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                AND t.legal_licence_front_url = #{legalLicenceFrontUrl, jdbcType=VARCHAR}
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                AND t.legal_licence_back_url = #{legalLicenceBackUrl, jdbcType=VARCHAR}
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                AND t.legal_real_name like concat('%',#{legalRealName, jdbcType=VARCHAR},'%')
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                AND t.legal_mobile like concat('%',#{legalMobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                AND t.settle_card_no like concat('%',#{settleCardNo, jdbcType=VARCHAR},'%')
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                AND t.settle_bank_code = #{settleBankCode, jdbcType=VARCHAR}
            </if>
            <if test="settleBankName != null and settleBankName != '' ">
                AND t.settle_bank_name like concat('%',#{settleBankName, jdbcType=VARCHAR},'%')
            </if>
            <if test="province != null and province != '' ">
                AND t.province = #{province, jdbcType=VARCHAR}
            </if>
            <if test="city != null and city != '' ">
                AND t.city = #{city, jdbcType=VARCHAR}
            </if>
            <if test="district != null and district != '' ">
                AND t.district = #{district, jdbcType=VARCHAR}
            </if>
            <if test="address != null and address != '' ">
                AND t.address = #{address, jdbcType=VARCHAR}
            </if>
            <if test="requestNo != null and requestNo != '' ">
                AND t.request_no = #{requestNo, jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                AND t.merchant_no = #{merchantNo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=TIMESTAMP}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserPaymentAccountInfo">
        insert into nft_user_payment_account_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name,
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce,
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                divide_flag,
            </if>
            <if test="platDivideRate != null ">
                plat_divide_rate,
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                legal_licence_type,
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                legal_licence_no,
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                legal_licence_front_url,
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                legal_licence_back_url,
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                legal_real_name,
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                legal_mobile,
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no,
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code,
            </if>
            <if test="settleBankName != null and settleBankName != '' ">
                settle_bank_name,
            </if>
            <if test="province != null and province != '' ">
                province,
            </if>
            <if test="city != null and city != '' ">
                city,
            </if>
            <if test="district != null and district != '' ">
                district,
            </if>
            <if test="address != null and address != '' ">
                address,
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no,
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null and shortName != '' ">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                #{divideFlag,jdbcType=VARCHAR},
            </if>
            <if test="platDivideRate != null">
                #{platDivideRate,jdbcType=DECIMAL},
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                #{legalLicenceType,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                #{legalLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                #{legalLicenceFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                #{legalLicenceBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                #{legalRealName,jdbcType=VARCHAR},
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                #{legalMobile,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="settleBankName != null and settleBankName != '' ">
                #{settleBankName, jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="district != null and district != '' ">
                #{district,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                #{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=TIMESTAMP},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_user_payment_account_info
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserPaymentAccountInfo">
        update nft_user_payment_account_info
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                divide_flag = #{divideFlag,jdbcType=VARCHAR},
            </if>
            <if test="platDivideRate != null">
                plat_divide_rate = #{platDivideRate,jdbcType=DECIMAL},
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                legal_licence_type = #{legalLicenceType,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                legal_licence_no = #{legalLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                legal_licence_front_url = #{legalLicenceFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                legal_licence_back_url = #{legalLicenceBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                legal_real_name = #{legalRealName,jdbcType=VARCHAR},
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                legal_mobile = #{legalMobile,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no = #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code = #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="settleBankName != null and settleBankName != '' ">
                settle_bank_name = #{settleBankName, jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="district != null and district != '' ">
                district = #{district,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no = #{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=TIMESTAMP},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_payment_account_info t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserPaymentAccountInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_payment_account_info t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.UserPaymentAccountInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_payment_account_info t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>