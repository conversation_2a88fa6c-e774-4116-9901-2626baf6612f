<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ContractMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Contract">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="contract_address" jdbcType="VARCHAR" property="contractAddress"/>
        <result column="protocol" jdbcType="VARCHAR" property="protocol"/>
        <result column="chain" jdbcType="VARCHAR" property="chain"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="token_general_type" jdbcType="VARCHAR" property="tokenGeneralType"/>

        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.contract_address
        , t.protocol
        , t.chain
        , t.logo
        , t.token_general_type
        , t.status
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                AND t.contract_address = #{contractAddress, jdbcType=VARCHAR}
            </if>
            <if test="protocol != null and protocol != '' ">
                AND t.protocol = #{protocol, jdbcType=VARCHAR}
            </if>
            <if test="chain != null and chain != '' ">
                AND t.chain = #{chain, jdbcType=VARCHAR}
            </if>
            <if test="logo != null and logo != '' ">
                AND t.logo = #{logo, jdbcType=VARCHAR}
            </if>
            <if test="tokenGeneralType != null and tokenGeneralType != '' ">
                AND t.token_general_type = #{tokenGeneralType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Contract">
        insert into nft_contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                contract_address,
            </if>
            <if test="protocol != null and protocol != '' ">
                protocol,
            </if>
            <if test="chain != null and chain != '' ">
                chain,
            </if>
            <if test="logo != null and logo != '' ">
                logo,
            </if>
            <if test="tokenGeneralType != null and tokenGeneralType != '' ">
                token_general_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                #{contractAddress,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null and protocol != '' ">
                #{protocol,jdbcType=VARCHAR},
            </if>
            <if test="chain != null and chain != '' ">
                #{chain,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                #{logo, jdbcType=VARCHAR},
            </if>
            <if test="tokenGeneralType != null and tokenGeneralType != '' ">
                #{tokenGeneralType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_contract
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Contract">
        update nft_contract
        <set>
            <if test="contractAddress != null and contractAddress != '' ">
                contract_address = #{contractAddress,jdbcType=VARCHAR},
            </if>
            <if test="protocol != null and protocol != '' ">
                protocol = #{protocol,jdbcType=VARCHAR},
            </if>
            <if test="chain != null and chain != '' ">
                chain = #{chain,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                logo = #{logo, jdbcType=VARCHAR},
            </if>
            <if test="tokenGeneralType != null and tokenGeneralType != '' ">
                token_general_type = #{tokenGeneralType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Contract"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>