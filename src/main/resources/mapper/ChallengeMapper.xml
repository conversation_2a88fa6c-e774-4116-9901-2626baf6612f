<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChallengeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Challenge">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="cover_pic_url" jdbcType="VARCHAR" property="coverPicUrl"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="effective_hours" jdbcType="INTEGER" property="effectiveHours"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="actual_end_time" jdbcType="TIMESTAMP" property="actualEndTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="award_ref_id" jdbcType="BIGINT" property="awardRefId"/>
        <result column="award_quantity" jdbcType="INTEGER" property="awardQuantity"/>
        <result column="award_remain_quantity" jdbcType="INTEGER" property="awardRemainQuantity"/>
        <result column="total_collection_num" jdbcType="INTEGER" property="totalCollectionNum"/>
        <result column="read_count" jdbcType="INTEGER" property="readCount"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="start_status" jdbcType="VARCHAR" property="startStatus"/>
        <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId"/>
        <result column="apply_note" jdbcType="VARCHAR" property="applyNote"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="approve_user_id" jdbcType="BIGINT" property="approveUserId"/>
        <result column="approve_note" jdbcType="VARCHAR" property="approveNote"/>
        <result column="approve_datetime" jdbcType="TIMESTAMP" property="approveDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="ChallengeDetailResMap" type="com.std.core.pojo.response.ChallengeDetailRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="cover_pic_url" jdbcType="VARCHAR" property="coverPicUrl"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="total_collection_num" jdbcType="INTEGER" property="totalCollectionNum"/>
        <result column="start_status" jdbcType="VARCHAR" property="startStatus"/>
        <result column="companyName" jdbcType="VARCHAR" property="companyName"/>
        <result column="companyLogo" jdbcType="VARCHAR" property="companyLogo"/>
        <association property="awardRes" javaType="com.std.core.pojo.response.ChallengeDetailAwardRes">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="award_ref_id" jdbcType="BIGINT" property="id"/>
            <result column="type" jdbcType="VARCHAR" property="type"/>
            <result column="award_quantity" jdbcType="INTEGER" property="awardQuantity"/>
        </association>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.company_id
        , t.channel_id
        , t.name
        , t.cover_pic_url
        , t.start_time
        , t.effective_hours
        , t.end_time
        , t.actual_end_time
        , t.type
        , t.award_ref_id
        , t.award_quantity
        , t.award_remain_quantity
        , t.total_collection_num
        , t.read_count
        , t.status
        , t.start_status
        , t.apply_user_id
        , t.apply_note
        , t.apply_datetime
        , t.approve_user_id
        , t.approve_note
        , t.approve_datetime
        , t.updater
        , t.update_datetime
        , t.location
        , t.order_no
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="coverPicUrl != null and coverPicUrl != '' ">
                AND t.cover_pic_url = #{coverPicUrl, jdbcType=VARCHAR}
            </if>
            <if test="startTime != null">
                AND t.start_time = #{startTime, jdbcType=TIMESTAMP}
            </if>
            <if test="effectiveHours != null">
                AND t.effective_hours = #{effectiveHours, jdbcType=INTEGER}
            </if>
            <if test="endTime != null">
                AND t.end_time = #{endTime, jdbcType=TIMESTAMP}
            </if>
            <if test="actualEndTime != null">
                AND t.actual_end_time = #{actualEndTime, jdbcType=TIMESTAMP}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="awardRefId != null">
                AND t.award_ref_id = #{awardRefId, jdbcType=BIGINT}
            </if>
            <if test="awardQuantity != null">
                AND t.award_quantity = #{awardQuantity, jdbcType=INTEGER}
            </if>
            <if test="awardRemainQuantity != null">
                AND t.award_remain_quantity = #{awardRemainQuantity, jdbcType=INTEGER}
            </if>
            <if test="totalCollectionNum != null">
                AND t.total_collection_num = #{totalCollectionNum, jdbcType=INTEGER}
            </if>
            <if test="readCount != null">
                AND t.read_count = #{readCount, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startStatus != null and startStatus != '' ">
                AND t.start_status = #{startStatus, jdbcType=VARCHAR}
            </if>
            <if test="startStatusList != null and startStatusList.size() != 0 ">
                AND t.start_status in
                <foreach item="item" index="index" collection="startStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="applyUserId != null">
                AND t.apply_user_id = #{applyUserId, jdbcType=BIGINT}
            </if>
            <if test="applyNote != null and applyNote != '' ">
                AND t.apply_note = #{applyNote, jdbcType=VARCHAR}
            </if>
            <if test="applyDatetime != null">
                AND t.apply_datetime = #{applyDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="approveUserId != null">
                AND t.approve_user_id = #{approveUserId, jdbcType=BIGINT}
            </if>
            <if test="approveNote != null and approveNote != '' ">
                AND t.approve_note = #{approveNote, jdbcType=VARCHAR}
            </if>
            <if test="approveDatetime != null">
                AND t.approve_datetime = #{approveDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="approveDatetimeStart  != null">
                <![CDATA[AND t.approve_datetime >= #{approveDatetimeStart , jdbcType=TIMESTAMP}]]>
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.std.core.pojo.domain.Challenge">
        insert into nft_challenge
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="coverPicUrl != null and coverPicUrl != '' ">
                cover_pic_url,
            </if>
            <if test="startTime != null ">
                start_time,
            </if>
            <if test="effectiveHours != null ">
                effective_hours,
            </if>
            <if test="endTime != null ">
                end_time,
            </if>
            <if test="actualEndTime != null">
                actual_end_time,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="awardRefId != null ">
                award_ref_id,
            </if>
            <if test="awardQuantity != null ">
                award_quantity,
            </if>
            <if test="awardRemainQuantity != null ">
                award_remain_quantity,
            </if>
            <if test="totalCollectionNum != null ">
                total_collection_num,
            </if>
            <if test="readCount != null ">
                read_count,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status,
            </if>
            <if test="applyUserId != null ">
                apply_user_id,
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note,
            </if>
            <if test="applyDatetime != null ">
                apply_datetime,
            </if>
            <if test="approveUserId != null ">
                approve_user_id,
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note,
            </if>
            <if test="approveDatetime != null ">
                approve_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="location != null and location != '' ">
                location,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="coverPicUrl != null and coverPicUrl != '' ">
                #{coverPicUrl,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="effectiveHours != null">
                #{effectiveHours,jdbcType=INTEGER},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndTime != null">
                #{actualEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="awardRefId != null">
                #{awardRefId,jdbcType=BIGINT},
            </if>
            <if test="awardQuantity != null">
                #{awardQuantity,jdbcType=INTEGER},
            </if>
            <if test="awardRemainQuantity != null">
                #{awardRemainQuantity,jdbcType=INTEGER},
            </if>
            <if test="totalCollectionNum != null">
                #{totalCollectionNum,jdbcType=INTEGER},
            </if>
            <if test="readCount != null">
                #{readCount,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                #{applyUserId,jdbcType=BIGINT},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUserId != null">
                #{approveUserId,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null">
                #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="location != null and location != '' ">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_challenge
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Challenge">
        update nft_challenge
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="coverPicUrl != null and coverPicUrl != '' ">
                cover_pic_url = #{coverPicUrl,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="effectiveHours != null">
                effective_hours = #{effectiveHours,jdbcType=INTEGER},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndTime != null">
                actual_end_time = #{actualEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="awardRefId != null">
                award_ref_id = #{awardRefId,jdbcType=BIGINT},
            </if>
            <if test="awardQuantity != null">
                award_quantity = #{awardQuantity,jdbcType=INTEGER},
            </if>
            <if test="awardRemainQuantity != null">
                award_remain_quantity = #{awardRemainQuantity,jdbcType=INTEGER},
            </if>
            <if test="totalCollectionNum != null">
                total_collection_num = #{totalCollectionNum,jdbcType=INTEGER},
            </if>
            <if test="readCount != null">
                read_count = #{readCount,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status = #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="applyUserId != null">
                apply_user_id = #{applyUserId,jdbcType=BIGINT},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note = #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                apply_datetime = #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUserId != null">
                approve_user_id = #{approveUserId,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note = #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null">
                approve_datetime = #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="location != null and location != '' ">
                location = #{location,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="challengeStart">
        UPDATE nft_challenge
        SET start_status = '1'
        WHERE start_status = '0'
          and `status` = '2'
          AND DATE_FORMAT(start_time, '%Y-%m-%d %H:%i') <![CDATA[ <=]]> DATE_FORMAT(#{date}, '%Y-%m-%d %H:%i');
    </update>
    <update id="challengeEnd">
        UPDATE nft_challenge
        SET start_status   = '2'
          , actual_end_time=#{date}
        WHERE start_status = '1'
          and `status` = '2'
          AND end_time <![CDATA[ <=]]> #{date};
    </update>
    <update id="updateAwardRemainQuantity">
        UPDATE nft_challenge
        SET award_remain_quantity = award_remain_quantity - 1
        WHERE id = #{id,jdbcType=BIGINT}
          AND start_status = '1'
          AND `status` = '2'
          AND award_remain_quantity <![CDATA[ >]]> 0;
    </update>
    <update id="doChallengeFinal">
        UPDATE nft_challenge
        SET start_status    = '3',
            actual_end_time = #{actualEndTime}
        WHERE id IN
              (SELECT id FROM (SELECT id FROM nft_challenge WHERE start_status = '1' AND `status` = '2' AND award_remain_quantity <![CDATA[ <= ]]> 0) t);
    </update>


    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Challenge"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.ChallengePageRes">
        SELECT
        a.id,
        a.`name`,
        a.coverPicUrl,
        a.startTime,
        a.endTime,
        a.type,
        a.orderNo,
        a.startStatus,
        a.companyName,
        a.companyLogo,
        a.totalCollectionNum,
        a.channelId FROM (SELECT t.id,
        t.NAME,
        t.cover_pic_url coverPicUrl,
        t.start_time startTime,
        t.order_no orderNo,
        CASE
        WHEN t.actual_end_time IS NOT NULL THEN
        t.actual_end_time ELSE t.end_time
        END AS endTime,
        t.type,
        CASE
        WHEN t.start_status = 3 THEN
        2 ELSE t.start_status
        END AS startStatus,
        ta.NAME companyName,
        ta.logo companyLogo,
        t.total_collection_num totalCollectionNum,
        t.channel_id channelId
        FROM
        nft_challenge t
        INNER JOIN nft_company ta ON t.company_id = ta.id
        <include refid="where_condition"/>) a
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCollectionByChallengeCondition" resultType="com.std.core.pojo.response.ChallengeByCollectionPageRes">
        select t.id
             , nc.name
             , nc.cover_file_url coverPicUrl
             , nc.file_type      fileType
             , nc.tags
             , t.award_quantity  totalQuantity
             , nc.author_id      authorId
             , t.type            challengeType
             , t.id              collectionId
        from nft_challenge t
                 INNER JOIN nft_collection nc on t.award_ref_id = nc.id
        where t.type = '0'
          and t.company_id = #{companyId}
          and t.status in ('2', '4')
        order by t.id desc
    </select>

    <select id="selectByPrimaryKeyFront" resultMap="ChallengeDetailResMap">
        select t.id
             , t.name
             , t.award_ref_id
             , t.cover_pic_url
             , t.start_time
             , CASE
                   WHEN
                       t.actual_end_time is not null
                       THEN
                       t.actual_end_time
                   else
                       t.end_time
            end as     end_time
             , t.type
             , t.start_status
             , ta.name companyName
             , ta.logo companyLogo
             , t.total_collection_num
             , t.award_ref_id
             , t.award_quantity
             , t.channel_id
        from nft_challenge t
                 inner join nft_company ta on t.company_id = ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectChannelMerchant" resultType="java.lang.Long">
        select channel_id
        from nft_challenge
        where company_id = #{companyId}
        group by channel_id
    </select>
    <select id="selectMaxOrderNo" resultType="java.lang.Integer">
        select COALESCE(max(order_no),0) from nft_challenge
    </select>

</mapper>