<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LotteryActivityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LotteryActivity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="start_sell_date" jdbcType="TIMESTAMP" property="startSellDate"/>

        <result column="start_status" jdbcType="VARCHAR" property="startStatus"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="award_name" jdbcType="VARCHAR" property="awardName"/>
        <result column="rule_pic" jdbcType="VARCHAR" property="rulePic"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="join_quantity" jdbcType="INTEGER" property="joinQuantity"/>
        <result column="sold_quantity" jdbcType="INTEGER" property="soldQuantity"/>
        <result column="draw_param" jdbcType="INTEGER" property="drawParam"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="calculation_datetime" jdbcType="TIMESTAMP" property="calculationDatetime"/>
        <result column="bring_out_datetime" jdbcType="TIMESTAMP" property="bringOutDatetime"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.pic
        , t.name
        , t.type
        , t.status
        , t.start_sell_date
        , t.start_status
        , t.ref_id
        , t.award_name
        , t.rule_pic
        , t.total_quantity
        , t.join_quantity
        , t.sold_quantity
        , t.draw_param
        , t.end_datetime
        , t.calculation_datetime
        , t.bring_out_datetime
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.content
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="startSellDate != null">
                AND t.start_sell_date = #{startSellDate, jdbcType=TIMESTAMP}
            </if>
            <if test="startStatus != null and startStatus != '' ">
                AND t.start_status = #{startStatus, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="awardName != null and awardName != '' ">
                AND t.award_name like concat('%',#{awardName, jdbcType=VARCHAR},'%')
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="joinQuantity != null">
                AND t.join_quantity = #{joinQuantity, jdbcType=INTEGER}
            </if>
            <if test="soldQuantity != null">
                AND t.sold_quantity = #{soldQuantity, jdbcType=INTEGER}
            </if>
            <if test="drawParam != null">
                AND t.draw_param = #{drawParam, jdbcType=INTEGER}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="calculationDatetime != null">
                AND t.calculation_datetime = #{calculationDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="bringOutDatetime != null">
                AND t.bring_out_datetime = #{bringOutDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.LotteryActivity" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_lottery_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="startSellDate != null ">
                start_sell_date,
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="awardName != null and awardName != '' ">
                award_name,
            </if>
            <if test="rulePic != null and rulePic != '' ">
                rule_pic,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="joinQuantity != null ">
                join_quantity,
            </if>
            <if test="soldQuantity != null ">
                sold_quantity,
            </if>
            <if test="drawParam != null ">
                draw_param,
            </if>
            <if test="endDatetime != null ">
                end_datetime,
            </if>
            <if test="calculationDatetime != null ">
                calculation_datetime,
            </if>
            <if test="bringOutDatetime != null ">
                bring_out_datetime,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="startSellDate != null ">
                #{startSellDate,jdbcType=TIMESTAMP},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="awardName != null and awardName != '' ">
                #{awardName, jdbcType=VARCHAR},
            </if>
            <if test="rulePic != null and rulePic != '' ">
                #{rulePic, jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="joinQuantity != null">
                #{joinQuantity,jdbcType=INTEGER},
            </if>
            <if test="soldQuantity != null">
                #{soldQuantity,jdbcType=INTEGER},
            </if>
            <if test="drawParam != null">
                #{drawParam,jdbcType=INTEGER},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="calculationDatetime != null">
                #{calculationDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="bringOutDatetime != null">
                #{bringOutDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_lottery_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LotteryActivity">
        update nft_lottery_activity
        <set>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="startSellDate != null">
                start_sell_date = #{startSellDate,jdbcType=TIMESTAMP},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status = #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="awardName != null and awardName != '' ">
                award_name =#{awardName, jdbcType=VARCHAR},
            </if>
            <if test="rulePic != null and rulePic != '' ">
                rule_pic =#{rulePic, jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="joinQuantity != null">
                join_quantity = #{joinQuantity,jdbcType=INTEGER},
            </if>
            <if test="soldQuantity != null">
                sold_quantity = #{soldQuantity,jdbcType=INTEGER},
            </if>
            <if test="drawParam != null">
                draw_param = #{drawParam,jdbcType=INTEGER},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="calculationDatetime != null">
                calculation_datetime = #{calculationDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="bringOutDatetime != null">
                bring_out_datetime = #{bringOutDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 选择修改 -->
    <update id="updateAddJoinQuantityByPrimaryKey" parameterType="com.std.core.pojo.domain.LotteryActivity">
        update nft_lottery_activity
        set join_quantity = join_quantity + 1
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LotteryActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectUpActivity" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.status = '1' limit 1;
    </select>

    <select id="selectStartSellDateEnd" resultType="com.std.core.pojo.domain.LotteryActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.status='1' and t.start_status='-1' and t.start_sell_date
        <![CDATA[ <=]]>
        #{date}
    </select>

    <select id="selectPeriodDrawStrawsEnd" resultType="com.std.core.pojo.domain.LotteryActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.status='1' and t.start_status='1' and t.bring_out_datetime
        <![CDATA[ <=]]>
        #{date}
    </select>

    <select id="selectLotteryActivityEnd" resultType="com.std.core.pojo.domain.LotteryActivity">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity t
        where t.status='1' and t.start_status='0' and t.end_datetime
        <![CDATA[ <=]]>
        #{date}
    </select>

</mapper>