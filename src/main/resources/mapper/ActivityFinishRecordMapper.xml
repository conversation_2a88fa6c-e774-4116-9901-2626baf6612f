<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActivityFinishRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ActivityFinishRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.goods_id
        , t.mobile
        , t.status
        , t.order_no
        , t.amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>

        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ActivityFinishRecord">
        insert into mall_activity_finish_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="goodsId != null ">
                goods_id,
              </if>
              <if test="mobile != null and mobile != '' ">
                mobile,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="orderNo != null ">
                order_no,
              </if>
            <if test="amount != null">
                amount ,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount, jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into mall_activity_finish_record
            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.goodsId != null ">
                    goods_id,
                </if>
                <if test="item.mobile != null and item.mobile != '' ">
                    mobile,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.orderNo != null ">
                    order_no,
                </if>
                <if test="item.amount != null">
                    amount,
                </if>
                <if test="item.createDatetime != null">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.goodsId != null">
                    #{item.goodsId,jdbcType=BIGINT},
                </if>
                <if test="item.mobile != null and item.mobile != '' ">
                    #{item.mobile,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.orderNo != null">
                    #{item.orderNo,jdbcType=INTEGER},
                </if>
                <if test="item.amount != null">
                    #{item.amount, jdbcType=DECIMAL},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime, jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mall_activity_finish_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ActivityFinishRecord">
        update mall_activity_finish_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount, jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_activity_finish_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ActivityFinishRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_activity_finish_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByUser" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_activity_finish_record t
        where t.user_id = #{userId,jdbcType=BIGINT}
    </select>
</mapper>