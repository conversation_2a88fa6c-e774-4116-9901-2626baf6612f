<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailDaySnapshotMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailDaySnapshot">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_detail_id
        , t.owner_id
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="ownerId != null">
                AND t.owner_id = #{ownerId, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d')=
                DATE_FORMAT(#{createDatetime, jdbcType=TIMESTAMP},'%Y-%m-%d')
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailDaySnapshot">
        insert into nft_collection_detail_day_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="ownerId != null ">
                owner_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_day_snapshot
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailDaySnapshot">
        update nft_collection_detail_day_snapshot
        <set>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_day_snapshot t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailDaySnapshot"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_day_snapshot t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>