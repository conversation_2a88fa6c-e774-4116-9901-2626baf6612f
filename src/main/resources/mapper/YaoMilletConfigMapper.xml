<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoMilletConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoMilletConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="yao_pic" jdbcType="VARCHAR" property="yaoPic"/>
        <result column="millet_pic" jdbcType="VARCHAR" property="milletPic"/>
        <result column="day_max" jdbcType="DECIMAL" property="dayMax"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.currency
        , t.name
        , t.yao_pic
        , t.millet_pic
        , t.day_max
        , t.yin_yao
        , t.yang_yao
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="yaoPic != null and yaoPic != '' ">
                AND t.yao_pic = #{yaoPic, jdbcType=VARCHAR}
            </if>
            <if test="milletPic != null and milletPic != '' ">
                AND t.millet_pic = #{milletPic, jdbcType=VARCHAR}
            </if>
            <if test="dayMax != null">
                AND t.day_max = #{dayMax, jdbcType=DECIMAL}
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoMilletConfig">
        insert into es_yao_millet_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="yaoPic != null and yaoPic != '' ">
                yao_pic,
              </if>
              <if test="milletPic != null and milletPic != '' ">
                millet_pic,
              </if>
              <if test="dayMax != null ">
                day_max,
              </if>
              <if test="yinYao != null ">
                yin_yao,
              </if>
              <if test="yangYao != null ">
                yang_yao,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="orderNo != null ">
                order_no,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="yaoPic != null and yaoPic != '' ">
                #{yaoPic,jdbcType=VARCHAR},
            </if>
            <if test="milletPic != null and milletPic != '' ">
                #{milletPic,jdbcType=VARCHAR},
            </if>
            <if test="dayMax != null">
                #{dayMax,jdbcType=DECIMAL},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from es_yao_millet_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoMilletConfig">
        update es_yao_millet_config
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="currency != null and currency != '' ">
                currency = #{currency, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="yaoPic != null and yaoPic != '' ">
                yao_pic = #{yaoPic,jdbcType=VARCHAR},
            </if>
            <if test="milletPic != null and milletPic != '' ">
                millet_pic = #{milletPic,jdbcType=VARCHAR},
            </if>
            <if test="dayMax != null">
                day_max = #{dayMax,jdbcType=DECIMAL},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoMilletConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectListFront" resultType="com.std.core.pojo.response.AccountDiamondExchangeMilletRes">
        SELECT id
        , type
        , `name`
        , currency
        , yao_pic yaoPic
        , millet_pic milletPic
        , day_max dayMax
        , status
        , order_no orderNo
        , amount
        FROM ( SELECT t.id
                    , t.type
                    , t.currency
                    , t.name
                    , t.yao_pic
                    , t.millet_pic
                    , t.day_max
                    , t.status
                    , t.order_no
                    ,( SELECT COALESCE ( available_amount, 0 ) FROM tstd_account a WHERE user_id = #{userId} AND a.currency = t.currency ) amount
        FROM
        es_yao_millet_config t
        ) a
        ORDER BY
            amount DESC,orderNo DESC
    </select>
</mapper>