<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.KeywordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Keyword">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="word" jdbcType="VARCHAR" property="word"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="reaction" jdbcType="VARCHAR" property="reaction"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.word
        , t.level
        , t.reaction
        , t.updater
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="word != null and word != '' ">
                AND t.word = #{word, jdbcType=VARCHAR}
            </if>
            <if test="level != null and level != '' ">
                AND t.level = #{level, jdbcType=VARCHAR}
            </if>
            <if test="reaction != null and reaction != '' ">
                AND t.reaction = #{reaction, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Keyword" useGeneratedKeys="true" keyProperty="id">
        insert into tstd_forum_keyword
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="word != null and word != '' ">
                word,
              </if>
              <if test="level != null and level != '' ">
                level,
              </if>
              <if test="reaction != null and reaction != '' ">
                reaction,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="word != null and word != '' ">
                #{word,jdbcType=VARCHAR},
            </if>
            <if test="level != null and level != '' ">
                #{level,jdbcType=VARCHAR},
            </if>
            <if test="reaction != null and reaction != '' ">
                #{reaction,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tstd_forum_keyword
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Keyword">
        update tstd_forum_keyword
        <set>
            <if test="word != null and word != '' ">
                word = #{word,jdbcType=VARCHAR},
            </if>
            <if test="level != null and level != '' ">
                level = #{level,jdbcType=VARCHAR},
            </if>
            <if test="reaction != null and reaction != '' ">
                reaction = #{reaction,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_keyword t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Keyword"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_keyword t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>