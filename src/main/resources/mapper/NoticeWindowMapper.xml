<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.NoticeWindowMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.NoticeWindow">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="config_key" jdbcType="VARCHAR" property="configKey"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.name
        , t.config_key
        , t.content
        , t.status
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="configKey != null and configKey != '' ">
                AND t.config_key = #{configKey, jdbcType=VARCHAR}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.NoticeWindow">
        insert into tstd_notice_window
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="configKey != null and configKey != '' ">
                config_key,
              </if>
              <if test="content != null and content != '' ">
                content,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
            <if test="orderNo != null">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="configKey != null and configKey != '' ">
                #{configKey,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertRecordSelective">
        insert into tstd_notice_window_real_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="noticeId != null ">
                notice_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="noticeId != null ">
                #{noticeId,jdbcType=BIGINT},
            </if>
            <if test="userId != null ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null ">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_notice_window
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.NoticeWindow">
        update tstd_notice_window
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="configKey != null and configKey != '' ">
                config_key = #{configKey,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_notice_window t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.NoticeWindow"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_notice_window t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFront" parameterType="com.std.core.pojo.domain.NoticeWindow"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_notice_window t
        where
        1=1
        <if test="id != null">
            AND t.id = #{id, jdbcType=BIGINT}
        </if>
        <if test="type != null and type != '' ">
            AND t.type = #{type, jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != '' ">
            AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
        </if>
        <if test="configKey != null and configKey != '' ">
            AND t.config_key = #{configKey, jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != '' ">
            AND t.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="statusList != null and statusList.size() != 0 ">
            AND t.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator=","
                     close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        and t.id NOT IN (
        SELECT
        notice_id
        FROM
        tstd_notice_window_real_record
        WHERE
        user_id =#{userId})
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>