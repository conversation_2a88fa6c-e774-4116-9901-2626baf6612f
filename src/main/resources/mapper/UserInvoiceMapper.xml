<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserInvoiceMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserInvoice">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="tax_no" jdbcType="VARCHAR" property="taxNo"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="register_phone" jdbcType="VARCHAR" property="registerPhone"/>
        <result column="open_branch" jdbcType="VARCHAR" property="openBranch"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.name
        , t.tax_no
        , t.register_address
        , t.register_phone
        , t.open_branch
        , t.bank_account
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="taxNo != null and taxNo != '' ">
                AND t.tax_no = #{taxNo, jdbcType=VARCHAR}
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                AND t.register_address = #{registerAddress, jdbcType=VARCHAR}
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                AND t.register_phone = #{registerPhone, jdbcType=VARCHAR}
            </if>
            <if test="openBranch != null and openBranch != '' ">
                AND t.open_branch = #{openBranch, jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                AND t.bank_account = #{bankAccount, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserInvoice">
        insert into tstd_user_invoice
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="taxNo != null and taxNo != '' ">
                tax_no,
              </if>
              <if test="registerAddress != null and registerAddress != '' ">
                register_address,
              </if>
              <if test="registerPhone != null and registerPhone != '' ">
                register_phone,
              </if>
              <if test="openBranch != null and openBranch != '' ">
                open_branch,
              </if>
              <if test="bankAccount != null and bankAccount != '' ">
                bank_account,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="taxNo != null and taxNo != '' ">
                #{taxNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="openBranch != null and openBranch != '' ">
                #{openBranch,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_user_invoice
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserInvoice">
        update tstd_user_invoice
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="taxNo != null and taxNo != '' ">
                tax_no = #{taxNo,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                register_address = #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                register_phone = #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="openBranch != null and openBranch != '' ">
                open_branch = #{openBranch,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                bank_account = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_invoice t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserInvoice"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_invoice t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>