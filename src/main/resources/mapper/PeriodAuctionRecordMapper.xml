<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodAuctionRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodAuctionRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="post_time" jdbcType="TIMESTAMP" property="postTime"/>
        <result column="periodName" jdbcType="VARCHAR" property="periodName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.period_id
        , t.user_id
        , t.price
        , t.post_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="postTime != null">
                AND t.post_time = #{postTime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodAuctionRecord" useGeneratedKeys="true" keyProperty="id">
        insert into nft_period_auction_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="periodId != null ">
                period_id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="postTime != null ">
                post_time,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="postTime != null">
                #{postTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_period_auction_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodAuctionRecord">
        update nft_period_auction_record
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="postTime != null">
                post_time = #{postTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name periodName
        from nft_period_auction_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_period_auction ta on t.period_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodAuctionRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name periodName
        from nft_period_auction_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_period_auction ta on t.period_id = ta.period_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFrontList" resultType="com.std.core.pojo.response.PeriodAuctionRecordListRes">
        select
        t.id
        , t.period_id periodId
        , t.user_id userId
        , t.price
        , t.post_time postTime
        from nft_period_auction_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFrontPage" resultType="com.std.core.pojo.response.PeriodAuctionRecordPageRes">

        select
        t.id
        , t.period_id periodId
        , t.user_id userId
        , t.price
        , t.post_time postTime
        from nft_period_auction_record t
       where t.period_id=#{periodId}
        order by t.id desc

    </select>
    <select id="selectByConditionMyListFront"
            resultType="com.std.core.pojo.response.PeriodAuctionMyRecordListRes">
        select
        t.id
        , t.price
        , t.post_time postTime
        from nft_period_auction_record t
        where t.period_id=#{periodId} and t.user_id=#{userId}
        order by t.id desc
    </select>


</mapper>