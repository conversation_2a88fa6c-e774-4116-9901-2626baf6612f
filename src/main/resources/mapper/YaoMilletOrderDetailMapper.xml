<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoMilletOrderDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoMilletOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="millet_type" jdbcType="VARCHAR" property="milletType"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="yao_pic" jdbcType="VARCHAR" property="yaoPic"/>
        <result column="millet_pic" jdbcType="VARCHAR" property="milletPic"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.order_id
        , t.currency
        , t.millet_type
        , t.yin_yao
        , t.yang_yao
        , t.quantity
        , t.create_datetime
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="milletType != null and milletType != '' ">
                AND t.millet_type = #{milletType, jdbcType=VARCHAR}
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=DECIMAL}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoMilletOrderDetail">
        insert into es_yao_millet_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="milletType != null and milletType != '' ">
                millet_type,
            </if>
            <if test="yinYao != null ">
                yin_yao,
            </if>
            <if test="yangYao != null ">
                yang_yao,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="createDatetime != null">
                create_datetime,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="milletType != null and milletType != '' ">
                #{milletType,jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime, jdbcType=DECIMAL},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from es_yao_millet_order_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoMilletOrderDetail">
        update es_yao_millet_order_detail
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="milletType != null and milletType != '' ">
                millet_type = #{milletType,jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime, jdbcType=DECIMAL},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_order_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoMilletOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name ,ta.yao_pic,ta.millet_pic
        from es_yao_millet_order_detail t
        INNER JOIN es_yao_millet_config ta on t.millet_type = ta.type
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectDaySum" resultType="java.math.BigDecimal">
        select count(1)
        from es_yao_millet_order_detail t
                 inner join es_yao_millet_order ta on t.order_id = ta.id
        where t.currency = #{currency}
          and t.create_datetime = DATE_FORMAT(#{date}, '%Y-%m-%d')
          and ta.status = '1'
    </select>
    <select id="selectTotalByMillet" parameterType="com.std.core.pojo.domain.YaoMilletOrderDetail"
            resultMap="BaseResultMap">
        select
            COALESCE(sum(t.yin_yao),0) yin_yao,
            COALESCE(sum(t.yang_yao),0) yang_yao,
            COALESCE(sum(t.quantity),0)  quantity
        from es_yao_millet_order_detail t
        inner join es_yao_millet_order ta on t.order_id =ta.id
        where t.currency=#{currency} and ta.status='1'
    </select>
</mapper>