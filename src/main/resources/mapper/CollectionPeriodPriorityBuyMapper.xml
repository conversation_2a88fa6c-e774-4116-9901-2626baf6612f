<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionPeriodPriorityBuyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionPeriodPriorityBuy">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="advance_mins" jdbcType="INTEGER" property="advanceMins"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.period_id
        , t.collection_id
        , t.quantity
        , t.advance_mins
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="advanceMins != null">
                AND t.advance_mins = #{advanceMins, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionPeriodPriorityBuy">
        insert into nft_collection_period_priority_buy
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="advanceMins != null">
                advance_mins,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                #{advanceMins, jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_period_priority_buy
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByPeriodId" parameterType="java.lang.Long">
        delete
        from nft_collection_period_priority_buy
        where period_id = #{periodId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionPeriodPriorityBuy">
        update nft_collection_period_priority_buy
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                advance_mins = #{advanceMins, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_priority_buy t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriodPriorityBuy"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_priority_buy t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectExistCollectionTotalCount" parameterType="com.std.core.pojo.domain.CollectionPeriodPriorityBuy"
            resultType="java.lang.Long">
        select count(1)
        from nft_collection_period_priority_buy
        where collection_id in (
            select distinct collection_id
            from nft_collection_detail
            where owner_id = #{userId}
              and status = #{status})
          and period_id = #{periodId}
    </select>
    <select id="getOtherJoinCount" resultType="java.lang.Integer">
        SELECT COALESCE
                   (SUM(quantity * amount), 0)
        FROM (
                 SELECT t.quantity,
                        count(1) amount
                 FROM nft_collection_period_priority_buy t
                          INNER JOIN nft_collection_detail ta ON t.collection_id = ta.collection_id
                 WHERE t.period_id = #{periodId}
                   AND ta.owner_type = '0'
                   AND ta.owner_id = #{userId}
                 GROUP BY ta.collection_id
             ) a

    </select>
    <select id="getOtherJoinCountDistinct" resultType="java.lang.Integer">
        SELECT ifnull(sum(quantity), 0)
        from (
                 SELECT quantity
                 FROM nft_collection_period_priority_buy t
                          INNER JOIN nft_collection_detail ta ON t.collection_id = ta.collection_id
                 WHERE t.period_id = #{periodId}
                   AND ta.owner_type = '0'
                   AND ta.owner_id = #{userId}
                 GROUP BY ta.collection_id
             ) a
    </select>

    <select id="getInDrawCollection" resultType="java.lang.Long">
        select collection_id from nft_collection_period_priority_buy where period_id in (select id from nft_collection_period where `status`='1' and start_status='1')
        union
        select collection_id from nft_collection_period_priority_buy where period_id in (select id from nft_collection_period where category !='3' and `status`='1' and start_status='0' and #{date} <![CDATA[ >=]]> DATE_ADD(start_sell_date,INTERVAL -advance_mins MINUTE))
    </select>
    <select id="selectExistCollectionDetailId" resultType="java.lang.Long">
        select collection_id
        from nft_collection_period_priority_buy
        where collection_id in (
            select distinct collection_id
            from nft_collection_detail
            where owner_id = #{userId}
              and status = #{status})
          and period_id = #{periodId} limit 1
    </select>
    <select id="selectMaxAdvanceMins" resultType="java.lang.Integer">
        select COALESCE(max(advance_mins),0)
        from nft_collection_period_priority_buy
        where collection_id in (
            select distinct collection_id
            from nft_collection_detail
            where owner_id = #{userId}
              and status = #{status})
          and period_id = #{periodId}
    </select>

</mapper>