<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CompanyApplySettleRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CompanyApplySettleRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="account_id" jdbcType="BIGINT" property="accountId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="invoice_pdf" jdbcType="VARCHAR" property="invoicePdf"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="settle_bank_code" jdbcType="VARCHAR" property="settleBankCode"/>
        <result column="settle_card_no" jdbcType="VARCHAR" property="settleCardNo"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.company_id
        , t.account_id
        , t.amount
        , t.invoice_pdf
        , t.status
        , t.settle_bank_code
        , t.settle_card_no
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="accountId != null">
                AND t.account_id = #{accountId, jdbcType=BIGINT}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="invoicePdf != null and invoicePdf != '' ">
                AND t.invoice_pdf = #{invoicePdf, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                AND t.settle_bank_code = #{settleBankCode, jdbcType=VARCHAR}
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                AND t.settle_card_no = #{settleCardNo, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >= ]]> DATE_FORMAT(#{createDatetimeStart, jdbcType=TIMESTAMP},'%Y-%m-%d')
            </if>
            <if test="createDatetimeEnd != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ <= ]]> DATE_FORMAT(#{createDatetimeEnd, jdbcType=TIMESTAMP},'%Y-%m-%d')
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CompanyApplySettleRecord" useGeneratedKeys="true" keyProperty="id">
        insert into nft_company_apply_settle_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="accountId != null">
                account_id,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="invoicePdf != null and invoicePdf != '' ">
                invoice_pdf,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code,
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                #{accountId, jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="invoicePdf != null and invoicePdf != '' ">
                #{invoicePdf,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_company_apply_settle_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CompanyApplySettleRecord">
        update nft_company_apply_settle_record
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="accountId != null">
                account_id = #{accountId, jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="invoicePdf != null and invoicePdf != '' ">
                invoice_pdf = #{invoicePdf,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code = #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no = #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company_apply_settle_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CompanyApplySettleRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company_apply_settle_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>