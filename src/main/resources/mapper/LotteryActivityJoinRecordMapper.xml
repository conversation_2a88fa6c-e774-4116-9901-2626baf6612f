<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LotteryActivityJoinRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LotteryActivityJoinRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="join_millis" jdbcType="BIGINT" property="joinMillis"/>
        <result column="group" jdbcType="BIGINT" property="group"/>
        <result column="is_deal" jdbcType="VARCHAR" property="isDeal"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.activity_id
        , t.activity_name
        , t.status
        , t.user_id
        , t.collection_id
        , t.collection_name
        , t.order_no
        , t.create_datetime
        , t.create_time
        , t.update_datetime
        , t.join_millis
        , t.group
        , t.is_deal
        , t.order_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="activityName != null and activityName != '' ">
                AND t.activity_name like concat('%',#{activityName, jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name = #{collectionName, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="joinMillis != null">
                AND t.join_millis = #{joinMillis, jdbcType=BIGINT}
            </if>
            <if test="group != null">
                AND t.group = #{group, jdbcType=BIGINT}
            </if>
            <if test="isDeal != null and isDeal != '' ">
                AND t.is_deal = #{isDeal, jdbcType=VARCHAR}
            </if>
            <if test="orderId != null and orderId != '' ">
                AND t.order_id = #{orderId, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.std.core.pojo.domain.LotteryActivityJoinRecord">
        insert into nft_lottery_activity_join_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="activityName != null and activityName != '' ">
                activity_name,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name,
            </if>
            order_no,
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="joinMillis != null ">
                join_millis,
            </if>
            <if test="group != null ">
                `group`,
            </if>
            <if test="isDeal != null and isDeal != '' ">
                is_deal,
            </if>
            <if test="orderId != null">
                order_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName, jdbcType=VARCHAR},
            </if>
            ( SELECT number from (SELECT count( 1 )+ 1 number FROM nft_lottery_activity_join_record WHERE activity_id =
            #{activityId,jdbcType=BIGINT})a ),
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="joinMillis != null">
                #{joinMillis,jdbcType=BIGINT},
            </if>
            <if test="group != null">
                #{group,jdbcType=BIGINT},
            </if>
            <if test="isDeal != null and isDeal != '' ">
                #{isDeal, jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_lottery_activity_join_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LotteryActivityJoinRecord">
        update nft_lottery_activity_join_record
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="joinMillis != null">
                join_millis = #{joinMillis,jdbcType=BIGINT},
            </if>
            <if test="group != null">
                `group` = #{group,jdbcType=BIGINT},
            </if>
            <if test="isDeal != null and isDeal != '' ">
                is_deal = #{isDeal, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateLotterActivityEnd">
        UPDATE nft_lottery_activity_join_record
        SET `status` = '1',
        <if test="collectionId != null">
            collection_id = #{collectionId, jdbcType=BIGINT},
        </if>
        <if test="collectionName != null and collectionName != '' ">
            collection_name = #{collectionName, jdbcType=VARCHAR},
        </if>
        is_deal = #{isDeal},
        update_datetime = #{updateDatetime}
        WHERE
        id IN (
        SELECT
        id
        FROM
        ( SELECT id FROM nft_lottery_activity_join_record t WHERE t.activity_id = #{activityId}
        AND t.`status` = '0' ORDER BY t.GROUP, RAND() LIMIT #{limit}
        ) a
        );
    </update>
    <update id="updateLotterActivityMiss">
        UPDATE nft_lottery_activity_join_record
        SET `status`        = '2',
            is_deal         = '0',
            update_datetime = #{updateDatetime}
        WHERE `status` = '0'
          AND activity_id = #{activityId}
    </update>
    <update id="updateData">
        update nft_lottery_activity_join_record
        <set>
            <if test="isDeal != null and isDeal != '' ">
                is_deal = #{isDeal,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_record t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LotteryActivityJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByActivityLimit10"
            resultType="com.std.core.pojo.response.LotteryActivityJoinRecordListRes">
        select t.id,
               t.activity_id     activityId,
               t.status,
               tu.nickname,
               tu.photo,
               t.order_no        orderNo,
               t.create_datetime createDatetime,
               t.create_time     createTime
        from nft_lottery_activity_join_record t
                 INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.activity_id = #{activityId}
        order by t.order_no desc limit 10
    </select>


    <resultMap id="BaseMap" type="com.std.core.pojo.response.LotteryActivityJoinRecordDetailRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>

        <collection property="activityJoinDetailDetailResList"
                ofType="com.std.core.pojo.response.LotteryActivityJoinDetailDetailRes">
            <id column="detailId" property="id"/>
            <result column="collection_id" property="collectionId"/>
            <result column="collection_name" property="collectionName"/>
            <result column="cover_file_url" property="coverFileUrl"/>
            <result column="collection_detail_id" property="collectionDetailId"/>
            <result column="token_id" property="tokenId"/>
        </collection>

    </resultMap>

    <select id="selectJoinRecordDetailList"
            resultType="com.std.core.pojo.response.LotteryActivityJoinRecordDetailRes" resultMap="BaseMap">
        select t.id
             , t.status
             , t.user_id
             , t.create_time

             , ta.id detailId
             , ta.collection_id
             , ta.collection_name
             , ta.cover_file_url
             , ta.collection_detail_id
             , ta.token_id
        from nft_lottery_activity_join_record t
                 INNER join nft_lottery_activity_join_detail ta
                            on t.id = ta.join_id
        where t.activity_id = #{activityId}
          and t.user_id = #{userId}
        order by t.create_time desc
    </select>

    <select id="selectNoDealJoinRecordByActivity" resultType="java.lang.Integer">
        select count(1)
        from nft_lottery_activity_join_record t
        where ((t.status in ('1', '2')
            and t.is_deal = '1') or t.status = '0')
          and t.activity_id = #{activityId} limit 1;
    </select>
    <select id="selectJoinCount" resultType="java.lang.Integer">
        select count(1)
        from nft_lottery_activity_join_record t
        where t.activity_id = #{activityId};
    </select>
    <select id="selectUserJoinCount" resultType="java.lang.Integer">
        select count(1)
        from nft_lottery_activity_join_record t
        where t.activity_id = #{activityId}
          and t.user_id = #{userId};
    </select>
    <select id="selectUserWinCount" resultType="java.lang.Integer">
        select count(1)
        from nft_lottery_activity_join_record t
        where t.activity_id = #{activityId}
          and t.user_id = #{userId}
          and t.status = '1';
    </select>

    <select id="selectByActivity" resultType="com.std.core.pojo.response.LotteryActivityJoinRecordAllRes">
        select t.id,
               t.activity_id     activityId,
               t.status,
               tu.nickname,
               tu.photo,
               t.order_no        orderNo,
               t.create_datetime createDatetime,
               t.create_time     createTime
        from nft_lottery_activity_join_record t
                 INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.activity_id = #{activityId}
        order by t.order_no desc
    </select>
    <select id="selectNoDealJoinRecord" resultType="com.std.core.pojo.domain.LotteryActivityJoinRecord">
        select t.id
        from nft_lottery_activity_join_record t
        where t.status in ('1', '2')
          and t.is_deal = '1'
          and t.activity_id = #{activityId} limit 500;
    </select>

</mapper>