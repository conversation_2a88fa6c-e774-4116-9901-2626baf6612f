<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailDayAllSnapshotMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailDaySnapshot">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_detail_id
        , t.owner_id
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="ownerId != null">
                AND t.owner_id = #{ownerId, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailDaySnapshot">
        insert into nft_collection_detail_day_all_snapshot
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="ownerId != null ">
                owner_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_day_all_snapshot
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailDayAllSnapshot">
        update nft_collection_detail_day_all_snapshot
        <set>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_day_all_snapshot t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailDayAllSnapshot"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_day_all_snapshot t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectListByOwnerId" parameterType="com.std.core.pojo.domain.CollectionDetailDayAllSnapshot"
            resultType="java.lang.Long">
        select distinct owner_id
        from nft_collection_detail_day_all_snapshot ns
        where DATE_FORMAT(create_datetime, '%Y-%m-%d') = DATE_FORMAT(#{createDatetime}, '%Y-%m-%d')
        order by id asc
    </select>

    <!-- 组合条件查询 -->
    <select id="selectListByOwnerIdCollectionId" parameterType="com.std.core.pojo.domain.CollectionDetailDayAllSnapshot"
            resultType="java.lang.Long">
        select distinct owner_id from nft_collection_detail_day_all_snapshot ns, nft_collection_detail nd
        where DATE_FORMAT(create_datetime,'%Y-%m-%d') = DATE_FORMAT(#{createDatetime},'%Y-%m-%d')
        and ns.collection_detail_id = nd.id
        <if test="collectionIdList != null and collectionIdList.size > 0 ">
            AND nd.collection_id in
            <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                    close=")">
                #{item}
            </foreach>
        </if>
        order by ns.id asc
    </select>
</mapper>