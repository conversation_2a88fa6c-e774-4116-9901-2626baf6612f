<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserRole">
        <result column="t_user_id" jdbcType="BIGINT" property="userId"/>
        <result column="t_role_id" jdbcType="BIGINT" property="roleId"/>
    </resultMap>

    <sql id="Base_Column_List">
    t.user_id as t_user_id,
    t.role_id as t_role_id
  </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="userId != null and userId != '' ">
              AND t.user_id = #{userId,jdbcType=BIGINT}
            </if>
            <if test="roleId != null and roleId != '' ">
              AND t.role_id = #{roleId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserRole"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
      from tsys_user_role t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCountByCondition" parameterType="com.std.core.pojo.domain.UserRole"
            resultType="java.lang.Long">
      select count(1) from tsys_user_role t
        <include refid="where_condition"/>
    </select>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
      from tsys_user_role t
      where t.id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tsys_user_role
    where id = #{id,jdbcType=BIGINT}
  </delete>

    <delete id="deleteByUserId" parameterType="java.lang.Long">
    delete from tsys_user_role
    where user_id = #{userId,jdbcType=BIGINT}
  </delete>

    <insert id="insert" parameterType="com.std.core.pojo.domain.UserRole">
    insert into tsys_user_role (user_id, role_id
    )
    values (#{userId,jdbcType=BIGINT}, #{roleId,jdbcType=BIGINT}
    )
  </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserRole">
      update tsys_user_role
        <set>
            <if test="userId != null">
              user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
              role_id = #{roleId,jdbcType=BIGINT},
            </if>
        </set>
      where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.std.core.pojo.domain.UserRole">
    update tsys_user_role
    set user_id = #{userId,jdbcType=BIGINT},
    role_id = #{roleId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>
