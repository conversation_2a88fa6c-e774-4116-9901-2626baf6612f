<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LanguageResourceMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LanguageResource">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="table" jdbcType="VARCHAR" property="table"/>
        <result column="column" jdbcType="VARCHAR" property="column"/>
        <result column="ref_id" jdbcType="VARCHAR" property="refId"/>
        <result column="zn_data" jdbcType="VARCHAR" property="znData"/>
        <result column="en_data" jdbcType="VARCHAR" property="enData"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.`table`
        , t.`column`
        , t.ref_id
        , t.zn_data
        , t.en_data
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="table != null and table != '' ">
                AND t.`table` = #{table, jdbcType=VARCHAR}
            </if>
            <if test="column != null and column != '' ">
                AND t.`column` = #{column, jdbcType=VARCHAR}
            </if>
            <if test="refId != null and refId != '' ">
                AND t.ref_id = #{refId, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.LanguageResource">
        insert into tsys_language_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="table != null and table != '' ">
                  `table`,
              </if>
              <if test="column != null and column != '' ">
                  `column`,
              </if>
              <if test="refId != null and refId != '' ">
                ref_id,
              </if>
              <if test="znData != null and znData != '' ">
                zn_data,
              </if>
              <if test="enData != null and enData != '' ">
                en_data,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="table != null and table != '' ">
                #{table,jdbcType=VARCHAR},
            </if>
            <if test="column != null and column != '' ">
                #{column,jdbcType=VARCHAR},
            </if>
            <if test="refId != null and refId != '' ">
                #{refId,jdbcType=VARCHAR},
            </if>
            <if test="znData != null and znData != '' ">
                #{znData,jdbcType=VARCHAR},
            </if>
            <if test="enData != null and enData != '' ">
                #{enData,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 选择添加 -->
    <insert id="replaceSelective" parameterType="com.std.core.pojo.domain.LanguageResource">
        replace into tsys_language_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="table != null and table != '' ">
                `table`,
            </if>
            <if test="column != null and column != '' ">
                `column`,
            </if>
            <if test="refId != null and refId != '' ">
                ref_id,
            </if>
            <if test="znData != null and znData != '' ">
                zn_data,
            </if>
            <if test="enData != null and enData != '' ">
                en_data,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="table != null and table != '' ">
                #{table,jdbcType=VARCHAR},
            </if>
            <if test="column != null and column != '' ">
                #{column,jdbcType=VARCHAR},
            </if>
            <if test="refId != null and refId != '' ">
                #{refId,jdbcType=VARCHAR},
            </if>
            <if test="znData != null and znData != '' ">
                #{znData,jdbcType=VARCHAR},
            </if>
            <if test="enData != null and enData != '' ">
                #{enData,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tsys_language_resource
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <delete id="deleteByRef" parameterType="com.std.core.pojo.domain.LanguageResource">
        delete from tsys_language_resource
        where `table` = #{table,jdbcType=VARCHAR} and ref_id = #{refId,jdbcType=VARCHAR}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LanguageResource">
        update tsys_language_resource
        <set>
            <if test="znData != null and znData != '' ">
                zn_data = #{znData,jdbcType=VARCHAR},
            </if>
            <if test="enData != null and enData != '' ">
                en_data = #{enData,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByRefSelective" parameterType="com.std.core.pojo.domain.LanguageResource">
        update tsys_language_resource
        <set>
            <if test="znData != null and znData != '' ">
                zn_data = #{znData,jdbcType=VARCHAR},
            </if>
            <if test="enData != null and enData != '' ">
                en_data = #{enData,jdbcType=VARCHAR},
            </if>
        </set>
        where 1 = 1
        <if test="table != null and table != '' ">
            AND `table` = #{table, jdbcType=VARCHAR}
        </if>
        <if test="column != null and column != '' ">
            AND `column` = #{column, jdbcType=VARCHAR}
        </if>
        <if test="refId != null and refId != '' ">
            AND ref_id = #{refId, jdbcType=VARCHAR}
        </if>
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_language_resource t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LanguageResource"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_language_resource t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>