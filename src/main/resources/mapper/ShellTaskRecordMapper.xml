<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ShellTaskRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ShellTaskRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="biz_category" jdbcType="VARCHAR" property="bizCategory"/>
        <result column="biz_category_note" jdbcType="VARCHAR" property="bizCategoryNote"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_note" jdbcType="VARCHAR" property="bizNote"/>
        <result column="ref_no" jdbcType="VARCHAR" property="refNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="series_no" jdbcType="BIGINT" property="seriesNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.currency
        , t.biz_category
        , t.biz_category_note
        , t.biz_type
        , t.biz_note
        , t.ref_no
        , t.amount
        , t.remark
        , t.create_datetime
        , t.create_time
        , t.series_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                AND t.biz_category = #{bizCategory, jdbcType=VARCHAR}
            </if>
            <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                AND t.biz_category_note = #{bizCategoryNote, jdbcType=VARCHAR}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizNote != null and bizNote != '' ">
                AND t.biz_note = #{bizNote, jdbcType=VARCHAR}
            </if>
            <if test="refNo != null and refNo != '' ">
                AND t.ref_no = #{refNo, jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
            <if test="seriesNo != null">
                AND t.series_no = #{seriesNo, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ShellTaskRecord">
        insert into yg_shell_task_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="currency != null and currency != '' ">
                currency,
              </if>
              <if test="bizCategory != null and bizCategory != '' ">
                biz_category,
              </if>
              <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                biz_category_note,
              </if>
              <if test="bizType != null and bizType != '' ">
                biz_type,
              </if>
              <if test="bizNote != null and bizNote != '' ">
                biz_note,
              </if>
            <if test="refNo != null and refNo != '' ">
                ref_no,
            </if>
              <if test="amount != null ">
                amount,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
              <if test="seriesNo != null ">
                series_no,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                #{bizCategoryNote,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizNote != null and bizNote != '' ">
                #{bizNote,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                #{refNo, jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="seriesNo != null">
                #{seriesNo,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_shell_task_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ShellTaskRecord">
        update yg_shell_task_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="bizCategory != null and bizCategory != '' ">
                biz_category = #{bizCategory,jdbcType=VARCHAR},
            </if>
            <if test="bizCategoryNote != null and bizCategoryNote != '' ">
                biz_category_note = #{bizCategoryNote,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizNote != null and bizNote != '' ">
                biz_note = #{bizNote,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                ref_no = #{refNo, jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="seriesNo != null">
                series_no = #{seriesNo,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_task_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ShellTaskRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_task_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectDayLoginRecord" parameterType="com.std.core.pojo.domain.YaoChangeRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_task_record t
        where t.user_id=#{userId} and t.biz_category=#{bizCategory} and t.biz_type=#{bizType} and DATE_FORMAT(t.create_datetime,'%Y-%m-%d')
        =DATE_FORMAT(#{date},'%Y-%m-%d') limit 1
    </select>
    <select id="selectLastDayLoginRecord" parameterType="com.std.core.pojo.domain.YaoChangeRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_task_record t
        where t.user_id=#{userId} and t.biz_category=#{bizCategory} and t.biz_type=#{bizType} order by id desc limit 1
    </select>
    <select id="selectCountByCondition" resultType="java.lang.Integer">
        select
        count(1)
        from yg_shell_task_record t
        <include refid="where_condition"/>
    </select>
    <select id="selectWeekRecord" parameterType="com.std.core.pojo.domain.YaoChangeRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_task_record t
        where t.user_id=#{userId} and t.biz_category=#{bizCategory} and t.biz_type=#{bizType} and DATE_FORMAT(t.create_datetime,'%Y-%m-%d')
        <![CDATA[ >=]]> DATE_FORMAT(#{date},'%Y-%m-%d') and t.ref_no=#{refId} limit 1
    </select>
</mapper>