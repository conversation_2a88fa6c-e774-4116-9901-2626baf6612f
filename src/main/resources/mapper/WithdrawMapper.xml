<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.WithdrawMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Withdraw">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="account_type" jdbcType="VARCHAR" property="accountType"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="fee_deduction_type" jdbcType="VARCHAR" property="feeDeductionType"/>
        <result column="fee_deduction_ref_id" jdbcType="VARCHAR" property="feeDeductionRefId"/>
        <result column="actual_fee" jdbcType="DECIMAL" property="actualFee"/>

        <result column="bill_flag" jdbcType="VARCHAR" property="billFlag"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="channel_bank_code" jdbcType="VARCHAR" property="channelBankCode"/>
        <result column="channel_bank" jdbcType="VARCHAR" property="channelBank"/>
        <result column="channel_account_info" jdbcType="VARCHAR" property="channelAccountInfo"/>
        <result column="channel_account_number" jdbcType="VARCHAR" property="channelAccountNumber"/>
        <result column="channel_order" jdbcType="VARCHAR" property="channelOrder"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="apply_user" jdbcType="BIGINT" property="applyUser"/>
        <result column="apply_user_kind" jdbcType="VARCHAR" property="applyUserKind"/>
        <result column="apply_note" jdbcType="VARCHAR" property="applyNote"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="pay_user" jdbcType="BIGINT" property="payUser"/>
        <result column="pay_note" jdbcType="VARCHAR" property="payNote"/>
        <result column="pay_fee" jdbcType="DECIMAL" property="payFee"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="approve_user" jdbcType="BIGINT" property="approveUser"/>
        <result column="approve_note" jdbcType="VARCHAR" property="approveNote"/>
        <result column="approve_datetime" jdbcType="TIMESTAMP" property="approveDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.account_number
        , t.account_type
        , t.currency
        , t.biz_type
        , t.amount
        , t.fee
        , t.fee_deduction_type
        , t.fee_deduction_ref_id
        , t.actual_fee
        , t.actual_amount
        , t.balance_amount
        , t.bill_flag
        , t.channel_type
        , t.channel_bank_code
        , t.channel_bank
        , t.channel_account_info
        , t.channel_account_number
        , t.channel_order
        , t.status
        , t.apply_user
        , t.apply_user_kind
        , t.apply_note
        , t.apply_datetime
        , t.pay_user
        , t.pay_note
        , t.pay_fee
        , t.pay_datetime
        , t.approve_user
        , t.approve_note
        , t.approve_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                AND t.account_number = #{accountNumber, jdbcType=VARCHAR}
            </if>
            <if test="accountType != null and accountType != '' ">
                AND t.account_type = #{accountType, jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="feeDeductionType != null and feeDeductionType != '' ">
                AND t.fee_deduction_type = #{feeDeductionType, jdbcType=VARCHAR}
            </if>

            <if test="billFlag != null and billFlag != '' ">
                AND t.bill_flag = #{billFlag, jdbcType=VARCHAR}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="channelBankCode != null and channelBankCode != '' ">
                AND t.channel_bank_code = #{channelBankCode, jdbcType=VARCHAR}
            </if>

            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>

            <if test="applyUser != null ">
                AND t.apply_user = #{applyUser, jdbcType=BIGINT}
            </if>
            <if test="applyUserKind != null and applyUserKind != '' ">
                AND t.apply_user_kind = #{applyUserKind, jdbcType=VARCHAR}
            </if>
            <if test="payUser != null">
                AND t.pay_user = #{payUser, jdbcType=BIGINT}
            </if>
            <if test="approveUser != null">
                AND t.approve_user = #{approveUser, jdbcType=BIGINT}
            </if>

            <if test="applyDatetimeStart != null">
                <![CDATA[AND t.apply_datetime >= #{applyDatetimeStart}]]>
            </if>

            <if test="applyDatetimeEnd != null">
                <![CDATA[AND t.apply_datetime <= #{applyDatetimeEnd}]]>
            </if>

            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>

            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>

            <if test="idForQuery != null ">
                AND id like concat('%',#{idForQuery},'%')
            </if>

            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                t.channel_account_info like concat('%',#{keywords, jdbcType=VARCHAR},'%') OR
                t.channel_account_number like concat('%',#{keywords, jdbcType=VARCHAR},'%') OR
                t.id like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>

        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Withdraw">
        insert into tstd_withdraw
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                account_number,
            </if>
            <if test="accountType != null and accountType != '' ">
                account_type,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="feeDeductionType != null and feeDeductionType != '' ">
                fee_deduction_type,
            </if>
            <if test="feeDeductionRefId != null and feeDeductionRefId != '' ">
                fee_deduction_ref_id,
            </if>
            <if test="actualFee != null">
                actual_fee,
            </if>
            <if test="actualAmount != null  ">
                actual_amount,
            </if>
            <if test="balanceAmount != null">
                balance_amount,
            </if>
            <if test="billFlag != null and billFlag != '' ">
                bill_flag,
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type,
            </if>
            <if test="channelBankCode != null and channelBankCode != '' ">
                channel_bank_code,
            </if>
            <if test="channelBank != null and channelBank != '' ">
                channel_bank,
            </if>
            <if test="channelAccountInfo != null and channelAccountInfo != '' ">
                channel_account_info,
            </if>
            <if test="channelAccountNumber != null and channelAccountNumber != '' ">
                channel_account_number,
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                channel_order,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="applyUser != null ">
                apply_user,
            </if>
            <if test="applyUserKind != null and applyUserKind != '' ">
                apply_user_kind,
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note,
            </if>
            <if test="applyDatetime != null">
                apply_datetime,
            </if>
            <if test="payUser != null and payUser != '' ">
                pay_user,
            </if>
            <if test="payNote != null and payNote != '' ">
                pay_note,
            </if>
            <if test="payFee != null  ">
                pay_fee,
            </if>
            <if test="payDatetime != null  ">
                pay_datetime,
            </if>
            <if test="approveUser != null ">
                approve_user,
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note,
            </if>
            <if test="approveDatetime != null ">
                approve_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="accountNumber != null and accountNumber != '' ">
                #{accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="accountType != null and accountType != '' ">
                #{accountType,jdbcType=VARCHAR},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="feeDeductionType != null and feeDeductionType != '' ">
                #{feeDeductionType,jdbcType=VARCHAR},
            </if>
            <if test="feeDeductionRefId != null and feeDeductionRefId != '' ">
                #{feeDeductionRefId,jdbcType=VARCHAR},
            </if>
            <if test="actualFee != null">
                #{actualFee,jdbcType=DECIMAL},
            </if>
            <if test="actualAmount != null">
                #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="balanceAmount != null">
                #{balanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="billFlag != null and billFlag != '' ">
                #{billFlag,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelBankCode != null and channelBankCode != '' ">
                #{channelBankCode,jdbcType=VARCHAR},
            </if>

            <if test="channelBank != null and channelBank != '' ">
                #{channelBank,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountInfo != null and channelAccountInfo != '' ">
                #{channelAccountInfo,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountNumber != null and channelAccountNumber != '' ">
                #{channelAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                #{channelOrder,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="applyUser != null ">
                #{applyUser,jdbcType=BIGINT},
            </if>
            <if test="applyUserKind != null and applyUserKind != '' ">
                #{applyUserKind,jdbcType=VARCHAR},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payUser != null">
                #{payUser,jdbcType=BIGINT},
            </if>
            <if test="payNote != null and payNote != '' ">
                #{payNote,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null">
                #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUser != null  ">
                #{approveUser,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null ">
                #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_withdraw
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Withdraw">
        update tstd_withdraw
        <set>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="channelBankCode != null and channelBankCode != '' ">
                channel_bank_code = #{channelBankCode,jdbcType=VARCHAR},
            </if>
            <if test="channelBank != null and channelBank != '' ">
                channel_bank = #{channelBank,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountInfo != null and channelAccountInfo != '' ">
                channel_account_info = #{channelAccountInfo,jdbcType=VARCHAR},
            </if>
            <if test="channelAccountNumber != null and channelAccountNumber != '' ">
                channel_account_number = #{channelAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="channelOrder != null and channelOrder != '' ">
                channel_order = #{channelOrder,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="payUser != null">
                pay_user = #{payUser,jdbcType=BIGINT},
            </if>
            <if test="payNote != null and payNote != '' ">
                pay_note = #{payNote,jdbcType=VARCHAR},
            </if>
            <if test="payFee != null">
                pay_fee = #{payFee,jdbcType=DECIMAL},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUser != null ">
                approve_user = #{approveUser,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note = #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null ">
                approve_datetime = #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Withdraw"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalAmount" parameterType="com.std.core.pojo.domain.Withdraw" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(amount), 0) FROM tstd_withdraw t
        <include refid="where_condition"/>
    </select>

    <select id="selectActualTotalAmount" parameterType="com.std.core.pojo.domain.Withdraw" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(actual_fee), 0) FROM tstd_withdraw t
        <include refid="where_condition"/>
    </select>

    <select id="selectTotalCount" parameterType="com.std.core.pojo.domain.Withdraw" resultType="java.lang.Long">
        SELECT count(1) FROM tstd_withdraw t
        <include refid="where_condition"/>
    </select>

    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.WithdrawPageFrontRes">
        select
        t
        .
        id
        , t.account_number accountNumber
        , t.currency
        , t.amount
        , t.actual_fee fee
        , t.channel_bank_code channelBankCode
        , t.channel_bank channelBank
        , t.channel_account_number channelAccountNumber
        , t.status
        , t.apply_datetime applyDatetime
        , t.approve_note approveNote
        , t.pay_note payNote
        , tcb.bank_name channelBankName
        from tstd_withdraw t
        left join tstd_channel_bank tcb on t.channel_bank_code=tcb.bank_code
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectWithdrawApplyByOss" resultType="com.std.core.pojo.response.WithdrawListByOssRes">
        select t.user_id as userId
        from tstd_withdraw_apply_by_oss t
    </select>
</mapper>