<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserApplyInvoiceRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserApplyInvoiceRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="invoice_type" jdbcType="VARCHAR" property="invoiceType"/>
        <result column="invoice_name" jdbcType="VARCHAR" property="invoiceName"/>
        <result column="tax_number" jdbcType="VARCHAR" property="taxNumber"/>
        <result column="register_address" jdbcType="VARCHAR" property="registerAddress"/>
        <result column="register_phone" jdbcType="VARCHAR" property="registerPhone"/>
        <result column="open_branch" jdbcType="VARCHAR" property="openBranch"/>
        <result column="bank_account" jdbcType="VARCHAR" property="bankAccount"/>
        <result column="receive_type" jdbcType="VARCHAR" property="receiveType"/>
        <result column="receive_content" jdbcType="VARCHAR" property="receiveContent"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="open_company_type" jdbcType="VARCHAR" property="openCompanyType"/>
        <result column="open_company_id" jdbcType="BIGINT" property="openCompanyId"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="open_company_user_id" jdbcType="BIGINT" property="openCompanyUserId"/>
        <result column="open_handle_datetime" jdbcType="TIMESTAMP" property="openHandleDatetime"/>
        <result column="open_invoice_pdf" jdbcType="VARCHAR" property="openInvoicePdf"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.amount
        , t.type
        , t.invoice_type
        , t.invoice_name
        , t.tax_number
        , t.register_address
        , t.register_phone
        , t.open_branch
        , t.bank_account
        , t.receive_type
        , t.receive_content
        , t.status
        , t.open_company_type
        , t.open_company_id
        , t.apply_datetime
        , t.open_company_user_id
        , t.open_handle_datetime
        , t.open_invoice_pdf
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="invoiceType != null and invoiceType != '' ">
                AND t.invoice_type = #{invoiceType, jdbcType=VARCHAR}
            </if>
            <if test="invoiceName != null and invoiceName != '' ">
                AND t.invoice_name = #{invoiceName, jdbcType=VARCHAR}
            </if>
            <if test="taxNumber != null and taxNumber != '' ">
                AND t.tax_number = #{taxNumber, jdbcType=VARCHAR}
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                AND t.register_address = #{registerAddress, jdbcType=VARCHAR}
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                AND t.register_phone = #{registerPhone, jdbcType=VARCHAR}
            </if>
            <if test="openBranch != null and openBranch != '' ">
                AND t.open_branch = #{openBranch, jdbcType=VARCHAR}
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                AND t.bank_account = #{bankAccount, jdbcType=VARCHAR}
            </if>
            <if test="receiveType != null and receiveType != '' ">
                AND t.receive_type = #{receiveType, jdbcType=VARCHAR}
            </if>
            <if test="receiveContent != null and receiveContent != '' ">
                AND t.receive_content = #{receiveContent, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="openCompanyType != null and openCompanyType != '' ">
                AND t.open_company_type = #{openCompanyType, jdbcType=VARCHAR}
            </if>
            <if test="openCompanyId != null">
                AND t.open_company_id = #{openCompanyId, jdbcType=BIGINT}
            </if>
            <if test="applyDatetime != null">
                AND t.apply_datetime = #{applyDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="applyDatetimeStart != null">
                AND t.apply_datetime <![CDATA[ >=]]> #{applyDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="applyDatetimeEnd != null">
                AND t.apply_datetime <![CDATA[ <]]> #{applyDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
            <if test="openCompanyUserId != null">
                AND t.open_company_user_id = #{openCompanyUserId, jdbcType=BIGINT}
            </if>
            <if test="openHandleDatetime != null">
                AND t.open_handle_datetime = #{openHandleDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="openInvoicePdf != null and openInvoicePdf != '' ">
                AND t.open_invoice_pdf = #{openInvoicePdf, jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserApplyInvoiceRecord" useGeneratedKeys="true" keyProperty="id">
        insert into tstd_user_apply_invoice_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null ">
                user_id,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="invoiceType != null and invoiceType != '' ">
                invoice_type,
            </if>
            <if test="invoiceName != null and invoiceName != '' ">
                invoice_name,
            </if>
            <if test="taxNumber != null and taxNumber != '' ">
                tax_number,
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                register_address,
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                register_phone,
            </if>
            <if test="openBranch != null and openBranch != '' ">
                open_branch,
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                bank_account,
            </if>
            <if test="receiveType != null and receiveType != '' ">
                receive_type,
            </if>
            <if test="receiveContent != null and receiveContent != '' ">
                receive_content,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="openCompanyType != null and openCompanyType != '' ">
                open_company_type,
            </if>
            <if test="openCompanyId != null ">
                open_company_id,
            </if>
            <if test="applyDatetime != null">
                apply_datetime,
            </if>
            <if test="openCompanyUserId != null ">
                open_company_user_id,
            </if>
            <if test="openHandleDatetime != null ">
                open_handle_datetime,
            </if>
            <if test="openInvoicePdf != null and openInvoicePdf != '' ">
                open_invoice_pdf,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null and invoiceType != '' ">
                #{invoiceType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceName != null and invoiceName != '' ">
                #{invoiceName,jdbcType=VARCHAR},
            </if>
            <if test="taxNumber != null and taxNumber != '' ">
                #{taxNumber,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="openBranch != null and openBranch != '' ">
                #{openBranch,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="receiveType != null and receiveType != '' ">
                #{receiveType,jdbcType=VARCHAR},
            </if>
            <if test="receiveContent != null and receiveContent != '' ">
                #{receiveContent,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="openCompanyType != null and openCompanyType != '' ">
                #{openCompanyType,jdbcType=VARCHAR},
            </if>
            <if test="openCompanyId != null">
                #{openCompanyId,jdbcType=BIGINT},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="openCompanyUserId != null">
                #{openCompanyUserId,jdbcType=BIGINT},
            </if>
            <if test="openHandleDatetime != null">
                #{openHandleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="openInvoicePdf != null and openInvoicePdf != '' ">
                #{openInvoicePdf,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_user_apply_invoice_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserApplyInvoiceRecord">
        update tstd_user_apply_invoice_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="invoiceType != null and invoiceType != '' ">
                invoice_type = #{invoiceType,jdbcType=VARCHAR},
            </if>
            <if test="invoiceName != null and invoiceName != '' ">
                invoice_name = #{invoiceName,jdbcType=VARCHAR},
            </if>
            <if test="taxNumber != null and taxNumber != '' ">
                tax_number = #{taxNumber,jdbcType=VARCHAR},
            </if>
            <if test="registerAddress != null and registerAddress != '' ">
                register_address = #{registerAddress,jdbcType=VARCHAR},
            </if>
            <if test="registerPhone != null and registerPhone != '' ">
                register_phone = #{registerPhone,jdbcType=VARCHAR},
            </if>
            <if test="openBranch != null and openBranch != '' ">
                open_branch = #{openBranch,jdbcType=VARCHAR},
            </if>
            <if test="bankAccount != null and bankAccount != '' ">
                bank_account = #{bankAccount,jdbcType=VARCHAR},
            </if>
            <if test="receiveType != null and receiveType != '' ">
                receive_type = #{receiveType,jdbcType=VARCHAR},
            </if>
            <if test="receiveContent != null and receiveContent != '' ">
                receive_content = #{receiveContent,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="openCompanyType != null and openCompanyType != '' ">
                open_company_type = #{openCompanyType,jdbcType=VARCHAR},
            </if>
            <if test="openCompanyId != null">
                open_company_id = #{openCompanyId,jdbcType=BIGINT},
            </if>
            <if test="openCompanyUserId != null">
                open_company_user_id = #{openCompanyUserId,jdbcType=BIGINT},
            </if>
            <if test="openHandleDatetime != null">
                open_handle_datetime = #{openHandleDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="openInvoicePdf != null and openInvoicePdf != '' ">
                open_invoice_pdf = #{openInvoicePdf,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_apply_invoice_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_apply_invoice_record t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserApplyInvoiceRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_apply_invoice_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByOrderCondition" parameterType="com.std.core.pojo.request.MyInvoiceOrderReq"
            resultType="com.std.core.pojo.response.UserOrderInvoicePageRes">
        select
        t.id
        , t.order_type bizCategory
        , t.order_id orderId
        , t.order_note bizNote
        , t.amount
        , t.company_name companyName
        , t.create_datetime orderDatetime
        , t.status
        from tstd_invoice_order t
        where t.user_id = #{userId}
        and t.status = #{status}
        <if test="invoiceApplyId != null">
            AND t.invoice_apply_id = #{invoiceApplyId, jdbcType=BIGINT}
        </if>
        <if test="searchLaster != null and searchLaster != '' ">
            AND t.create_ts <![CDATA[ >= ]]> #{searchMiddleTs}
        </if>
        <if test="searchHistory != null and searchHistory != '' ">
            AND t.create_ts <![CDATA[ < ]]> #{searchMiddleTs}
        </if>
        order by id desc
    </select>
</mapper>