<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.GroupMapper">

    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Group">
        <id column="t_id" property="id" jdbcType="BIGINT"/>
        <result column="t_parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="t_kind" property="kind" jdbcType="VARCHAR"/>
        <result column="t_name" property="name" jdbcType="VARCHAR"/>
        <result column="t_order_no" property="orderNo" jdbcType="INTEGER"/>
        <result column="t_creater" property="creater" jdbcType="VARCHAR"/>

        <result column="t_create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="t_updater" property="updater" jdbcType="VARCHAR"/>
        <result column="t_update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="t_remark" property="remark" jdbcType="VARCHAR"/>
        <result column="t_company_id" property="companyId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id as t_id,
        t.parent_id as t_parent_id,
        t.kind as t_kind,
        t.name as t_name,
        t.order_no as t_order_no,
        t.creater as t_creater,
        t.create_time as t_create_time,
        t.updater as t_updater,

        t.update_time as t_update_time,
        t.remark as t_remark,
        t.company_id as t_company_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id,jdbcType=BIGINT}
            </if>
            <if test="parentId != null">
                AND t.parent_id = #{parentId,jdbcType=BIGINT}
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind,jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat(concat('%',#{name}),'%')
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from tsys_group t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Group"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_group t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="listOtherGroup" parameterType="com.std.core.pojo.domain.Group"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_group t
        WHERE t.id != #{id,jdbcType=BIGINT}
        <if test="kind != null and kind != '' ">
            AND t.kind = #{kind,jdbcType=VARCHAR}
        </if>
        <if test="companyId != null">
            AND t.company_id = #{companyId,jdbcType=BIGINT}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_group
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Group">
        insert into tsys_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="kind != null">
                kind,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="creater != null">
                creater,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=BIGINT},
            </if>
            <if test="kind != null">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{order_no,jdbcType=INTEGER},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Group">
        update tsys_group
        <set>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
