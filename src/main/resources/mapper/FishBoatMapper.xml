<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishBoatMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishBoat">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="region_type" jdbcType="VARCHAR" property="regionType"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="speed" jdbcType="DECIMAL" property="speed"/>
        <result column="endurance_time" jdbcType="INTEGER" property="enduranceTime"/>
        <result column="endurance_mileage" jdbcType="DECIMAL" property="enduranceMileage"/>
        <result column="fishing_time" jdbcType="DECIMAL" property="fishingTime"/>
        <result column="deadweight" jdbcType="DECIMAL" property="deadweight"/>
        <result column="deadweight_once" jdbcType="DECIMAL" property="deadweightOnce"/>
        <result column="load_factor" jdbcType="DECIMAL" property="loadFactor"/>
        <result column="sail_num_month" jdbcType="INTEGER" property="sailNumMonth"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.type
        , t.region_type
        , t.image
        , t.speed
        , t.endurance_time
        , t.endurance_mileage
        , t.fishing_time
        , t.deadweight
        , t.deadweight_once
        , t.load_factor
        , t.sail_num_month
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="regionType != null and regionType != '' ">
                AND t.region_type = #{regionType, jdbcType=VARCHAR}
            </if>
            <if test="image != null and image != '' ">
                AND t.image = #{image, jdbcType=VARCHAR}
            </if>
            <if test="speed != null">
                AND t.speed = #{speed, jdbcType=DECIMAL}
            </if>
            <if test="enduranceTime != null">
                AND t.endurance_time = #{enduranceTime, jdbcType=DECIMAL}
            </if>
            <if test="enduranceMileage != null">
                AND t.endurance_mileage = #{enduranceMileage, jdbcType=DECIMAL}
            </if>
            <if test="fishingTime != null">
                AND t.fishing_time = #{fishingTime, jdbcType=INTEGER}
            </if>
            <if test="deadweight != null">
                AND t.deadweight = #{deadweight, jdbcType=DECIMAL}
            </if>
            <if test="deadweightOnce != null">
                AND t.deadweight_once = #{deadweightOnce, jdbcType=DECIMAL}
            </if>
            <if test="loadFactor != null">
                AND t.load_factor = #{loadFactor, jdbcType=DECIMAL}
            </if>
            <if test="sailNumMonth != null">
                AND t.sail_num_month = #{sailNumMonth, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishBoat">
        insert into yg_fish_boat
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="regionType != null and regionType != '' ">
                region_type,
              </if>
            <if test="image != null and image != '' ">
                image,
            </if>
              <if test="speed != null ">
                speed,
              </if>
              <if test="enduranceTime != null ">
                endurance_time,
              </if>
              <if test="enduranceMileage != null ">
                endurance_mileage,
              </if>
              <if test="fishingTime != null ">
                fishing_time,
              </if>
              <if test="deadweight != null ">
                deadweight,
              </if>
              <if test="deadweightOnce != null ">
                deadweight_once,
              </if>
              <if test="loadFactor != null ">
                load_factor,
              </if>
              <if test="sailNumMonth != null ">
                sail_num_month,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="regionType != null and regionType != '' ">
                #{regionType,jdbcType=VARCHAR},
            </if>
            <if test="image != null and image != '' ">
                #{image, jdbcType=VARCHAR},
            </if>
            <if test="speed != null">
                #{speed,jdbcType=DECIMAL},
            </if>
            <if test="enduranceTime != null">
                #{enduranceTime,jdbcType=INTEGER},
            </if>
            <if test="enduranceMileage != null">
                #{enduranceMileage,jdbcType=DECIMAL},
            </if>
            <if test="fishingTime != null">
                #{fishingTime,jdbcType=DECIMAL},
            </if>
            <if test="deadweight != null">
                #{deadweight,jdbcType=DECIMAL},
            </if>
            <if test="deadweightOnce != null">
                #{deadweightOnce,jdbcType=DECIMAL},
            </if>
            <if test="loadFactor != null">
                #{loadFactor,jdbcType=DECIMAL},
            </if>
            <if test="sailNumMonth != null">
                #{sailNumMonth,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_fish_boat
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishBoat">
        update yg_fish_boat
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="regionType != null and regionType != '' ">
                region_type = #{regionType,jdbcType=VARCHAR},
            </if>
            <if test="image != null and image != '' ">
                image = #{image, jdbcType=VARCHAR},
            </if>
            <if test="speed != null">
                speed = #{speed,jdbcType=DECIMAL},
            </if>
            <if test="enduranceTime != null">
                endurance_time = #{enduranceTime,jdbcType=DECIMAL},
            </if>
            <if test="enduranceMileage != null">
                endurance_mileage = #{enduranceMileage,jdbcType=DECIMAL},
            </if>
            <if test="fishingTime != null">
                fishing_time = #{fishingTime,jdbcType=INTEGER},
            </if>
            <if test="deadweight != null">
                deadweight = #{deadweight,jdbcType=DECIMAL},
            </if>
            <if test="deadweightOnce != null">
                deadweight_once = #{deadweightOnce,jdbcType=DECIMAL},
            </if>
            <if test="loadFactor != null">
                load_factor = #{loadFactor,jdbcType=DECIMAL},
            </if>
            <if test="sailNumMonth != null">
                sail_num_month = #{sailNumMonth,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_boat t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishBoat"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_boat t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>