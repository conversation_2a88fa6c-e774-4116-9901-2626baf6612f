<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.WithdrawRuleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.WithdrawRule">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="kind" jdbcType="VARCHAR" property="kind"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="symbol" jdbcType="VARCHAR" property="symbol"/>
        <result column="withdraw_min" jdbcType="DECIMAL" property="withdrawMin"/>
        <result column="withdraw_max" jdbcType="DECIMAL" property="withdrawMax"/>
        <result column="withdraw_step" jdbcType="DECIMAL" property="withdrawStep"/>
        <result column="withdraw_limit" jdbcType="DECIMAL" property="withdrawLimit"/>
        <result column="withdraw_fee_take_location" jdbcType="VARCHAR" property="withdrawFeeTakeLocation"/>
        <result column="withdraw_fee_type" jdbcType="VARCHAR" property="withdrawFeeType"/>
        <result column="withdraw_fee" jdbcType="DECIMAL" property="withdrawFee"/>
        <result column="approve_flag" jdbcType="VARCHAR" property="approveFlag"/>
        <result column="withdraw_rule" jdbcType="VARCHAR" property="withdrawRule"/>
        <result column="withdraw_min1" jdbcType="DECIMAL" property="withdrawMin1"/>
        <result column="withdraw_min2" jdbcType="DECIMAL" property="withdrawMin2"/>
        <result column="withdraw_min3" jdbcType="DECIMAL" property="withdrawMin3"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.kind
        , t.type
        , t.name
        , t.symbol
        , t.withdraw_min
        , t.withdraw_max
        , t.withdraw_step
        , t.withdraw_limit
        , t.withdraw_fee_take_location
        , t.withdraw_fee_type
        , t.withdraw_fee
        , t.approve_flag
        , t.withdraw_rule
        , t.withdraw_min1
        , t.withdraw_min2
        , t.withdraw_min3
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="symbol != null and symbol != '' ">
                AND t.symbol = #{symbol, jdbcType=VARCHAR}
            </if>
            <if test="withdrawFeeTakeLocation != null and withdrawFeeTakeLocation != '' ">
                AND t.withdraw_fee_take_location = #{withdrawFeeTakeLocation, jdbcType=VARCHAR}
            </if>
            <if test="withdrawFeeType != null and withdrawFeeType != '' ">
                AND t.withdraw_fee_type = #{withdrawFeeType, jdbcType=VARCHAR}
            </if>
            <if test="approveFlag != null and approveFlag != '' ">
                AND t.approve_flag = #{approveFlag, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.WithdrawRule">
        insert into tstd_withdraw_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="kind != null and kind != '' ">
                kind,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="symbol != null and symbol != '' ">
                symbol,
              </if>
              <if test="withdrawMin != null">
                withdraw_min,
              </if>
              <if test="withdrawMax != null ">
                withdraw_max,
              </if>
              <if test="withdrawStep != null ">
                withdraw_step,
              </if>
              <if test="withdrawLimit != null ">
                withdraw_limit,
              </if>
              <if test="withdrawFeeTakeLocation != null and withdrawFeeTakeLocation != '' ">
                withdraw_fee_take_location,
              </if>
              <if test="withdrawFeeType != null and withdrawFeeType != '' ">
                withdraw_fee_type,
              </if>
              <if test="withdrawFee != null ">
                withdraw_fee,
              </if>
              <if test="approveFlag != null and approveFlag != '' ">
                approve_flag,
              </if>
              <if test="withdrawRule != null and withdrawRule != '' ">
                withdraw_rule,
              </if>
            <if test="withdrawMin1 != null">
                withdraw_min1,
            </if>
            <if test="withdrawMin2 != null">
                withdraw_min2,
            </if>
            <if test="withdrawMin3 != null">
                withdraw_min3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="kind != null and kind != '' ">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="symbol != null and symbol != '' ">
                #{symbol,jdbcType=VARCHAR},
            </if>
            <if test="withdrawMin != null">
                #{withdrawMin,jdbcType=DECIMAL},
            </if>
            <if test="withdrawMax != null">
                #{withdrawMax,jdbcType=DECIMAL},
            </if>
            <if test="withdrawStep != null">
                #{withdrawStep,jdbcType=DECIMAL},
            </if>
            <if test="withdrawLimit != null">
                #{withdrawLimit,jdbcType=DECIMAL},
            </if>
            <if test="withdrawFeeTakeLocation != null and withdrawFeeTakeLocation != '' ">
                #{withdrawFeeTakeLocation,jdbcType=VARCHAR},
            </if>
            <if test="withdrawFeeType != null and withdrawFeeType != '' ">
                #{withdrawFeeType,jdbcType=VARCHAR},
            </if>
            <if test="withdrawFee != null">
                #{withdrawFee,jdbcType=DECIMAL},
            </if>
            <if test="approveFlag != null and approveFlag != '' ">
                #{approveFlag,jdbcType=VARCHAR},
            </if>
            <if test="withdrawRule != null and withdrawRule != '' ">
                #{withdrawRule,jdbcType=VARCHAR},
            </if>
            <if test="withdrawMin1 != null">
                #{withdrawMin1,jdbcType=DECIMAL},
            </if>
            <if test="withdrawMin2 != null">
                #{withdrawMin2,jdbcType=DECIMAL},
            </if>
            <if test="withdrawMin3 != null">
                #{withdrawMin3,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tstd_withdraw_rule
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.WithdrawRule">
        update tstd_withdraw_rule
        <set>
            <choose>
                <when test="withdrawMin != null ">
                    withdraw_min = #{withdrawMin,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    withdraw_min = 0.00,
                </otherwise>
            </choose>
<!--            <if test="withdrawMin != null">-->
<!--                withdraw_min = #{withdrawMin,jdbcType=DECIMAL},-->
<!--            </if>-->
            <choose>
                <when test="withdrawMax != null ">
                    withdraw_max = #{withdrawMax,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    withdraw_max = null,
                </otherwise>
            </choose>
<!--            <if test="withdrawMax != null">-->
<!--                withdraw_max = #{withdrawMax,jdbcType=DECIMAL},-->
<!--            </if>-->
            <choose>
                <when test="withdrawStep != null ">
                    withdraw_step = #{withdrawStep,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    withdraw_step = null,
                </otherwise>
            </choose>
<!--            <if test="withdrawStep != null">-->
<!--                withdraw_step = #{withdrawStep,jdbcType=DECIMAL},-->
<!--            </if>-->
            <choose>
                <when test="withdrawLimit != null ">
                    withdraw_limit = #{withdrawLimit,jdbcType=DECIMAL},
                </when>
                <otherwise>
                    withdraw_limit = null,
                </otherwise>
            </choose>
<!--            <if test="withdrawLimit != null">-->
<!--                withdraw_limit = #{withdrawLimit,jdbcType=DECIMAL},-->
<!--            </if>-->
            <if test="withdrawFeeTakeLocation != null and withdrawFeeTakeLocation != '' ">
                withdraw_fee_take_location = #{withdrawFeeTakeLocation,jdbcType=VARCHAR},
            </if>
            <if test="withdrawFeeType != null and withdrawFeeType != '' ">
                withdraw_fee_type = #{withdrawFeeType,jdbcType=VARCHAR},
            </if>
            <if test="withdrawFee != null">
                withdraw_fee = #{withdrawFee,jdbcType=DECIMAL},
            </if>
            <if test="approveFlag != null and approveFlag != '' ">
                approve_flag = #{approveFlag,jdbcType=VARCHAR},
            </if>
            <choose>
                <when test="withdrawRule != null ">
                    withdraw_rule = #{withdrawRule,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    withdraw_rule = null,
                </otherwise>
            </choose>
<!--            <if test="withdrawRule != null and withdrawRule != '' ">-->
<!--                withdraw_rule = #{withdrawRule,jdbcType=VARCHAR},-->
<!--            </if>-->
            <if test="withdrawMin1 != null">
                withdraw_min1 = #{withdrawMin1,jdbcType=DECIMAL},
            </if>
            <if test="withdrawMin2 != null">
                withdraw_min2 = #{withdrawMin2,jdbcType=DECIMAL},
            </if>
            <if test="withdrawMin3 != null">
                withdraw_min3 = #{withdrawMin3,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw_rule t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.WithdrawRule"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw_rule t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <!-- 组合条件查询 -->
    <select id="selectRule" parameterType="com.std.core.pojo.domain.WithdrawRule"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_withdraw_rule t
        where 1=1
        and t.symbol != 'NAT_LT'
        <if test="id != null">
            AND t.id = #{id, jdbcType=INTEGER}
        </if>
        <if test="kind != null and kind != '' ">
            AND t.kind = #{kind, jdbcType=VARCHAR}
        </if>
        <if test="type != null and type != '' ">
            AND t.type = #{type, jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != '' ">
            AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
        </if>
        <if test="symbol != null and symbol != '' ">
            AND t.symbol = #{symbol, jdbcType=VARCHAR}
        </if>
        <if test="withdrawFeeTakeLocation != null and withdrawFeeTakeLocation != '' ">
            AND t.withdraw_fee_take_location = #{withdrawFeeTakeLocation, jdbcType=VARCHAR}
        </if>
        <if test="withdrawFeeType != null and withdrawFeeType != '' ">
            AND t.withdraw_fee_type = #{withdrawFeeType, jdbcType=VARCHAR}
        </if>
        <if test="approveFlag != null and approveFlag != '' ">
            AND t.approve_flag = #{approveFlag, jdbcType=VARCHAR}
        </if>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>