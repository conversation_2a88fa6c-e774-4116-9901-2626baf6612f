<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionBuyOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionBuyOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="parent_biz_id" jdbcType="BIGINT" property="parentBizId"/>
        <result column="blindbox_collection_detail_id" jdbcType="BIGINT" property="blindboxCollectionDetailId"/>

        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="author_id" jdbcType="BIGINT" property="authorId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="discount_price" jdbcType="DECIMAL" property="discountPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
        <result column="company_amount" jdbcType="DECIMAL" property="companyAmount"/>
        <result column="word" jdbcType="VARCHAR" property="word"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="buy_source" jdbcType="VARCHAR" property="buySource"/>
        <result column="pit_id" jdbcType="BIGINT" property="pitId"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.biz_type
        , t.biz_id
        , t.parent_biz_id
        , t.blindbox_collection_detail_id
        , t.user_id
        , t.collection_id
        , t.author_id
        , t.price
        , t.discount_price
        , t.quantity
        , t.pay_amount
        , t.create_time
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.commission_amount
        , t.company_amount
        , t.word
        , t.channel_id
        , t.buy_source
        , t.pit_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizId != null">
                AND t.biz_id = #{bizId, jdbcType=BIGINT}
            </if>
            <if test="parentBizId != null">
                AND t.parent_biz_id = #{parentBizId, jdbcType=BIGINT}
            </if>
            <if test="blindboxCollectionDetailId != null">
                AND t.blindbox_collection_detail_id = #{blindboxCollectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="blindboxCollectionDetailIdList != null and blindboxCollectionDetailIdList.size() != 0 ">
                AND t.blindbox_collection_detail_id in
                <foreach item="item" index="index" collection="blindboxCollectionDetailIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="authorId != null">
                AND t.author_id = #{authorId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payStatusList != null and payStatusList.size() != 0 ">
                AND t.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="companyAmount != null">
                AND t.company_amount = #{companyAmount, jdbcType=DECIMAL}
            </if>
            <if test="deduction != null and deduction != '' ">
                AND t.pay_balance_amount <![CDATA[ >]]> 0
            </if>
            <if test="noDeduction != null and noDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ <=]]> 0
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="buySource != null and buySource != '' ">
                AND t.buy_source = #{buySource, jdbcType=VARCHAR}
            </if>
            <if test="pitId != null">
                AND t.pit_id = #{pitId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizId != null">
                AND t.biz_id = #{bizId, jdbcType=BIGINT}
            </if>
            <if test="parentBizId != null">
                AND t.parent_biz_id = #{parentBizId, jdbcType=BIGINT}
            </if>
            <if test="blindboxCollectionDetailId != null">
                AND t.blindbox_collection_detail_id = #{blindboxCollectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="authorId != null">
                AND t.author_id = #{authorId, jdbcType=BIGINT}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payStatusList != null and payStatusList.size() != 0 ">
                AND t.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="companyAmount != null">
                AND t.company_amount = #{companyAmount, jdbcType=DECIMAL}
            </if>
            <if test="deduction != null and deduction != '' ">
                AND t.pay_balance_amount <![CDATA[ >]]> 0
            </if>
            <if test="noDeduction != null and noDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ <=]]> 0
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND ta.name like concat('%', #{collectionName, jdbcType=VARCHAR},'%')
            </if>
            <if test="word != null and word != '' ">
                AND t.word = #{word}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="buySource != null and buySource != '' ">
                AND t.buy_source = #{buySource, jdbcType=VARCHAR}
            </if>
            <if test="pitId != null">
                AND t.pit_id = #{pitId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionBuyOrder" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_buy_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != '' ">
                biz_type,
            </if>
            <if test="bizId != null ">
                biz_id,
            </if>
            <if test="parentBizId != null ">
                parent_biz_id,
            </if>
            <if test="blindboxCollectionDetailId != null ">
                blindbox_collection_detail_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="authorId != null">
                author_id,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="discountPrice != null ">
                discount_price,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="payBalanceAmount != null ">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null ">
                pay_cash_amount,
            </if>
            <if test="commissionAmount != null ">
                commission_amount,
            </if>
            <if test="companyAmount != null">
                company_amount,
            </if>
            <if test="word != null and word != '' ">
                word,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="promoteChannelId != null">
                promote_channel_id,
            </if>
            <if test="buySource != null and buySource != '' ">
                buy_source,
            </if>
            <if test="pitId != null">
                pit_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="parentBizId != null ">
                #{parentBizId,jdbcType=BIGINT},
            </if>
            <if test="blindboxCollectionDetailId != null ">
                #{blindboxCollectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="authorId != null">
                #{authorId, jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="discountPrice != null ">
                #{discountPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="companyAmount != null">
                #{companyAmount, jdbcType=DECIMAL},
            </if>
            <if test="word != null and word != '' ">
                #{word,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="promoteChannelId != null">
                #{promoteChannelId, jdbcType=BIGINT},
            </if>
            <if test="buySource != null and buySource != '' ">
                #{buySource, jdbcType=VARCHAR},
            </if>
            <if test="pitId != null">
                #{pitId, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_buy_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionBuyOrder">
        update nft_collection_buy_order
        <set>
            <if test="bizType != null and bizType != '' ">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="authorId != null">
                author_id = #{authorId, jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="discountPrice != null">
                discount_price = #{discountPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="companyAmount != null">
                company_amount = #{companyAmount, jdbcType=DECIMAL},
            </if>
            <if test="word != null and word != '' ">
                word = #{word,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="buySource != null and buySource != '' ">
                buy_source = #{buySource, jdbcType=VARCHAR},
            </if>
            <if test="pitId != null">
                pit_id = #{pitId, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectSimpleByCondition" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalTradeCount" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultType="java.lang.Integer">
        select count(1)
        from nft_collection_buy_order t
        <include refid="where_condition"/>
    </select>

    <select id="selectTotalTradeAmount" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultType="java.math.BigDecimal">
        select COALESCE(sum(t.pay_amount), 0)
        from nft_collection_buy_order t
        <include refid="where_condition"/>
    </select>

    <select id="selectTotalCount" resultType="java.lang.Integer">
        select COALESCE(sum(t.quantity), 0)
        from nft_collection_buy_order t
        <include refid="where_condition"/>
    </select>

    <select id="getUserBuyCount" resultType="java.lang.Integer">
        select COALESCE(sum(t.quantity), 0)
        from nft_collection_buy_order t
        where t.biz_type = '0'
          and t.biz_id = #{bizId, jdbcType=BIGINT}
          and t.user_id = #{userId}
          and t.pay_status not in ('2', '10')
    </select>

    <select id="getFirstMarketTimeoutOrder" resultType="com.std.core.pojo.domain.OrderClose">
        select 8                as bizType,
               t.pay_order_code as orderCode
        from nft_collection_buy_order t
        where t.pay_status = '0'
          and DATE_ADD(t.create_time, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{now}
          and t.biz_type = '0'
    </select>

    <select id="getOtherTimeoutOrder" resultType="com.std.core.pojo.domain.OrderClose">
        select 9                as bizType,
               t.pay_order_code as orderCode
        from nft_collection_detail_transfer_record t
        where t.pay_status = '0'
          and DATE_ADD(t.create_datetime, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{now}
        union
        select 10               as bizType,
               t.pay_order_code as orderCode
        from nft_collection_period_join_record t
        where t.pay_status = '0'
          and DATE_ADD(t.create_datetime, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{now}
        union
        select 14               as bizType,
               t.pay_order_code as orderCode
        from nft_collection_buy_order t
        where t.pay_status = '0'
          and DATE_ADD(t.create_time, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{now}
          and t.biz_type = '1'
    </select>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        left JOIN tsys_user tu on t.user_id = tu.id
        left join nft_collection ta on t.collection_id=ta.id
        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectMyLimitBuyBlindBoxCollectionId" parameterType="com.std.core.pojo.domain.CollectionBuyOrder"
            resultType="java.lang.Long">
        select distinct collection_id
        from nft_collection_buy_order t
                 inner join nft_collection tnc on t.collection_id = tnc.id
        where tnc.level_type in ('2', '3', '4')
          and t.user_id = #{userId}
          and t.biz_type = '1'
          and t.biz_id in (select id from nft_change_recommend where series_id = #{seriesId})
          and t.pay_status = '1'
    </select>
    <select id="selectByOrderCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_buy_order t
        where t.pay_order_code = #{orderCode,jdbcType=VARCHAR}
    </select>
    <select id="selectCountByCondition" resultType="java.lang.Integer">
        select
        count(1)
        from nft_collection_buy_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        left join nft_collection ta on t.collection_id=ta.id
        <include refid="where_condition_oss"/>
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select
        count(1)
        from nft_collection_buy_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectCompanyHomeSalesStatistical"
            resultType="com.std.core.pojo.response.CompanySalesStatisticalRes">
        SELECT @id := @id + 1 id,
	DATE_FORMAT( t.pay_datetime, '%Y-%m-%d' ) date,
	SUM( t.company_amount ) amount
        FROM
            nft_collection_buy_order t, (
            SELECT
            @id := 0
            ) ta
        WHERE
            t.pay_status IN ( '1'
            , '9' )
          and t.author_id=#{companyId}
          and DATE_FORMAT(t.pay_datetime
            , '%m-%d-%Y') <![CDATA[ >= ]]> DATE_FORMAT(#{startDatetime}
            , '%m-%d-%Y')
          and DATE_FORMAT(t.pay_datetime
            , '%m-%d-%Y') <![CDATA[ <= ]]> DATE_FORMAT(#{endDatetime}
            , '%m-%d-%Y')
        GROUP BY
            DATE_FORMAT(
            t.pay_datetime,
            '%Y-%m-%d')
    </select>

    <select id="selectTotalSaleAmount" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(t.pay_amount), 0) amount
        FROM nft_collection_buy_order t
        WHERE t.pay_status IN ('1', '9')
          and t.author_id = #{companyId}
    </select>

    <resultMap id="TotalResultMap" type="com.std.core.pojo.domain.CollectionBuyOrder">
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
    </resultMap>

    <select id="selectQuantityAmount" parameterType="com.std.core.pojo.domain.CollectionBuyOrder" resultMap="TotalResultMap">
        select
        count(1) quantity,
        ifnull(sum(pay_amount),0) pay_amount
        from nft_collection_buy_order t
        <include refid="where_condition"/>
    </select>

</mapper>