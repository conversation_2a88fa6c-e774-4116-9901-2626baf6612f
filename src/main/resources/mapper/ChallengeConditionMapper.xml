<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChallengeConditionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChallengeCondition">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="challenge_id" jdbcType="BIGINT" property="challengeId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="back_type" jdbcType="VARCHAR" property="backType"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.challenge_id
        , t.name
        , t.quantity
        , t.back_type
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="challengeId != null">
                AND t.challenge_id = #{challengeId, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="backType != null and backType != '' ">
                AND t.back_type = #{backType, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChallengeCondition" useGeneratedKeys="true" keyProperty="id">
        insert into nft_challenge_condition
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="challengeId != null ">
                challenge_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="backType != null and backType != '' ">
                back_type,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="challengeId != null ">
                #{challengeId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="backType != null and backType != '' ">
                #{backType,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_challenge_condition
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByChallenge">
        delete
        from nft_challenge_condition
        where challenge_id = #{challengeId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChallengeCondition">
        update nft_challenge_condition
        <set>
            <if test="challengeId != null ">
                challengeId = #{challengeId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="backType != null and backType != '' ">
                back_type = #{backType,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_condition t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChallengeCondition"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_condition t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalCount" parameterType="com.std.core.pojo.domain.ChallengeCondition"
            resultType="java.lang.Integer">
        select ifnull(sum(quantity), 0)
        from nft_challenge_condition t
        <include refid="where_condition"/>
    </select>
</mapper>