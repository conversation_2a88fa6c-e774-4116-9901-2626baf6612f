<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelTempCodeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelTempCode">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="channel_user_id" jdbcType="VARCHAR" property="channelUserId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="temp_code" jdbcType="VARCHAR" property="tempCode"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="expire_time" jdbcType="BIGINT" property="expireTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.channel_code
        , t.channel_user_id
        , t.user_id
        , t.temp_code
        , t.status
        , t.create_datetime
        , t.expire_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="channelCode != null and channelCode != '' ">
                AND t.channel_code = #{channelCode, jdbcType=VARCHAR}
            </if>
            <if test="channelUserId != null and channelUserId != '' ">
                AND t.channel_user_id = #{channelUserId, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="tempCode != null">
                AND t.temp_code = #{tempCode, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="expireTime != null">
                AND t.expire_time = #{expireTime, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelTempCode" useGeneratedKeys="true" keyProperty="id">
        insert into cs_channel_temp_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="channelCode != null and channelCode != '' ">
                channel_code,
            </if>
            <if test="channelUserId != null and channelUserId != '' ">
                channel_user_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="tempCode != null and tempCode != '' ">
                temp_code,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="expireTime != null ">
                expire_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="channelCode != null and channelCode != '' ">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelUserId != null and channelUserId != '' ">
                #{channelUserId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="tempCode != null and tempCode != '' ">
                #{tempCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                #{expireTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cs_channel_temp_code
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelTempCode">
        update cs_channel_temp_code
        <set>
            <if test="channelCode != null and channelCode != '' ">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelUserId != null and channelUserId != '' ">
                channel_user_id = #{channelUserId,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="tempCode != null and tempCode != '' ">
                temp_code = #{tempCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_temp_code t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelTempCode"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_temp_code t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>