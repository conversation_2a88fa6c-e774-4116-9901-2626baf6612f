<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishTransferRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishTransferRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="from_user_id" jdbcType="BIGINT" property="fromUserId"/>
        <result column="to_user_id" jdbcType="BIGINT" property="toUserId"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <result column="keyword" jdbcType="VARCHAR" property="keyword"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.from_user_id
        , t.to_user_id
        , t.currency
        , t.quantity
        , t.keyword
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="fromUserId != null">
                AND t.from_user_id = #{fromUserId, jdbcType=BIGINT}
            </if>
            <if test="toUserId != null">
                AND t.to_user_id = #{toUserId, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=DECIMAL}
            </if>
            <if test="keyword != null and keyword != '' ">
                AND t.keyword = #{keyword, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null ">
                <![CDATA[AND t.create_datetime >= #{createDatetimeStart}]]>
            </if>
            <if test="createDatetimeEnd != null">
                <![CDATA[AND t.create_datetime <= #{createDatetimeEnd}]]>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishTransferRecord">
        insert into yg_fish_transfer_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="fromUserId != null ">
                from_user_id,
              </if>
              <if test="toUserId != null ">
                to_user_id,
              </if>
              <if test="currency != null and currency != '' ">
                currency,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="keyword != null and keyword != '' ">
                keyword,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fromUserId != null">
                #{fromUserId,jdbcType=BIGINT},
            </if>
            <if test="toUserId != null">
                #{toUserId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="keyword != null and keyword != '' ">
                #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_fish_transfer_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishTransferRecord">
        update yg_fish_transfer_record
        <set>
            <if test="fromUserId != null">
                from_user_id = #{fromUserId,jdbcType=BIGINT},
            </if>
            <if test="toUserId != null">
                to_user_id = #{toUserId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="keyword != null and keyword != '' ">
                keyword = #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_transfer_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishTransferRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_transfer_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>