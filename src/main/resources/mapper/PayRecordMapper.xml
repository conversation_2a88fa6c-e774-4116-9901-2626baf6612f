<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PayRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PayRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_method" jdbcType="VARCHAR" property="payMethod"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="callback_time" jdbcType="TIMESTAMP" property="callbackTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="biz_code" jdbcType="BIGINT" property="bizCode"/>
        <result column="biz_status" jdbcType="VARCHAR" property="bizStatus"/>
        <result column="request" jdbcType="VARCHAR" property="request"/>
        <result column="response" jdbcType="VARCHAR" property="response"/>
        <result column="trade_no" jdbcType="VARCHAR" property="tradeNo"/>
        <result column="user_bind_id" jdbcType="BIGINT" property="userBindId"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.pay_type
        , t.pay_method
        , t.amount
        , t.create_time
        , t.callback_time
        , t.status
        , t.biz_type
        , t.biz_code
        , t.biz_status
        , t.request
        , t.response
        , t.trade_no
        , t.user_bind_id
        , t.ref_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payMethod != null and payMethod != '' ">
                AND t.pay_method = #{payMethod, jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="callbackTime != null">
                AND t.callback_time = #{callbackTime, jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="bizCode != null">
                AND t.biz_code = #{bizCode, jdbcType=BIGINT}
            </if>
            <if test="bizStatus != null and bizStatus != '' ">
                AND t.biz_status = #{bizStatus, jdbcType=VARCHAR}
            </if>
            <if test="tradeNo != null and tradeNo != '' ">
                AND t.trade_no = #{tradeNo, jdbcType=VARCHAR}
            </if>
            <if test="userBindId != null">
                AND t.user_bind_id = #{userBindId, jdbcType=BIGINT}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PayRecord">
        insert into tstd_pay_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payMethod != null and payMethod != '' ">
                pay_method,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="callbackTime != null ">
                callback_time,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type,
            </if>
            <if test="bizCode != null ">
                biz_code,
            </if>
            <if test="bizStatus != null and bizStatus != '' ">
                biz_status,
            </if>
            <if test="userBindId != null">
                user_bind_id,
            </if>
            <if test="refId != null">
                ref_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null and payMethod != '' ">
                #{payMethod,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackTime != null">
                #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null">
                #{bizCode,jdbcType=BIGINT},
            </if>
            <if test="bizStatus != null and bizStatus != '' ">
                #{bizStatus,jdbcType=VARCHAR},
            </if>
            <if test="userBindId != null">
                #{userBindId, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                #{refId, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_pay_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PayRecord">
        update tstd_pay_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payMethod != null and payMethod != '' ">
                pay_method = #{payMethod,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackTime != null">
                callback_time = #{callbackTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="bizCode != null">
                biz_code = #{bizCode,jdbcType=BIGINT},
            </if>
            <if test="bizStatus != null and bizStatus != '' ">
                biz_status = #{bizStatus,jdbcType=VARCHAR},
            </if>
            <if test="request != null and request != '' ">
                request = #{request,jdbcType=VARCHAR},
            </if>
            <if test="response != null and response != '' ">
                response = #{response,jdbcType=VARCHAR},
            </if>
            <if test="tradeNo != null and tradeNo != '' ">
                trade_no = #{tradeNo,jdbcType=VARCHAR},
            </if>
            <if test="userBindId != null">
                user_bind_id = #{userBindId, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                ref_id = #{refId, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_pay_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PayRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_pay_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectLastUserBindIdByCondition" parameterType="com.std.core.pojo.domain.PayRecord"
            resultType="java.lang.Long">
        select user_bind_id
        from tstd_pay_record
        where user_id = #{userId}
          and pay_type = #{payType}
          and status = '1'
        order by id desc limit 1
    </select>

    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_pay_record t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>