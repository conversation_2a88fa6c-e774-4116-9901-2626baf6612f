<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.HisStaticMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.HisStatic">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="first_amount" jdbcType="DECIMAL" property="firstAmount"/>
        <result column="luck_amount" jdbcType="DECIMAL" property="luckAmount"/>
        <result column="withdraw_amount" jdbcType="DECIMAL" property="withdrawAmount"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.first_amount
        , t.luck_amount
        , t.withdraw_amount
        , t.total_amount
        , t.status
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="firstAmount != null">
                AND t.first_amount = #{firstAmount, jdbcType=DECIMAL}
            </if>
            <if test="luckAmount != null">
                AND t.luck_amount = #{luckAmount, jdbcType=DECIMAL}
            </if>
            <if test="withdrawAmount != null">
                AND t.withdraw_amount = #{withdrawAmount, jdbcType=DECIMAL}
            </if>
            <if test="totalAmount != null">
                AND t.total_amount = #{totalAmount, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.HisStatic">
        insert into tstd_his_static
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="firstAmount != null ">
                first_amount,
              </if>
              <if test="luckAmount != null ">
                luck_amount,
              </if>
              <if test="withdrawAmount != null ">
                withdraw_amount,
              </if>
              <if test="totalAmount != null ">
                total_amount,
              </if>
            <if test="status != null and status != '' ">
                status,
            </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="firstAmount != null">
                #{firstAmount,jdbcType=DECIMAL},
            </if>
            <if test="luckAmount != null">
                #{luckAmount,jdbcType=DECIMAL},
            </if>
            <if test="withdrawAmount != null">
                #{withdrawAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_his_static
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.HisStatic">
        update tstd_his_static
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="firstAmount != null">
                first_amount = #{firstAmount,jdbcType=DECIMAL},
            </if>
            <if test="luckAmount != null">
                luck_amount = #{luckAmount,jdbcType=DECIMAL},
            </if>
            <if test="withdrawAmount != null">
                withdraw_amount = #{withdrawAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_his_static t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.HisStatic"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_his_static t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>