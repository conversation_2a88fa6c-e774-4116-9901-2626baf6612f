<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodUserReadMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodUserRead">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="put_on_ts" jdbcType="BIGINT" property="putOnTs"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.period_id
        , t.put_on_ts
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodUserRead">
        insert into nft_period_user_read
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="putOnTs != null ">
                put_on_ts,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="putOnTs != null ">
                #{putOnTs,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_period_user_read
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodUserRead">
        update nft_period_user_read
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="putOnTs != null">
                put_on_ts = #{putOnTs,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_user_read t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodUserRead"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_user_read t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByLaster" parameterType="com.std.core.pojo.domain.PeriodUserRead"
            resultMap="BaseResultMap">
        select t
            .
            id
             , t.user_id
             , t.period_id
             , t.put_on_ts
             , t.create_datetime
        from nft_period_user_read t
        where t.user_id = #{userId, jdbcType=BIGINT}
        order by t.id desc limit 1
    </select>
</mapper>