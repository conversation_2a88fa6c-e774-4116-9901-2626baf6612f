<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="three_channel_type" jdbcType="VARCHAR" property="threeChannelType"/>
        <result column="three_channel_id" jdbcType="VARCHAR" property="threeChannelId"/>
        <result column="collection_detail_hash" jdbcType="VARCHAR" property="collectionDetailHash"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="plate_category" jdbcType="VARCHAR" property="plateCategory"/>
        <result column="order_number" jdbcType="INTEGER" property="orderNumber"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="owner_type" jdbcType="VARCHAR" property="ownerType"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>

        <result column="contract_token_id" jdbcType="BIGINT" property="contractTokenId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="sell_type" jdbcType="VARCHAR" property="sellType"/>
        <result column="sell_id" jdbcType="VARCHAR" property="sellId"/>

        <result column="sell_price" jdbcType="DECIMAL" property="sellPrice"/>
        <result column="buy_channel" jdbcType="VARCHAR" property="buyChannel"/>
        <result column="buy_price" jdbcType="DECIMAL" property="buyPrice"/>
        <result column="buy_datetime" jdbcType="TIMESTAMP" property="buyDatetime"/>

        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
        <result column="lock_datetime" jdbcType="TIMESTAMP" property="lockDatetime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>

        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="tokenId" jdbcType="BIGINT" property="tokenId"/>
        <result column="challenge_number" jdbcType="INTEGER" property="challengeNumber"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="transform_limit_time" jdbcType="INTEGER" property="transformLimitTime"/>
        <result column="secondAuctionTime" jdbcType="TIMESTAMP" property="secondAuctionTime"/>
        <result column="integral_flag" jdbcType="VARCHAR" property="integralFlag"/>
        <result column="yao_flag" jdbcType="VARCHAR" property="yaoFlag"/>
        <result column="unlock_datetime" jdbcType="TIMESTAMP" property="unlockDatetime"/>
        <result column="serial_no" jdbcType="VARCHAR" property="serialNo"/>
        <result column="exchange_xmeta" jdbcType="VARCHAR" property="exchangeXmeta"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.three_channel_type
        , t.three_channel_id
        , t.collection_detail_hash
        , t.collection_id
        , t.plate_category
        , t.order_number
        , t.user_type
        , t.user_id
        , t.owner_type
        , t.owner_id
        , t.contract_token_id
        , t.token_id
        , t.sell_type
        , t.sell_id
        , t.sell_price
        , t.buy_channel
        , t.buy_price
        , t.buy_datetime
        , t.lock_time
        , t.lock_datetime
        , t.status
        , t.create_datetime
        , t.update_time
        , t.ref_id
        , t.ref_type
        , t.challenge_number
        , t.source
        , t.transform_limit_time
        , t.second_auction_time
        , t.integral_flag
        , t.yao_flag
        , t.unlock_datetime
        , t.serial_no
        , t.exchange_xmeta
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="idList != null and idList.size() != 0 ">
                AND t.id in
                <foreach item="item" index="index" collection="idList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                AND t.three_channel_type = #{threeChannelType, jdbcType=VARCHAR}
            </if>
            <if test="threeChannelId != null">
                AND t.three_channel_id = #{threeChannelId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                AND t.collection_detail_hash = #{collectionDetailHash, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
            </if>
            <if test="orderNumber != null">
                AND t.order_number = #{orderNumber, jdbcType=INTEGER}
            </if>
            <if test="userType != null and userType != '' ">
                AND t.user_type = #{userType, jdbcType=VARCHAR}
            </if>
            <if test="ownerType != null and ownerType != '' ">
                AND t.owner_type = #{ownerType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="ownerId != null">
                AND t.owner_id = #{ownerId, jdbcType=BIGINT}
            </if>
            <if test="contractTokenId != null">
                AND t.contract_token_id = #{contractTokenId, jdbcType=BIGINT}
            </if>
            <if test="tokenId != null and tokenId != '' ">
                AND t.token_id = #{tokenId, jdbcType=VARCHAR}
            </if>
            <if test="sellType != null and sellType != '' ">
                AND t.sell_type = #{sellType, jdbcType=VARCHAR}
            </if>
            <if test="sellId != null and sellId != '' ">
                AND t.sell_id = #{sellId, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="noStatus != null and noStatus != '' ">
                AND t.status <![CDATA[ !=]]> #{noStatus, jdbcType=VARCHAR}
            </if>
            <if test="noStatusList != null and noStatusList.size() != 0 ">
                AND t.status not in
                <foreach item="item" index="index" collection="noStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                AND t.buy_channel = #{buyChannel, jdbcType=VARCHAR}
            </if>
            <if test="buyPrice != null ">
                AND t.buy_price = #{buyPrice, jdbcType=DECIMAL}
            </if>
            <if test="buyDatetime != null">
                AND t.buy_datetime = #{buyDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="lockDatetimeEnd != null">
                <![CDATA[AND t.lock_datetime <= #{lockDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="sellTypeList != null and sellTypeList.size() != 0 ">
                AND t.sell_type in
                <foreach item="item" index="index" collection="sellTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="collectionIdList != null and collectionIdList.size() != 0 ">
                AND t.collection_id in
                <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=BIGINT}
            </if>
            <if test="levelType != null and levelType != '' ">
                AND nc.level_type = #{levelType, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                nc.name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="fileType != null and fileType != '' ">
                AND nc.file_type = #{fileType, jdbcType=VARCHAR}
            </if>
            <if test="special3dFlag != null and special3dFlag != '' ">
                AND nc.special_3d_flag = #{special3dFlag}
            </if>
            <if test="authorId != null ">
                AND nc.author_id = #{authorId, jdbcType=BIGINT}
            </if>
            <if test="author != null and author != '' ">
                AND ta.name like concat('%',#{author, jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != '' ">
                AND nc.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refType != null">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="lockTime != null and lockTime != '' ">
                AND t.lock_time = #{lockTime, jdbcType=BIGINT}
            </if>
            <if test="noLockTime != null and noLockTime != '' ">
                AND t.lock_time <![CDATA[ != ]]> '-1'
            </if>
            <if test="isFilter != null and isFilter != '' ">
                AND ((t.lock_datetime is null or t.lock_datetime <![CDATA[ <=]]> #{lockDatetime, jdbcType=TIMESTAMP})
                and t.lock_time !=
                '-1')
            </if>
            <if test="secondTransferFlag != null and secondTransferFlag != '' ">
                AND ((t.lock_datetime is not null and t.lock_datetime <![CDATA[ >=]]>
                #{lockDatetime, jdbcType=TIMESTAMP})
                or t.lock_time =
                '-1')
            </if>
            <if test="isTransfer != null and isTransfer != '' ">
                AND ((t.transform_limit_time is null or date_add(t.create_datetime,INTERVAL t.transform_limit_time HOUR)
                <![CDATA[ <=]]>
                #{lockDatetime, jdbcType=TIMESTAMP})
                and t.transform_limit_time !=
                '-1')
            </if>
            <if test="transferFlag != null and transferFlag != '' ">
                AND ((t.transform_limit_time is not null and date_add(t.create_datetime,INTERVAL t.transform_limit_time
                HOUR)
                <![CDATA[ >=]]>
                #{lockDatetime, jdbcType=TIMESTAMP})
                or t.transform_limit_time =
                '-1')
            </if>
            <if test="xmetaPutOnFlag != null and xmetaPutOnFlag != '' ">
                AND (nc.category != '2' AND t.lock_time ='-1' AND (nc.publish_xmeta = '0' OR (nc.publish_xmeta = '1' AND publish_xmeta_style
                = '1')))
            </if>
            <if test="challengeNumber != null">
                AND t.challenge_number = #{challengeNumber, jdbcType=INTEGER}
            </if>
            <if test="source != null and source != '' ">
                AND t.source = #{source, jdbcType=VARCHAR}
            </if>
            <if test="transformLimitTime != null">
                AND t.transform_limit_time = #{transformLimitTime, jdbcType=INTEGER}
            </if>
            <if test="secondAuctionTime != null">
                AND (t.second_auction_time is null or t.second_auction_time <![CDATA[ < ]]>
                #{secondAuctionTime, jdbcType=TIMESTAMP})
            </if>
            <if test="integralFlag != null and integralFlag != '' ">
                AND t.integral_flag = #{integralFlag, jdbcType=VARCHAR}
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                AND t.yao_flag = #{yaoFlag, jdbcType=VARCHAR}
            </if>
            <if test="fileType != null and fileType != '' ">
                AND nc.file_type = #{fileType, jdbcType=VARCHAR}
            </if>
            <if test="levelTypeList != null and levelTypeList.size() != 0 ">
                AND nc.level_type in
                <foreach item="item" index="index" collection="levelTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="unlockDatetime != null">
                AND t.unlock_datetime = #{unlockDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                AND t.exchange_xmeta = #{exchangeXmeta, jdbcType=VARCHAR}
            </if>
            <if test="authorId != null">
                AND t.collection_id in (select id from nft_collection where author_id = #{authorId})
            </if>
        </trim>
    </sql>


    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type,
            </if>
            <if test="threeChannelId != null">
                three_channel_id,
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                collection_detail_hash,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="orderNumber != null ">
                order_number,
            </if>
            <if test="userType != null and userType != '' ">
                user_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="ownerType != null and ownerType != '' ">
                owner_type,
            </if>
            <if test="ownerId != null ">
                owner_id,
            </if>
            <if test="contractTokenId != null ">
                contract_token_id,
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id ,
            </if>
            <if test="sellType != null and sellType != '' ">
                sell_type,
            </if>
            <if test="sellId != null and sellId != '' ">
                sell_id,
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                sell_price,
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                buy_channel,
            </if>
            <if test="buyPrice != null ">
                buy_price,
            </if>
            <if test="buyDatetime != null">
                buy_datetime,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="lockDatetime != null">
                lock_datetime,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="refId != null">
                ref_id ,
            </if>
            <if test="refType != null">
                ref_type,
            </if>
            <if test="challengeNumber != null">
                challenge_number,
            </if>
            <if test="source != null and source != '' ">
                source,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="secondAuctionTime != null">
                second_auction_time,
            </if>
            <if test="integralFlag != null and integralFlag != '' ">
                integral_flag,
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                yao_flag,
            </if>
            <if test="unlockDatetime != null">
                unlock_datetime,
            </if>
            <if test="serialNo != null and serialNo != '' ">
                serial_no,
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                exchange_xmeta,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                #{collectionDetailHash, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null">
                #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="userType != null and userType != '' ">
                #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null and ownerType != '' ">
                #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="contractTokenId != null">
                #{contractTokenId,jdbcType=INTEGER},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId, jdbcType=VARCHAR},
            </if>
            <if test="sellType != null and sellType != '' ">
                #{sellType,jdbcType=VARCHAR},
            </if>
            <if test="sellId != null and sellId != '' ">
                #{sellId,jdbcType=BIGINT},
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                #{sellPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                #{buyChannel, jdbcType=VARCHAR},
            </if>
            <if test="buyPrice != null ">
                #{buyPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyDatetime != null">
                #{buyDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="lockDatetime != null">
                #{lockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                #{refId, jdbcType=BIGINT},
            </if>
            <if test="refType != null">
                #{refType, jdbcType=VARCHAR},
            </if>
            <if test="challengeNumber != null">
                #{challengeNumber, jdbcType=INTEGER},
            </if>
            <if test="source != null and source != '' ">
                #{source, jdbcType=VARCHAR},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="secondAuctionTime != null">
                #{secondAuctionTime, jdbcType=TIMESTAMP},
            </if>
            <if test="integralFlag != null and integralFlag != '' ">
                #{integralFlag, jdbcType=VARCHAR},
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                #{yaoFlag, jdbcType=VARCHAR},
            </if>
            <if test="unlockDatetime != null">
                #{unlockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="serialNo != null and serialNo != '' ">
                #{serialNo, jdbcType=VARCHAR},
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                #{exchangeXmeta, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into nft_collection_detail
        (
        three_channel_type
        , three_channel_id
        , collection_id
        , plate_category
        , order_number
        , user_type
        , user_id
        , owner_type
        , owner_id
        , contract_token_id
        , token_id
        , create_datetime
        , status
        , buy_channel
        , buy_price
        , buy_datetime
        , lock_time
        , lock_datetime
        , update_time
        , ref_id
        , ref_type
        , source
        , transform_limit_time
        , serial_no
        , exchange_xmeta
        )
        values
        <foreach item="collectionDetail" index="index" collection="list" separator=",">
            (
            #{collectionDetail.threeChannelType,jdbcType=VARCHAR},
            #{collectionDetail.threeChannelId,jdbcType=BIGINT},
            #{collectionDetail.collectionId,jdbcType=BIGINT},
            #{collectionDetail.plateCategory, jdbcType=VARCHAR},
            #{collectionDetail.orderNumber,jdbcType=INTEGER},
            #{collectionDetail.userType, jdbcType=VARCHAR},
            #{collectionDetail.userId,jdbcType=BIGINT},
            #{collectionDetail.ownerType, jdbcType=VARCHAR},
            #{collectionDetail.ownerId,jdbcType=BIGINT},
            #{collectionDetail.contractTokenId,jdbcType=BIGINT},
            #{collectionDetail.tokenId, jdbcType=VARCHAR},
            #{collectionDetail.createDatetime,jdbcType=TIMESTAMP},
            #{collectionDetail.status,jdbcType=VARCHAR},
            #{collectionDetail.buyChannel, jdbcType=VARCHAR},
            #{collectionDetail.buyPrice, jdbcType=DECIMAL},
            #{collectionDetail.buyDatetime, jdbcType=TIMESTAMP},
            #{collectionDetail.lockTime, jdbcType=INTEGER},
            #{collectionDetail.lockDatetime, jdbcType=TIMESTAMP},
            #{collectionDetail.updateTime, jdbcType=TIMESTAMP},
            #{collectionDetail.refId, jdbcType=BIGINT},
            #{collectionDetail.refType, jdbcType=VARCHAR},
            #{collectionDetail.source, jdbcType=VARCHAR},
            #{collectionDetail.transformLimitTime, jdbcType=INTEGER},
            #{collectionDetail.serialNo, jdbcType=VARCHAR},
            #{collectionDetail.exchangeXmeta, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <insert id="channelInsert" useGeneratedKeys="true" keyProperty="id">
        insert into nft_collection_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type,
            </if>
            <if test="threeChannelId != null">
                three_channel_id,
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                collection_detail_hash,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="orderNumber != null ">
                order_number,
            </if>
            <if test="userType != null and userType != '' ">
                user_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="ownerType != null and ownerType != '' ">
                owner_type,
            </if>
            <if test="ownerId != null ">
                owner_id,
            </if>
            <if test="contractTokenId != null ">
                contract_token_id,
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id ,
            </if>
            <if test="sellType != null and sellType != '' ">
                sell_type,
            </if>
            <if test="sellId != null and sellId != '' ">
                sell_id,
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                sell_price,
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                buy_channel,
            </if>
            <if test="buyPrice != null ">
                buy_price,
            </if>
            <if test="buyDatetime != null">
                buy_datetime,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="lockDatetime != null">
                lock_datetime,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="refId != null">
                ref_id ,
            </if>
            <if test="refType != null">
                ref_type,
            </if>
            <if test="challengeNumber != null">
                challenge_number,
            </if>
            <if test="source != null and source != '' ">
                source,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="secondAuctionTime != null">
                second_auction_time,
            </if>
            <if test="serialNo != null and serialNo != '' ">
                serial_no,
            </if>
        </trim>
        select
        <trim suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                #{collectionDetailHash, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null">
                #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="userType != null and userType != '' ">
                #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null and ownerType != '' ">
                #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="contractTokenId != null">
                #{contractTokenId,jdbcType=INTEGER},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId, jdbcType=VARCHAR},
            </if>
            <if test="sellType != null and sellType != '' ">
                #{sellType,jdbcType=VARCHAR},
            </if>
            <if test="sellId != null and sellId != '' ">
                #{sellId,jdbcType=BIGINT},
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                #{sellPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                #{buyChannel, jdbcType=VARCHAR},
            </if>
            <if test="buyPrice != null ">
                #{buyPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyDatetime != null">
                #{buyDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="lockDatetime != null">
                #{lockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                #{refId, jdbcType=BIGINT},
            </if>
            <if test="refType != null">
                #{refType, jdbcType=VARCHAR},
            </if>
            <if test="challengeNumber != null">
                #{challengeNumber, jdbcType=INTEGER},
            </if>
            <if test="source != null and source != '' ">
                #{source, jdbcType=VARCHAR},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="secondAuctionTime != null">
                #{secondAuctionTime, jdbcType=TIMESTAMP},
            </if>
            <if test="serialNo != null and serialNo != '' ">
                #{serialNo, jdbcType=VARCHAR},
            </if>

        </trim>
        FROM (SELECT 1)a
        where not exists (select token_id from nft_collection_detail where token_id=#{tokenId} and `status` != '11')
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetail">
        update nft_collection_detail
        <set>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type = #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                three_channel_id = #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailHash != null and collectionDetailHash != '' ">
                collection_detail_hash = #{collectionDetailHash, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category = #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="orderNumber != null">
                order_number = #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="userType != null and userType != '' ">
                user_type = #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null and ownerType != '' ">
                owner_type = #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="contractTokenId != null">
                contract_token_id = #{contractTokenId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id = #{tokenId, jdbcType=VARCHAR},
            </if>
            <if test="sellType != null and sellType != '' ">
                sell_type = #{sellType,jdbcType=VARCHAR},
            </if>
            <if test="sellId != null and sellId != '' ">
                sell_id = #{sellId,jdbcType=BIGINT},
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                sell_price = #{sellPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                buy_channel = #{buyChannel, jdbcType=VARCHAR},
            </if>
            <if test="buyPrice != null ">
                buy_price = #{buyPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyDatetime != null">
                buy_datetime = #{buyDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="lockDatetime != null">
                lock_datetime = #{lockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="challengeNumber != null">
                challenge_number = #{challengeNumber, jdbcType=INTEGER},
            </if>
            <if test="source != null and source != '' ">
                source = #{source, jdbcType=VARCHAR},
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time = #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="secondAuctionTime != null">
                second_auction_time = #{secondAuctionTime, jdbcType=TIMESTAMP},
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                yao_flag = #{yaoFlag, jdbcType=VARCHAR},
            </if>
            <if test="unlockDatetime != null">
                unlock_datetime = #{unlockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                exchange_xmeta = #{exchangeXmeta, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateTransforOwner">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_collection_detail
            <set>
                <if test="item.ownerType != null and item.ownerType != '' ">
                    owner_type = #{item.ownerType, jdbcType=VARCHAR},
                </if>
                <if test="item.ownerId != null">
                    owner_id = #{item.ownerId,jdbcType=BIGINT},
                </if>
                <if test="item.status != null and item.status != '' ">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime, jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="updateTradeTransforOwner">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_collection_detail
            <set>
                <if test="item.buyChannel != null and item.buyChannel != '' ">
                    buy_channel = #{item.buyChannel, jdbcType=VARCHAR},
                </if>
                <if test="item.buyPrice != null and item.buyPrice != '' ">
                    buy_price = #{item.buyPrice,  jdbcType=DECIMAL},
                </if>
                <if test="item.buyDatetime != null">
                    buy_datetime = #{item.buyDatetime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.ownerType != null and item.ownerType != '' ">
                    owner_type = #{item.ownerType, jdbcType=VARCHAR},
                </if>
                <if test="item.ownerId != null">
                    owner_id = #{item.ownerId,jdbcType=BIGINT},
                </if>
                <if test="item.status != null and item.status != '' ">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updateTime != null">
                    update_time = #{item.updateTime, jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="channelTransferModify">
        UPDATE nft_collection_detail
        SET `status` = '10'
        WHERE
        id IN
        <foreach item="item" index="index" collection="idList" open="(" separator=","
                close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        AND ( select amount from (SELECT count( 1 ) amount FROM nft_collection_detail WHERE id IN
        <foreach item="item" index="index" collection="idList" open="(" separator=","
                close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        AND owner_type = '0' AND owner_id = #{ownerId} AND `status` = '0')a )=#{count}
    </update>
    <update id="batchModifyStatus">
        UPDATE nft_collection_detail
        SET `status` = #{status}
        WHERE
        id IN
        <foreach item="item" index="index" collection="idList" open="(" separator=","
                close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="addChallengeNumber">
        update nft_collection_detail
        set challenge_number=challenge_number + 1
        where id = #{id};
    </update>

    <update id="updateThreeSystem">
        update nft_collection_detail
        <set>
            <if test="orderNumber != null">
                order_number = #{orderNumber,jdbcType=INTEGER},
            </if>
            <if test="ownerType != null and ownerType != '' ">
                owner_type = #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="sellType != null and sellType != '' ">
                sell_type = #{sellType,jdbcType=VARCHAR},
            </if>
            <if test="sellId != null and sellId != '' ">
                sell_id = #{sellId,jdbcType=BIGINT},
            </if>
            <if test="sellPrice != null and sellPrice != '' ">
                sell_price = #{sellPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyChannel != null and buyChannel != '' ">
                buy_channel = #{buyChannel, jdbcType=VARCHAR},
            </if>
            <if test="buyPrice != null ">
                buy_price = #{buyPrice, jdbcType=DECIMAL},
            </if>
            <if test="buyDatetime != null">
                buy_datetime = #{buyDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="lockDatetime != null">
                lock_datetime = #{lockDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="challengeNumber != null">
                challenge_number = #{challengeNumber, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time = #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="secondAuctionTime != null">
                second_auction_time = #{secondAuctionTime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIntegralFlag">
        update nft_collection_detail
        <set>
            <if test="integralFlag != null and integralFlag != '' ">
                integral_flag = #{integralFlag, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateDegressionAuctionEndUnLock">
        update nft_collection_detail
        set `status`='0'
        where id in (SELECT collection_detail_id
                     from nft_degression_auction_collection
                     where auction_id = #{id}
                       and `status` = '0')
    </update>
    <update id="updateLotteryActivityEndUnLock">
        update nft_collection_detail
        set `status`='0'
        where id in (SELECT collection_detail_id
                     from nft_lottery_activity_join_detail
                     where activity_id = #{lotteryActivityId})
    </update>
    <update id="updateModifyPlateCategory">
        update nft_collection_detail
        set `plate_category`=#{plateCategory}
        where collection_id = #{collectionId};
    </update>
    <update id="updateOrderNumberByCollection">
        SET
        @uuid=0;
        UPDATE nft_collection_detail
        set order_number=(@uuid:=@uuid+1)
        WHERE collection_id = #{collectionId};
    </update>
    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>, ct.token_id tokenId
        from nft_collection_detail t
        INNER JOIN nft_contract_token ct on t.contract_token_id = ct.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        INNER JOIN nft_collection nc on t.collection_id = nc.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByConditionSimpleIds" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Long">
        select
        t.id
        from nft_collection_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectTotalCount" parameterType="com.std.core.pojo.domain.CollectionDetail" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByConditionSimple" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="StaticBaseResultMap" type="com.std.core.pojo.domain.CollectionDetail">
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
    </resultMap>

    <!-- 组合条件查询 -->
    <select id="selectByConditionGroupByUserId" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="StaticBaseResultMap">
        select
        owner_id,
        count(1) total_quantity
        from nft_collection_detail t
        <include refid="where_condition"/>
        group by owner_id
        order by count(1) desc
    </select>

    <!-- 组合条件查询 -->
    <select id="selectFrontByCondition" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        INNER JOIN nft_collection nc on t.collection_id = nc.id
        left join nft_company ta on nc.author_id=ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectCollectionByUserId" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Long">
        select
        distinct collection_id
        from nft_collection_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 挑战可挑选的时刻查询 -->
    <select id="challengeCollectionList" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Long">
        SELECT DISTINCT collection_id
        FROM nft_collection_detail
        WHERE ((`status` = 0
        AND owner_id = #{ownerId, jdbcType=BIGINT})
        or (`status` = 2))
        and collection_id in (select id from nft_collection tc
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="keywords != null and keywords !=''">
                AND tc.name like concat('%', #{keywords}, '%')
            </if>
        </trim>
        )

    </select>

    <!-- 组合条件查询 -->
    <select id="countByCondition" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Integer">
        select
        count(1)
        from nft_collection_detail t
        <include refid="where_condition"/>
    </select>

    <!-- 组合条件查询 -->
    <select id="countCollectionCount" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Integer">
        SELECT
        count( 1 )
        FROM
        (
        SELECT
        count( collection_id )
        FROM
        nft_collection_detail t
        WHERE 1=1
        <if test="collectionId != null">
            AND collection_id = #{collectionId, jdbcType=BIGINT}
        </if>
        <if test="ownerId != null">
            AND owner_id = #{ownerId, jdbcType=BIGINT}
        </if>
        GROUP BY
        collection_id) t
    </select>

    <!-- 组合条件查询 -->
    <select id="minByCondition" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.String">
        select
        min(sell_type)
        from nft_collection_detail t
        <include refid="where_condition"/>
    </select>
    <select id="getFocusOnCollectionDetail" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseResultMap">
        SELECT
        tb.*
        FROM
        tstd_forum_action t
        LEFT JOIN nft_collection_detail tb ON t.ref_id=tb.id
        WHERE
        t.type = '5'
        and t.user_id=#{userId}
        AND tb.`status`='2'
        AND tb.sell_type in ('0','1')

        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="collectionDetailPersonFront" resultType="com.std.core.pojo.response.CollectionDetailListFrontRes">
        select
        t.id id
        , t.three_channel_type threeChannelType
        , nc.id collectionId
        , t.user_id userId
        , t.owner_id ownerId
        , t.contract_token_id contractTokenId
        , ct.token_id tokenId
        , t.sell_type sellType
        , t.buy_channel buyChannel
        , t.status
        , t.buy_datetime buyDatetime
        , t.lock_time lockTime
        , t.lock_datetime lockDatetime
        , t.transform_limit_time transformLimitTime
        , t.create_datetime createDatetime
        , nc.name
        , nc.type
        , nc.level_type levelType
        , nc.file_type fileType
        , nc.cover_file_url coverFileUrl
        , nc.file_url fileUrl
        , nc.chain_type chainType
        , t.challenge_number challengeNumber
        , t.update_time updateTime
        , t.integral_flag integralFlag
        from nft_collection_detail t
        INNER JOIN nft_collection nc on t.collection_id = nc.id
        INNER JOIN nft_contract_token ct on t.contract_token_id = ct.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>


    <select id="collectionDetailPersonFrontU3d" resultType="com.std.core.pojo.response.CollectionDetailListFrontU3dRes">
        select
        t.id id
        , nc.id collectionId
        , t.owner_id ownerId
        , t.token_id tokenId
        , nc.name
        , nc.type
        , nc.level_type levelType
        , nc.file_type fileType
        , nc.cover_file_url coverFileUrl
        , nc.file_url fileUrl
        , nc.special_3d_flag u3dFlag
        , bu.boat_type boatType
        , (select count(1) from yg_fish_user_exhibits where collection_detail_id = t.id) showFlag
        FROM nft_collection_detail t
        INNER JOIN nft_collection nc on t.collection_id = nc.id
        LEFT JOIN yg_fish_boat_user bu on t.id = bu.collection_detail_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="challengeCollectionUseList" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Long">
        SELECT DISTINCT collection_id
        FROM nft_collection_detail
        WHERE ((`status` = 0
        AND owner_id = #{ownerId, jdbcType=BIGINT})
        )
        and collection_id in (select id from nft_collection tc
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="keywords != null and keywords !=''">
                AND tc.name like concat('%', #{keywords}, '%')
            </if>
        </trim>
        )

    </select>
    <select id="getNewBuyPrice" resultType="java.math.BigDecimal">
        select buy_price
        from nft_collection_detail
        where collection_id = #{collectionId}
          and buy_channel = '1'
        order by buy_datetime desc LIMIT 1;
    </select>

    <select id="getCollectCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM (SELECT owner_id
              FROM nft_collection_detail
              WHERE collection_id = #{collectionId}
                and (owner_type = '0' or (owner_type = '1' and buy_channel = '0'))
                and `status` in ('0', '1', '2', '3', '5', '9', '10', '14', '15', '16', '18', '19')) t
    </select>

    <select id="detailChallengeFinalCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM (
                 SELECT DISTINCT collection_id collectionId
                 FROM nft_collection_detail
                 WHERE owner_id = #{userId}
                   and owner_type = '0'
                   and `status` = '0') t

        WHERE t.collectionId IN (
            SELECT collection_id
            FROM nft_challenge_collection
            WHERE challenge_id = #{id});
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>

    <select id="getOfferCount" parameterType="com.std.core.pojo.domain.CollectionDetail" resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_detail
        WHERE collection_id = #{collectionId}
          and status = #{status}
    </select>

    <select id="selectTotalCountCollectionDetail" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_detail t
        <include refid="where_condition"/>
    </select>

    <select id="selectXmetaAvailableCountCollectionDetail" parameterType="java.lang.Long"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_detail t
                 inner join nft_collection nc on t.collection_id = nc.id
        where nc.category = '2'
          and t.owner_id = #{userId}
          and t.owner_type = '0'
          and t.exchange_xmeta = '1'
    </select>

    <select id="selectXmetaAvailableCollectionDetailList" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nft_collection_detail t
        inner join nft_collection nc on t.collection_id = nc.id
        where nc.category = '2'
        and t.owner_id = #{userId}
        and t.owner_type = '0'
        and t.exchange_xmeta = '1'
    </select>

    <select id="selectCanChallenge" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        nft_collection_detail t
        WHERE
        t.id NOT IN (
        SELECT
        collection_detail_id
        FROM
        nft_challenge_order_detail
        WHERE
        challenge_id = #{challengeId}
        <if test="conditionId != null">
            AND condition_id = #{conditionId, jdbcType=BIGINT}
        </if>
        AND order_id IN (
        SELECT
        id
        FROM
        nft_challenge_order
        WHERE
        `status` IN ( '0', '1' )))
        <if test="collectionId != null">
            AND t.collection_id = #{collectionId, jdbcType=BIGINT}
        </if>
        <if test="collectionIdList != null and collectionIdList.size() != 0 ">
            AND t.collection_id in
            <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                    close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="ownerType != null and ownerType != '' ">
            AND t.owner_type = #{ownerType, jdbcType=VARCHAR}
        </if>
        <if test="ownerId != null">
            AND t.owner_id = #{ownerId, jdbcType=BIGINT}
        </if>
        <if test="status != null and status != '' ">
            AND t.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="statusList != null and statusList.size() != 0 ">
            AND t.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator=","
                    close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="lockTime != null">
            AND (t.lock_time = #{lockTime, jdbcType=INTEGER} or (t.lock_time <![CDATA[ != ]]> -1 AND t.lock_datetime <![CDATA[ >= ]]>
            now()))
        </if>
        <if test="noLockTime != null">
            AND t.lock_time <![CDATA[ != ]]> -1 AND t.lock_datetime <![CDATA[ < ]]> now()
        </if>
    </select>

    <select id="selectCanChallengeTotalCount" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM
        nft_collection_detail td inner join nft_challenge_condition_detail nccd on td.collection_id = nccd.collection_id
        WHERE
        (nccd.lock_condition = '0'
        OR (td.lock_time != '-1'
        AND td.lock_datetime <![CDATA[ <= ]]> now()
        AND nccd.lock_condition = '1')
        OR ((td.lock_time = '-1' or (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ > ]]> now()))
        AND nccd.lock_condition = '2'))
        AND td.id NOT IN (
        SELECT
        collection_detail_id
        FROM
        nft_challenge_order_detail
        WHERE
        challenge_id = #{challengeId}
        <if test="conditionId != null">
            AND condition_id = #{conditionId, jdbcType=BIGINT}
        </if>
        AND order_id IN (
        SELECT
        id
        FROM
        nft_challenge_order
        WHERE
        `status` IN ( '0', '1' )))
        <if test="collectionId != null">
            AND td.collection_id = #{collectionId, jdbcType=BIGINT}
        </if>
        <if test="collectionIdList != null and collectionIdList.size() != 0 ">
            AND td.collection_id in
            <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                    close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="ownerType != null and ownerType != '' ">
            AND td.owner_type = #{ownerType, jdbcType=VARCHAR}
        </if>
        <if test="ownerId != null">
            AND td.owner_id = #{ownerId, jdbcType=BIGINT}
        </if>
        <if test="status != null and status != '' ">
            AND td.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="statusList != null and statusList.size() != 0 ">
            AND td.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator=","
                    close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="conditionId != null">
            AND nccd.condition_id = #{conditionId, jdbcType=BIGINT}
        </if>
    </select>

    <select id="detailByTransferRecordId" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.id in (select collection_detail_id from nft_collection_detail_transfer_detail where
        transfer_record_id=#{transferRecordId})
    </select>
    <select id="detailSimpleList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.owner_type='0' and t.owner_id=#{ownerId} and t.status='10' and t.id in
        <foreach item="item" index="index" collection="collectionDetailIdList" open="(" separator=","
                close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectCountByTokenId" resultType="java.lang.Long">
        select t.id
        from nft_collection_detail t
        where t.token_id = #{tokenId}
    </select>

    <select id="selectU3dOpenFlag" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultType="java.lang.Integer">
        select count(1)
        from nft_collection_detail
        where owner_id = #{ownerId}
          and status not in ('4', '6', '11')
          and collection_id in (select id from nft_collection where u3d_flag = '1')
    </select>

    <resultMap id="BaseU3dResultMap" type="com.std.core.pojo.response.CollectionListU3dRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="android_ab" jdbcType="VARCHAR" property="androidAb"/>
        <result column="ios_ab" jdbcType="VARCHAR" property="iosAb"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    </resultMap>

    <select id="selectMyCollectionList" parameterType="com.std.core.pojo.domain.CollectionDetail"
            resultMap="BaseU3dResultMap">
        select t.id
             , t.name
             , t.level_type
             , t.file_type
             , t.cover_file_url
             , t.file_url
             , (select count(1)
                from nft_collection_detail td
                where td.owner_id = #{ownerId}
                  and t.id = td.collection_id
                  and td.status not in ('4', '6', '11')) quantity
        from nft_collection t
        where id in (select collection_id from nft_collection_detail where owner_id = #{ownerId})
    </select>

    <select id="detailUserHaveCollectionNumber"
            resultType="com.std.core.pojo.domain.CollectionDetailUserHaveNumber">
        SELECT t.owner_id            userId,
               COALESCE(count(1), 0) quantity
        FROM nft_collection_detail t
        WHERE collection_id = #{needCollectionId}
          AND owner_type = '0'
          AND `status` in ('0', '2')
        GROUP BY t.owner_id
    </select>
    <select id="detailSimpleListByUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.owner_type='0' and t.owner_id=#{userId} and t.status='0'
        AND ((t.lock_datetime is null or t.lock_datetime <![CDATA[ <=]]> #{date})
        and t.lock_time !=
        '-1')
        AND ((t.transform_limit_time is null or date_add(t.create_datetime,INTERVAL t.transform_limit_time HOUR)
        <![CDATA[ <=]]>
        #{date})
        and t.transform_limit_time !=
        '-1')
        limit 300;
    </select>
    <select id="selectPageIntegralExchange"
            resultType="com.std.core.pojo.response.CollectionDetailPageIntegralExchangeRes">
        SELECT ta.id             collectionId,
               ta.name,
               ta.file_type      fileType,
               ta.cover_file_url coverFileUrl,
               ta.file_url       fileUrl,
               ta.integral_price integralPrice,
               count(1)          quantity
        FROM nft_collection_detail t
                 INNER JOIN nft_collection ta ON t.collection_id = ta.id
        where t.owner_type = '0'
          AND t.owner_id = #{ownerId}
          AND t.integral_flag = '0'
          AND t.status = '0'
          AND ta.integral_price <![CDATA[>]]> 0
        GROUP BY t.collection_id
    </select>

    <select id="selectMyHoldPropsList" resultType="java.lang.String">
        select distinct props_code
        from nft_u3d_props
        where type = '0'
        union
        select distinct props_code
        from nft_u3d_props
        where type = '1'
          and id in (select props_id
                     from nft_collection_u3d_props
                     where collection_id in (select distinct collection_id
                                             from nft_collection_detail
                                             where status not in ('4', '6', '11')
                                               and owner_type = '0'
                                               and owner_id = #{ownerId}))
    </select>
    <select id="detailMaxNumber" resultType="java.lang.Integer">
        select COALESCE(max(t.order_number), 0)
        from nft_collection_detail t
        where t.collection_id = #{collectionId}
    </select>
    <select id="selectCollectionIdGruop" resultType="java.lang.Long">
        select DISTINCT collection_id
        from nft_collection_detail
        order by collection_id asc
    </select>
    <select id="selectByHash" parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail t
        where t.collection_detail_hash = #{assetHash}
    </select>
    <select id="selectCollectionDetailUserId" resultType="java.lang.Long">
        select DISTINCT owner_id
        from nft_collection_detail
        where owner_type = '0'
          and collection_id = #{collectionId}
    </select>
    <select id="selectDistinctUser" resultType="java.lang.Long">
        select
        Distinct t.owner_id
        from nft_collection_detail t
        <include refid="where_condition"/>
    </select>

    <select id="selectMyHavePublishXmetaCollectionDetail" resultType="com.xmeta.opensdk.model.vo.GoodsListVO">
        select t.cover_file_url coverFileUrl
        , t.id archiveId
        , ncd.id goodsId
        , ncd.order_number goodsNo
        , t.total_quantity totalQuantity
        , t.name goodsName
        , t.content goodsDescription
        from nft_collection_detail ncd
        inner join nft_collection t
        on ncd.collection_id = t.id
        where t.status = '1'
        and t.type in ('1', '2', '3')
        and t.publish_xmeta = '1'
        and (
        (t.publish_xmeta_style ='0') or
        (t.publish_xmeta_style='1' and ncd.id in (select publish_collection_detail_id from nft_collection_detail_exchange_card where
        status='0'))
        )
        and ncd.status = '0'
        and ncd.owner_type = '0'
        and ncd.owner_id = #{ownerId}
        and ncd.lock_time ='-1'
        <if test="collectionId != null">
            AND t.id = #{collectionId, jdbcType=BIGINT}
        </if>
        order by ncd.id desc
        limit #{startSize}, #{pageCount}
    </select>
    <select id="selectMetaRole" resultType="java.lang.String">
        SELECT ta.meta_biz_type
        FROM nft_collection ta
        where ta.id IN (SELECT DISTINCT collection_id FROM nft_collection_detail t where t.owner_id = #{userId})
          and u3d_flag = '1'
        group by ta.meta_biz_type
    </select>


</mapper>