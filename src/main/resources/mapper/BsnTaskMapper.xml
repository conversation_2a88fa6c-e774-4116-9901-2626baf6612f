<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.BsnTaskMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.BsnTask">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <result column="tx_hash" jdbcType="VARCHAR" property="txHash"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="token_id" jdbcType="BIGINT" property="tokenId"/>
        <result column="class_id" jdbcType="VARCHAR" property="classId"/>
        <result column="nft_id" jdbcType="VARCHAR" property="nftId"/>
        <result column="owner" jdbcType="VARCHAR" property="owner"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.task_id
        , t.tx_hash
        , t.name
        , t.collection_id
        , t.collection_detail_id
        , t.token_id
        , t.class_id
        , t.nft_id
        , t.owner
        , t.create_datetime
        , t.status
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="taskId != null and taskId != '' ">
                AND t.task_id = #{taskId, jdbcType=VARCHAR}
            </if>
            <if test="txHash != null and txHash != '' ">
                AND t.tx_hash = #{txHash, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="tokenId != null">
                AND t.token_id = #{tokenId, jdbcType=BIGINT}
            </if>
            <if test="classId != null and classId != '' ">
                AND t.class_id = #{classId, jdbcType=VARCHAR}
            </if>
            <if test="nftId != null and nftId != '' ">
                AND t.nft_id = #{nftId, jdbcType=VARCHAR}
            </if>
            <if test="owner != null and owner != '' ">
                AND t.owner = #{owner, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.BsnTask">
        insert into nft_bsn_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="taskId != null and taskId != '' ">
                task_id,
            </if>
            <if test="txHash != null and txHash != '' ">
                tx_hash,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="tokenId != null ">
                token_id,
            </if>
            <if test="classId != null and classId != '' ">
                class_id,
            </if>
            <if test="nftId != null and nftId != '' ">
                nft_id,
            </if>
            <if test="owner != null and owner != '' ">
                owner,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null and taskId != '' ">
                #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="txHash != null and txHash != '' ">
                #{txHash,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null">
                #{tokenId,jdbcType=BIGINT},
            </if>
            <if test="classId != null and classId != '' ">
                #{classId,jdbcType=VARCHAR},
            </if>
            <if test="nftId != null and nftId != '' ">
                #{nftId,jdbcType=VARCHAR},
            </if>
            <if test="owner != null and owner != '' ">
                #{owner,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 选择添加 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        insert into nft_bsn_task
        (type
        ,task_id
        ,tx_hash
        ,name
        ,collection_id
        ,collection_detail_id
        ,token_id
        ,class_id
        ,nft_id
        ,owner
        ,create_datetime
        ,status
        ,remark)
        values
        <foreach item="bsnTask" index="index" collection="list" separator=",">
            (
            #{bsnTask.type}
            ,#{bsnTask.taskId}
            ,#{bsnTask.txHash}
            ,#{bsnTask.name}
            ,#{bsnTask.collectionId}
            ,#{bsnTask.collectionDetailId}
            ,#{bsnTask.tokenId}
            ,#{bsnTask.classId}
            ,#{bsnTask.nftId}
            ,#{bsnTask.owner}
            ,#{bsnTask.createDatetime}
            ,#{bsnTask.status}
            ,#{bsnTask.remark}
            )
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_bsn_task
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.BsnTask">
        update nft_bsn_task
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="taskId != null and taskId != '' ">
                task_id = #{taskId,jdbcType=VARCHAR},
            </if>
            <if test="txHash != null and txHash != '' ">
                tx_hash = #{txHash,jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null">
                token_id = #{tokenId,jdbcType=BIGINT},
            </if>
            <if test="classId != null and classId != '' ">
                class_id = #{classId,jdbcType=VARCHAR},
            </if>
            <if test="nftId != null and nftId != '' ">
                nft_id = #{nftId,jdbcType=VARCHAR},
            </if>
            <if test="owner != null and owner != '' ">
                owner = #{owner,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_bsn_task t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.BsnTask"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_bsn_task t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>