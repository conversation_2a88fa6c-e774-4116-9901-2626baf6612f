<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailTransferDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailTransferDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="transfer_record_id" jdbcType="BIGINT" property="transferRecordId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_detail_id
        , t.transfer_record_id
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="transferRecordId != null">
                AND t.transfer_record_id = #{transferRecordId, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailTransferDetail">
        insert into nft_collection_detail_transfer_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="transferRecordId != null ">
                transfer_record_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="transferRecordId != null">
                #{transferRecordId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="batchCreate">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_collection_detail_transfer_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.transferRecordId != null ">
                    transfer_record_id,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.transferRecordId != null">
                    #{item.transferRecordId,jdbcType=BIGINT},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>

        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_transfer_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailTransferDetail">
        update nft_collection_detail_transfer_detail
        <set>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="transferRecordId != null">
                transfer_record_id = #{transferRecordId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailTransferDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectCollectionDetailIds" parameterType="com.std.core.pojo.domain.CollectionDetailTransferDetail"
            resultType="java.lang.Long">
        select
        collection_detail_id
        from nft_collection_detail_transfer_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>


    </select>
    <select id="selectByConditionOss"
            resultType="com.std.core.pojo.response.CollectionDetailTransferDetailOssPageRes">
        SELECT t.id,
               ta.id                  transferRecordId,
               ta.from_user_id        fromUserId,
               ta.to_user_id          toUserId,
               ta.type                type,
               ta.fee                 fee,
               ta.pay_type            payType,
               ta.pay_order_code      payOrderCode,
               ta.pay_status          payStatus,
               ta.pay_datetime        payDatetime,
               t.collection_detail_id collectionDetailId,
               tc.`name`              collectionName,
               tb.token_id            tokenId

        FROM nft_collection_detail_transfer_detail t
                 INNER JOIN nft_collection_detail_transfer_record ta ON ta.id = t.transfer_record_id
                 INNER JOIN nft_collection_detail tb ON t.collection_detail_id = tb.id
                 INNER JOIN nft_collection tc ON tb.collection_id = tc.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="transferRecordId != null">
                AND t.transfer_record_id = #{transferRecordId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND tc.id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND tc.name like concat ('%',#{collectionName, jdbcType=VARCHAR},'%')
            </if>
            <if test="fromUserId != null">
                AND ta.from_user_id = #{fromUserId, jdbcType=BIGINT}
            </if>
            <if test="toUserId != null">
                AND ta.to_user_id = #{toUserId, jdbcType=BIGINT}
            </if>
            <if test="payType != null and payType != '' ">
                AND ta.pay_type = #{payType, jdbcType=BIGINT}
            </if>
        </trim>
    </select>
</mapper>