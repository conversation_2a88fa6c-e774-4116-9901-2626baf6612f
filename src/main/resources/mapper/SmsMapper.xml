<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.SmsMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Sms">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="target" jdbcType="VARCHAR" property="target"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creator" jdbcType="BIGINT" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_no" jdbcType="VARCHAR" property="refNo"/>
        <result column="number" jdbcType="BIGINT" property="number"/>
        <result column="is_read" jdbcType="BIGINT" property="isRead"/>
        <result column="is_send" jdbcType="BIGINT" property="isSend"/>
        <result column="activity_key" jdbcType="VARCHAR" property="activityKey"/>
        <result column="activity_action" jdbcType="VARCHAR" property="activityAction"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    </resultMap>

    <resultMap id="BaseNewsMap" type="com.std.core.pojo.response.SmsNewsRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="content" jdbcType="LONGVARCHAR" property="content"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="creator_name" jdbcType="TIMESTAMP" property="creatorName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.target
        , t.type
        , t.title
        , t.content
        , t.user_id
        , t.status
        , t.creator
        , t.creator_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.ref_type
        , t.ref_no
        , t.is_read
        , t.is_send
        , t.activity_key
        , t.activity_action
        , t.channel_id
        , t.company_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="target != null and target != '' ">
                AND t.target = #{target, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title = #{title, jdbcType=VARCHAR}
            </if>
            <if test="content != null">
                AND t.content = #{content, jdbcType=LONGVARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>

            <if test="creator != null">
                AND t.creator = #{creator, jdbcType=BIGINT}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refNo != null and refNo != '' ">
                AND t.ref_no = #{refNo, jdbcType=VARCHAR}
            </if>
            <if test="isRead != null and isRead != '' ">
                AND t.is_read = #{isRead, jdbcType=VARCHAR}
            </if>
            <if test="isSend != null and isSend != '' ">
                AND t.is_send = #{isSend, jdbcType=VARCHAR}
            </if>
            <if test="activityKey != null and activityKey != '' ">
                AND t.activity_key = #{activityKey, jdbcType=VARCHAR}
            </if>
            <if test="activityAction != null and activityAction != '' ">
                AND t.activity_action = #{activityAction, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="channelIdList != null and channelIdList.size() != 0 ">
                AND t.channel_id in
                <foreach item="item" index="index" collection="channelIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.id_no like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="companyId != null ">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Sms"
            useGeneratedKeys="true" keyProperty="id">
        insert into tstd_sms
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="target != null and target != '' ">
                target,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="title != null and title != '' ">
                title,
            </if>
            <if test="content != null ">
                content,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="creator != null">
                creator,
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="refNo != null and refNo != '' ">
                ref_no,
            </if>
            <if test="isSend != null and isSend != '' ">
                is_send,
            </if>
            <if test="activityKey != null and activityKey != '' ">
                activity_key,
            </if>
            <if test="activityAction != null and activityAction != '' ">
                activity_action,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="target != null and target != '' ">
                #{target,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=BIGINT},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                #{refNo,jdbcType=VARCHAR},
            </if>
            <if test="isSend != null and isSend != '' ">
                #{isSend, jdbcType=VARCHAR},
            </if>
            <if test="activityKey != null and activityKey != '' ">
                #{activityKey, jdbcType=VARCHAR},
            </if>
            <if test="activityAction != null and activityAction != '' ">
                #{activityAction, jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null ">
                #{companyId, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 批量添加 -->
    <insert id="batchInsertSelective" parameterType="com.std.core.pojo.domain.Sms"
            useGeneratedKeys="true" keyProperty="id">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into tstd_sms
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.target != null and item.target != '' ">
                    target,
                </if>
                <if test="item.type != null and item.type != '' ">
                    type,
                </if>
                <if test="item.title != null and item.title != '' ">
                    title,
                </if>
                <if test="item.content != null ">
                    content,
                </if>
                <if test="item.userId != null">
                    user_id,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.creator != null">
                    creator,
                </if>
                <if test="item.creatorName != null and item.creatorName != '' ">
                    creator_name,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
                <if test="item.refType != null and item.refType != '' ">
                    ref_type,
                </if>
                <if test="item.refNo != null and item.refNo != '' ">
                    ref_no,
                </if>
                <if test="item.isSend != null and item.isSend != '' ">
                    is_send,
                </if>
                <if test="item.activityKey != null and item.activityKey != '' ">
                    activity_key,
                </if>
                <if test="item.activityAction != null and item.activityAction != '' ">
                    activity_action,
                </if>
                <if test="item.channelId != null">
                    channel_id,
                </if>
                <if test="item.companyId != null ">
                    company_id,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.target != null and item.target != '' ">
                    #{item.target,jdbcType=VARCHAR},
                </if>
                <if test="item.type != null and item.type != '' ">
                    #{item.type,jdbcType=VARCHAR},
                </if>
                <if test="item.title != null and item.title != '' ">
                    #{item.title,jdbcType=VARCHAR},
                </if>
                <if test="item.content != null">
                    #{item.content,jdbcType=LONGVARCHAR},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=LONGVARCHAR},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null">
                    #{item.creator,jdbcType=BIGINT},
                </if>
                <if test="item.creatorName != null and item.creatorName != '' ">
                    #{item.creatorName,jdbcType=VARCHAR},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.refType != null and item.refType != '' ">
                    #{item.refType,jdbcType=VARCHAR},
                </if>
                <if test="item.refNo != null and item.refNo != '' ">
                    #{item.refNo,jdbcType=VARCHAR},
                </if>
                <if test="item.isSend != null and item.isSend != '' ">
                    #{item.isSend, jdbcType=VARCHAR},
                </if>
                <if test="item.activityKey != null and item.activityKey != '' ">
                    #{item.activityKey, jdbcType=VARCHAR},
                </if>
                <if test="item.activityAction != null and item.activityAction != '' ">
                    #{item.activityAction, jdbcType=VARCHAR},
                </if>
                <if test="item.channelId != null">
                    #{item.channelId,jdbcType=BIGINT},
                </if>
                <if test="item.companyId != null ">
                    #{item.companyId, jdbcType=BIGINT},
                </if>
            </trim>
        </foreach>
    </insert>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_sms
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Sms">
        update tstd_sms
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="target != null and target != '' ">
                target = #{target,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                ref_no = #{refNo,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null and isRead != '' ">
                is_read = #{isRead, jdbcType=VARCHAR},
            </if>
            <if test="isSend != null and isSend != '' ">
                is_send = #{isSend, jdbcType=VARCHAR},
            </if>
            <if test="activityKey != null and activityKey != '' ">
                activity_key = #{activityKey, jdbcType=VARCHAR},
            </if>
            <if test="activityAction != null and activityAction != '' ">
                activity_action = #{activityAction, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="unifiedRead">
        UPDATE tstd_sms
        SET is_read = '1'
        WHERE id IN (SELECT IN
        FROM
            tstd_sms
        WHERE
            is_read = '0'
          AND user_id =#{userId,jdbcType=BIGINT}
          AND type =#{type,jdbcType=VARCHAR})
    </update>
    <update id="updateBatchRevokeSmsCompany">
        update tstd_sms
        <set>
            <if test="target != null and target != '' ">
                target = #{target,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="content != null">
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="refNo != null and refNo != '' ">
                ref_no = #{refNo,jdbcType=VARCHAR},
            </if>
            <if test="isRead != null and isRead != '' ">
                is_read = #{isRead, jdbcType=VARCHAR},
            </if>
            <if test="isSend != null and isSend != '' ">
                is_send = #{isSend, jdbcType=VARCHAR},
            </if>
            <if test="activityKey != null and activityKey != '' ">
                activity_key = #{activityKey, jdbcType=VARCHAR},
            </if>
            <if test="activityAction != null and activityAction != '' ">
                activity_action = #{activityAction, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
        </set>
        where ref_type=#{refType,jdbcType=VARCHAR} and ref_no=#{refNo,jdbcType=VARCHAR}
    </update>
    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Sms"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <!-- 组合条件查询 -->
    <select id="select" parameterType="com.std.core.pojo.domain.Sms"
            resultMap="BaseResultMap">
        select
        t.id
        , t.target
        , t.type
        , t.title
        , t.content
        , t.user_id
        , t.status
        , t.creator
        , t.creator_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.ref_type
        , t.ref_no
        , CONCAT(ifnull(tu.real_name,tu.nickname),'(',tu.mobile,')') as userName
        , t.channel_id
        from tstd_sms t
        left join tsys_user tu on t.user_id=tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectFrontLimit" parameterType="com.std.core.pojo.domain.Sms"
            resultMap="BaseResultMap">
        select
        t.id
        , t.content
        , t.create_datetime
        from tstd_sms t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
        limit ${limitSize}
    </select>

    <!-- 消息中心 -->
    <select id="messageCenter" parameterType="com.std.core.pojo.domain.Sms"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms t LEFT JOIN tstd_sms_read tsr ON t.id=tsr.sms_code
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="target != null and target != '' ">
                AND t.target = #{target, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title = #{title, jdbcType=VARCHAR}
            </if>
            <if test="content != null">
                AND t.content = #{content, jdbcType=LONGVARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="creator != null">
                AND t.creator = #{creator, jdbcType=BIGINT}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refNo != null and refNo != '' ">
                AND t.ref_no = #{refNo, jdbcType=VARCHAR}
            </if>
            AND tsr.status != '2'
            AND tsr.receive_way !="短信"
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 未读消息 -->
    <select id="unreadMessages" parameterType="com.std.core.pojo.domain.Sms"
            resultType="java.lang.Long">
        SELECT (m.A - n.B)
        FROM (SELECT count(*) AS A
              FROM tstd_sms
              WHERE (user_id = #{userId, jdbcType=BIGINT} OR type = '1')
                AND status = '1') m,
             (SELECT count(*) AS B
              FROM tstd_sms t
                       LEFT JOIN tstd_sms_read tsr ON t.id = tsr.sms_code
              WHERE tsr.user_id = #{userId, jdbcType=BIGINT}
                AND t.status = '1'
                AND tsr.STATUS != '0') n
    </select>

    <select id="unreadMessagesNumber" parameterType="com.std.core.pojo.domain.Sms" resultType="java.lang.Long">
        SELECT (m.A - n.B)
        FROM (SELECT count(*) AS A FROM tstd_sms t WHERE t.type = '2' AND t.`status` = '1' and t.user_id = #{userId}) m,
             (SELECT count(*) AS B
              FROM tstd_sms t
                       LEFT JOIN tstd_sms_read tsr ON t.id = tsr.sms_code
              WHERE t.`status` = '1'
                AND t.type = '2'
                AND tsr.user_id = #{userId}
                AND tsr.`status` = '1') n
    </select>

    <select id="unreadNoticeNumber" parameterType="com.std.core.pojo.domain.Sms" resultType="java.lang.Long">
        SELECT (m.A - n.B)
        FROM (SELECT count(*) AS A FROM tstd_sms t WHERE t.type = '1' AND t.`status` = '1') m,
             (SELECT count(*) AS B
              FROM tstd_sms t
                       LEFT JOIN tstd_sms_read tsr ON t.id = tsr.sms_code
              WHERE t.`status` = '1'
                AND t.type = '1'
                AND tsr.user_id = #{userId}
                AND tsr.`status` = '1') n
    </select>

    <select id="messageCenterPage" parameterType="com.std.core.pojo.domain.Sms" resultMap="BaseResultMap">
        SELECT<include refid="Base_Column_List"/>,tu.nickname as userName
        FROM tstd_sms t left join tsys_user tu on t.user_id=tu.id
        WHERE (t.type ='1'
        <if test="userId != null">
            OR t.user_id = #{userId, jdbcType=BIGINT}
        </if>
        ) AND t.status='1'
        AND t.id NOT IN (
        SELECT tsr.sms_code FROM tstd_sms_read tsr WHERE status ='2'
        <if test="userId != null">
            AND tsr.user_id = #{userId, jdbcType=BIGINT}
        </if>
        )
    </select>

    <select id="selectNewsDetail" parameterType="com.std.core.pojo.domain.Sms" resultMap="BaseNewsMap">
        SELECT
        t.id,
        t.title,
        t.content,
        t.status,
        t.user_id,
        t.create_datetime,
        t.creator_name
        FROM
        tstd_sms t
        where t.type='2'
        <if test="id != null">
            AND t.id = #{id, jdbcType=BIGINT}
        </if>
    </select>

    <!-- 查询我的公告未阅读 -->
    <select id="selectMyUnread" parameterType="com.std.core.pojo.domain.Sms" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms t
        where t.type = #{type} and t.target= #{target} and t.status = #{status}
        and not exists (select * from tstd_sms_read tr where tr.user_id = #{userId} and tr.sms_code = t.id)
    </select>

    <!-- 查询我的消息未阅读 -->
    <select id="selectMsgMyUnread" parameterType="com.std.core.pojo.domain.Sms" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_sms t
        where t.target= #{target} and t.status = #{status} and t.user_id = #{userId}
        and not exists (select * from tstd_sms_read tr where tr.user_id = #{userId} and tr.sms_code = t.id)
    </select>

    <!-- 查询我的消息未阅读 -->
    <select id="selectSmsNoticeUserList" parameterType="com.std.core.pojo.domain.Sms" resultType="java.lang.String">
        select login_name
        from tsys_user_sms_notice
    </select>
</mapper>