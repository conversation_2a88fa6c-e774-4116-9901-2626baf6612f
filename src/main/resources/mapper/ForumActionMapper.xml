<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ForumActionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ForumAction">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="read_flag" jdbcType="VARCHAR" property="readFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.ref_id
        , t.user_id
        , t.create_datetime
        , t.read_flag
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refIdList != null and refIdList.size() != 0 ">
                AND t.ref_id in
                <foreach item="item" index="index" collection="refIdList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="readFlag != null and readFlag != '' ">
                AND t.read_flag = #{readFlag, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <sql id="where_extend_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="userId != null ">
                AND user_id = #{userId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ForumAction" useGeneratedKeys="true" keyProperty="id">
        insert into tstd_forum_action
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="refId != null ">
                ref_id,
              </if>
              <if test="userId != null  ">
                user_id,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="readFlag != null and readFlag != '' ">
                read_flag,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="userId != null ">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="readFlag != null and readFlag != '' ">
                #{readFlag,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_forum_action
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ForumAction">
        update tstd_forum_action
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="userId != null  ">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="readFlag != null and readFlag != '' ">
                read_flag = #{readFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_action t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ForumAction"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_action t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 统计组合条件查询 -->
    <select id="countByCondition" parameterType="com.std.core.pojo.domain.ForumAction"
            resultType="java.lang.Long">
        select
         count(1)
        from tstd_forum_action t
        <include refid="where_condition"/>
    </select>

    <!-- 删除组合条件查询 -->
    <delete id="deleteByCondition" parameterType="com.std.core.pojo.domain.ForumAction">
        DELETE FROM tstd_forum_action
        <include refid="where_extend_condition"/>
    </delete>

</mapper>