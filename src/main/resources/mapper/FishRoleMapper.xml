<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishRoleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishRole">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="is_catch_fish" jdbcType="VARCHAR" property="isCatchFish"/>
        <result column="is_boat_race" jdbcType="VARCHAR" property="isBoatRace"/>
        <result column="is_manage" jdbcType="VARCHAR" property="isManage"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.sex
        , t.type
        , t.name
        , t.is_catch_fish
        , t.is_boat_race
        , t.is_manage
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="sex != null and sex != '' ">
                AND t.sex = #{sex, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != ''">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="isCatchFish != null and isCatchFish != '' ">
                AND t.is_catch_fish = #{isCatchFish, jdbcType=VARCHAR}
            </if>
            <if test="isBoatRace != null and isBoatRace != '' ">
                AND t.is_boat_race = #{isBoatRace, jdbcType=VARCHAR}
            </if>
            <if test="isManage != null and isManage != '' ">
                AND t.is_manage = #{isManage, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishRole">
        insert into yg_fish_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="sex != null and sex != '' ">
                sex,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="isCatchFish != null and isCatchFish != '' ">
                is_catch_fish,
            </if>
            <if test="isBoatRace != null and isBoatRace != '' ">
                is_boat_race,
            </if>
            <if test="isManage != null and isManage != '' ">
                is_manage,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="sex != null and sex != '' ">
                #{sex, jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != ''">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCatchFish != null and isCatchFish != '' ">
                #{isCatchFish,jdbcType=VARCHAR},
            </if>
            <if test="isBoatRace != null and isBoatRace != '' ">
                #{isBoatRace,jdbcType=VARCHAR},
            </if>
            <if test="isManage != null and isManage != '' ">
                #{isManage,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from yg_fish_role
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishRole">
        update yg_fish_role
        <set>
            <if test="sex != null and sex != '' ">
                sex = #{sex, jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="isCatchFish != null and isCatchFish != '' ">
                is_catch_fish = #{isCatchFish,jdbcType=VARCHAR},
            </if>
            <if test="isBoatRace != null and isBoatRace != '' ">
                is_boat_race = #{isBoatRace,jdbcType=VARCHAR},
            </if>
            <if test="isManage != null and isManage != '' ">
                is_manage = #{isManage,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_role t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishRole"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_role t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>