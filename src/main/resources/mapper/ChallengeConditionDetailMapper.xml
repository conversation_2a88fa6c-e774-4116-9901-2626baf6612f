<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChallengeConditionDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChallengeConditionDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="challenge_id" jdbcType="BIGINT" property="challengeId"/>
        <result column="condition_id" jdbcType="BIGINT" property="conditionId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="collection_cover_file_url" jdbcType="VARCHAR" property="collectionCoverFileUrl"/>
        <result column="collection_quantity" jdbcType="INTEGER" property="collectionQuantity"/>
        <result column="lock_condition" jdbcType="VARCHAR" property="lockCondition"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.challenge_id
        , t.condition_id
        , t.collection_id
        , t.collection_name
        , t.collection_cover_file_url
        , t.collection_quantity
        , t.lock_condition
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="challengeId != null">
                AND t.challenge_id = #{challengeId, jdbcType=BIGINT}
            </if>
            <if test="conditionId != null">
                AND t.condition_id = #{conditionId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionQuantity != null">
                AND t.collection_quantity = #{collectionQuantity, jdbcType=INTEGER}
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                AND t.lock_condition = #{lockCondition, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail">
        insert into nft_challenge_condition_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="challengeId != null ">
                challenge_id,
            </if>
            <if test="conditionId != null ">
                condition_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name,
            </if>
            <if test="collectionCoverFileUrl != null and collectionCoverFileUrl != '' ">
                collection_cover_file_url,
            </if>
            <if test="collectionQuantity != null ">
                collection_quantity,
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                lock_condition,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="challengeId != null">
                #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="conditionId != null">
                #{conditionId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionCoverFileUrl != null and collectionCoverFileUrl != '' ">
                #{collectionCoverFileUrl, jdbcType=VARCHAR},
            </if>
            <if test="collectionQuantity != null">
                #{collectionQuantity,jdbcType=INTEGER},
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                #{lockCondition, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_challenge_condition_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 根据兑换条件删除 -->
    <delete id="deleteByConditionId" parameterType="java.lang.Long">
        delete
        from nft_challenge_condition_detail
        where condition_id = #{conditionId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByChallengeId">
        delete
        from nft_challenge_condition_detail
        where challenge_id = #{challengeId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail">
        update nft_challenge_condition_detail
        <set>
            <if test="challengeId != null">
                challenge_id = #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="conditionId != null">
                condition_id = #{conditionId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collectionName= #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionCoverFileUrl != null and collectionCoverFileUrl != '' ">
                collectionCoverFileUrl = #{collectionCoverFileUrl, jdbcType=VARCHAR},
            </if>
            <if test="collectionQuantity != null">
                collection_quantity = #{collectionQuantity,jdbcType=INTEGER},
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                lock_condition = #{lockCondition, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_condition_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_condition_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectMyOwnerCount" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail"
            resultType="java.lang.Integer">
        select count(td.id)
        from nft_collection_detail td
                 inner join nft_collection tc on td.collection_id = tc.id
                 inner join nft_challenge_condition_detail nccd on nccd.collection_id = tc.id
        where nccd.condition_id = #{conditionId}
          and td.owner_type = '0'
          and td.owner_id = #{userId}
          and td.status = '0'
          and (nccd.lock_condition = '0'
            OR (td.lock_time != '-1'
                AND td.lock_datetime <![CDATA[ <= ]]> now()
          AND nccd.lock_condition = '1')
            OR ((td.lock_time = '-1' or (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ > ]]> now()))
                AND nccd.lock_condition = '2'))
          and td.id NOT IN (
            SELECT collection_detail_id
            FROM nft_challenge_order_detail
            WHERE condition_id = #{conditionId, jdbcType=BIGINT}
              AND order_id IN (
                SELECT id
                FROM nft_challenge_order
                WHERE `status` IN ('0', '1')))
    </select>

    <select id="selectDistinctCollectionIdList" resultType="java.lang.Long">
        select distinct collection_id
        from nft_challenge_condition_detail
        where condition_id = #{conditionId}
    </select>

    <resultMap id="CollectionBaseResultMap" type="com.std.core.pojo.response.ChallengeConditionDetailFrontRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="owner_quantity" jdbcType="INTEGER" property="ownerQuantity"/>
        <result column="lock_condition" jdbcType="VARCHAR" property="lockCondition"/>
    </resultMap>

    <select id="selectMyOwnerQuantityByCondition" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail"
            resultMap="CollectionBaseResultMap">
        select distinct tc.id,
                        tc.name,
                        tc.type,
                        tc.level_type,
                        tc.file_type,
                        tc.cover_file_url,
                        count(1) owner_quantity,
                        nccd.lock_condition
        from nft_collection_detail td
                 inner join nft_collection tc on td.collection_id = tc.id
                 inner join nft_challenge_condition_detail nccd on nccd.collection_id = tc.id
        where nccd.condition_id = #{conditionId}
          and td.owner_type = '0'
          and td.owner_id = #{userId}
          and td.status = '0'
          and (nccd.lock_condition = '0' OR (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ <= ]]> now()
 AND nccd.lock_condition = '1')
            OR ((td.lock_time = '-1' or (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ > ]]> now())) AND nccd.lock_condition = '2'))
          and td.id NOT IN (
            SELECT collection_detail_id
            FROM nft_challenge_order_detail
            WHERE condition_id = #{conditionId, jdbcType=BIGINT}
              AND order_id IN (
                SELECT id
                FROM nft_challenge_order
                WHERE `status` IN ('0', '1')))
        group by td.collection_id
    </select>

    <select id="selectMyNoOwnerByCondition" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail"
            resultMap="CollectionBaseResultMap">
        select distinct tc.id,
                        tc.name,
                        tc.type,
                        tc.level_type,
                        tc.file_type,
                        tc.cover_file_url,
                        0 owner_quantity,
                        nccd.lock_condition
        from nft_collection tc
                 inner join nft_challenge_condition_detail nccd on nccd.collection_id = tc.id
        where nccd.condition_id = #{conditionId}
          and tc.id NOT IN (
            select collection_id
            from nft_collection_detail td
            where id not in
                  (SELECT collection_detail_id
                   FROM nft_challenge_order_detail
                   WHERE condition_id = #{conditionId, jdbcType=BIGINT}
                     and user_id = #{userId})
              and owner_id = #{userId}
              and status = '0'
              and (nccd.lock_condition = '0' OR (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ <= ]]> now()
                AND nccd.lock_condition = '1')
                OR
                   ((td.lock_time = '-1' or (td.lock_time != '-1' AND td.lock_datetime <![CDATA[ > ]]> now())) AND nccd.lock_condition = '2'))
        )
    </select>

    <select id="selectFrontByCondition" parameterType="com.std.core.pojo.domain.ChallengeConditionDetail"
            resultMap="CollectionBaseResultMap">
        select distinct tc.id,
                        tc.name,
                        tc.type,
                        tc.level_type,
                        tc.file_type,
                        tc.cover_file_url,
                        0 owner_quantity,
                        nccd.lock_condition
        from nft_collection tc
                 inner join nft_challenge_condition_detail nccd on nccd.collection_id = tc.id
        where nccd.condition_id = #{conditionId}
    </select>

</mapper>