<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserSettleAccountMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserSettleAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="to_settle_amount" jdbcType="DECIMAL" property="toSettleAmount"/>
        <result column="on_settle_amount" jdbcType="DECIMAL" property="onSettleAmount"/>
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.user_id
        , t.to_settle_amount
        , t.on_settle_amount
        , t.settle_amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="toSettleAmount != null">
                AND t.to_settle_amount = #{toSettleAmount, jdbcType=DECIMAL}
            </if>
            <if test="onSettleAmount != null">
                AND t.on_settle_amount = #{onSettleAmount, jdbcType=DECIMAL}
            </if>
            <if test="settleAmount != null">
                AND t.settle_amount = #{settleAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserSettleAccount" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_user_settle_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="toSettleAmount != null ">
                to_settle_amount,
            </if>
            <if test="onSettleAmount != null">
                on_settle_amount,
            </if>
            <if test="settleAmount != null ">
                settle_amount,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="toSettleAmount != null">
                #{toSettleAmount,jdbcType=DECIMAL},
            </if>
            <if test="onSettleAmount != null">
                #{onSettleAmount, jdbcType=DECIMAL},
            </if>
            <if test="settleAmount != null">
                #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_user_settle_account
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserSettleAccount">
        update nft_user_settle_account
        <set>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="toSettleAmount != null">
                to_settle_amount = #{toSettleAmount,jdbcType=DECIMAL},
            </if>
            <if test="onSettleAmount != null">
                on_settle_amount = #{onSettleAmount, jdbcType=DECIMAL},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="addToSettleAmount">
        update nft_user_settle_account
        <set>
            to_settle_amount = to_settle_amount+#{toSettleAmount,jdbcType=DECIMAL}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="addSettleAmount">
        update nft_user_settle_account
        <set>
            to_settle_amount = to_settle_amount-#{settleAmount,jdbcType=DECIMAL},
            settle_amount = settle_amount+#{settleAmount,jdbcType=DECIMAL}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="addOnSettleAmount">
        update nft_user_settle_account
        <set>
            to_settle_amount = to_settle_amount-#{onSettleAmount,jdbcType=DECIMAL},
            on_settle_amount = on_settle_amount+#{onSettleAmount,jdbcType=DECIMAL}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="subtractOnSettleAmount">
        update nft_user_settle_account
        <set>
            on_settle_amount = on_settle_amount-#{onSettleAmount,jdbcType=DECIMAL},
            settle_amount = settle_amount+#{onSettleAmount,jdbcType=DECIMAL}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="subtractOnToSettleAmount">
        update nft_user_settle_account
        <set>
            to_settle_amount = to_settle_amount-#{onSettleAmount,jdbcType=DECIMAL},
            settle_amount = settle_amount+#{onSettleAmount,jdbcType=DECIMAL}
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserSettleAccount"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByuserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        where t.user_id = #{userId,jdbcType=BIGINT} and `type`='C';
    </select>
    <select id="selectPlatAccount" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        where `type`='P';
    </select>
    <select id="selectByCompanyId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        where t.user_id = #{companyId,jdbcType=BIGINT} and `type`='BP';
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_user_settle_account t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>