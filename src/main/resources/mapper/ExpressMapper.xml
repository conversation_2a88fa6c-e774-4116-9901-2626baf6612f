<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ExpressMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Express">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="letter" jdbcType="CHAR" property="letter"/>
        <result column="order_no" jdbcType="TINYINT" property="orderNo"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="kdn_code" jdbcType="VARCHAR" property="kdnCode"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.code
        , t.letter
        , t.order_no
        , t.url
        , t.status
        , t.kdn_code
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="code != null and code != '' ">
                AND t.code = #{code, jdbcType=VARCHAR}
            </if>
            <if test="letter != null and letter != ''">
                AND t.letter = #{letter, jdbcType=CHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=TINYINT}
            </if>
            <if test="url != null and url != '' ">
                AND t.url = #{url, jdbcType=VARCHAR}
            </if>
            <if test="status != null">
                AND t.status = #{status, jdbcType=TINYINT}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="kdnCode != null and kdnCode != '' ">
                AND t.kdn_code = #{kdnCode, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Express">
        insert into tstd_express
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="code != null and code != '' ">
                code,
              </if>
              <if test="letter != null ">
                letter,
              </if>
              <if test="orderNo != null ">
                order_no,
              </if>
              <if test="url != null and url != '' ">
                url,
              </if>
              <if test="status != null ">
                status,
              </if>
              <if test="kdnCode != null and kdnCode != '' ">
                kdn_code,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null and code != '' ">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="letter != null">
                #{letter,jdbcType=CHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=TINYINT},
            </if>
            <if test="url != null and url != '' ">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="kdnCode != null and kdnCode != '' ">
                #{kdnCode,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_express
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Express">
        update tstd_express
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null and code != '' ">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="letter != null">
                letter = #{letter,jdbcType=CHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=TINYINT},
            </if>
            <if test="url != null and url != '' ">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="kdnCode != null and kdnCode != '' ">
                kdn_code = #{kdnCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_express t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Express"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_express t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>