<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LockMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Lock">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="ref_code" jdbcType="VARCHAR" property="refCode"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.biz_type
        , t.ref_code
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="refCode != null and refCode != '' ">
                AND t.ref_code = #{refCode, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Lock">
        insert into tstd_lock
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="bizType != null and bizType != '' ">
                biz_type,
              </if>
              <if test="refCode != null and refCode != '' ">
                ref_code,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="refCode != null and refCode != '' ">
                #{refCode,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tstd_lock
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Lock">
        update tstd_lock
        <set>
            <if test="id != null">
                id = #{id,jdbcType=INTEGER},
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="refCode != null and refCode != '' ">
                ref_code = #{refCode,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_lock t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Lock"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_lock t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>