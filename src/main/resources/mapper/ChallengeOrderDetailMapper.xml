<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChallengeOrderDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChallengeOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="back_type" jdbcType="VARCHAR" property="backType"/>

        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="challenge_id" jdbcType="BIGINT" property="challengeId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="collectionName" jdbcType="VARCHAR" property="collectionName"/>
        <result column="lock_condition" jdbcType="VARCHAR" property="lockCondition"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.back_type
        , t.order_id
        , t.challenge_id
        , t.user_id
        , t.collection_detail_id
        , t.lock_condition
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="backType != null and backType != '' ">
                AND t.back_type = #{backType, jdbcType=VARCHAR}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="challengeId != null">
                AND t.challenge_id = #{challengeId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                AND t.lock_condition = #{lockCondition, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChallengeOrderDetail">
        insert into nft_challenge_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="backType != null and backType != '' ">
                back_type,
            </if>
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="challengeId != null ">
                challenge_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                lock_condition,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="backType != null and backType != '' ">
                #{backType, jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="challengeId != null">
                #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                #{lockCondition, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchAdd">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_challenge_order_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">

                <if test="item.backType != null and item.backType != '' ">
                    back_type,
                </if>
                <if test="item.orderId != null ">
                    order_id,
                </if>
                <if test="item.challengeId != null ">
                    challenge_id,
                </if>
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.conditionId != null ">
                    condition_id,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.lockCondition != null and item.lockCondition != '' ">
                    lock_condition,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">

                <if test="item.backType != null and item.backType != '' ">
                    #{item.backType, jdbcType=VARCHAR},
                </if>
                <if test="item.orderId != null">
                    #{item.orderId,jdbcType=BIGINT},
                </if>
                <if test="item.challengeId != null">
                    #{item.challengeId,jdbcType=BIGINT},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.conditionId != null ">
                    #{item.conditionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.lockCondition != null and item.lockCondition != '' ">
                    #{item.lockCondition,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_challenge_order_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChallengeOrderDetail">
        update nft_challenge_order_detail
        <set>
            <if test="backType != null and backType != '' ">
                back_type = #{backType, jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="challengeId != null">
                challenge_id = #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="lockCondition != null and lockCondition != '' ">
                lock_condition = #{lockCondition, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_order_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChallengeOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_order_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="detailByOrderId" resultType="com.std.core.pojo.response.ChallengeOrderExchangeDetailRes">
        select t.back_type       backType,
               ta.id,
               tb.name,
               tb.cover_file_url coverFileUrl,
               tb.file_type      fileType,
               ct.token_id       tokenId,
               t.lock_condition  lockCondition
        from nft_challenge_order_detail t
                 inner join nft_collection_detail ta on t.collection_detail_id = ta.id
                 inner join nft_collection tb on ta.collection_id = tb.id
                 INNER JOIN nft_contract_token ct on ta.contract_token_id = ct.id
        where t.order_id = #{id}
    </select>

    <select id="selectByOrderId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,tb.name collectionName
        from nft_challenge_order_detail t
        inner join nft_collection_detail ta on t.collection_detail_id=ta.id
        inner join nft_collection tb on ta.collection_id= tb.id
        where t.order_id=#{id}
    </select>

    <select id="selectExistIngCount" parameterType="java.lang.Long" resultType="java.lang.Long">
        select count(1)
        from nft_challenge_order_detail td,
             nft_challenge nc
        where td.challenge_id = nc.id
          and nc.status = '2'
          and nc.start_status = '1'
          and td.back_type = '1'
          and td.collection_detail_id = #{collectionDetailId}
    </select>

</mapper>