<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionYaoConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionYaoConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_id
        , t.type
        , t.status
        , t.yin_yao
        , t.yang_yao
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionYaoConfig">
        insert into es_collection_yao_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="yinYao != null ">
                yin_yao,
            </if>
            <if test="yangYao != null ">
                yang_yao,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from es_collection_yao_config
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionYaoConfig">
        update es_collection_yao_config
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_collection_yao_config t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionYaoConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_collection_yao_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectExchangeCollection" parameterType="com.std.core.pojo.domain.CollectionYaoConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_collection_yao_config t
        inner join nft_collection ta on t.collection_id =ta.id
        where ta.lock_time <![CDATA[ >]]> -1
        and
        (t.yin_yao <![CDATA[ >]]> 0
        or t.yang_yao <![CDATA[ >]]> 0)
        <if test="id != null">
            AND t.id = #{id, jdbcType=BIGINT}
        </if>
        <if test="collectionId != null">
            AND t.collection_id = #{collectionId, jdbcType=BIGINT}
        </if>
        <if test="type != null and type != '' ">
            AND t.type = #{type, jdbcType=VARCHAR}
        </if>
        <if test="status != null and status != '' ">
            AND t.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="creater != null">
            AND t.creater = #{creater, jdbcType=BIGINT}
        </if>
        <if test="createrName != null and createrName != '' ">
            AND t.creater_name = #{createrName, jdbcType=VARCHAR}
        </if>
        <if test="createDatetime != null">
            AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
        </if>
        <if test="updater != null">
            AND t.updater = #{updater, jdbcType=BIGINT}
        </if>
        <if test="updaterName != null and updaterName != '' ">
            AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
        </if>
        <if test="updateDatetime != null">
            AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
        </if>
        order by ta.type desc,t.id asc
    </select>
    <select id="selectYaoExchangeCollectionDetail"
            resultType="com.std.core.pojo.response.CollectionDetailPageYapExchangeRes">
        SELECT ta.collection_id collectionId,
               ta.yin_yao       yinYao,
               ta.yang_yao      yangYao,
               count(1)         quantity
        FROM nft_collection_detail t
                 INNER JOIN es_collection_yao_config ta ON t.collection_id = ta.collection_id
        where t.owner_type = '0'
          AND t.owner_id = #{ownerId}
          AND t.lock_time <![CDATA[>]]> '-1'
          AND t.status = '0'
          and ta.type = '0'
          AND (ta.yin_yao <![CDATA[>]]> 0 or ta.yang_yao <![CDATA[>]]> 0)
          and ta.status = '1'
        GROUP BY t.collection_id
    </select>
</mapper>