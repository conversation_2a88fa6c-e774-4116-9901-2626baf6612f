<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ArticleTypeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ArticleType">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.type
        , t.icon
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.location
        , t.channel_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="updater != null ">
                AND t.updater = #{updater, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=INTEGER}
            </if>
            <if test="channelIdList != null and channelIdList.size() != 0 ">
                AND t.channel_id in
                <foreach item="item" index="index" collection="channelIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ArticleType">
        insert into tsys_article_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="icon != null and icon != '' ">
                icon,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="location != null and location != '' ">
                location,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="icon != null and icon != '' ">
                #{icon,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                #{location,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null">
                #{channelId,jdbcType=INTEGER}
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_article_type
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ArticleType">
        update tsys_article_type
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <choose>
                <when test="icon != null and icon !=''">
                    icon = #{icon,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    icon = '',
                </otherwise>
            </choose>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <choose>
                <when test="remark != null and remark !=''">
                    remark = #{remark,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    remark = '',
                </otherwise>
            </choose>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="updater != null ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null ">
                update_datetime = #{updateDatetime,jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_article_type t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ArticleType"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_article_type t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>