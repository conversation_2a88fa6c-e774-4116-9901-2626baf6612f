<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ContractTokenMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ContractToken">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="contract_address" jdbcType="VARCHAR" property="contractAddress"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>

        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_type
        , t.user_id
        , t.type
        , t.contract_id
        , t.contract_address
        , t.token_id
        , t.address
        , t.collection_id
        , t.collection_detail_id
        , t.ref_id
        , t.ref_type
        , t.status
        , t.create_datetime
        , t.updater
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userType != null and userType != '' ">
                AND t.user_type = #{userType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="contractId != null">
                AND t.contract_id = #{contractId, jdbcType=BIGINT}
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                AND t.contract_address = #{contractAddress, jdbcType=VARCHAR}
            </if>
            <if test="tokenId != null and tokenId != '' ">
                AND t.token_id = #{tokenId, jdbcType=VARCHAR}
            </if>
            <if test="address != null and address != '' ">
                AND t.address = #{address, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refType != null">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ContractToken" useGeneratedKeys="true" keyProperty="id">
        insert into nft_contract_token
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userType != null and userType != '' ">
                user_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="contractId != null and contractId != '' ">
                contract_id,
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                contract_address,
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id,
            </if>
            <if test="address != null and address != '' ">
                address,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="refType != null  and refType != '' ">
                ref_type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null and updater != '' ">
                updater,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userType != null and userType != '' ">
                #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null and contractId != '' ">
                #{contractId,jdbcType=BIGINT},
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                #{contractAddress,jdbcType=VARCHAR},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="refType != null  and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null ">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>

        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_contract_token
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ContractToken">
        update nft_contract_token
        <set>
            <if test="userType != null and userType != '' ">
                user_type = #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null and contractId != '' ">
                contract_id = #{contractId,jdbcType=BIGINT},
            </if>
            <if test="contractAddress != null and contractAddress != '' ">
                contract_address = #{contractAddress,jdbcType=VARCHAR},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id = #{tokenId,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="discardToken">
        update nft_contract_token set `status`='-1'
        where collection_id=#{collectionId} and ref_id=#{refId} and ref_type=#{refType} and `type`='0' and `status`='3';
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract_token t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ContractToken"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract_token t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="periodToken" resultType="com.std.core.pojo.response.CollectionContractTokenRes">
        SELECT COALESCE(MIN(t.token_id + 0), 1)    contractTokenStart,
               COALESCE(max(t.token_id + 0), 1000) contractTokenEnd,
               contract_address                    contractAddress
        FROM nft_contract_token t
        WHERE t.type = '0'
          AND t.ref_type = '3'
          AND t.collection_id IN (
            SELECT ta.collection_id
            FROM nft_collection_period_relation ta
            WHERE ta.period_id = #{periodId})
    </select>

    <select id="periodToken1155" resultType="com.std.core.pojo.response.CollectionContractTokenRes">
        SELECT t.token_id       contractTokenStart,
               t.token_id       contractTokenEnd,
               contract_address contractAddress
        FROM nft_contract_token t
        WHERE t.type = '0'
          AND t.ref_type = '3'
          AND t.collection_id IN (
            SELECT ta.collection_id
            FROM nft_collection_period_relation ta
            WHERE ta.period_id = #{periodId})
    </select>

    <select id="periodToken1155List" resultType="com.std.core.pojo.response.CollectionContractTokenRes">
        SELECT t.token_id       contractTokenStart,
               t.token_id       contractTokenEnd,
               contract_address contractAddress
        FROM nft_contract_token t
        WHERE t.type = '0'
          AND t.collection_id IN (
            SELECT ta.collection_id
            FROM nft_collection_period_relation ta
            WHERE ta.period_id = #{periodId})
    </select>

    <select id="collectionToken" resultType="com.std.core.pojo.response.CollectionContractTokenRes">
        SELECT COALESCE(MIN(t.token_id + 0), 1)    contractTokenStart,
               COALESCE(max(t.token_id + 0), 1000) contractTokenEnd,
               t.contract_address                  contractAddress,
               tc.protocol                         contractProtocol,
               tc.chain                            contractChain
        FROM nft_contract_token t
                 inner join nft_contract tc on t.contract_id = tc.id
        WHERE t.collection_id = #{id}
    </select>

    <select id="collectionToken1155" resultType="com.std.core.pojo.response.CollectionContractTokenRes">
        SELECT t.token_id         contractTokenStart,
               t.token_id         contractTokenEnd,
               t.contract_address contractAddress,
               tc.protocol        contractProtocol,
               tc.chain           contractChain
        FROM nft_contract_token t
                 inner join nft_contract tc on t.contract_id = tc.id
        WHERE t.collection_id = #{id}
    </select>

    <select id="selectMaxTokenIdByCondition" parameterType="java.lang.Long"
            resultType="java.lang.Long">
        select ifnull(max(cast(token_id+0 AS Decimal(32))), 0)
        from nft_contract_token t
        where contract_id = #{contractId}
    </select>

</mapper>