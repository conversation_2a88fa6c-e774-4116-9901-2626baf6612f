<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionRightCompanyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionRightCompany">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="plate_category" jdbcType="VARCHAR" property="plateCategory"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="number_flag" jdbcType="VARCHAR" property="numberFlag"/>
        <result column="remain_number" jdbcType="INTEGER" property="remainNumber"/>
        <result column="advance_mins" jdbcType="INTEGER" property="advanceMins"/>
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.plate_category
        , t.company_id
        , t.ref_id
        , t.ref_type
        , t.number
        , t.status
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
            </if>
            <if test="noPlateCategory != null and noPlateCategory != '' ">
                AND t.plate_category <![CDATA[!=]]> #{noPlateCategory, jdbcType=VARCHAR}
            </if>
            <if test="plateCategoryList != null and plateCategoryList.size() != 0 ">
                AND t.plate_category in
                <foreach item="item" index="index" collection="plateCategoryList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="noCompanyId != null">
                AND t.company_id <![CDATA[!=]]> #{noCompanyId, jdbcType=BIGINT}
            </if>
            <if test="companyIdList != null and companyIdList.size() != 0 ">
                AND t.company_id in
                <foreach item="item" index="index" collection="companyIdList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refIdList != null and refIdList.size() != 0 ">
                AND t.ref_id in
                <foreach item="item" index="index" collection="refIdList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionRightCompany" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_right_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="number != null ">
                number,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into nft_collection_right_company
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.plateCategory != null and item.plateCategory != '' ">
                    plate_category,
                </if>
                <if test="item.companyId != null ">
                    company_id,
                </if>
                <if test="item.refId != null ">
                    ref_id,
                </if>
                <if test="item.refType != null and item.refType != '' ">
                    ref_type,
                </if>
                <if test="item.number != null ">
                    number,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.updater != null">
                    updater,
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    updater_name,
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.plateCategory != null and item.plateCategory != '' ">
                    #{item.plateCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.companyId != null">
                    #{item.companyId,jdbcType=BIGINT},
                </if>
                <if test="item.refId != null">
                    #{item.refId,jdbcType=BIGINT},
                </if>
                <if test="item.refType != null and item.refType != '' ">
                    #{item.refType,jdbcType=VARCHAR},
                </if>
                <if test="item.number != null">
                    #{item.number,jdbcType=INTEGER},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updater != null">
                    #{item.updater, jdbcType=BIGINT},
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    #{item.updaterName, jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    #{item.updateDatetime, jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_right_company
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByCollectionId">
        delete
        from nft_collection_right_company
        where ref_id in (select id from nft_collection_rights_detail where collection_id = #{collectionId});
    </delete>
    <delete id="deleteByRecord">
        DELETE
        FROM
            nft_collection_right_company
        WHERE
                ref_id IN ( SELECT id FROM nft_collection_rights_detail WHERE id IN ( SELECT right_id FROM nft_collection_right_record WHERE ref_id = #{refId} AND ref_type = #{type} ) AND create_type = '1' )
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionRightCompany">
        update nft_collection_right_company
        <set>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category = #{plateCategory,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatchDown">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_collection_right_company
            <set>
                <if test="item.status != null and item.status != '' ">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updater != null">
                    updater = #{item.updater, jdbcType=BIGINT},
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    updater_name = #{item.updaterName, jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime = #{item.updateDatetime, jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>


    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_company t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionRightCompany"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_company t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_company t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectCreatePeriod" parameterType="com.std.core.pojo.domain.CollectionRightCompany"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.collection_id,ta.advance_mins,ta.discount_rate,ta.remain_number,ta.number_flag
        from nft_collection_right_company t
        inner join nft_collection_rights_detail ta on t.ref_id=ta.id
        inner join nft_collection tb on ta.collection_id=tb.id
        where
        ((ta.remain_number <![CDATA[ >]]> 0 and ta.number_flag ='0') or ta.number_flag ='1')
        and tb.status in ('1','6')
        <if test="plateCategory != null and plateCategory != '' ">
            AND (t.plate_category = #{plateCategory, jdbcType=VARCHAR} or t.plate_category ='0')
        </if>
        <if test="companyId != null">
            AND (t.company_id = #{companyId, jdbcType=BIGINT} or t.company_id =0 )
        </if>
        <if test="refId != null">
            AND t.ref_id = #{refId, jdbcType=BIGINT}
        </if>
        <if test="refType != null and refType != '' ">
            AND t.ref_type = #{refType, jdbcType=VARCHAR}
        </if>
        <if test="number != null">
            AND t.number = #{number, jdbcType=INTEGER}
        </if>
        <if test="status != null and status != '' ">
            AND t.status = #{status, jdbcType=VARCHAR}
        </if>
        <if test="statusList != null and statusList.size() != 0 ">
            AND t.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator=","
                     close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="collectionName != null and collectionName != '' ">
            AND tb.name like concat('%', #{collectionName, jdbcType=VARCHAR},'%')
        </if>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectCompanyId" resultType="java.lang.Long">
        select DISTINCT t.company_id
        from nft_collection_right_company t
        where t.ref_id = #{refId,jdbcType=BIGINT} and t.status='1';
    </select>
    <select id="selectPlateCategory" resultType="java.lang.String">
        select DISTINCT t.plate_category
        from nft_collection_right_company t
        where t.ref_id = #{refId,jdbcType=BIGINT} and t.status='1';
    </select>
    <select id="selectAllCompanyOrAllPlate" parameterType="com.std.core.pojo.domain.CollectionRightCompany"
            resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_company t
        where t.status='1' and (t.company_id='0' or t.plate_category='0') and t.ref_id=#{refId}
    </select>
</mapper>