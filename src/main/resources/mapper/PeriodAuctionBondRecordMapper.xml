<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodAuctionBondRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodAuctionBondRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="refund_flag" jdbcType="CHAR" property="refundFlag"/>
        <result column="periodName" jdbcType="VARCHAR" property="periodName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.period_id
        , t.collection_detail_id
        , t.user_id
        , t.status
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.commission_amount
        , t.create_datetime
        , t.update_datetime
        , t.refund_flag
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payStatusList != null and payStatusList.size() != 0 ">
                AND t.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="refundFlag != null">
                AND t.refund_flag = #{refundFlag, jdbcType=CHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodAuctionBondRecord" useGeneratedKeys="true" keyProperty="id">
        insert into nft_period_auction_bond_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="payBalanceAmount != null ">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null ">
                pay_cash_amount,
            </if>
            <if test="commissionAmount != null ">
                commission_amount,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="refundFlag != null ">
                refund_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundFlag != null">
                #{refundFlag,jdbcType=CHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_period_auction_bond_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodAuctionBondRecord">
        update nft_period_auction_bond_record
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="refundFlag != null">
                refund_flag = #{refundFlag,jdbcType=CHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchModify">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_period_auction_bond_record
            <set>
                <if test="item.periodId != null">
                    period_id = #{item.periodId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionDetailId != null">
                    collection_detail_id = #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.userId != null">
                    user_id = #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.status != null and item.status != '' ">
                    status = #{itemstatus,jdbcType=VARCHAR},
                </if>
                <if test="item.payType != null and item.payType != '' ">
                    pay_type = #{item.payType,jdbcType=VARCHAR},
                </if>
                <if test="item.payOrderCode != null and item.payOrderCode != '' ">
                    pay_order_code = #{item.payOrderCode,jdbcType=VARCHAR},
                </if>
                <if test="item.payStatus != null and item.payStatus != '' ">
                    pay_status = #{item.payStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.payDatetime != null">
                    pay_datetime = #{item.payDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.payBalanceAmount != null">
                    pay_balance_amount = #{item.payBalanceAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.payCashAmount != null">
                    pay_cash_amount = #{item.payCashAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.commissionAmount != null">
                    commission_amount = #{item.commissionAmount,jdbcType=DECIMAL},
                </if>
                <if test="item.createDatetime != null">
                    create_datetime = #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime = #{item.updateDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.refundFlag != null">
                    refund_flag = #{item.refundFlag,jdbcType=CHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name periodName
        from nft_period_auction_bond_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_period_auction ta on t.period_id = ta.period_id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction_bond_record t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodAuctionBondRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name periodName
        from nft_period_auction_bond_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_period_auction ta on t.period_id = ta.period_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectJoinData" resultType="java.lang.Long">
        select
        t.user_id
        from nft_period_auction_bond_record t
        <include refid="where_condition"/>
    </select>
    <select id="isExsit" resultType="java.lang.Long">
        select t.id
        from nft_period_auction_bond_record t
        where t.collection_detail_id = #{detailId}
          and t.period_id = #{periodId}
          and t.pay_status in ('0', '1');
    </select>
    <select id="selectByPayOrderCode" parameterType="com.std.core.pojo.domain.PeriodAuctionBondRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction_bond_record t
        where t.pay_order_code =#{orderCode};
    </select>
    <select id="detailCancelData" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction_bond_record t
        where t.pay_status ='0' and DATE_ADD(t.create_datetime, INTERVAL #{closeTime} MINUTE) <![CDATA[ <]]> #{date} limit 100;
    </select>
    <select id="detailByPeriodEnd" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction_bond_record t
        where t.pay_status ='1' and t.`status`='0' and period_id in (select id from nft_collection_period where category='4' and
        `start_status`='2') limit 100;
    </select>
</mapper>