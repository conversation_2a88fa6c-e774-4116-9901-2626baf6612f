<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MetaMilletTransferDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MetaMilletTransferDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="transfer_record_id" jdbcType="BIGINT" property="transferRecordId"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="millet_type" jdbcType="VARCHAR" property="milletType"/>
        <result column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.transfer_record_id
        , t.currency
        , t.millet_type
        , t.quantity
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="transferRecordId != null">
                AND t.transfer_record_id = #{transferRecordId, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="milletType != null and milletType != '' ">
                AND t.millet_type = #{milletType, jdbcType=VARCHAR}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MetaMilletTransferDetail">
        insert into es_meta_millet_transfer_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="transferRecordId != null ">
                transfer_record_id,
              </if>
              <if test="currency != null and currency != '' ">
                currency,
              </if>
              <if test="milletType != null and milletType != '' ">
                millet_type,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="transferRecordId != null">
                #{transferRecordId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="milletType != null and milletType != '' ">
                #{milletType,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into es_meta_millet_transfer_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.transferRecordId != null ">
                    transfer_record_id,
                </if>
                <if test="item.currency != null and item.currency != '' ">
                    currency,
                </if>
                <if test="item.milletType != null and item.milletType != '' ">
                    millet_type,
                </if>
                <if test="item.quantity != null ">
                    quantity,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.transferRecordId != null">
                    #{item.transferRecordId,jdbcType=BIGINT},
                </if>
                <if test="item.currency != null and item.currency != '' ">
                    #{item.currency,jdbcType=VARCHAR},
                </if>
                <if test="item.milletType != null and item.milletType != '' ">
                    #{item.milletType,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    #{item.quantity,jdbcType=DECIMAL},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from es_meta_millet_transfer_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MetaMilletTransferDetail">
        update es_meta_millet_transfer_detail
        <set>
            <if test="transferRecordId != null">
                transfer_record_id = #{transferRecordId,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="milletType != null and milletType != '' ">
                millet_type = #{milletType,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_meta_millet_transfer_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MetaMilletTransferDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_meta_millet_transfer_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" resultType="com.std.core.pojo.domain.MetaMilletTransferDetail">
        select
        t.id
        , t.transfer_record_id transferRecordId
        , t.currency
        , t.millet_type milletType
        , t.quantity
        , t.create_datetime createDatetime
        , ta.from_user_id fromUserId
        , ta.to_user_id toUserId
        from es_meta_millet_transfer_detail t
        inner join es_meta_millet_transfer_record ta on t.transfer_record_id=ta.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="transferRecordId != null">
                AND t.transfer_record_id = #{transferRecordId, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="milletType != null and milletType != '' ">
                AND t.millet_type = #{milletType, jdbcType=VARCHAR}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="fromUserId != null">
                AND ta.from_user_id = #{fromUserId, jdbcType=BIGINT}
            </if>
            <if test="toUserId != null">
                AND ta.to_user_id = #{toUserId, jdbcType=BIGINT}
            </if>
        </trim>
    </select>
</mapper>