<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IntegralExchangeDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.IntegralExchangeDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="exchange_id" jdbcType="BIGINT" property="exchangeId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="collectionName" jdbcType="VARCHAR" property="collectionName"/>
        <result column="levelType" jdbcType="VARCHAR" property="levelType"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.exchange_id
        , t.collection_id
        , t.collection_detail_id
        , t.token_id
        , t.integral_price
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="exchangeId != null">
                AND t.exchange_id = #{exchangeId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="tokenId != null and tokenId != '' ">
                AND t.token_id = #{tokenId, jdbcType=VARCHAR}
            </if>
            <if test="integralPrice != null">
                AND t.integral_price = #{integralPrice, jdbcType=DECIMAL}
            </if>
            <if test="createDatetimeStart != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND t.create_datetime <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.IntegralExchangeDetail">
        insert into mall_integral_exchange_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="exchangeId != null ">
                exchange_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id,
            </if>
            <if test="integralPrice != null ">
                integral_price,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId, jdbcType=BIGINT},
            </if>
            <if test="exchangeId != null">
                #{exchangeId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId, jdbcType=VARCHAR},
            </if>
            <if test="integralPrice != null">
                #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into mall_integral_exchange_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.userId != null">
                    user_id ,
                </if>
                <if test="item.exchangeId != null ">
                    exchange_id,
                </if>
                <if test="item.collectionId != null">
                    collection_id,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    token_id,
                </if>
                <if test="item.integralPrice != null ">
                    integral_price,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.userId != null">
                    #{item.userId, jdbcType=BIGINT},
                </if>
                <if test="item.exchangeId != null">
                    #{item.exchangeId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId, jdbcType=BIGINT},
                </if>
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    #{item.tokenId, jdbcType=VARCHAR},
                </if>
                <if test="item.integralPrice != null">
                    #{item.integralPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mall_integral_exchange_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.IntegralExchangeDetail">
        update mall_integral_exchange_detail
        <set>
            <if test="userId != null">
                user_id = #{userId, jdbcType=BIGINT},
            </if>
            <if test="exchangeId != null">
                exchange_id = #{exchangeId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id = #{tokenId, jdbcType=VARCHAR},
            </if>
            <if test="integralPrice != null">
                integral_price = #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_integral_exchange_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.IntegralExchangeDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_integral_exchange_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.IntegralExchangeDetailPageRes">
        select
        t.id,
        t.collection_detail_id collectionDetailId,
        t.integral_price integralPrice,
        tb.cover_file_url coverFileUrl,
        tb.name,
        tb.level_type levelType,
        ta.token_id tokenId
        from mall_integral_exchange_detail t
        inner join nft_collection_detail ta on t.collection_detail_id=ta.id
        inner join nft_collection tb on ta.collection_id=tb.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="exchangeId != null">
                AND t.exchange_id = #{exchangeId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND tb.name like concat('%',#{collectionName, jdbcType=VARCHAR},'%')
            </if>
            <if test="levelType != null and levelType != '' ">
                AND tb.level_type = #{levelType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetimeStart != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND t.create_datetime <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.IntegralExchangeDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,tb.name collectionName,tb.level_type levelType
        from mall_integral_exchange_detail t
        inner join nft_collection tb on t.collection_id = tb.id
        INNER JOIN tsys_user tu on tu.id = t.user_id
        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSum" resultType="com.std.core.pojo.response.IntegralExchangeDetailSumRes">
        select
        count(1) totalCount
        from mall_integral_exchange_detail t
        inner join nft_collection tb on t.collection_id = tb.id
        INNER JOIN tsys_user tu on tu.id = t.user_id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="exchangeId != null">
                AND t.exchange_id = #{exchangeId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND tb.name like concat('%',#{collectionName, jdbcType=VARCHAR},'%')
            </if>
            <if test="levelType != null and levelType != '' ">
                AND tb.level_type = #{levelType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetimeStart != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </select>
</mapper>