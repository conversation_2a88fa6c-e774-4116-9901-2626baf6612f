<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishBuyOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishBuyOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="biz_type" jdbcType="VARCHAR" property="bizType"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.biz_id
        , t.biz_type
        , t.price
        , t.quantity
        , t.pay_amount
        , t.pay_order_code
        , t.pay_type
        , t.pay_status
        , t.create_datetime
        , t.pay_datetime
        , t.remarks
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="bizId != null">
                AND t.biz_id = #{bizId, jdbcType=BIGINT}
            </if>
            <if test="bizType != null and bizType != '' ">
                AND t.biz_type = #{bizType, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remarks != null and remarks != '' ">
                AND t.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishBuyOrder">
        insert into yg_fish_buy_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="bizId != null ">
                biz_id,
              </if>
              <if test="bizType != null and bizType != '' ">
                biz_type,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="payAmount != null ">
                pay_amount,
              </if>
              <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
              </if>
              <if test="payType != null and payType != '' ">
                pay_type,
              </if>
              <if test="payStatus != null and payStatus != '' ">
                pay_status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="payDatetime != null ">
                pay_datetime,
              </if>
              <if test="remarks != null and remarks != '' ">
                remarks,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizType != null and bizType != '' ">
                #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null and remarks != '' ">
                #{remarks,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_fish_buy_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishBuyOrder">
        update yg_fish_buy_order
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                biz_id = #{bizId,jdbcType=BIGINT},
            </if>
            <if test="bizType != null and bizType != '' ">
                biz_type = #{bizType,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remarks != null and remarks != '' ">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_buy_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishBuyOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_buy_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>