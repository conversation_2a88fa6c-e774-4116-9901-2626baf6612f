<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishOceanPondMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishOceanPond">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="image" jdbcType="VARCHAR" property="image"/>
        <result column="total_quantity" jdbcType="DECIMAL" property="totalQuantity"/>
        <result column="remaining_quantity" jdbcType="DECIMAL" property="remainingQuantity"/>
        <result column="is_repurchase" jdbcType="VARCHAR" property="isRepurchase"/>
        <result column="repurchase_price" jdbcType="DECIMAL" property="repurchasePrice"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.type
        , t.image
        , t.total_quantity
        , t.remaining_quantity
        , t.is_repurchase
        , t.repurchase_price
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="typeList != null and typeList.size() != 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="image != null and image != '' ">
                AND t.image = #{image, jdbcType=VARCHAR}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=DECIMAL}
            </if>
            <if test="remainingQuantity != null">
                AND t.remaining_quantity = #{remainingQuantity, jdbcType=DECIMAL}
            </if>
            <if test="remainingQuantityMin != null">
                AND t.remaining_quantity <![CDATA[ >]]> #{remainingQuantityMin, jdbcType=DECIMAL}
            </if>
            <if test="isRepurchase != null and isRepurchase != '' ">
                AND t.is_repurchase = #{isRepurchase, jdbcType=VARCHAR}
            </if>
            <if test="repurchasePrice != null">
                AND t.repurchase_price = #{repurchasePrice, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishOceanPond">
        insert into yg_fish_ocean_pond
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="image != null and image != '' ">
                image,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainingQuantity != null ">
                remaining_quantity,
            </if>
            <if test="isRepurchase != null and isRepurchase != '' ">
                is_repurchase,
            </if>
            <if test="repurchasePrice != null ">
                repurchase_price,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="image != null and image != '' ">
                #{image,jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=DECIMAL},
            </if>
            <if test="remainingQuantity != null">
                #{remainingQuantity,jdbcType=DECIMAL},
            </if>
            <if test="isRepurchase != null and isRepurchase != '' ">
                #{isRepurchase,jdbcType=VARCHAR},
            </if>
            <if test="repurchasePrice != null">
                #{repurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from yg_fish_ocean_pond
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishOceanPond">
        update yg_fish_ocean_pond
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="image != null and image != '' ">
                image = #{image,jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=DECIMAL},
            </if>
            <if test="remainingQuantity != null">
                remaining_quantity = #{remainingQuantity,jdbcType=DECIMAL},
            </if>
            <if test="isRepurchase != null and isRepurchase != '' ">
                is_repurchase = #{isRepurchase,jdbcType=VARCHAR},
            </if>
            <if test="repurchasePrice != null">
                repurchase_price = #{repurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateChangeQuantity">
        update yg_fish_ocean_pond set
            total_quantity = total_quantity+#{totalChangeQuantity,jdbcType=DECIMAL},
            remaining_quantity = remaining_quantity+#{changeQuantity,jdbcType=DECIMAL}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_ocean_pond t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishOceanPond"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_ocean_pond t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_ocean_pond t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectRank" resultType="com.std.core.pojo.domain.AwardOceanPondEntity">
        select
        t.id ,
        t.remaining_quantity weight
        from yg_fish_ocean_pond t
        where t.status='1' and t.remaining_quantity <![CDATA[ >]]> 0
    </select>

</mapper>