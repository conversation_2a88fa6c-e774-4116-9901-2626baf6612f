<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishSailRecordDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishSailRecordDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="frequency_record_id" jdbcType="BIGINT" property="frequencyRecordId"/>
        <result column="variety_id" jdbcType="BIGINT" property="varietyId"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="fishing_quantity" jdbcType="DECIMAL" property="fishingQuantity"/>
        <result column="total_quantity" jdbcType="DECIMAL" property="totalQuantity"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.frequency_record_id
        , t.variety_id
        , t.order_no
        , t.fishing_quantity
        , t.total_quantity
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="frequencyRecordId != null">
                AND t.frequency_record_id = #{frequencyRecordId, jdbcType=BIGINT}
            </if>
            <if test="varietyId != null">
                AND t.variety_id = #{varietyId, jdbcType=BIGINT}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="fishingQuantity != null">
                AND t.fishing_quantity = #{fishingQuantity, jdbcType=DECIMAL}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishSailRecordDetail">
        insert into yg_fish_sail_record_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="frequencyRecordId != null ">
                frequency_record_id,
            </if>
            <if test="varietyId != null ">
                variety_id,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="fishingQuantity != null ">
                fishing_quantity,
            </if>
            <if test="totalQuantity != null">
                total_quantity,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="frequencyRecordId != null">
                #{frequencyRecordId,jdbcType=BIGINT},
            </if>
            <if test="varietyId != null">
                #{varietyId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="fishingQuantity != null">
                #{fishingQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity, jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        insert into yg_fish_sail_record_detail
        (
        frequency_record_id,
        variety_id,
        order_no,
        fishing_quantity,
        total_quantity
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            ( #{item.frequencyRecordId,jdbcType=BIGINT},
            #{item.varietyId,jdbcType=BIGINT},
            #{item.orderNo, jdbcType=INTEGER},
            #{item.fishingQuantity,jdbcType=DECIMAL},
            #{item.totalQuantity, jdbcType=DECIMAL}
            )
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from yg_fish_sail_record_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishSailRecordDetail">
        update yg_fish_sail_record_detail
        <set>
            <if test="frequencyRecordId != null">
                frequency_record_id = #{frequencyRecordId,jdbcType=BIGINT},
            </if>
            <if test="varietyId != null">
                variety_id = #{varietyId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="fishingQuantity != null">
                fishing_quantity = #{fishingQuantity,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity, jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishSailRecordDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSumByRecord" resultType="java.math.BigDecimal">
        select COALESCE(sum(fishing_quantity), 0)
        from yg_fish_sail_record_detail t
        where t.frequency_record_id = #{recordId}
    </select>

    <select id="selectVarietyByRecord" resultType="com.std.core.pojo.response.FishSailRecordDetailListRes">
        select variety_id varietyId, sum(fishing_quantity) fishingQuantity, ta.image, ta.name varietyName
        from yg_fish_sail_record_detail t
                 inner join yg_fish_ocean_pond ta on t.variety_id = ta.id
        where t.frequency_record_id = #{recordId}
        group by variety_id
    </select>

</mapper>