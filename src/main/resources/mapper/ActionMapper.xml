<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ActionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Action">
        <id column="t_id" jdbcType="BIGINT" property="id"/>
        <result column="t_type" jdbcType="VARCHAR" property="type"/>
        <result column="t_name" jdbcType="VARCHAR" property="name"/>
        <result column="t_code" jdbcType="VARCHAR" property="code"/>
        <result column="t_url" jdbcType="VARCHAR" property="url"/>
        <result column="t_input" jdbcType="VARCHAR" property="input"/>
        <result column="t_output" jdbcType="VARCHAR" property="output"/>
        <result column="t_status" jdbcType="VARCHAR" property="status"/>
        <result column="t_remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        t.id as t_id, t.type as t_type, t.name as t_name, t.code as t_code,
    t.url as t_url, t.input as t_input, t.output as t_output, t.status as t_status,
    t.remark as t_remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null and id != '' ">
                AND id = #{id}
            </if>
            <if test="type != null and type != '' ">
                AND type = #{type}
            </if>
            <if test="name != null and name != '' ">
                AND name like concat('%',#{name},'%')
            </if>
            <if test="code != null and code != '' ">
                AND code = #{code}
            </if>
            <if test="url != null and url != '' ">
                AND url = #{url}
            </if>
            <if test="urlForQuery != null and urlForQuery != '' ">
                AND url like concat('%',#{urlForQuery},'%')
            </if>
            <if test="status != null and status != '' ">
                AND status = #{status}
            </if>
        </trim>
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_action t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByActionUrl" parameterType="java.lang.String" resultType="java.lang.Long">
        select
        count(1)
        from tsys_action t
        where t.url = #{url,jdbcType=VARCHAR}
    </select>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Action"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_action t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByConditionByRole" parameterType="java.util.ArrayList"
            resultMap="BaseResultMap">
        select
        distinct
        <include refid="Base_Column_List"/>
        from tsys_action t,tsys_permission_role tpr
        where t.id = tpr.resource_id
        AND tpr.role_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectByConditionByMenu" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        distinct
        <include refid="Base_Column_List"/>
        from tsys_action t,tsys_menu_action tma
        where t.id = tma.action_id AND tma.menu_id=#{menuId}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_action
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Action">
        insert into tsys_action
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="url != null">
                url,
            </if>
            <if test="input != null">
                input,
            </if>
            <if test="output != null">
                output,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                #{url,jdbcType=VARCHAR},
            </if>
            <if test="input != null">
                #{input,jdbcType=VARCHAR},
            </if>
            <if test="output != null">
                #{output,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Action">
        update tsys_action
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="url != null">
                url = #{url,jdbcType=VARCHAR},
            </if>
            <if test="input != null">
                input = #{input,jdbcType=VARCHAR},
            </if>
            <if test="output != null">
                output = #{output,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
