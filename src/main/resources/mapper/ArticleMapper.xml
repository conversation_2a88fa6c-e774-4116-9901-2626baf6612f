<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ArticleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Article">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_id" jdbcType="BIGINT" property="typeId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="cover" jdbcType="VARCHAR" property="cover"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="subtitle" jdbcType="VARCHAR" property="subtitle"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="readNumber" jdbcType="INTEGER" property="readNumber"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type_id
        , t.type
        , t.cover
        , t.pic
        , t.title
        , t.subtitle
        , t.content_type
        , t.content
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.channel_id
    </sql>
    <sql id="Base_Column_PC_List">
        t
        .
        id
        , t.type_id
        , t.type
        , t.cover
        , t.pic
        , t.title
        , t.subtitle
        , t.content_type
        , t.content
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , t.channel_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="typeId != null ">
                AND t.type_id = #{typeId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="contentType != null and contentType != '' ">
                AND t.content_type = #{contentType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title like concat(concat('%',#{title}),'%')
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="channelIdList != null and channelIdList.size() != 0 ">
                AND t.channel_id in
                <foreach item="item" index="index" collection="channelIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <sql id="join_condition">
        <if test="frontFlag != null and frontFlag != ''">
            left join tsys_article_type tat on t.type_id = tat.id
        </if>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Article">
        insert into tsys_article
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="typeId != null ">
                type_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="cover != null and cover != '' ">
                cover,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="title != null and title != '' ">
                title,
            </if>
            <if test="subtitle != null and subtitle != '' ">
                subtitle,
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="channelId != null ">
                channel_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="typeId != null ">
                #{typeId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="cover != null and cover != '' ">
                #{cover,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title,jdbcType=VARCHAR},
            </if>
            <if test="subtitle != null and subtitle != '' ">
                #{subtitle,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                #{contentType, jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="channelId != null ">
                #{channelId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <insert id="batchArticlePeriods">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_article_period
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.articleId != null ">
                    article_id,
                </if>
                <if test="item.periodId != null ">
                    period_id,
                </if>
                <if test="item.orderNo != null ">
                    order_no,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.articleId != null ">
                    #{item.articleId,jdbcType=BIGINT},
                </if>
                <if test="item.periodId != null ">
                    #{item.periodId,jdbcType=BIGINT},
                </if>
                <if test="item.orderNo != null ">
                    #{item.orderNo,jdbcType=INTEGER},
                </if>

            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_article
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByArticle">
        delete
        from nft_article_period
        where article_id = #{articleId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Article">
        update tsys_article
        <set>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="cover != null and cover != '' ">
                cover = #{cover,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="subtitle != null and subtitle != '' ">
                subtitle = #{subtitle,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type = #{contentType, jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <choose>
                <when test="remark != null and remark !=''">
                    remark = #{remark,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    remark = '',
                </otherwise>
            </choose>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeySelectiveOss">
        update tsys_article
        <set>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            cover = #{cover,jdbcType=VARCHAR},
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="subtitle != null and subtitle != '' ">
                subtitle = #{subtitle,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type = #{contentType, jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="updater != null ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <choose>
                <when test="remark != null and remark !=''">
                    remark = #{remark,jdbcType=VARCHAR},
                </when>
                <otherwise>
                    remark = '',
                </otherwise>
            </choose>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_PC_List"/>
        from tsys_article t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectTitleByCondition" parameterType="com.std.core.pojo.domain.Article"
            resultMap="BaseResultMap">
        select
        t.id
        , t.type_id
        , t.type
        , t.cover
        , t.pic
        , t.title
        , t.subtitle
        , t.content_type
        , t.content
        , t.status
        , t.order_no
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
        , tr.readNumber
        from tsys_article t
        <include refid="join_condition"/>
        left join (select ref_id,number as readNumber from tyx_browse_records tbr where ref_type="7" and action_type='0'
        group by ref_id) tr
        on t.id=tr.ref_id
        <include refid="where_condition"/>
        ORDER BY tr.readNumber DESC
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Article"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_article t
        <include refid="join_condition"/>
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <!-- 组合条件查询 -->
    <select id="selectPCPage" parameterType="com.std.core.pojo.domain.Article"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_PC_List"/>
        from tsys_article t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectArticlePeriod" resultType="com.std.core.pojo.domain.ArticlePeriod">
        select t.id
             , t.article_id articleId
             , t.period_id  periodId
             , ta.name      periodName
             , t.order_no   orderNo
        from nft_article_period t
                 inner join nft_collection_period ta on t.period_id = ta.id
        where t.article_id = #{articleId}
    </select>
    <select id="selectArticlePeriodFront" resultType="com.std.core.pojo.response.ArticlePeriodDetailRes">
        select ta.id
             , ta.category
             , ta.file_type      fileType
             , ta.name           name
             , ta.cover_file_url coverFileUrl
             , ta.total_quantity totalQuantity
             , ta.price
             , CASE
                   WHEN ta.category = '0' THEN
                       "个"
                   ELSE "份"
            END                  totalQuantityUnit
        from nft_article_period t
                 inner join nft_collection_period ta on t.period_id = ta.id
        where t.article_id = #{articleId}
        order by t.order_no
    </select>
</mapper>