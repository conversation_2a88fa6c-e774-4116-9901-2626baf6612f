<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityUserSumMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivityUserSum">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_size" jdbcType="INTEGER" property="collectionSize"/>
        <result column="invitation_size" jdbcType="INTEGER" property="invitationSize"/>
        <result column="invitation_finish_size" jdbcType="INTEGER" property="invitationFinishSize"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="activityName" jdbcType="VARCHAR" property="activityName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.activity_id
        , t.user_id
        , t.collection_id
        , t.collection_size
        , t.invitation_size
        , t.invitation_finish_size
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionSize != null">
                AND t.collection_size = #{collectionSize, jdbcType=INTEGER}
            </if>
            <if test="invitationSize != null">
                AND t.invitation_size = #{invitationSize, jdbcType=INTEGER}
            </if>
            <if test="invitationFinishSize != null">
                AND t.invitation_finish_size = #{invitationFinishSize, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="companyId != null">
                AND ta.company_id = #{companyId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivityUserSum"
            useGeneratedKeys="true" keyProperty="id">
        insert into lxa_invitation_activity_user_sum
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionSize != null ">
                collection_size,
            </if>
            <if test="invitationSize != null ">
                invitation_size,
            </if>
            <if test="invitationFinishSize != null ">
                invitation_finish_size,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionSize != null">
                #{collectionSize,jdbcType=INTEGER},
            </if>
            <if test="invitationSize != null">
                #{invitationSize,jdbcType=INTEGER},
            </if>
            <if test="invitationFinishSize != null">
                #{invitationFinishSize,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from lxa_invitation_activity_user_sum
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivityUserSum">
        update lxa_invitation_activity_user_sum
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionSize != null">
                collection_size = #{collectionSize,jdbcType=INTEGER},
            </if>
            <if test="invitationSize != null">
                invitation_size = #{invitationSize,jdbcType=INTEGER},
            </if>
            <if test="invitationFinishSize != null">
                invitation_finish_size = #{invitationFinishSize,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="addInvitationSize">
        update lxa_invitation_activity_user_sum
        set invitation_size =invitation_size + 1
        where id = #{id,jdbcType=BIGINT};
    </update>
    <update id="addInvitationFinishSize">
        update lxa_invitation_activity_user_sum
        set invitation_finish_size =invitation_finish_size + 1
        where id = #{id,jdbcType=BIGINT};
    </update>
    <update id="addCollectionSize">
        update lxa_invitation_activity_user_sum
        set collection_size =collection_size + 1
        where id = #{id,jdbcType=BIGINT};
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_sum t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivityUserSum"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_sum t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_user_sum t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name activityname
        from lxa_invitation_activity_user_sum t
        inner join lxa_invitation_activity ta on ta.id =t.activity_id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.InvitationActivityUserSum"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name activityname
        from lxa_invitation_activity_user_sum t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        inner join lxa_invitation_activity ta on ta.id =t.activity_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectRankNoList" parameterType="com.std.core.pojo.domain.InvitationActivityUserSumFront"
            resultType="com.std.core.pojo.domain.InvitationActivityUserSumFront">
        SELECT @rank_no := @rank_no + 1 as id,
        user_id as userId,
        tu.nickname as nickname,
        tu.login_name as loginName,
        tu.photo as photo,
        t.invitation_finish_size as invitationFinishSize
        FROM lxa_invitation_activity_user_sum t, (SELECT @rank_no := 0) t1, tsys_user tu
        WHERE tu.id = t.user_id
          AND t.activity_id = #{activityId}
          AND t.invitation_finish_size <![CDATA[ > ]]> 0
        order by t.invitation_finish_size desc
            limit 100
    </select>

    <select id="selectRankNoCount" parameterType="com.std.core.pojo.domain.InvitationActivityUserSumFront"
            resultType="java.lang.Long">
        SELECT count(1)
        FROM lxa_invitation_activity_user_sum t
        WHERE t.activity_id = #{activityId}
        AND t.invitation_finish_size <![CDATA[ > ]]> 0
        <if test="invitationFinishSizeMin != null">
            AND t.invitation_finish_size <![CDATA[ > ]]> #{invitationFinishSizeMin, jdbcType=BIGINT}
        </if>
    </select>
</mapper>