<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvoiceOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvoiceOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="order_note" jdbcType="VARCHAR" property="orderNote"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="company_type" jdbcType="VARCHAR" property="companyType"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="create_ts" jdbcType="BIGINT" property="createTs"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="invoice_apply_id" jdbcType="BIGINT" property="invoiceApplyId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.order_type
        , t.order_id
        , t.order_note
        , t.amount
        , t.company_type
        , t.company_id
        , t.company_name
        , t.create_datetime
        , t.create_ts
        , t.status
        , t.invoice_apply_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="idList != null and idList.size() != 0 ">
                AND t.id in
                <foreach item="item" index="index" collection="idList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="orderType != null and orderType != '' ">
                AND t.order_type = #{orderType, jdbcType=VARCHAR}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="orderNote != null and orderNote != '' ">
                AND t.order_note = #{orderNote, jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="companyType != null and companyType != '' ">
                AND t.company_type = #{companyType, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="companyName != null and companyName != '' ">
                AND t.company_name = #{companyName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="invoiceApplyId != null">
                AND t.invoice_apply_id = #{invoiceApplyId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvoiceOrder">
        insert into tstd_invoice_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="orderType != null and orderType != '' ">
                order_type,
            </if>
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="orderNote != null and orderNote != '' ">
                order_note,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="companyType != null and companyType != '' ">
                company_type,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="companyName != null and companyName != '' ">
                company_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="createTs != null ">
                create_ts,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="invoiceApplyId != null ">
                invoice_apply_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderType != null and orderType != '' ">
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNote != null and orderNote != '' ">
                #{orderNote,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="companyType != null and companyType != '' ">
                #{companyType, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="companyName != null and companyName != '' ">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTs != null ">
                #{createTs,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_invoice_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvoiceOrder">
        update tstd_invoice_order
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderType != null and orderType != '' ">
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="orderNote != null and orderNote != '' ">
                order_note = #{orderNote,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="companyType != null and companyType != '' ">
                company_type = #{companyType, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="companyName != null and companyName != '' ">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="invoiceApplyId != null">
                invoice_apply_id = #{invoiceApplyId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_invoice_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvoiceOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_invoice_order t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>