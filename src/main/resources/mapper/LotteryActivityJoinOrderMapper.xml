<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LotteryActivityJoinOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LotteryActivityJoinOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="join_time" jdbcType="BIGINT" property="joinTime"/>
        <result column="sold_quantity" jdbcType="INTEGER" property="soldQuantity"/>

        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="re_mobile" jdbcType="VARCHAR" property="reMobile"/>
        <result column="re_address" jdbcType="VARCHAR" property="reAddress"/>
        <result column="deliverer" jdbcType="VARCHAR" property="deliverer"/>
        <result column="delivery_datetime" jdbcType="TIMESTAMP" property="deliveryDatetime"/>
        <result column="logistics_company" jdbcType="VARCHAR" property="logisticsCompany"/>
        <result column="logistics_code" jdbcType="VARCHAR" property="logisticsCode"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.activity_id
        , t.activity_name
        , t.user_id
        , t.status
        , t.join_time
        , t.sold_quantity
        , t.create_datetime
        , t.update_datetime
        , t.receiver
        , t.re_mobile
        , t.re_address
        , t.deliverer
        , t.delivery_datetime
        , t.logistics_company
        , t.logistics_code
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="activityName != null and activityName != '' ">
                AND t.activity_name = #{activityName, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="joinTime != null">
                AND t.join_time = #{joinTime, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="receiver != null and receiver != '' ">
                AND t.receiver = #{receiver, jdbcType=VARCHAR}
            </if>
            <if test="reMobile != null and reMobile != '' ">
                AND t.re_mobile = #{reMobile, jdbcType=VARCHAR}
            </if>
            <if test="reAddress != null and reAddress != '' ">
                AND t.re_address = #{reAddress, jdbcType=VARCHAR}
            </if>
            <if test="deliverer != null and deliverer != '' ">
                AND t.deliverer = #{deliverer, jdbcType=VARCHAR}
            </if>
            <if test="deliveryDatetime != null">
                AND t.delivery_datetime = #{deliveryDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                AND t.logistics_company = #{logisticsCompany, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                AND t.logistics_code = #{logisticsCode, jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.LotteryActivityJoinOrder" useGeneratedKeys="true" keyProperty="id">
        insert into nft_lottery_activity_join_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="activityId != null ">
                activity_id,
            </if>
            <if test="activityName != null and activityName != '' ">
                activity_name,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="joinTime != null ">
                join_time,
            </if>
            <if test="soldQuantity != null ">
                sold_quantity,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver,
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile,
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address,
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer,
            </if>
            <if test="deliveryDatetime != null ">
                delivery_datetime,
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company,
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="joinTime != null">
                #{joinTime,jdbcType=BIGINT},
            </if>
            <if test="soldQuantity != null">
                #{soldQuantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiver != null and receiver != '' ">
                #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_lottery_activity_join_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LotteryActivityJoinOrder">
        update nft_lottery_activity_join_order
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="joinTime != null">
                join_time = #{joinTime,jdbcType=BIGINT},
            </if>
            <if test="soldQuantity != null">
                sold_quantity = #{soldQuantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile = #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address = #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer = #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                delivery_datetime = #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company = #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code = #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_order t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LotteryActivityJoinOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_order t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>