<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsIntegralMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsIntegral">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category_id" jdbcType="BIGINT" property="categoryId"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="goods_type" jdbcType="VARCHAR" property="goodsType"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sold_status" jdbcType="VARCHAR" property="soldStatus"/>
        <result column="show_pic" jdbcType="VARCHAR" property="showPic"/>
        <result column="content_pic" jdbcType="VARCHAR" property="contentPic"/>
        <result column="play_pic" jdbcType="VARCHAR" property="playPic"/>
        <result column="unlock_condition" jdbcType="DECIMAL" property="unlockCondition"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="update_time" jdbcType="BIGINT" property="updateTime"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.category_id
        , t.supplier_id
        , t.name
        , t.pic
        , t.original_price
        , t.price
        , t.integral_price
        , t.total_quantity
        , t.remain_quantity
        , t.goods_type
        , t.type
        , t.status
        , t.sold_status
        , t.show_pic
        , t.content_pic
        , t.play_pic
        , t.unlock_condition
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.update_time
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bigCategoryId != null">
                AND t.category_id in (select id from mall_goods_category where parent_id =#{bigCategoryId, jdbcType=BIGINT})
            </if>
            <if test="categoryId != null">
                AND t.category_id = #{categoryId, jdbcType=BIGINT}
            </if>
            <if test="supplierId != null">
                AND t.supplier_id = #{supplierId, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="originalPrice != null">
                AND t.original_price = #{originalPrice, jdbcType=DECIMAL}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="integralPrice != null">
                AND t.integral_price = #{integralPrice, jdbcType=DECIMAL}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="goodsType != null and goodsType != '' ">
                AND t.goods_type = #{goodsType, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="typeList != null and typeList.size() != 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="soldStatusList != null and soldStatusList.size() != 0 ">
                AND t.sold_status in
                <foreach item="item" index="index" collection="soldStatusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                AND t.sold_status = #{soldStatus, jdbcType=VARCHAR}
            </if>
            <if test="showPic != null and showPic != '' ">
                AND t.show_pic = #{showPic, jdbcType=VARCHAR}
            </if>
            <if test="contentPic != null and contentPic != '' ">
                AND t.content_pic = #{contentPic, jdbcType=VARCHAR}
            </if>
            <if test="playPic != null and playPic != '' ">
                AND t.play_pic = #{playPic, jdbcType=VARCHAR}
            </if>
            <if test="unlockCondition != null">
                AND t.unlock_condition = #{unlockCondition, jdbcType=DECIMAL}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=BIGINT}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsIntegral" useGeneratedKeys="true"
            keyProperty="id">
        insert into mall_goods_integral
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="categoryId != null ">
                category_id,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="integralPrice != null ">
                integral_price,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="goodsType != null and goodsType != '' ">
                goods_type,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status,
            </if>
            <if test="showPic != null and showPic != '' ">
                show_pic,
            </if>
            <if test="contentPic != null and contentPic != '' ">
                content_pic,
            </if>
            <if test="playPic != null and playPic != '' ">
                play_pic,
            </if>
            <if test="unlockCondition != null ">
                unlock_condition,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="categoryId != null">
                #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="supplierId != null">
                #{supplierId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="originalPrice != null">
                #{originalPrice, jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="integralPrice != null">
                #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null and goodsType != '' ">
                #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                #{soldStatus,jdbcType=VARCHAR},
            </if>
            <if test="showPic != null and showPic != '' ">
                #{showPic,jdbcType=VARCHAR},
            </if>
            <if test="contentPic != null and contentPic != '' ">
                #{contentPic,jdbcType=VARCHAR},
            </if>
            <if test="playPic != null and playPic != '' ">
                #{playPic, jdbcType=VARCHAR},
            </if>
            <if test="unlockCondition != null">
                #{unlockCondition,jdbcType=INTEGER},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mall_goods_integral
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsIntegral">
        update mall_goods_integral
        <set>
            <if test="categoryId != null">
                category_id = #{categoryId,jdbcType=BIGINT},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice, jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="integralPrice != null">
                integral_price = #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null and goodsType != '' ">
                goods_type = #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status = #{soldStatus,jdbcType=VARCHAR},
            </if>
            <if test="showPic != null and showPic != '' ">
                show_pic = #{showPic,jdbcType=VARCHAR},
            </if>
            <if test="contentPic != null and contentPic != '' ">
                content_pic = #{contentPic,jdbcType=VARCHAR},
            </if>
            <if test="playPic != null and playPic != '' ">
                play_pic = #{playPic, jdbcType=VARCHAR},
            </if>
            <if test="unlockCondition != null">
                unlock_condition = #{unlockCondition,jdbcType=DECIMAL},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime, jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateGoodsRemainQuantity">
        update mall_goods_integral
        set remain_quantity=remain_quantity - #{quantity}
        where id = #{id}
          and status = '1'
          and sold_status = '1'
          and remain_quantity <![CDATA[ >=]]> #{quantity}
    </update>
    <update id="updateIntegralGoodsEnd">
        update mall_goods_integral
        set sold_status='2'
        where id in
              (select id from (select id from mall_goods_integral where remain_quantity = 0 and sold_status !='2' and status ='1') a);
    </update>
    <update id="updateRemainQuantity">
        update mall_goods_integral
        set remain_quantity=remain_quantity - #{quantity}
        where id = #{id}
          and remain_quantity <![CDATA[ >=]]> #{quantity} and (remain_quantity - #{quantity}) > (SELECT count(1) FROM mall_goods_buy_record where goods_type='1' AND `status`='0' AND goods_id= #{id})
    </update>
    <update id="updateRemainQuantityAdd">
        update mall_goods_integral
        set remain_quantity=remain_quantity + #{quantity},
            sold_status='1'
        where id = #{id}
    </update>
    <update id="updateTotalQuantityAdd">
        update mall_goods_integral
        set remain_quantity=remain_quantity + #{quantity},
            total_quantity=total_quantity + #{quantity}
        where id = #{id}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_integral t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsIntegral"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_integral t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.GoodsIntegralPageRes">
        select
        t.id
        , t.name
        , t.pic
        , t.price
        , t.integral_price integralPrice
        , t.goods_type goodsType
        , t.type
        , t.sold_status soldStatus
        from mall_goods_integral t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_integral t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="getMaxUnlockCondition" resultType="java.math.BigDecimal">
        select
            COALESCE(max(t.unlock_condition),0)
        from mall_goods_integral t
    </select>
</mapper>