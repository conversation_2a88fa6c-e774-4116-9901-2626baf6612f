<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.HorseMatchRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.HorseMatchRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="match_id" jdbcType="BIGINT" property="matchId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="horse_id" jdbcType="BIGINT" property="horseId"/>
        <result column="horse_user_id" jdbcType="BIGINT" property="horseUserId"/>
        <result column="rank" jdbcType="INTEGER" property="rank"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="time" jdbcType="BIGINT" property="time"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.match_id
        , t.user_id
        , t.horse_id
        , t.horse_user_id
        , t.rank
        , t.create_datetime
        , t.update_datetime
        , t.time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="matchId != null">
                AND t.match_id = #{matchId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="horseId != null">
                AND t.horse_id = #{horseId, jdbcType=BIGINT}
            </if>
            <if test="horseUserId != null">
                AND t.horse_user_id = #{horseUserId, jdbcType=BIGINT}
            </if>
            <if test="rank != null">
                AND t.rank = #{rank, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="time != null">
                AND t.time = #{time, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.HorseMatchRecord">
        insert into hr_horse_match_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="matchId != null ">
                match_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="horseId != null ">
                horse_id,
            </if>
            <if test="horseUserId != null ">
                horse_user_id,
            </if>
            <if test="rank != null ">
                rank,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="time != null">
                time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="matchId != null">
                #{matchId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="horseId != null">
                #{horseId,jdbcType=BIGINT},
            </if>
            <if test="horseUserId != null">
                #{horseUserId,jdbcType=BIGINT},
            </if>
            <if test="rank != null">
                #{rank,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="time != null">
                #{time, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into hr_horse_match_record
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.matchId != null ">
                    match_id,
                </if>
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.horseId != null ">
                    horse_id,
                </if>
                <if test="item.horseUserId != null ">
                    horse_user_id,
                </if>
                <if test="item.rank != null ">
                    rank,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime,
                </if>
                <if test="item.time != null">
                    #{item.time, jdbcType=BIGINT},
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.matchId != null">
                    #{item.matchId,jdbcType=BIGINT},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.horseId != null">
                    #{item.horseId,jdbcType=BIGINT},
                </if>
                <if test="item.horseUserId != null">
                    #{item.horseUserId,jdbcType=BIGINT},
                </if>
                <if test="item.rank != null">
                    #{item.rank,jdbcType=INTEGER},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateDatetime != null">
                    #{item.updateDatetime, jdbcType=TIMESTAMP},
                </if>
                <if test="item.time != null">
                    #{item.time, jdbcType=BIGINT},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from hr_horse_match_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.HorseMatchRecord">
        update hr_horse_match_record
        <set>
            <if test="matchId != null">
                match_id = #{matchId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="horseId != null">
                horse_id = #{horseId,jdbcType=BIGINT},
            </if>
            <if test="horseUserId != null">
                horse_user_id = #{horseUserId,jdbcType=BIGINT},
            </if>
            <if test="rank != null">
                rank = #{rank,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="time != null">
                time = #{time, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_horse_match_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.HorseMatchRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_horse_match_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.HorseMatchRecordPageRes">
        select
        t.id
        , t.user_id userId
        , ta.name matchName
        , tb.pic horsePic
        , tb.name horseName
        , ta.number
        , t.rank
        , t.create_datetime createDatetime
        from hr_horse_match_record t
        INNER JOIN hr_horse_match ta on t.match_id = ta.id
        INNER JOIN hr_horse_config tb on t.horse_id = tb.id
        <include refid="where_condition"/>

    </select>
</mapper>