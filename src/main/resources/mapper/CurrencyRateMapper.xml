<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CurrencyRateMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CurrencyRate">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="refer_currency" jdbcType="VARCHAR" property="referCurrency"/>
        <result column="origin" jdbcType="VARCHAR" property="origin"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.currency
        , t.refer_currency
        , t.origin
        , t.rate
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=INTEGER}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="referCurrency != null and referCurrency != '' ">
                AND t.refer_currency = #{referCurrency, jdbcType=VARCHAR}
            </if>
            <if test="origin != null and origin != '' ">
                AND t.origin = #{origin, jdbcType=VARCHAR}
            </if>
            <if test="rate != null">
                AND t.rate = #{rate, jdbcType=DECIMAL}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CurrencyRate">
        insert into tcoin_currency_rate
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="currency != null and currency != '' ">
                currency,
              </if>
              <if test="referCurrency != null and referCurrency != '' ">
                refer_currency,
              </if>
              <if test="origin != null and origin != '' ">
                origin,
              </if>
              <if test="rate != null ">
                rate,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="referCurrency != null and referCurrency != '' ">
                #{referCurrency,jdbcType=VARCHAR},
            </if>
            <if test="origin != null and origin != '' ">
                #{origin,jdbcType=VARCHAR},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from tcoin_currency_rate
        where id = #{id,jdbcType=INTEGER}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CurrencyRate">
        update tcoin_currency_rate
        <set>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="referCurrency != null and referCurrency != '' ">
                refer_currency = #{referCurrency,jdbcType=VARCHAR},
            </if>
            <if test="origin != null and origin != '' ">
                origin = #{origin,jdbcType=VARCHAR},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tcoin_currency_rate t
        where t.id = #{id,jdbcType=INTEGER}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CurrencyRate"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tcoin_currency_rate t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>