<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.TransferOrderDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.TransferOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.order_id
        , t.collection_id
        , t.collection_detail_id
        , t.price
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.TransferOrderDetail">
        insert into cs_transfer_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="collectionId != null">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="price != null">
                price,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price, jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert">
        <foreach collection="list" separator=";" index="index" item="item">
            insert into cs_transfer_order_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null ">
                    id,
                </if>
                <if test="item.orderId != null ">
                    order_id,
                </if>
                <if test="item.collectionId != null">
                    collection_id,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.price != null">
                    price,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.orderId != null">
                    #{item.orderId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId, jdbcType=BIGINT},
                </if>
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.price != null">
                    #{item.price, jdbcType=DECIMAL},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cs_transfer_order_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.TransferOrderDetail">
        update cs_transfer_order_detail
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId, jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price, jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_transfer_order_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.TransferOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_transfer_order_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.TransferOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_transfer_order_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>