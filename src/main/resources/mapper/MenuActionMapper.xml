<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.MenuActionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MenuAction">
        <id column="t_menu_id" property="menuId" jdbcType="BIGINT"/>
        <id column="t_action_id" property="actionId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
    t.menu_id as t_menu_id,
    t.action_id as t_action_id
  </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="menuId != null and menuId != '' ">
              AND t.menu_id = #{menuId,jdbcType=BIGINT}
            </if>
            <if test="actionId != null and actionId != '' ">
              AND t.action_id = #{actionId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MenuAction"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
      from tsys_menu_action t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <delete id="deleteByMenuId" parameterType="com.std.core.pojo.domain.MenuAction">
    delete from tsys_menu_action
    where menu_id = #{menuId,jdbcType=BIGINT}
  </delete>

    <insert id="batchInsert" parameterType="java.util.List">
      insert into tsys_menu_action (menu_id, action_id)
        values
        <foreach item="menuAction" index="index" collection="list" separator=",">
            (#{menuAction.menuId,jdbcType=BIGINT}, #{menuAction.actionId,jdbcType=BIGINT})
        </foreach>

    </insert>

</mapper>
