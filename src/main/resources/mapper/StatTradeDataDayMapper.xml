<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.StatTradeDataDayMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.StatTradeDataDay">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="current_day" jdbcType="VARCHAR" property="currentDay"/>
        <result column="register_end_count" jdbcType="INTEGER" property="registerEndCount"/>
        <result column="login_count" jdbcType="INTEGER" property="loginCount"/>
        <result column="person_visit_count" jdbcType="INTEGER" property="personVisitCount"/>
        <result column="first_trade_count" jdbcType="INTEGER" property="firstTradeCount"/>
        <result column="first_trade_amount" jdbcType="DECIMAL" property="firstTradeAmount"/>
        <result column="diy_trade_count" jdbcType="INTEGER" property="diyTradeCount"/>
        <result column="diy_trade_amount" jdbcType="DECIMAL" property="diyTradeAmount"/>
        <result column="blind_box_trade_count" jdbcType="INTEGER" property="blindBoxTradeCount"/>
        <result column="blind_box_trade_amount" jdbcType="DECIMAL" property="blindBoxTradeAmount"/>
        <result column="buyout_trade_count" jdbcType="INTEGER" property="buyoutTradeCount"/>
        <result column="buyout_trade_amount" jdbcType="DECIMAL" property="buyoutTradeAmount"/>
        <result column="auction_trade_count" jdbcType="INTEGER" property="auctionTradeCount"/>
        <result column="auction_trade_amount" jdbcType="DECIMAL" property="auctionTradeAmount"/>
        <result column="re_purchase_count" jdbcType="INTEGER" property="rePurchaseCount"/>
        <result column="transfer_introduce_count" jdbcType="INTEGER" property="transferIntroduceCount"/>
        <result column="withdraw_amount" jdbcType="DECIMAL" property="withdrawAmount"/>
        <result column="charge_amount" jdbcType="DECIMAL" property="chargeAmount"/>
        <result column="draw_join_amount" jdbcType="DECIMAL" property="drawJoinAmount"/>
        <result column="draw_success_amount" jdbcType="DECIMAL" property="drawSuccessAmount"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.current_day
        , t.register_end_count
        , t.login_count
        , t.person_visit_count
        , t.first_trade_count
        , t.first_trade_amount
        , t.diy_trade_count
        , t.diy_trade_amount
        , t.blind_box_trade_count
        , t.blind_box_trade_amount
        , t.buyout_trade_count
        , t.buyout_trade_amount
        , t.auction_trade_count
        , t.auction_trade_amount
        , t.re_purchase_count
        , t.transfer_introduce_count
        , t.withdraw_amount
        , t.charge_amount
        , t.draw_join_amount
        , t.draw_success_amount
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="currentDay != null and currentDay != '' ">
                AND t.current_day = #{currentDay, jdbcType=VARCHAR}
            </if>
            <if test="registerEndCount != null">
                AND t.register_end_count = #{registerEndCount, jdbcType=INTEGER}
            </if>
            <if test="loginCount != null">
                AND t.login_count = #{loginCount, jdbcType=INTEGER}
            </if>
            <if test="firstTradeCount != null">
                AND t.first_trade_count = #{firstTradeCount, jdbcType=INTEGER}
            </if>
            <if test="firstTradeAmount != null">
                AND t.first_trade_amount = #{firstTradeAmount, jdbcType=DECIMAL}
            </if>
            <if test="diyTradeCount != null">
                AND t.diy_trade_count = #{diyTradeCount, jdbcType=INTEGER}
            </if>
            <if test="diyTradeAmount != null">
                AND t.diy_trade_amount = #{diyTradeAmount, jdbcType=DECIMAL}
            </if>
            <if test="blindBoxTradeCount != null">
                AND t.blind_box_trade_count = #{blindBoxTradeCount, jdbcType=INTEGER}
            </if>
            <if test="blindBoxTradeAmount != null">
                AND t.blind_box_trade_amount = #{blindBoxTradeAmount, jdbcType=DECIMAL}
            </if>
            <if test="buyoutTradeCount != null">
                AND t.buyout_trade_count = #{buyoutTradeCount, jdbcType=INTEGER}
            </if>
            <if test="buyoutTradeAmount != null">
                AND t.buyout_trade_amount = #{buyoutTradeAmount, jdbcType=DECIMAL}
            </if>
            <if test="auctionTradeCount != null">
                AND t.auction_trade_count = #{auctionTradeCount, jdbcType=INTEGER}
            </if>
            <if test="auctionTradeAmount != null">
                AND t.auction_trade_amount = #{auctionTradeAmount, jdbcType=DECIMAL}
            </if>
            <if test="rePurchaseCount != null">
                AND t.re_purchase_count = #{rePurchaseCount, jdbcType=INTEGER}
            </if>
            <if test="transferIntroduceCount != null">
                AND t.transfer_introduce_count = #{transferIntroduceCount, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.StatTradeDataDay">
        insert into tmm_stat_trade_data_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="currentDay != null and currentDay != '' ">
                current_day,
            </if>
            <if test="registerEndCount != null ">
                register_end_count,
            </if>
            <if test="loginCount != null ">
                login_count,
            </if>
            <if test="personVisitCount != null ">
                person_visit_count,
            </if>
            <if test="firstTradeCount != null ">
                first_trade_count,
            </if>
            <if test="firstTradeAmount != null ">
                first_trade_amount,
            </if>
            <if test="diyTradeCount != null ">
                diy_trade_count,
            </if>
            <if test="diyTradeAmount != null ">
                diy_trade_amount,
            </if>
            <if test="blindBoxTradeCount != null ">
                blind_box_trade_count,
            </if>
            <if test="blindBoxTradeAmount != null ">
                blind_box_trade_amount,
            </if>
            <if test="buyoutTradeCount != null ">
                buyout_trade_count,
            </if>
            <if test="buyoutTradeAmount != null ">
                buyout_trade_amount,
            </if>
            <if test="auctionTradeCount != null ">
                auction_trade_count,
            </if>
            <if test="auctionTradeAmount != null ">
                auction_trade_amount,
            </if>
            <if test="rePurchaseCount != null ">
                re_purchase_count,
            </if>
            <if test="transferIntroduceCount != null ">
                transfer_introduce_count,
            </if>
            <if test="withdrawAmount != null ">
                withdraw_amount,
            </if>
            <if test="chargeAmount != null ">
                charge_amount,
            </if>
            <if test="drawJoinAmount != null ">
                draw_join_amount,
            </if>
            <if test="drawSuccessAmount != null ">
                draw_success_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="currentDay != null and currentDay != '' ">
                #{currentDay,jdbcType=VARCHAR},
            </if>
            <if test="registerEndCount != null">
                #{registerEndCount,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null ">
                #{loginCount,jdbcType=INTEGER},
            </if>
            <if test="personVisitCount != null ">
                #{personVisitCount,jdbcType=INTEGER},
            </if>
            <if test="firstTradeCount != null">
                #{firstTradeCount,jdbcType=INTEGER},
            </if>
            <if test="firstTradeAmount != null">
                #{firstTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="diyTradeCount != null">
                #{diyTradeCount,jdbcType=INTEGER},
            </if>
            <if test="diyTradeAmount != null">
                #{diyTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="blindBoxTradeCount != null">
                #{blindBoxTradeCount,jdbcType=INTEGER},
            </if>
            <if test="blindBoxTradeAmount != null">
                #{blindBoxTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="buyoutTradeCount != null">
                #{buyoutTradeCount,jdbcType=INTEGER},
            </if>
            <if test="buyoutTradeAmount != null">
                #{buyoutTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="auctionTradeCount != null">
                #{auctionTradeCount,jdbcType=INTEGER},
            </if>
            <if test="auctionTradeAmount != null">
                #{auctionTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="rePurchaseCount != null">
                #{rePurchaseCount,jdbcType=INTEGER},
            </if>
            <if test="transferIntroduceCount != null">
                #{transferIntroduceCount,jdbcType=INTEGER},
            </if>
            <if test="withdrawAmount != null ">
                #{withdrawAmount,jdbcType=DECIMAL},
            </if>
            <if test="chargeAmount != null ">
                #{chargeAmount,jdbcType=DECIMAL},
            </if>
            <if test="drawJoinAmount != null ">
                #{drawJoinAmount,jdbcType=DECIMAL},
            </if>
            <if test="drawSuccessAmount != null ">
                #{drawSuccessAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tmm_stat_trade_data_day
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.StatTradeDataDay">
        update tmm_stat_trade_data_day
        <set>
            <if test="currentDay != null and currentDay != '' ">
                current_day = #{currentDay,jdbcType=VARCHAR},
            </if>
            <if test="registerEndCount != null">
                register_end_count = #{registerEndCount,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null">
                login_count = #{loginCount, jdbcType=INTEGER},
            </if>
            <if test="personVisitCount != null">
                person_visit_count = #{personVisitCount, jdbcType=INTEGER},
            </if>
            <if test="firstTradeCount != null">
                first_trade_count = #{firstTradeCount,jdbcType=INTEGER},
            </if>
            <if test="firstTradeAmount != null">
                first_trade_amount = #{firstTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="diyTradeCount != null">
                diy_trade_count = #{diyTradeCount,jdbcType=INTEGER},
            </if>
            <if test="diyTradeAmount != null">
                diy_trade_amount = #{diyTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="blindBoxTradeCount != null">
                blind_box_trade_count = #{blindBoxTradeCount,jdbcType=INTEGER},
            </if>
            <if test="blindBoxTradeAmount != null">
                blind_box_trade_amount = #{blindBoxTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="buyoutTradeCount != null">
                buyout_trade_count = #{buyoutTradeCount,jdbcType=INTEGER},
            </if>
            <if test="buyoutTradeAmount != null">
                buyout_trade_amount = #{buyoutTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="auctionTradeCount != null">
                auction_trade_count = #{auctionTradeCount,jdbcType=INTEGER},
            </if>
            <if test="auctionTradeAmount != null">
                auction_trade_amount = #{auctionTradeAmount,jdbcType=DECIMAL},
            </if>
            <if test="rePurchaseCount != null">
                re_purchase_count = #{rePurchaseCount,jdbcType=INTEGER},
            </if>
            <if test="transferIntroduceCount != null">
                transfer_introduce_count = #{transferIntroduceCount,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_stat_trade_data_day t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.StatTradeDataDay"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_stat_trade_data_day t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectRePurchaseTotalCount" parameterType="com.std.core.pojo.domain.StatTradeDataDay" resultType="java.lang.Integer">
        select count(1)
        from (
                 SELECT user_id, count(user_id)
                 FROM nft_collection_detail_record
                 where trade_type in ('0', '1', '2', '3', '6')
                   and user_id in (select id
                                   from tsys_user
                                   where
                                     <![CDATA[register_datetime <= #{registerDatetimeEnd, jdbcType=TIMESTAMP}]]>
                 )
                 group by user_id
                 having count(user_id) >= 2) t
    </select>

    <select id="selectIntroduceUserTotalCount" parameterType="com.std.core.pojo.domain.StatTradeDataDay" resultType="java.lang.Integer">
        select count(1)
        from tsys_user
        where id in (SELECT distinct user_id FROM nft_collection_detail_record where `owner_type` = '0')
          and id in (select user_referee from tsys_user)
          and id in (select id
                     from tsys_user
                     where <![CDATA[register_datetime <= #{registerDatetimeEnd, jdbcType=TIMESTAMP})
        ]]>
    </select>
</mapper>