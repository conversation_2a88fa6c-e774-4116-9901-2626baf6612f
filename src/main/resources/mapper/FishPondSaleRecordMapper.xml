<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishPondSaleRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishPondSaleRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="variety_id" jdbcType="BIGINT" property="varietyId"/>
        <result column="repurchase_price" jdbcType="DECIMAL" property="repurchasePrice"/>
        <result column="repurchase_number" jdbcType="INTEGER" property="repurchaseNumber"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.variety_id
        , t.repurchase_price
        , t.repurchase_number
        , t.total_amount
        , t.create_datetime
        , t.user_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="varietyId != null">
                AND t.variety_id = #{varietyId, jdbcType=BIGINT}
            </if>
            <if test="repurchasePrice != null">
                AND t.repurchase_price = #{repurchasePrice, jdbcType=DECIMAL}
            </if>
            <if test="repurchaseNumber != null">
                AND t.repurchase_number = #{repurchaseNumber, jdbcType=INTEGER}
            </if>
            <if test="totalAmount != null">
                AND t.total_amount = #{totalAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id" parameterType="com.std.core.pojo.domain.FishPondSaleRecord">
        insert into yg_fish_pond_sale_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="varietyId != null ">
                variety_id,
              </if>
              <if test="repurchasePrice != null ">
                repurchase_price,
              </if>
              <if test="repurchaseNumber != null ">
                repurchase_number,
              </if>
              <if test="totalAmount != null ">
                total_amount,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="varietyId != null">
                #{varietyId,jdbcType=BIGINT},
            </if>
            <if test="repurchasePrice != null">
                #{repurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="repurchaseNumber != null">
                #{repurchaseNumber,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_fish_pond_sale_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishPondSaleRecord">
        update yg_fish_pond_sale_record
        <set>
            <if test="varietyId != null">
                variety_id = #{varietyId,jdbcType=BIGINT},
            </if>
            <if test="repurchasePrice != null">
                repurchase_price = #{repurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="repurchaseNumber != null">
                repurchase_number = #{repurchaseNumber,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_pond_sale_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishPondSaleRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_pond_sale_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>