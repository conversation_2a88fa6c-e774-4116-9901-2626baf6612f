<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.JpushRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.JpushRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="platform" jdbcType="VARCHAR" property="platform"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="notice_info" jdbcType="VARCHAR" property="noticeInfo"/>
        <result column="alias" jdbcType="VARCHAR" property="alias"/>
        <result column="activity_key" jdbcType="VARCHAR" property="activityKey"/>
        <result column="activity_action" jdbcType="VARCHAR" property="activityAction"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.ref_type
        , t.ref_id
        , t.platform
        , t.title
        , t.notice_info
        , t.alias
        , t.activity_key
        , t.activity_action
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.content
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null ">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="platform != null and platform != '' ">
                AND t.platform = #{platform, jdbcType=VARCHAR}
            </if>
            <if test="title != null and title != '' ">
                AND t.title = #{title, jdbcType=VARCHAR}
            </if>
            <if test="noticeInfo != null and noticeInfo != '' ">
                AND t.notice_info = #{noticeInfo, jdbcType=VARCHAR}
            </if>
            <if test="alias != null and alias != '' ">
                AND t.alias = #{alias, jdbcType=VARCHAR}
            </if>
            <if test="activityKey != null and activityKey != '' ">
                AND t.activity_key = #{activityKey, jdbcType=VARCHAR}
            </if>
            <if test="activityAction != null and activityAction != '' ">
                AND t.activity_action = #{activityAction, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.JpushRecord">
        insert into tstd_jpush_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type ,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="platform != null and platform != '' ">
                platform,
            </if>
            <if test="title != null and title != '' ">
                title,
            </if>
            <if test="noticeInfo != null and noticeInfo != '' ">
                notice_info,
            </if>
            <if test="alias != null and alias != '' ">
                alias,
            </if>
            <if test="activityKey != null and activityKey != '' ">
                activity_key,
            </if>
            <if test="activityAction != null and activityAction != '' ">
                activity_action,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType, jdbcType=VARCHAR},
            </if>
            <if test="refId != null ">
                #{refId, jdbcType=BIGINT},
            </if>
            <if test="platform != null and platform != '' ">
                #{platform,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                #{title, jdbcType=VARCHAR},
            </if>
            <if test="noticeInfo != null and noticeInfo != '' ">
                #{noticeInfo,jdbcType=VARCHAR},
            </if>
            <if test="alias != null and alias != '' ">
                #{alias,jdbcType=VARCHAR},
            </if>
            <if test="activityKey != null and activityKey != '' ">
                #{activityKey,jdbcType=VARCHAR},
            </if>
            <if test="activityAction != null and activityAction != '' ">
                #{activityAction,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="insertRegistration">
        insert into tstd_jpush_registration
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null ">
                user_id,
            </if>
            <if test="registrationId != null and registrationId != '' ">
                registration_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="registrationId != null and registrationId != '' ">
                #{registrationId,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_jpush_record
        where user_id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteRegistration">
        delete
        from tstd_jpush_registration
        where user_id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.JpushRecord">
        update tstd_jpush_record
        <set>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType, jdbcType=VARCHAR},
            </if>
            <if test="refId != null ">
                ref_id = #{refId, jdbcType=BIGINT},
            </if>
            <if test="platform != null and platform != '' ">
                platform = #{platform,jdbcType=VARCHAR},
            </if>
            <if test="title != null and title != '' ">
                title = #{title, jdbcType=VARCHAR},
            </if>
            <if test="noticeInfo != null and noticeInfo != '' ">
                notice_info = #{noticeInfo,jdbcType=VARCHAR},
            </if>
            <if test="alias != null and alias != '' ">
                alias = #{alias,jdbcType=VARCHAR},
            </if>
            <if test="activityKey != null and activityKey != '' ">
                activity_key = #{activityKey,jdbcType=VARCHAR},
            </if>
            <if test="activityAction != null and activityAction != '' ">
                activity_action = #{activityAction,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_jpush_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.JpushRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_jpush_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectRegistration" resultType="java.lang.String">
        select registration_id
        from tstd_jpush_registration
        where user_id = #{id}
    </select>
</mapper>