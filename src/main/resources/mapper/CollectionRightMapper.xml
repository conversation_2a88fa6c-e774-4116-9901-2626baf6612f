<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionRightMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionRight">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="cycle_type" jdbcType="INTEGER" property="cycleType"/>
        <result column="cycle_time" jdbcType="INTEGER" property="cycleTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_id
        , t.type
        , t.ref_id
        , t.cycle_type
        , t.cycle_time
        , t.status
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="cycleType != null">
                AND t.cycle_type = #{cycleType, jdbcType=INTEGER}
            </if>
            <if test="cycleTime != null">
                AND t.cycle_time = #{cycleTime, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionRight">
        insert into nft_collection_right
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="cycleType != null ">
                cycle_type,
            </if>
            <if test="cycleTime != null ">
                cycle_time,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="cycleType != null">
                #{cycleType,jdbcType=INTEGER},
            </if>
            <if test="cycleTime != null">
                #{cycleTime,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_right
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByCollectionId" parameterType="java.lang.Long">
        delete
        from nft_collection_right
        where collection_id = #{collectionId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionRight">
        update nft_collection_right
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="cycleType != null">
                cycle_type = #{cycleType,jdbcType=INTEGER},
            </if>
            <if test="cycleTime != null">
                cycle_time = #{cycleTime,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionRight"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectLastExistCollection" parameterType="com.std.core.pojo.domain.CollectionRight"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right t
        where type = #{type}
        <if test="refId != null">
            AND t.ref_id = #{refId, jdbcType=BIGINT}
        </if>
        and collection_id in (
        select collection_id
        from nft_collection_detail
        where owner_id = #{userId}) order by id desc limit 1
    </select>
    <select id="selectRight" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right t
        where t.type = #{type, jdbcType=VARCHAR}
        AND (t.ref_id = #{refId, jdbcType=BIGINT} or t.ref_id is null)
        AND t.status = #{status, jdbcType=VARCHAR}

    </select>
</mapper>