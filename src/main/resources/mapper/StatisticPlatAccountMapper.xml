<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.StatisticPlatAccountMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.StatisticPlatAccount">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="total_balance" jdbcType="DECIMAL" property="totalBalance"/>
        <result column="today_charge" jdbcType="DECIMAL" property="todayCharge"/>
        <result column="total_charge" jdbcType="DECIMAL" property="totalCharge"/>
        <result column="today_withdraw" jdbcType="DECIMAL" property="todayWithdraw"/>
        <result column="total_withdraw" jdbcType="DECIMAL" property="totalWithdraw"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.total_balance
        , t.today_charge
        , t.total_charge
        , t.today_withdraw
        , t.total_withdraw
        , t.date
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="totalBalance != null">
                AND t.total_balance = #{totalBalance, jdbcType=DECIMAL}
            </if>
            <if test="todayCharge != null">
                AND t.today_charge = #{todayCharge, jdbcType=DECIMAL}
            </if>
            <if test="totalCharge != null">
                AND t.total_charge = #{totalCharge, jdbcType=DECIMAL}
            </if>
            <if test="todayWithdraw != null">
                AND t.today_withdraw = #{todayWithdraw, jdbcType=DECIMAL}
            </if>
            <if test="totalWithdraw != null">
                AND t.total_withdraw = #{totalWithdraw, jdbcType=DECIMAL}
            </if>
            <if test="date != null">
                AND t.date = #{date, jdbcType=DATE}
            </if>
            <if test="dateStart != null">
                <![CDATA[AND t.date >= #{dateStart, jdbcType=DATE}]]>
            </if>
            <if test="dateEnd != null">
                <![CDATA[AND t.date <= #{dateEnd, jdbcType=DATE}]]>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.StatisticPlatAccount">
        insert into ttask_statistic_plat_account
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="totalBalance != null ">
                total_balance,
              </if>
              <if test="todayCharge != null ">
                today_charge,
              </if>
              <if test="totalCharge != null ">
                total_charge,
              </if>
              <if test="todayWithdraw != null ">
                today_withdraw,
              </if>
              <if test="totalWithdraw != null ">
                total_withdraw,
              </if>
              <if test="date != null ">
                date,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="totalBalance != null">
                #{totalBalance,jdbcType=DECIMAL},
            </if>
            <if test="todayCharge != null">
                #{todayCharge,jdbcType=DECIMAL},
            </if>
            <if test="totalCharge != null">
                #{totalCharge,jdbcType=DECIMAL},
            </if>
            <if test="todayWithdraw != null">
                #{todayWithdraw,jdbcType=DECIMAL},
            </if>
            <if test="totalWithdraw != null">
                #{totalWithdraw,jdbcType=DECIMAL},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ttask_statistic_plat_account
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.StatisticPlatAccount">
        update ttask_statistic_plat_account
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="totalBalance != null">
                total_balance = #{totalBalance,jdbcType=DECIMAL},
            </if>
            <if test="todayCharge != null">
                today_charge = #{todayCharge,jdbcType=DECIMAL},
            </if>
            <if test="totalCharge != null">
                total_charge = #{totalCharge,jdbcType=DECIMAL},
            </if>
            <if test="todayWithdraw != null">
                today_withdraw = #{todayWithdraw,jdbcType=DECIMAL},
            </if>
            <if test="totalWithdraw != null">
                total_withdraw = #{totalWithdraw,jdbcType=DECIMAL},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ttask_statistic_plat_account t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.StatisticPlatAccount"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ttask_statistic_plat_account t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>