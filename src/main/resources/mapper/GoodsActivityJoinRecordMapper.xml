<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsActivityJoinRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsActivityJoinRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.total_amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="totalAmount != null">
                AND t.total_amount = #{totalAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND t.create_datetime <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsActivityJoinRecord">
        insert into mall_goods_activity_join_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null ">
                user_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        select
        <if test="userId != null">
            #{userId,jdbcType=BIGINT},
        </if>
        <if test="createDatetime != null">
            #{createDatetime,jdbcType=TIMESTAMP}
        </if>
        FROM (SELECT 1)a
        where not exists (select user_id from mall_goods_activity_join_record where user_id=#{userId})
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mall_goods_activity_join_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsActivityJoinRecord">
        update mall_goods_activity_join_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount, jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateAmount">
        update mall_goods_activity_join_record set
            total_amount =total_amount + #{totalAmount, jdbcType=DECIMAL}
        where user_id = #{userId,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_activity_join_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsActivityJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_activity_join_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSum" resultType="com.std.core.pojo.response.GoodsActivityJoinRecordDetailRes">
        select
        count(1) totalCount
        from mall_goods_activity_join_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="totalAmount != null">
                AND t.total_amount = #{totalAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectIntegralList" resultType="com.std.core.pojo.response.AccountIntegralListRes">
        select
            t.user_id userId,
            'INTEGRAL' as currency,
            t.total_amount amount
        FROM
            mall_goods_activity_join_record t
        order by t.total_amount desc limit #{limit}
    </select>
    <select id="selectByUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_activity_join_record t
        where t.user_id = #{userId,jdbcType=BIGINT}
    </select>
</mapper>