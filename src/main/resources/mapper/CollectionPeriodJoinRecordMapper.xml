<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionPeriodJoinRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionPeriodJoinRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="order_no" jdbcType="BIGINT" property="orderNo"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="discount_price" jdbcType="DECIMAL" property="discountPrice"/>

        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="priority_flag" jdbcType="VARCHAR" property="priorityFlag"/>
        <result column="white_flag" jdbcType="VARCHAR" property="whiteFlag"/>
        <result column="join_millis" jdbcType="BIGINT" property="joinMillis"/>

        <result column="join_time" jdbcType="BIGINT" property="joinTime"/>
        <result column="isDeduction" jdbcType="VARCHAR" property="isDeduction"/>
        <result column="is_deal" jdbcType="VARCHAR" property="isDeal"/>
        <result column="group" jdbcType="BIGINT" property="group"/>
        <result column="priority_group" jdbcType="BIGINT" property="priorityGroup"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.period_id
        , t.order_no
        , t.user_id
        , t.collection_id
        , t.price
        , t.discount_price
        , t.status
        , t.pay_amount
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.commission_amount
        , t.create_datetime
        , t.update_datetime
        , t.priority_flag
        , t.white_flag
        , t.join_millis
        , t.join_time
        , t.is_deal
        , t.group
        , t.priority_group
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="noStatus != null and noStatus != '' ">
                AND t.status != #{noStatus, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payStatusList != null and payStatusList.size() != 0 ">
                AND t.pay_status in
                <foreach item="item" index="index" collection="payStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="priorityFlag != null and priorityFlag != '' ">
                AND t.priority_flag = #{priorityFlag, jdbcType=VARCHAR}
            </if>
            <if test="whiteFlag != null and whiteFlag != '' ">
                AND t.white_flag = #{whiteFlag, jdbcType=VARCHAR}
            </if>
            <if test="joinMillis != null">
                AND t.join_millis = #{joinMillis, jdbcType=BIGINT}
            </if>
            <if test="noIdList != null and noIdList.size != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="noIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="isDeduction != null and isDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ >=]]> 0
            </if>
            <if test="noIsDeduction != null and noIsDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ <= ]]> 0
            </if>
            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
            <if test="group != null">
                AND t.group = #{group, jdbcType=BIGINT}
            </if>
            <if test="priorityGroup != null">
                AND t.priority_group = #{priorityGroup, jdbcType=BIGINT}
            </if>
            <if test="collectionPeriodName != null and collectionPeriodName != '' ">
                AND ta.name like concat('%', #{collectionPeriodName, jdbcType=VARCHAR},'%')
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord">
        insert into nft_collection_period_join_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            order_no,
            <if test="userId != null ">
                user_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="discountPrice != null ">
                discount_price,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="payBalanceAmount != null ">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null ">
                pay_cash_amount,
            </if>
            <if test="commissionAmount != null ">
                commission_amount,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="joinTime != null ">
                join_time,
            </if>
            <if test="isDeal != null and isDeal != '' ">
                is_deal,
            </if>
            <if test="priorityFlag != null and priorityFlag != '' ">
                priority_flag,
            </if>
            <if test="whiteFlag != null and whiteFlag != '' ">
                white_flag,
            </if>
            <if test="joinMillis != null">
                join_millis,
            </if>
            <if test="group != null">
                `group`,
            </if>
            <if test="priorityGroup != null">
                priority_group,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            (select order_no from (select IFNULL(max(order_no),0)+1 order_no from nft_collection_period_join_record
            where period_id=#{periodId,jdbcType=BIGINT})a),
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="discountPrice != null ">
                #{discountPrice,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="joinTime != null">
                #{joinTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeal != null and isDeal != '' ">
                #{isDeal,jdbcType=VARCHAR},
            </if>
            <if test="priorityFlag != null and priorityFlag != '' ">
                #{priorityFlag, jdbcType=VARCHAR},
            </if>
            <if test="whiteFlag != null and whiteFlag != '' ">
                #{whiteFlag, jdbcType=VARCHAR},
            </if>
            <if test="joinMillis != null">
                #{joinMillis, jdbcType=BIGINT},
            </if>
            <if test="group != null">
                #{group, jdbcType=BIGINT},
            </if>
            <if test="priorityGroup != null">
                #{priorityGroup, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <insert id="joinRecordMigration">
        INSERT INTO `nft_collection_period_join_record_history` (`id`,
                                                                 `period_id`,
                                                                 `order_no`,
                                                                 `user_id`,
                                                                 `collection_id`,
                                                                 `price`,
                                                                 `discount_price`,
                                                                 `status`,
                                                                 `pay_amount`,
                                                                 `pay_type`,
                                                                 `pay_order_code`,
                                                                 `pay_status`,
                                                                 `pay_datetime`,
                                                                 `pay_balance_amount`,
                                                                 `pay_cash_amount`,
                                                                 `commission_amount`,
                                                                 `create_datetime`,
                                                                 `update_datetime`,
                                                                 `join_time`,
                                                                 `is_deal`,
                                                                 `priority_flag`,
                                                                 `white_flag`,
                                                                 `join_millis`,
                                                                 `group`,
                                                                 `priority_group`)
        SELECT t.id,
               t.period_id,
               t.order_no,
               t.user_id,
               t.collection_id,
               t.price,
               t.`discount_price`,
               t.STATUS,
               t.pay_amount,
               t.pay_type,
               t.pay_order_code,
               t.pay_status,
               t.pay_datetime,
               t.pay_balance_amount,
               t.pay_cash_amount,
               t.commission_amount,
               t.create_datetime,
               t.update_datetime,
               t.join_time,
               t.is_deal,
               t.priority_flag,
               t.white_flag,
               t.join_millis,
               t.group,
               t.priority_group
        FROM nft_collection_period_join_record t
        where t.period_id in (select id from nft_collection_period where `status` = '2' and category = '3');

    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_period_join_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <delete id="joinRecordRemove">
        delete
        from nft_collection_period_join_record
        where period_id in (select id from nft_collection_period where `status` = '2');
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord">
        update nft_collection_period_join_record
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="joinTime != null">
                join_time = #{joinTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDeal != null and isDeal != '' ">
                is_deal = #{isDeal,jdbcType=VARCHAR},
            </if>
            <if test="priorityFlag != null and priorityFlag != '' ">
                priority_flag = #{priorityFlag, jdbcType=VARCHAR},
            </if>
            <if test="whiteFlag != null and whiteFlag != '' ">
                white_flag = #{whiteFlag, jdbcType=VARCHAR},
            </if>
            <if test="joinMillis != null">
                join_millis = #{joinMillis, jdbcType=BIGINT},
            </if>
            <if test="group != null">
                `group` = #{group, jdbcType=BIGINT},
            </if>
            <if test="priorityGroup != null">
                priority_group = #{priorityGroup, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateData" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord">
        update nft_collection_period_join_record
        <set>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="isDeal != null and isDeal != '' ">
                is_deal = #{isDeal,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_collection_period_join_record
            <set>
                <if test="item.collectionId != null">
                    collection_id = #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.status != null and item.status != '' ">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime = #{item.updateDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.isDeal != null and item.isDeal != '' ">
                    is_deal = #{item.isDeal,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <update id="doPeriodDrawStrawsEndWhite">
        UPDATE nft_collection_period_join_record
        SET `status`        = '1',
            collection_id   = #{collectionId},
            is_deal         = '1',
            update_datetime = #{updateDatetime}
        WHERE period_id = #{periodId}
          AND `status` = '0'
          AND pay_status = '1'
          AND white_flag = '1'
    </update>

    <update id="doPeriodDrawStrawsEnd">
        UPDATE nft_collection_period_join_record
        SET `status` = '1',
        collection_id = #{collectionId},
        is_deal = '1',
        update_datetime = #{updateDatetime}
        WHERE
        id IN (
        SELECT
        id
        FROM
        (
        SELECT
        id
        FROM
        nft_collection_period_join_record t
        WHERE
        t.period_id = #{periodId}
        AND t.`status` = '0'
        AND t.pay_status = '1'
        AND t.white_flag = '0'
        <if test="priorityFlag != null and priorityFlag != '' ">
            AND t.priority_flag = #{priorityFlag}
            AND t.priority_group IS NOT NULL
        </if>
        ORDER BY
        case when #{priorityFlag}='1' then t.priority_group else t.group end ,
        t.join_time
        LIMIT #{limit}
        ) a
        );
    </update>

    <update id="doPeriodDrawStrawsMiss">
        UPDATE nft_collection_period_join_record
        SET `status`        = '2',
            is_deal         = '1',
            update_datetime = #{updatetime}
        WHERE `status` = '0'
          AND period_id = #{periodId}
          AND pay_status = '1'
    </update>

    <update id="updateCancel">
        UPDATE nft_collection_period_join_record
        SET `status`          = #{status},
            is_deal           = #{isDeal},
            update_datetime   = #{updateDatetime},
            commission_amount = #{commissionAmount,jdbcType=DECIMAL}
        WHERE `id` = #{id}
          AND `status` = '0'
          AND pay_status = '1'
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByPrimaryKeyHistory" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record_history t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectSimpleByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select
        t.id
        , t.join_millis
        from nft_collection_period_join_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionHistory" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record_history t
        INNER JOIN nft_collection_period ta on t.period_id = ta.id
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="getJoinTotalNumber" resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_period_join_record
        WHERE period_id = #{periodId}
          AND pay_status = '1'
          AND `status` in ('0', '1', '2')
    </select>

    <select id="getJoinTotalNumberHistory" resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_period_join_record_history
        WHERE period_id = #{periodId}
          AND pay_status = '1'
          AND `status` in ('0', '1', '2')
    </select>


    <select id="getAlreadyJoinCount" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period_join_record
        where period_id = #{periodId}
          and user_id = #{userId}
          and pay_status = '1';
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <select id="detailNoDealJoinRecord" resultType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select t.id
        from nft_collection_period_join_record t
        where t.status in ('1', '2')
          and t.is_deal = '1' limit 500;
    </select>

    <select id="selectByPayOrderCode" resultType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record t
        where t.pay_order_code =#{orderCode};
    </select>

    <select id="getSuccessfulCount" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period_join_record t
        where t.period_id = #{periodId}
          and t.user_id = #{operatorId}
          and t.status = '1';
    </select>

    <select id="getJoinDrawStrawsNumber" resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_period_join_record
        WHERE period_id = #{periodId}
          AND pay_status = '1'
          and `status` = '1'
    </select>

    <select id="getJoinDrawStrawsNumberHistory" resultType="java.lang.Integer">
        SELECT count(1)
        FROM nft_collection_period_join_record_history
        WHERE period_id = #{periodId}
          AND pay_status = '1'
          and `status` = '1'
    </select>

    <select id="selectTotalAmount" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(price), 0) FROM nft_collection_period_join_record t
        <include refid="where_condition"/>
    </select>

    <select id="selectHistoryTotalAmount" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultType="java.math.BigDecimal">
        SELECT ifnull(sum(price), 0) FROM nft_collection_period_join_record_history t
        <include refid="where_condition"/>
    </select>

    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.CollectionPeriodJoinRecordPageRes">
        select
        t.id
        , t.order_no `order`
        , t.user_id userId
        , t.status
        , t.join_time `time`
        from nft_collection_period_join_record t
        <include refid="where_condition"/>
        order by t.id desc
    </select>

    <select id="selectByConditionListFront" resultType="com.std.core.pojo.response.CollectionPeriodJoinRecordListRes">
        select
        t.id
        , t.order_no `order`
        , t.user_id userId
        , t.status
        , t.join_time `time`
        from nft_collection_period_join_record t
        <include refid="where_condition"/>
        order by t.id desc
        limit 10
    </select>

    <select id="selectMyRecordList" resultType="com.std.core.pojo.response.CollectionPeriodJoinMyRecordRes">
        select
        t.id
        , t.period_id periodId
        , t.user_id userId
        , t.status
        , t.pay_amount price
        , t.join_time `time`
        from nft_collection_period_join_record t
        <include refid="where_condition"/>
        order by t.id desc
    </select>
    <select id="detailNoDealJoinRecordByPeriod" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period_join_record t
        where t.status in ('1', '2')
          and t.is_deal = '1'
          and t.period_id = #{periodId} limit 1;
    </select>

    <select id="selectCount" resultType="java.lang.Integer">
        select
        count(1)
        from nft_collection_period_join_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
    </select>

    <select id="selectTotalCount" resultType="java.lang.Integer">
        select
        count(1)
        from nft_collection_period_join_record t
        <include refid="where_condition"/>
    </select>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.CollectionPeriodJoinRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_join_record t
        INNER JOIN nft_collection_period ta on t.period_id = ta.id
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

</mapper>