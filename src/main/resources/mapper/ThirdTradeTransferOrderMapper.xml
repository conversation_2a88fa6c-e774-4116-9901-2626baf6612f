<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ThirdTradeTransferOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ThirdTradeTransferOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="plat_type" jdbcType="VARCHAR" property="platType"/>
        <result column="tran_no" jdbcType="VARCHAR" property="tranNo"/>
        <result column="source_user_id" jdbcType="BIGINT" property="sourceUserId"/>
        <result column="source_wallet_hash" jdbcType="VARCHAR" property="sourceWalletHash"/>
        <result column="source_mobile" jdbcType="VARCHAR" property="sourceMobile"/>
        <result column="target_user_id" jdbcType="BIGINT" property="targetUserId"/>
        <result column="target_wallet_hash" jdbcType="VARCHAR" property="targetWalletHash"/>
        <result column="target_mobile" jdbcType="VARCHAR" property="targetMobile"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="callback_datetime" jdbcType="TIMESTAMP" property="callbackDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.plat_type
        , t.tran_no
        , t.source_user_id
        , t.source_wallet_hash
        , t.source_mobile
        , t.target_user_id
        , t.target_wallet_hash
        , t.target_mobile
        , t.status
        , t.create_datetime
        , t.callback_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="platType != null and platType != '' ">
                AND t.plat_type = #{platType, jdbcType=VARCHAR}
            </if>
            <if test="tranNo != null and tranNo != '' ">
                AND t.tran_no = #{tranNo, jdbcType=VARCHAR}
            </if>
            <if test="sourceUserId != null">
                AND t.source_user_id = #{sourceUserId, jdbcType=BIGINT}
            </if>
            <if test="sourceWalletHash != null and sourceWalletHash != '' ">
                AND t.source_wallet_hash = #{sourceWalletHash, jdbcType=VARCHAR}
            </if>
            <if test="sourceMobile != null and sourceMobile != '' ">
                AND t.source_mobile = #{sourceMobile, jdbcType=VARCHAR}
            </if>
            <if test="targetUserId != null">
                AND t.target_user_id = #{targetUserId, jdbcType=BIGINT}
            </if>
            <if test="targetWalletHash != null and targetWalletHash != '' ">
                AND t.target_wallet_hash = #{targetWalletHash, jdbcType=VARCHAR}
            </if>
            <if test="targetMobile != null and targetMobile != '' ">
                AND t.target_mobile = #{targetMobile, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="callbackDatetime != null">
                AND t.callback_datetime = #{callbackDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrder" useGeneratedKeys="true" keyProperty="id">
        insert into cs_third_trade_transfer_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="platType != null and platType != '' ">
                plat_type,
            </if>
            <if test="tranNo != null and tranNo != '' ">
                tran_no,
            </if>
            <if test="sourceUserId != null ">
                source_user_id,
            </if>
            <if test="sourceWalletHash != null and sourceWalletHash != '' ">
                source_wallet_hash,
            </if>
            <if test="sourceMobile != null and sourceMobile != '' ">
                source_mobile,
            </if>
            <if test="targetUserId != null ">
                target_user_id,
            </if>
            <if test="targetWalletHash != null and targetWalletHash != '' ">
                target_wallet_hash,
            </if>
            <if test="targetMobile != null and targetMobile != '' ">
                target_mobile,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="callbackDatetime != null ">
                callback_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="platType != null and platType != '' ">
                #{platType,jdbcType=VARCHAR},
            </if>
            <if test="tranNo != null and tranNo != '' ">
                #{tranNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceUserId != null">
                #{sourceUserId,jdbcType=BIGINT},
            </if>
            <if test="sourceWalletHash != null and sourceWalletHash != '' ">
                #{sourceWalletHash,jdbcType=VARCHAR},
            </if>
            <if test="sourceMobile != null and sourceMobile != '' ">
                #{sourceMobile,jdbcType=VARCHAR},
            </if>
            <if test="targetUserId != null">
                #{targetUserId,jdbcType=BIGINT},
            </if>
            <if test="targetWalletHash != null and targetWalletHash != '' ">
                #{targetWalletHash,jdbcType=VARCHAR},
            </if>
            <if test="targetMobile != null and targetMobile != '' ">
                #{targetMobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackDatetime != null">
                #{callbackDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cs_third_trade_transfer_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrder">
        update cs_third_trade_transfer_order
        <set>
            <if test="platType != null and platType != '' ">
                plat_type = #{platType,jdbcType=VARCHAR},
            </if>
            <if test="tranNo != null and tranNo != '' ">
                tran_no = #{tranNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceUserId != null">
                source_user_id = #{sourceUserId,jdbcType=BIGINT},
            </if>
            <if test="sourceWalletHash != null and sourceWalletHash != '' ">
                source_wallet_hash = #{sourceWalletHash,jdbcType=VARCHAR},
            </if>
            <if test="sourceMobile != null and sourceMobile != '' ">
                source_mobile = #{sourceMobile,jdbcType=VARCHAR},
            </if>
            <if test="targetUserId != null">
                target_user_id = #{targetUserId,jdbcType=BIGINT},
            </if>
            <if test="targetWalletHash != null and targetWalletHash != '' ">
                target_wallet_hash = #{targetWalletHash,jdbcType=VARCHAR},
            </if>
            <if test="targetMobile != null and targetMobile != '' ">
                target_mobile = #{targetMobile,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="callbackDatetime != null">
                callback_datetime = #{callbackDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_third_trade_transfer_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_third_trade_transfer_order t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_third_trade_transfer_order t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>