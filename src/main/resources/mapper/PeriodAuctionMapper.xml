<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodAuctionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodAuction">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="bond" jdbcType="DECIMAL" property="bond"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="start_price" jdbcType="DECIMAL" property="startPrice"/>
        <result column="price_auction" jdbcType="DECIMAL" property="priceAuction"/>
        <result column="final_price" jdbcType="DECIMAL" property="finalPrice"/>
        <result column="last_user_id" jdbcType="BIGINT" property="lastUserId"/>
        <result column="current_price" jdbcType="DECIMAL" property="currentPrice"/>
        <result column="already_pay_amount" jdbcType="DECIMAL" property="alreadyPayAmount"/>
        <result column="auction_times" jdbcType="INTEGER" property="auctionTimes"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="time_limit" jdbcType="INTEGER" property="timeLimit"/>
        <result column="delayed_second" jdbcType="INTEGER" property="delayedSecond"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="delayed_time" jdbcType="TIMESTAMP" property="delayedTime"/>
        <result column="is_divides" jdbcType="VARCHAR" property="isDivides"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.period_id
        , t.bond
        , t.status
        , t.start_price
        , t.price_auction
        , t.final_price
        , t.last_user_id
        , t.current_price
        , t.already_pay_amount
        , t.auction_times
        , t.start_time
        , t.end_time
        , t.time_limit
        , t.delayed_second
        , t.create_time
        , t.update_time
        , t.delayed_time
        , t.is_divides
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name = #{name, jdbcType=VARCHAR}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="bond != null">
                AND t.bond = #{bond, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="startPrice != null">
                AND t.start_price = #{startPrice, jdbcType=DECIMAL}
            </if>
            <if test="priceAuction != null">
                AND t.price_auction = #{priceAuction, jdbcType=DECIMAL}
            </if>
            <if test="finalPrice != null">
                AND t.final_price = #{finalPrice, jdbcType=DECIMAL}
            </if>
            <if test="lastUserId != null">
                AND t.last_user_id = #{lastUserId, jdbcType=BIGINT}
            </if>
            <if test="currentPrice != null">
                AND t.current_price = #{currentPrice, jdbcType=DECIMAL}
            </if>
            <if test="alreadyPayAmount != null">
                AND t.already_pay_amount = #{alreadyPayAmount, jdbcType=DECIMAL}
            </if>
            <if test="auctionTimes != null">
                AND t.auction_times = #{auctionTimes, jdbcType=INTEGER}
            </if>
            <if test="startTime != null">
                AND t.start_time = #{startTime, jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null">
                AND t.end_time = #{endTime, jdbcType=TIMESTAMP}
            </if>
            <if test="timeLimit != null">
                AND t.time_limit = #{timeLimit, jdbcType=INTEGER}
            </if>
            <if test="delayedSecond != null">
                AND t.delayed_second = #{delayedSecond, jdbcType=INTEGER}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="delayedTime != null">
                AND t.delayed_time = #{delayedTime, jdbcType=TIMESTAMP}
            </if>
            <if test="isDivides != null and isDivides != '' ">
                AND t.is_divides = #{isDivides, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodAuction">
        insert into nft_period_auction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="bond != null ">
                bond,
            </if>
            <if test="status != null and status != '' ">
                status ,
            </if>
            <if test="startPrice != null ">
                start_price,
            </if>
            <if test="priceAuction != null ">
                price_auction,
            </if>
            <if test="finalPrice != null ">
                final_price,
            </if>
            <if test="lastUserId != null ">
                last_user_id,
            </if>
            <if test="currentPrice != null ">
                current_price,
            </if>
            <if test="alreadyPayAmount != null">
                already_pay_amount,
            </if>
            <if test="auctionTimes != null ">
                auction_times,
            </if>
            <if test="startTime != null ">
                start_time,
            </if>
            <if test="endTime != null ">
                end_time,
            </if>
            <if test="timeLimit != null ">
                time_limit,
            </if>
            <if test="delayedSecond != null ">
                delayed_second,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="delayedTime != null ">
                delayed_time,
            </if>
            <if test="isDivides != null and isDivides != '' ">
                is_divides,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != '' ">
                #{name, jdbcType=VARCHAR},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="bond != null">
                #{bond,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="startPrice != null">
                #{startPrice,jdbcType=DECIMAL},
            </if>
            <if test="priceAuction != null">
                #{priceAuction,jdbcType=DECIMAL},
            </if>
            <if test="finalPrice != null">
                #{finalPrice,jdbcType=DECIMAL},
            </if>
            <if test="lastUserId != null">
                #{lastUserId,jdbcType=BIGINT},
            </if>
            <if test="currentPrice != null">
                #{currentPrice,jdbcType=DECIMAL},
            </if>
            <if test="alreadyPayAmount != null">
                #{alreadyPayAmount, jdbcType=DECIMAL},
            </if>
            <if test="auctionTimes != null">
                #{auctionTimes,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeLimit != null">
                #{timeLimit,jdbcType=INTEGER},
            </if>
            <if test="delayedSecond != null">
                #{delayedSecond,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delayedTime != null">
                #{delayedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDivides != null and isDivides != '' ">
                #{isDivides, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_period_auction
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByPeriodId">
        delete
        from nft_period_auction
        where period_id = #{period_id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodAuction">
        update nft_period_auction
        <set>
            <if test="name != null and name != '' ">
                name = #{name, jdbcType=VARCHAR},
            </if>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="bond != null">
                bond = #{bond,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="startPrice != null">
                start_price = #{startPrice,jdbcType=DECIMAL},
            </if>
            <if test="priceAuction != null">
                price_auction = #{priceAuction,jdbcType=DECIMAL},
            </if>
            <if test="finalPrice != null">
                final_price = #{finalPrice,jdbcType=DECIMAL},
            </if>
            <if test="lastUserId != null">
                last_user_id = #{lastUserId,jdbcType=BIGINT},
            </if>
            <if test="currentPrice != null">
                current_price = #{currentPrice,jdbcType=DECIMAL},
            </if>
            <if test="alreadyPayAmount != null">
                already_pay_amount = #{alreadyPayAmount, jdbcType=DECIMAL},
            </if>
            <if test="auctionTimes != null">
                auction_times = #{auctionTimes,jdbcType=INTEGER},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="timeLimit != null">
                time_limit = #{timeLimit,jdbcType=INTEGER},
            </if>
            <if test="delayedSecond != null">
                delayed_second = #{delayedSecond,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="delayedTime != null">
                delayed_time = #{delayedTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isDivides != null and isDivides != '' ">
                is_divides = #{isDivides, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodAuction"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPeriod" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        where t.period_id = #{periodId,jdbcType=BIGINT}
    </select>
    <select id="detailTimeEnd" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        where t.status='0' and t.delayed_time <![CDATA[ <]]> #{date}

    </select>
    <select id="detailActionByEnd" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        where t.status='1' and DATE_ADD(t.delayed_time,INTERVAL #{time} SECOND) <![CDATA[<]]> #{date}
        and t.period_id in (select id from nft_collection_period where `status`='1' and `start_status`='2')
    </select>
    <select id="selectperiodAuctionDivides" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction t
        where t.is_divides = '1';
    </select>
</mapper>