<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MemberConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MemberConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="diamond_number" jdbcType="DECIMAL" property="diamondNumber"/>
        <result column="fans_number" jdbcType="INTEGER" property="fansNumber"/>
        <result column="real_status" jdbcType="INTEGER" property="realStatus"/>
        <result column="privilege" jdbcType="VARCHAR" property="privilege"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="creator_name" jdbcType="VARCHAR" property="creatorName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updater" jdbcType="VARCHAR" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.name
        , t.level
        , t.diamond_number
        , t.fans_number
        , t.real_status
        , t.privilege
        , t.creator
        , t.creator_name
        , t.create_time
        , t.updater
        , t.updater_name
        , t.update_time
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="level != null">
                AND t.level = #{level, jdbcType=INTEGER}
            </if>
            <if test="diamondNumber != null">
                AND t.diamond_number = #{diamondNumber, jdbcType=DECIMAL}
            </if>
            <if test="fansNumber != null">
                AND t.fans_number = #{fansNumber, jdbcType=INTEGER}
            </if>
            <if test="realStatus != null">
                AND t.real_status = #{realStatus, jdbcType=INTEGER}
            </if>
            <if test="privilege != null and privilege != '' ">
                AND t.privilege = #{privilege, jdbcType=VARCHAR}
            </if>
            <if test="creator != null and creator != '' ">
                AND t.creator = #{creator, jdbcType=VARCHAR}
            </if>
            <if test="creatorName != null and creatorName != '' ">
                AND t.creator_name = #{creatorName, jdbcType=VARCHAR}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null and updater != '' ">
                AND t.updater = #{updater, jdbcType=VARCHAR}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MemberConfig">
        insert into tsys_member_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="name != null and name != '' ">
                name,
              </if>
              <if test="level != null ">
                level,
              </if>
              <if test="diamondNumber != null ">
                diamond_number,
              </if>
              <if test="fansNumber != null ">
                fans_number,
              </if>
              <if test="realStatus != null ">
                real_status,
              </if>
              <if test="privilege != null and privilege != '' ">
                privilege,
              </if>
              <if test="creator != null and creator != '' ">
                creator,
              </if>
              <if test="creatorName != null and creatorName != '' ">
                creator_name,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
              <if test="updater != null and updater != '' ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateTime != null ">
                update_time,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                #{level,jdbcType=INTEGER},
            </if>
            <if test="diamondNumber != null">
                #{diamondNumber,jdbcType=DECIMAL},
            </if>
            <if test="fansNumber != null">
                #{fansNumber,jdbcType=INTEGER},
            </if>
            <if test="realStatus != null">
                #{realStatus,jdbcType=INTEGER},
            </if>
            <if test="privilege != null and privilege != '' ">
                #{privilege,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != '' ">
                #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tsys_member_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MemberConfig">
        update tsys_member_config
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="level != null">
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="diamondNumber != null">
                diamond_number = #{diamondNumber,jdbcType=DECIMAL},
            </if>
            <if test="fansNumber != null">
                fans_number = #{fansNumber,jdbcType=INTEGER},
            </if>
            <if test="realStatus != null">
                real_status = #{realStatus,jdbcType=INTEGER},
            </if>
            <if test="privilege != null and privilege != '' ">
                privilege = #{privilege,jdbcType=VARCHAR},
            </if>
            <if test="creator != null and creator != '' ">
                creator = #{creator,jdbcType=VARCHAR},
            </if>
            <if test="creatorName != null and creatorName != '' ">
                creator_name = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null and updater != '' ">
                updater = #{updater,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_member_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MemberConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_member_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>