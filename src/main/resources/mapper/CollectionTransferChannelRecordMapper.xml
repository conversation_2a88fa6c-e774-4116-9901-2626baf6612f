<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionTransferChannelRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionTransferChannelRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_hash" jdbcType="VARCHAR" property="collectionHash"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="channel_biz_code" jdbcType="VARCHAR" property="channelBizCode"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_id
        , t.collection_hash
        , t.channel_biz_code
        , t.status
        , t.content
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                AND t.collection_hash = #{collectionHash, jdbcType=VARCHAR}
            </if>
            <if test="channelBizCode != null and channelBizCode != '' ">
                AND t.channel_biz_code = #{channelBizCode, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionTransferChannelRecord">
        insert into nft_collection_transfer_channel_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                collection_hash,
            </if>
            <if test="channelBizCode != null and channelBizCode != '' ">
                channel_biz_code,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                #{collectionHash, jdbcType=VARCHAR},
            </if>
            <if test="channelBizCode != null and channelBizCode != '' ">
                #{channelBizCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content, jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_transfer_channel_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionTransferChannelRecord">
        update nft_collection_transfer_channel_record
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                collection_hash = #{collectionHash, jdbcType=VARCHAR},
            </if>
            <if test="channelBizCode != null and channelBizCode != '' ">
                channel_biz_code = #{channelBizCode,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content, jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_transfer_channel_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionTransferChannelRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_transfer_channel_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>