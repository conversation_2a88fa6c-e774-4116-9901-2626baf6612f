<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IntegralExchangeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.IntegralExchange">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.integral_price
        , t.quantity
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="integralPrice != null">
                AND t.integral_price = #{integralPrice, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.id in (
                SELECT
                exchangeId
                FROM
                ( SELECT ta.exchange_id exchangeId FROM mall_integral_exchange_detail ta INNER JOIN nft_collection tb ON ta.collection_id = tb.id WHERE tb.`name` like concat('%', #{collectionName, jdbcType=VARCHAR},'%'))a
                )
            </if>
            <if test="collectionId != null ">
                AND t.id in (
                SELECT
                exchangeId
                FROM
                ( SELECT ta.exchange_id exchangeId FROM mall_integral_exchange_detail ta INNER JOIN nft_collection tb ON ta.collection_id = tb.id WHERE tb.id = #{collectionId, jdbcType=BIGINT})a
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.IntegralExchange">
        insert into mall_integral_exchange
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="integralPrice != null ">
                integral_price,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="integralPrice != null">
                #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mall_integral_exchange
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.IntegralExchange">
        update mall_integral_exchange
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="integralPrice != null">
                integral_price = #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_integral_exchange t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.IntegralExchange"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_integral_exchange t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        select
            COALESCE(sum(t.integral_price),0)
        from mall_integral_exchange t
        where t.user_id=#{userId}
    </select>
</mapper>