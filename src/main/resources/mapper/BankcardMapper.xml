<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.BankcardMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Bankcard">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
      <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="bank_user_name" jdbcType="VARCHAR" property="bankUserName"/>
      <result column="channel_bank_id" jdbcType="BIGINT" property="channelBankId"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="subbranch" jdbcType="VARCHAR" property="subbranch"/>
        <result column="bankcard_number" jdbcType="VARCHAR" property="bankcardNumber"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
      <result column="default_flag" jdbcType="VARCHAR" property="defaultFlag"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
      <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.user_id
        , t.bank_user_name
        , t.channel_bank_id
        , t.bank_code
        , t.bank_name
        , t.subbranch
        , t.bankcard_number
        , t.status
        , t.default_flag
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
          <if test="userId != null ">
            AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
          <if test="channelBankId != null ">
            AND t.channel_bank_id = #{channelBankId, jdbcType=BIGINT}
          </if>
            <if test="bankUserName != null and bankUserName != '' ">
                AND t.bank_user_name = #{bankUserName, jdbcType=VARCHAR}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code = #{bankCode, jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name = #{bankName, jdbcType=VARCHAR}
            </if>
            <if test="subbranch != null and subbranch != '' ">
                AND t.subbranch = #{subbranch, jdbcType=VARCHAR}
            </if>
            <if test="bankcardNumber != null and bankcardNumber != '' ">
                AND t.bankcard_number = #{bankcardNumber, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
          <if test="defaultFlag != null and defaultFlag != '' ">
            AND t.default_flag = #{defaultFlag, jdbcType=VARCHAR}
          </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Bankcard">
        insert into tstd_bankcard
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null  ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
          <if test="userId != null">
                user_id,
              </if>
          <if test="channelBankId != null">
            channel_bank_id,
          </if>
              <if test="bankUserName != null and bankUserName != '' ">
                bank_user_name,
              </if>
              <if test="bankCode != null and bankCode != '' ">
                bank_code,
              </if>
              <if test="bankName != null and bankName != '' ">
                bank_name,
              </if>
              <if test="subbranch != null and subbranch != '' ">
                subbranch,
              </if>
              <if test="bankcardNumber != null and bankcardNumber != '' ">
                bankcard_number,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
          <if test="defaultFlag != null and defaultFlag != '' ">
            default_flag,
          </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
          <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null">
                update_datetime,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
          <if test="userId != null">
            #{userId,jdbcType=BIGINT},
            </if>
          <if test="channelBankId != null">
            #{channelBankId,jdbcType=BIGINT},
          </if>
            <if test="bankUserName != null and bankUserName != '' ">
                #{bankUserName,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="subbranch != null and subbranch != '' ">
                #{subbranch,jdbcType=VARCHAR},
            </if>
            <if test="bankcardNumber != null and bankcardNumber != '' ">
                #{bankcardNumber,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
          <if test="defaultFlag != null and defaultFlag != '' ">
            #{defaultFlag,jdbcType=VARCHAR},
          </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
          <if test="updater != null ">
            #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_bankcard
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Bankcard">
        update tstd_bankcard
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
          <if test="userId != null ">
            user_id = #{userId,jdbcType=BIGINT},
            </if>
          <if test="channelBankId != null ">
            channel_bank_id = #{channelBankId,jdbcType=BIGINT},
          </if>
            <if test="bankUserName != null and bankUserName != '' ">
                bank_user_name = #{bankUserName,jdbcType=VARCHAR},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="subbranch != null and subbranch != '' ">
                subbranch = #{subbranch,jdbcType=VARCHAR},
            </if>
            <if test="bankcardNumber != null and bankcardNumber != '' ">
                bankcard_number = #{bankcardNumber,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
          <if test="defaultFlag != null and defaultFlag != '' ">
            default_flag = #{defaultFlag,jdbcType=VARCHAR},
          </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
          <if test="updater != null ">
            updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

  <update id="updateByRef" parameterType="com.std.core.pojo.domain.Bankcard">
    update tstd_bankcard
    <set>
      <if test="defaultFlag != null and defaultFlag != '' ">
        default_flag = #{defaultFlag,jdbcType=VARCHAR},
      </if>
    </set>
    where 1 = 1
    <if test="type != null and type != '' ">
      AND type = #{type, jdbcType=VARCHAR}
    </if>
    <if test="userId != null ">
      AND user_id = #{userId, jdbcType=BIGINT}
    </if>
  </update>
    <!-- 查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bankcard t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Bankcard"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bankcard t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>