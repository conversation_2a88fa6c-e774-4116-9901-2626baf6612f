<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MetaTicketRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MetaTicketRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="right_id" jdbcType="BIGINT" property="rightId"/>
        <result column="ticket_type" jdbcType="VARCHAR" property="ticketType"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="failure_datetime" jdbcType="TIMESTAMP" property="failureDatetime"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.right_id
        , t.ticket_type
        , t.collection_detail_id
        , t.status
        , t.failure_datetime
        , t.creater
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="rightId != null">
                AND t.right_id = #{rightId, jdbcType=BIGINT}
            </if>
            <if test="ticketType != null and ticketType != '' ">
                AND t.ticket_type = #{ticketType, jdbcType=VARCHAR}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="failureDatetime != null">
                AND t.failure_datetime = #{failureDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MetaTicketRecord">
        insert into nft_meta_ticket_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="rightId != null ">
                right_id,
              </if>
              <if test="ticketType != null and ticketType != '' ">
                ticket_type,
              </if>
              <if test="collectionDetailId != null ">
                collection_detail_id,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="failureDatetime != null ">
                failure_datetime,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="rightId != null">
                #{rightId,jdbcType=BIGINT},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                #{ticketType,jdbcType=VARCHAR},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="failureDatetime != null">
                #{failureDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_meta_ticket_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MetaTicketRecord">
        update nft_meta_ticket_record
        <set>
            <if test="rightId != null">
                right_id = #{rightId,jdbcType=BIGINT},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type = #{ticketType,jdbcType=VARCHAR},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="failureDatetime != null">
                failure_datetime = #{failureDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateRecordTickTimeEnd">
        update nft_meta_ticket_record set `status`='0'
        where id in (select id from (select id from nft_meta_ticket_record where ticket_type='2' and `status`='1' and #{date} <![CDATA[ >=]]> failure_datetime )a)
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_meta_ticket_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MetaTicketRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_meta_ticket_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectNoFailureTicketByUser" parameterType="com.std.core.pojo.domain.MetaTicketRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_meta_ticket_record t
        inner join nft_collection_detail ta on t.collection_detail_id=ta.id
        where t.ticket_type='2' and t.status='1' and ta.owner_type='0' and ta.owner_id=#{userId}
    </select>
</mapper>