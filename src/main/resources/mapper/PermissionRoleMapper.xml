<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.PermissionRoleMapper">

    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PermissionRole">
        <result column="t_role_id" property="roleId" jdbcType="BIGINT"/>
        <result column="t_resource_id" property="resourceId" jdbcType="BIGINT"/>
        <result column="t_resource_type" property="resourceType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        distinct
    t.role_id as t_role_id,
    t.resource_id as t_resource_id,
    t.resource_type as t_resource_type
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="roleId != null and roleId != '' ">
                AND t.role_id = #{roleId,jdbcType=BIGINT}
            </if>
            <if test="resourceId != null and resourceId != '' ">
                AND t.resource_id = #{resourceId,jdbcType=BIGINT}
            </if>
            <if test="resourceType != null and resourceType != '' ">
                AND t.resource_type = #{resourceType,jdbcType=VARCHAR}
            </if>
            <if test="resourceTypeList != null and resourceTypeList.size() != 0 ">
                AND t.resource_type in
                <foreach item="item" index="index" collection="resourceTypeList" open="("
                        separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PermissionRole"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_permission_role t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCountByCondition" parameterType="com.std.core.pojo.domain.PermissionRole"
            resultType="java.lang.Long">
        select count(1) from tsys_permission_role t
        <include refid="where_condition"/>
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tsys_permission_role (role_id,resource_id,resource_type)
        values
        <foreach item="permissionRole" index="index" collection="list" separator=",">
            (#{permissionRole.roleId,jdbcType=BIGINT},
            #{permissionRole.resourceId,jdbcType=BIGINT},
            #{permissionRole.resourceType,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteByRole" parameterType="java.lang.Long">
        DELETE
        FROM tsys_permission_role
        WHERE role_id = #{roleId,jdbcType=BIGINT}
    </delete>

    <insert id="batchCreate" parameterType="com.std.core.pojo.domain.PermissionRole">

        <if test="menuIdList != null and menuIdList.size() != 0 ">
            WITH RECURSIVE t_menu_list AS (
            SELECT t1.*
            FROM tsys_menu AS t1 WHERE t1.ID in
            <foreach collection="menuIdList" index="index" item="item" open="(" separator=","
                    close=")">
                #{item}
            </foreach>
            UNION
            SELECT t2.*
            FROM tsys_menu AS t2 INNER JOIN t_menu_list AS t3 ON t2.ID = t3.parent_id
            ), t_menu AS (
            SELECT id, type AS resource_type FROM t_menu_list
            )
        </if>

        <if test="roleId != null and roleId != '' and menuIdList == null ">
            WITH t_menu AS (
            SELECT DISTINCT id, type AS resource_type FROM tsys_menu WHERE kind = (
            SELECT kind FROM tsys_role WHERE id = #{roleId,jdbcType=BIGINT}
            ) ORDER BY id
            )
        </if>

        , t_action AS (
        SELECT DISTINCT ma.action_id AS id, 'action' AS resource_type FROM tsys_menu_action ma INNER JOIN t_menu m ON ma.menu_id = m.id
        ), t_result AS (
        SELECT * FROM t_menu
        UNION
        SELECT * FROM t_action
        ) INSERT INTO tsys_permission_role(role_id,resource_id,resource_type) (
        SELECT #{roleId,jdbcType=BIGINT}, t.id, t.resource_type FROM t_result t ORDER BY resource_type,id
        )
    </insert>

    <delete id="deleteByCondition" parameterType="com.std.core.pojo.domain.PermissionRole">
        delete from tsys_permission_role
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="roleId != null and roleId != '' ">
                AND role_id = #{roleId,jdbcType=BIGINT}
            </if>
            <if test="resourceId != null and resourceId != '' ">
                AND resource_id = #{resourceId,jdbcType=BIGINT}
            </if>
            <if test="resourceType != null and resourceType != '' ">
                AND resource_type = #{resourceType,jdbcType=VARCHAR}
            </if>
            <if test="resourceTypeList != null and resourceTypeList.size() != 0 ">
                AND resource_type in
                <foreach item="item" index="index" collection="resourceTypeList" open="("
                        separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </delete>

</mapper>
