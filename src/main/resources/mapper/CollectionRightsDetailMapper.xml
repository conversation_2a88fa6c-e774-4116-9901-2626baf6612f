<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionRightsDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionRightsDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="ticket_type" jdbcType="VARCHAR" property="ticketType"/>
        <result column="create_type" jdbcType="VARCHAR" property="createType"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="number_flag" jdbcType="VARCHAR" property="numberFlag"/>
        <result column="total_number" jdbcType="INTEGER" property="totalNumber"/>
        <result column="remain_number" jdbcType="INTEGER" property="remainNumber"/>
        <result column="advance_mins" jdbcType="INTEGER" property="advanceMins"/>
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate"/>
        <result column="drop_number" jdbcType="INTEGER" property="dropNumber"/>

        <result column="collectionName" jdbcType="VARCHAR" property="collectionName"/>
        <result column="collectionStatus" jdbcType="VARCHAR" property="collectionStatus"/>

        <result column="coverFileUrl" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="companyId" jdbcType="BIGINT" property="companyId"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="send_flag" jdbcType="INTEGER" property="sendFlag"/>

        <result column="ticket_time" jdbcType="INTEGER" property="ticketTime"/>
        <result column="ticket_start_datetime" jdbcType="TIMESTAMP" property="ticketStartDatetime"/>
        <result column="ticket_end_datetime" jdbcType="TIMESTAMP" property="ticketEndDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_id
        , t.type
        , t.ticket_type
        , t.create_type
        , t.name
        , t.content
        , t.number_flag
        , t.total_number
        , t.remain_number
        , t.advance_mins
        , t.discount_rate
        , t.drop_number
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.order_no
        , t.send_flag
        , t.ticket_time
        , t.ticket_start_datetime
        , t.ticket_end_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="ticketType != null and ticketType != '' ">
                AND t.ticket_type = #{ticketType, jdbcType=VARCHAR}
            </if>
            <if test="createType != null and createType != '' ">
                AND t.create_type = #{createType, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="numberFlag != null and numberFlag != '' ">
                AND t.number_flag = #{numberFlag, jdbcType=VARCHAR}
            </if>
            <if test="totalNumber != null">
                AND t.total_number = #{totalNumber, jdbcType=INTEGER}
            </if>
            <if test="remainNumber != null">
                AND t.remain_number = #{remainNumber, jdbcType=INTEGER}
            </if>
            <if test="advanceMins != null">
                AND t.advance_mins = #{advanceMins, jdbcType=INTEGER}
            </if>
            <if test="discountRate != null">
                AND t.discount_rate = #{discountRate, jdbcType=DECIMAL}
            </if>
            <if test="dropNumber != null">
                AND t.drop_number = #{dropNumber, jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                AND t.send_flag = #{sendFlag, jdbcType=VARCHAR}
            </if>
            <if test="ticketTime != null">
                AND t.ticket_time = #{ticketTime, jdbcType=INTEGER}
            </if>
            <if test="ticketStartDatetime != null">
                AND t.ticket_start_datetime = #{ticketStartDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="ticketEndDatetime != null">
                AND t.ticket_end_datetime = #{ticketEndDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionRightsDetail" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_rights_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type,
            </if>
            <if test="createType != null and createType != '' ">
                create_type,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="numberFlag != null and numberFlag != '' ">
                number_flag,
            </if>
            <if test="totalNumber != null">
                total_number,
            </if>
            <if test="remainNumber != null">
                remain_number,
            </if>
            <if test="advanceMins != null">
                advance_mins,
            </if>
            <if test="discountRate != null">
                discount_rate,
            </if>
            <if test="dropNumber != null">
                drop_number,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                send_flag,
            </if>
            <if test="ticketTime != null ">
                ticket_time,
            </if>
            <if test="ticketStartDatetime != null ">
                ticket_start_datetime,
            </if>
            <if test="ticketEndDatetime != null ">
                ticket_end_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                #{ticketType, jdbcType=VARCHAR},
            </if>
            <if test="createType != null and createType != '' ">
                #{createType, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="numberFlag != null and numberFlag != '' ">
                #{numberFlag, jdbcType=VARCHAR},
            </if>
            <if test="totalNumber != null">
                #{totalNumber, jdbcType=INTEGER},
            </if>
            <if test="remainNumber != null">
                #{remainNumber, jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="discountRate != null">
                #{discountRate, jdbcType=DECIMAL},
            </if>
            <if test="dropNumber != null">
                #{dropNumber, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                #{sendFlag, jdbcType=VARCHAR},
            </if>
            <if test="ticketTime != null">
                #{ticketTime,jdbcType=INTEGER},
            </if>
            <if test="ticketStartDatetime != null">
                #{ticketStartDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="ticketEndDatetime != null">
                #{ticketEndDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_rights_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByCollectionId" parameterType="java.lang.Long">
        delete
        from nft_collection_rights_detail
        where collection_id = #{collectionId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByRecord">
        DELETE
        FROM
            nft_collection_rights_detail
        WHERE
                id IN ( SELECT right_id FROM nft_collection_right_record WHERE ref_id = #{refId} AND ref_type = #{type} )
          AND create_type = '1'
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionRightsDetail">
        update nft_collection_rights_detail
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type = #{ticketType, jdbcType=VARCHAR},
            </if>
            <if test="createType != null and createType != '' ">
                create_type = #{createType, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="numberFlag != null and numberFlag != '' ">
                number_flag = #{numberFlag, jdbcType=VARCHAR},
            </if>
            <if test="totalNumber != null">
                total_number = #{totalNumber, jdbcType=INTEGER},
            </if>
            <if test="remainNumber != null">
                remain_number = #{remainNumber, jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                advance_mins = #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate, jdbcType=DECIMAL},
            </if>
            <if test="dropNumber != null">
                drop_number = #{dropNumber, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                send_flag = #{sendFlag, jdbcType=VARCHAR},
            </if>
            <if test="ticketTime != null">
                ticket_time = #{ticketTime,jdbcType=INTEGER},
            </if>
            <if test="ticketStartDatetime != null">
                ticket_start_datetime = #{ticketStartDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="ticketEndDatetime != null">
                ticket_end_datetime = #{ticketEndDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateContent">
        update nft_collection_rights_detail
        <set>
            content = #{content,jdbcType=VARCHAR},
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_rights_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionRightsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_rights_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_rights_detail t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.CollectionRightsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name collectionName,ta.cover_file_url coverFileUrl,ta.author_id companyId,ta.status collectionStatus
        from nft_collection_rights_detail t
        inner join nft_collection ta on t.collection_id=ta.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND ta.author_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="ticketType != null and ticketType != '' ">
                AND t.ticket_type = #{ticketType, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name collectionName,ta.cover_file_url coverFileUrl,ta.author_id companyId
        from nft_collection_rights_detail t
        inner join nft_collection ta on t.collection_id=ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectHistoryDate" resultType="com.std.core.pojo.domain.CollectionRightsDetailHistory">
        select t
            .
            id
             , t.collection_id  collectionId
             , t.right_type     rightType
             , t.company
             , t.plate_category plateCategory
             , t.total_number   totalNumber
             , t.remain_number  remainNumber
             , t.advance_mins   advanceMins
             , t.discount_rate  discountRate
             , t.content
        from nft_collection_right_deal t
        where t.collection_id = #{collectionId}
    </select>
    <select id="selectHistoryDateCollectionId" resultType="java.lang.Long">
        select t.collection_id
        from nft_collection_right_deal t
        where t.collection_id is not null
        group by t.collection_id
    </select>
    <select id="selectMyMetaTicket" resultType="com.std.core.pojo.response.CollectionRightsMyTicketPageRes">
        SELECT
        a.id,
        a.collection_id collectionId,
        a.ticket_type type,
        a.remain_number remainNumber,
        a.ticket_time ticketTimeInt,
        a.ticket_start_datetime ticketStartDatetime,
        a.ticket_end_datetime ticketEndDatetime,
        b.category,
        b.`name` collectionName,
        b.cover_file_url collectionPic
        FROM
        (
        SELECT
        ta.id,
        t.collection_id,
        t.ticket_type,
        CASE

        WHEN t.ticket_type = '1'
        OR t.ticket_type = '2' THEN
        t.total_number - ( SELECT count( 1 ) FROM nft_meta_ticket_record a WHERE a.collection_detail_id = ta.id ) ELSE 0
        END remain_number,
        t.ticket_time,
        t.ticket_start_datetime,
        t.ticket_end_datetime,
        ta.update_time
        FROM
        nft_collection_rights_detail t
        INNER JOIN nft_collection_detail ta ON t.collection_id = ta.collection_id
        WHERE
        t.type='3'
        AND ta.owner_type = '0'
        AND ta.owner_id = #{ownerId}
        AND ta.status not in ('4','6','11','17')
        ) a
        INNER JOIN nft_collection b ON a.collection_id = b.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="failure != null and failure != '' ">
                a.ticket_type != '0'
                AND (
                (( a.ticket_type = '1' OR a.ticket_type = '2' ) AND a.remain_number <![CDATA[<]]> 1 )
                OR ( a.ticket_type = '3' AND #{date} <![CDATA[>]]> a.ticket_end_datetime )
                )
            </if>
            <if test="unFailure != null and unFailure != '' ">
                a.ticket_type = '0'
                OR (( a.ticket_type = '1' OR a.ticket_type = '2' ) AND a.remain_number <![CDATA[>=]]> 1 )
                OR ( a.ticket_type = '3' AND #{date} <![CDATA[<]]> a.ticket_end_datetime )
            </if>
        </trim>

        order by a.update_time desc
    </select>
    <select id="selectTypeTwoCanUseTicket" parameterType="com.std.core.pojo.domain.CollectionRightsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_rights_detail t
        where t.type='3' and t.ticket_type='3' and
        #{date} <![CDATA[>=]]> t.ticket_start_datetime and
        #{date} <![CDATA[<]]> t.ticket_end_datetime
    </select>
    <select id="selectTypeTwoCanUseTicketByUser" parameterType="com.std.core.pojo.domain.CollectionRightsDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_rights_detail t
        INNER JOIN nft_collection_detail ta ON t.collection_id=ta.collection_id
        where ta.owner_id=#{userId} AND t.type='3' and t.ticket_type='3' and
        #{date} <![CDATA[>=]]> t.ticket_start_datetime and
        #{date} <![CDATA[<]]> t.ticket_end_datetime
        group by t.collection_id
    </select>

</mapper>