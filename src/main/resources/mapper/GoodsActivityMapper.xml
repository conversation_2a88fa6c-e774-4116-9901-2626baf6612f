<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsActivityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsActivity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="exchange_function" jdbcType="VARCHAR" property="exchangeFunction"/>
        <result column="unlock_datetime" jdbcType="TIMESTAMP" property="unlockDatetime"/>
        <result column="close_datetime" jdbcType="TIMESTAMP" property="closeDatetime"/>
        <result column="actual_close_datetime" jdbcType="TIMESTAMP" property="actualCloseDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="actual_end_datetime" jdbcType="TIMESTAMP" property="actualEndDatetime"/>
        <result column="integral_double_start_datetime" jdbcType="TIMESTAMP" property="integralDoubleStartDatetime"/>
        <result column="integral_double_end_datetime" jdbcType="TIMESTAMP" property="integralDoubleEndDatetime"/>
        <result column="double_size" jdbcType="INTEGER" property="doubleSize"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.status
        , t.exchange_function
        , t.unlock_datetime
        , t.close_datetime
        , t.actual_close_datetime
        , t.end_datetime
        , t.actual_end_datetime
        , t.integral_double_start_datetime
        , t.integral_double_end_datetime
        , t.double_size
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="exchangeFunction != null and exchangeFunction != '' ">
                AND t.exchange_function = #{exchangeFunction, jdbcType=VARCHAR}
            </if>
            <if test="unlockDatetime != null">
                AND t.unlock_datetime = #{unlockDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="closeDatetime != null">
                AND t.close_datetime = #{closeDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="actualCloseDatetime != null">
                AND t.actual_close_datetime = #{actualCloseDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="actualEndDatetime != null">
                AND t.actual_end_datetime = #{actualEndDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="integralDoubleStartDatetime != null">
                AND t.integral_double_start_datetime = #{integralDoubleStartDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="integralDoubleEndDatetime != null">
                AND t.integral_double_end_datetime = #{integralDoubleEndDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="doubleSize != null">
                AND t.double_size = #{doubleSize, jdbcType=INTEGER}
            </if>

        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsActivity">
        insert into mall_goods_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="exchangeFunction != null and exchangeFunction != '' ">
                exchange_function,
              </if>
              <if test="unlockDatetime != null ">
                unlock_datetime,
              </if>
              <if test="closeDatetime != null ">
                close_datetime,
              </if>
              <if test="actualCloseDatetime != null ">
                actual_close_datetime,
              </if>
              <if test="endDatetime != null ">
                end_datetime,
              </if>
              <if test="actualEndDatetime != null ">
                actual_end_datetime,
              </if>
            <if test="integralDoubleStartDatetime != null">
                integral_double_start_datetime,
            </if>
            <if test="integralDoubleEndDatetime != null">
                integral_double_end_datetime,
            </if>
            <if test="doubleSize != null">
                double_size,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="exchangeFunction != null and exchangeFunction != '' ">
                #{exchangeFunction,jdbcType=VARCHAR},
            </if>
            <if test="unlockDatetime != null">
                #{unlockDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeDatetime != null">
                #{closeDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualCloseDatetime != null">
                #{actualCloseDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndDatetime != null">
                #{actualEndDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="integralDoubleStartDatetime != null">
                #{integralDoubleStartDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="integralDoubleEndDatetime != null">
                #{integralDoubleEndDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="doubleSize != null">
                #{doubleSize, jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mall_goods_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsActivity">
        update mall_goods_activity
        <set>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="exchangeFunction != null and exchangeFunction != '' ">
                exchange_function = #{exchangeFunction,jdbcType=VARCHAR},
            </if>
            <if test="unlockDatetime != null">
                unlock_datetime = #{unlockDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="closeDatetime != null">
                close_datetime = #{closeDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualCloseDatetime != null">
                actual_close_datetime = #{actualCloseDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualEndDatetime != null">
                actual_end_datetime = #{actualEndDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="integralDoubleStartDatetime != null">
                integral_double_start_datetime = #{integralDoubleStartDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="integralDoubleEndDatetime != null">
                integral_double_end_datetime = #{integralDoubleEndDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="doubleSize != null">
                double_size = #{doubleSize, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>