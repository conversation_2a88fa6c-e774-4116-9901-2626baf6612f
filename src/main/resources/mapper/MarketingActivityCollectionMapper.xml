<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.MarketingActivityCollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.MarketingActivityCollection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="collection_pic" jdbcType="VARCHAR" property="collectionPic"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.ref_type
        , t.ref_id
        , t.collection_id
        , t.collection_name
        , t.collection_pic
        , t.price
        , t.total_quantity
        , t.remain_quantity
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name = #{collectionName, jdbcType=VARCHAR}
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                AND t.collection_pic = #{collectionPic, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.MarketingActivityCollection">
        insert into tstd_marketing_activity_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name,
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                #{collectionPic,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertBrachSelective">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into tstd_marketing_activity_collection
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.refType != null and item.refType != '' ">
                    ref_type,
                </if>
                <if test="item.refId != null ">
                    ref_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    collection_name,
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    collection_pic,
                </if>
                <if test="item.price != null ">
                    price,
                </if>
                <if test="item.totalQuantity != null ">
                    total_quantity,
                </if>
                <if test="item.remainQuantity != null ">
                    remain_quantity,
                </if>
                <if test="item.orderNo != null ">
                    order_no,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.refType != null and item.refType != '' ">
                    #{item.refType,jdbcType=VARCHAR},
                </if>
                <if test="item.refId != null">
                    #{item.refId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    #{item.collectionName,jdbcType=VARCHAR},
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    #{item.collectionPic,jdbcType=VARCHAR},
                </if>
                <if test="item.price != null">
                    #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.totalQuantity != null">
                    #{item.totalQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.remainQuantity != null">
                    #{item.remainQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.orderNo != null">
                    #{item.orderNo,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_marketing_activity_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByRefType">
        delete
        from tstd_marketing_activity_collection
        where ref_type = #{refType}
          and ref_id = #{refId}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.MarketingActivityCollection">
        update tstd_marketing_activity_collection
        <set>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic = #{collectionPic,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateRemainQuantity">
        update tstd_marketing_activity_collection
        set remain_quantity=remain_quantity - 1
        where id = #{id}
          and remain_quantity <![CDATA[ >]]> 0
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_marketing_activity_collection t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.MarketingActivityCollection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_marketing_activity_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectAwardEntity" resultType="com.std.core.pojo.domain.AwardEntity">
        select id id, remain_quantity weight
        from tstd_marketing_activity_collection
        where ref_type = #{refType}
          and ref_id = #{refId}
          and remain_quantity <![CDATA[ >]]> 0
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_marketing_activity_collection t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>