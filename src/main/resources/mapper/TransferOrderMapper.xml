<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.TransferOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.TransferOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="biz_id" jdbcType="BIGINT" property="bizId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="channel_user_id" jdbcType="BIGINT" property="channelUserId"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="channelName" jdbcType="VARCHAR" property="channelName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.biz_id
        , t.user_id
        , t.channel_user_id
        , t.channel_code
        , t.mobile
        , t.type
        , t.price
        , t.pay_amount
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.commission_amount
        , t.create_datetime
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bizId != null">
                AND t.biz_id = #{bizId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="channelUserId != null">
                AND t.channel_user_id = #{channelUserId, jdbcType=BIGINT}
            </if>
            <if test="channelCode != null and channelCode != '' ">
                AND t.channel_code = #{channelCode, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetimeStart != null">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.TransferOrder" useGeneratedKeys="true" keyProperty="id">
        insert into cs_transfer_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
            <if test="bizId != null">
                biz_id,
            </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="channelUserId != null ">
                channel_user_id,
              </if>
              <if test="channelCode != null and channelCode != '' ">
                channel_code,
              </if>
            <if test="mobile != null and mobile != '' ">
                mobile,
            </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="payAmount != null ">
                pay_amount,
              </if>
              <if test="payType != null and payType != '' ">
                pay_type,
              </if>
              <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
              </if>
              <if test="payStatus != null and payStatus != '' ">
                pay_status,
              </if>
              <if test="payDatetime != null ">
                pay_datetime,
              </if>
              <if test="payBalanceAmount != null ">
                pay_balance_amount,
              </if>
              <if test="payCashAmount != null ">
                pay_cash_amount,
              </if>
              <if test="commissionAmount != null ">
                commission_amount,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bizId != null">
                #{bizId, jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelUserId != null">
                #{channelUserId,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null and channelCode != '' ">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile, jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from cs_transfer_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.TransferOrder">
        update cs_transfer_order
        <set>
            <if test="bizId != null">
                biz_id = #{bizId, jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelUserId != null">
                channel_user_id = #{channelUserId,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null and channelCode != '' ">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile, jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_transfer_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.TransferOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.channel_name channelName
        from cs_transfer_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN cs_channel_system ta on t.channel_code = ta.biz_code
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" resultType="com.std.core.pojo.response.TransferOrderRes">
        select
        tb.id
        , t.user_id userId
        , t.channel_user_id channelUserId
        , t.channel_code channelCode
        , t.mobile
        , t.type
        , t.pay_type payType
        , t.pay_order_code payOrderCode
        , t.pay_status payStatus
        , t.pay_datetime payDatetime
        , t.create_datetime createDatetime
        , tb.collection_id collectionId
        , tb.collection_detail_id collectionDetailId
        , tb.price
        , tc.name collectionName
        from cs_transfer_order_detail tb
        INNER JOIN cs_transfer_order t ON tb.order_id = t.id
        INNER JOIN nft_collection tc ON tc.id = tb.collection_id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="channelCode != null and channelCode != '' ">
                AND t.channel_code = #{channelCode, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile like concat('%',#{mobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code like concat('%',#{payOrderCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null ">
                AND tb.collection_id = #{collectionId}
            </if>
            <if test="collectionDetailId != null ">
                AND tb.collection_detail_id = #{collectionDetailId}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND tc.name like concat('%',#{collectionName, jdbcType=VARCHAR},'%')
            </if>
        </trim>
    </select>
    <select id="selectTransferNotFinish" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            cs_transfer_order t
                INNER JOIN cs_transfer_order_detail ta ON t.id = ta.order_id
        WHERE
            t.user_id = #{userId}
          AND ta.collection_id=#{collectionId}
          AND t.pay_status='3'
    </select>

</mapper>