<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodAuctionDelayedRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodAuctionDelayedRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="periodName" jdbcType="VARCHAR" property="periodName"/>

    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.period_id
        , t.user_id
        , t.create_datetime
        , t.end_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodAuctionDelayedRecord">
        insert into nft_period_auction_delayed_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="periodId != null ">
                period_id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="endDatetime != null ">
                end_datetime,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_period_auction_delayed_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodAuctionDelayedRecord">
        update nft_period_auction_delayed_record
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_auction_delayed_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodAuctionDelayedRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name periodName
        from nft_period_auction_delayed_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_period_auction ta on t.period_id = ta.period_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>