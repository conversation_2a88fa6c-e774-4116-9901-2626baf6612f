<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="user_type" jdbcType="VARCHAR" property="userType"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="owner_type" jdbcType="VARCHAR" property="ownerType"/>
        <result column="owner_id" jdbcType="BIGINT" property="ownerId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="trade_type" jdbcType="DECIMAL" property="tradeType"/>
        <result column="trade_id" jdbcType="BIGINT" property="tradeId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="start_datetime" jdbcType="TIMESTAMP" property="startDatetime"/>
        <result column="sell_datetime" jdbcType="TIMESTAMP" property="sellDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_detail_id
        , t.user_type
        , t.user_id
        , t.owner_type
        , t.owner_id
        , t.price
        , t.trade_type
        , t.trade_id
        , t.status
        , t.create_datetime
        , t.start_datetime
        , t.sell_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="userType != null and userType != '' ">
                AND t.user_type = #{userType, jdbcType=VARCHAR}
            </if>
            <if test="ownerType != null and ownerType !='' ">
                AND t.owner_type = #{ownerType, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="ownerId != null">
                AND t.owner_id = #{ownerId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="tradeType != null and tradeType != '' ">
                AND t.trade_type = #{tradeType, jdbcType=VARCHAR}
            </if>
            <if test="tradeId != null and tradeId != '' ">
                AND t.trade_id = #{tradeId, jdbcType=BIGINT}
            </if>

            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <sql id="where_extend_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="ownerId != null">
                AND t.owner_id = #{ownerId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="tradeType != null and tradeType != '' ">
                AND t.trade_type = #{tradeType, jdbcType=VARCHAR}
            </if>
            <if test="tradeId != null and tradeId != '' ">
                AND t.trade_id = #{tradeId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>


    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_detail_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="userType != null and userType != '' ">
                user_type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="ownerType != null and ownerType !='' ">
                owner_type,
            </if>
            <if test="ownerId != null ">
                owner_id,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="tradeType != null and tradeType != '' ">
                trade_type,
            </if>
            <if test="tradeId != null and tradeId != '' ">
                trade_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="startDatetime != null ">
                start_datetime,
            </if>
            <if test="sellDatetime != null ">
                sell_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userType != null and userType != '' ">
                #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null and ownerType !='' ">
                #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="tradeType != null and tradeType != '' ">
                #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="tradeId != null and tradeId != '' ">
                #{tradeId, jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="startDatetime != null ">
                #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="sellDatetime != null ">
                #{sellDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_detail_record
        (
        collection_detail_id
        , user_type
        , user_id
        , owner_type
        , owner_id
        , price
        , create_datetime
        , trade_type
        , trade_id
        , status)
        values
        <foreach item="collectionDetail" index="index" collection="list" separator=",">
            (
            #{collectionDetail.collectionDetailId,jdbcType=BIGINT},
            #{collectionDetail.userType, jdbcType=VARCHAR},
            #{collectionDetail.userId,jdbcType=BIGINT},
            #{collectionDetail.ownerType, jdbcType=VARCHAR},
            #{collectionDetail.ownerId,jdbcType=BIGINT},
            #{collectionDetail.price,jdbcType=DECIMAL},
            #{collectionDetail.createDatetime,jdbcType=TIMESTAMP},
            #{collectionDetail.tradeType,jdbcType=VARCHAR},
            #{collectionDetail.tradeId,jdbcType=BIGINT},
            #{collectionDetail.status,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailRecord">
        update nft_collection_detail_record
        <set>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userType != null and userType != '' ">
                user_type = #{userType, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="ownerType != null and ownerType !='' ">
                owner_type = #{ownerType, jdbcType=VARCHAR},
            </if>
            <if test="ownerId != null">
                owner_id = #{ownerId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="tradeType != null and tradeType != '' ">
                trade_type = #{tradeType,jdbcType=VARCHAR},
            </if>
            <if test="tradeId != null and tradeId != '' ">
                trade_id = #{tradeId, jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <update id="updateByCollectionDetailId" parameterType="com.std.core.pojo.domain.CollectionDetailRecord">
        update nft_collection_detail_record
        set status = #{status,jdbcType=VARCHAR}
        where collection_detail_id = #{collectionDetailId,jdbcType=BIGINT}
          and status = '1'
    </update>
    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>


    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByConditionList" parameterType="com.std.core.pojo.domain.CollectionDetailRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `nft_collection_detail_record` t where `collection_detail_id` in (
        select id from `nft_collection_detail` where `collection_id` in (
        select id from nft_collection where name in ('应龙','玄蛇','光之晨曦猎魔团','敦煌反弹琵琶飞天')))
        and `create_datetime` <![CDATA[ >= ]]> #{createDatetimeStart}
        and `create_datetime`<![CDATA[ <= ]]>  #{createDatetimeEnd}
        order by id asc
    </select>

    <!-- 删除组合条件查询 -->
    <delete id="deleteByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailRecord">
        DELETE FROM nft_collection_detail_record
        <include refid="where_extend_condition"/>
    </delete>

    <select id="countByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailRecord"
            resultType="java.lang.Long">
        select
        count(1)
        from nft_collection_detail_record t
        <include refid="where_condition"/>
    </select>
    <select id="pageDropRecord" resultType="com.std.core.pojo.response.CollectionDropRecordPageRes">
        select t.id
        ,t.collection_detail_id collectionDetailId
        ,t.user_id userId
        ,tb.name collectionName
        ,tb.category
        ,tb.cover_file_url coverFileUrl
        ,t.create_datetime createDatetime
        from nft_collection_detail_record t
        inner join nft_collection_detail ta on t.collection_detail_id=ta.id
        inner join nft_collection tb on ta.collection_id=tb.id
        where ta.collection_id=#{collectionId}
        and t.trade_type='5'
        <if test="userId != null">
            AND t.user_id = #{userId, jdbcType=BIGINT}
        </if>
        <if test="category != null and category !='' ">
            AND tb.category = #{category, jdbcType=BIGINT}
        </if>
        order by t.id desc
    </select>
</mapper>