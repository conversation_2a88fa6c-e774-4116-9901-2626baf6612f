<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoTaskConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoTaskConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="online_duration" jdbcType="INTEGER" property="onlineDuration"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.status
        , t.online_duration
        , t.yin_yao
        , t.yang_yao
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="onlineDuration != null and onlineDuration != '' ">
                AND t.online_duration = #{onlineDuration, jdbcType=VARCHAR}
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoTaskConfig">
        insert into es_yao_task_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="onlineDuration != null and onlineDuration != '' ">
                online_duration,
            </if>
            <if test="yinYao != null ">
                yin_yao,
            </if>
            <if test="yangYao != null ">
                yang_yao,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="onlineDuration != null and onlineDuration != '' ">
                #{onlineDuration, jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from es_yao_task_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoTaskConfig">
        update es_yao_task_config
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="onlineDuration != null and onlineDuration != '' ">
                online_duration = #{onlineDuration, jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_task_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoTaskConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_task_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectType"
            parameterType="java.lang.String"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_task_config t
        where t.type = #{type, jdbcType=VARCHAR}
    </select>
</mapper>