<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CompanyEntityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CompanyEntity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="send_quantity" jdbcType="INTEGER" property="sendQuantity"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="image_url" jdbcType="VARCHAR" property="imageUrl"/>
        <result column="companyName" jdbcType="VARCHAR" property="companyName"/>
        <result column="companyLogo" jdbcType="VARCHAR" property="companyLogo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="auditr" jdbcType="BIGINT" property="auditr"/>
        <result column="auditr_name" jdbcType="VARCHAR" property="auditrName"/>
        <result column="audit_datetime" jdbcType="TIMESTAMP" property="auditDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.company_id
        , t.status
        , t.send_quantity
        , t.name
        , t.content
        , t.image_url
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.auditr
        , t.auditr_name
        , t.audit_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="sendQuantity != null">
                AND t.send_quantity = #{sendQuantity, jdbcType=INTEGER}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="imageUrl != null and imageUrl != '' ">
                AND t.image_url = #{imageUrl, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="auditr != null ">
                AND t.auditr = #{auditr, jdbcType=BIGINT}
            </if>
            <if test="auditrName != null and auditrName != '' ">
                AND t.auditr_name = #{auditrName, jdbcType=VARCHAR}
            </if>
            <if test="auditDatetime != null">
                AND t.audit_datetime = #{auditDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CompanyEntity">
        insert into nft_company_entity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="sendQuantity != null ">
                send_quantity,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="imageUrl != null and imageUrl != '' ">
                image_url,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="auditr != null ">
                auditr,
            </if>
            <if test="auditrName != null and auditrName != '' ">
                auditr_name,
            </if>
            <if test="auditDatetime != null ">
                audit_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="sendQuantity != null">
                #{sendQuantity,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="imageUrl != null and imageUrl != '' ">
                #{imageUrl,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditr != null ">
                #{auditr,jdbcType=BIGINT},
            </if>
            <if test="auditrName != null and auditrName != '' ">
                #{auditrName,jdbcType=VARCHAR},
            </if>
            <if test="auditDatetime != null">
                #{auditDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_company_entity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CompanyEntity">
        update nft_company_entity
        <set>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="sendQuantity != null">
                send_quantity = #{sendQuantity,jdbcType=INTEGER},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="imageUrl != null and imageUrl != '' ">
                image_url = #{imageUrl,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditr != null">
                auditr = #{auditr,jdbcType=BIGINT},
            </if>
            <if test="auditrName != null and auditrName != '' ">
                auditr_name = #{auditrName,jdbcType=VARCHAR},
            </if>
            <if test="auditDatetime != null">
                audit_datetime = #{auditDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company_entity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CompanyEntity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company_entity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company_entity t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByPrimaryKeyFront" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name companyName ,ta.logo companyLogo
        from nft_company_entity t
        left join nft_company ta on t.company_id=ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
</mapper>