<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishSailRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishSailRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_boat_id" jdbcType="BIGINT" property="userBoatId"/>
        <result column="boat_id" jdbcType="BIGINT" property="boatId"/>
        <result column="boat_type" jdbcType="VARCHAR" property="boatType"/>
        <result column="region_type" jdbcType="VARCHAR" property="regionType"/>
        <result column="speed" jdbcType="DECIMAL" property="speed"/>
        <result column="endurance_time" jdbcType="INTEGER" property="enduranceTime"/>
        <result column="endurance_mileage" jdbcType="DECIMAL" property="enduranceMileage"/>
        <result column="fishing_time" jdbcType="INTEGER" property="fishingTime"/>
        <result column="deadweight" jdbcType="DECIMAL" property="deadweight"/>
        <result column="deadweight_once" jdbcType="DECIMAL" property="deadweightOnce"/>
        <result column="load_factor" jdbcType="DECIMAL" property="loadFactor"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sail_datetime" jdbcType="TIMESTAMP" property="sailDatetime"/>
        <result column="next_fishing_datetime" jdbcType="TIMESTAMP" property="nextFishingDatetime"/>
        <result column="back_datetime" jdbcType="TIMESTAMP" property="backDatetime"/>
        <result column="actual_back_datetime" jdbcType="TIMESTAMP" property="actualBackDatetime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.user_boat_id
        , t.boat_id
        , t.boat_type
        , t.region_type
        , t.speed
        , t.endurance_time
        , t.endurance_mileage
        , t.fishing_time
        , t.deadweight
        , t.deadweight_once
        , t.load_factor
        , t.order_no
        , t.status
        , t.sail_datetime
        , t.next_fishing_datetime
        , t.back_datetime
        , t.actual_back_datetime
        , t.create_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="userBoatId != null">
                AND t.user_boat_id = #{userBoatId, jdbcType=BIGINT}
            </if>
            <if test="boatId != null">
                AND t.boat_id = #{boatId, jdbcType=BIGINT}
            </if>
            <if test="boatType != null and boatType != '' ">
                AND t.boat_type = #{boatType, jdbcType=VARCHAR}
            </if>
            <if test="regionType != null and regionType != '' ">
                AND t.region_type = #{regionType, jdbcType=VARCHAR}
            </if>
            <if test="speed != null">
                AND t.speed = #{speed, jdbcType=DECIMAL}
            </if>
            <if test="enduranceTime != null">
                AND t.endurance_time = #{enduranceTime, jdbcType=INTEGER}
            </if>
            <if test="enduranceMileage != null">
                AND t.endurance_mileage = #{enduranceMileage, jdbcType=DECIMAL}
            </if>
            <if test="fishingTime != null">
                AND t.fishing_time = #{fishingTime, jdbcType=INTEGER}
            </if>
            <if test="deadweight != null">
                AND t.deadweight = #{deadweight, jdbcType=DECIMAL}
            </if>
            <if test="deadweightOnce != null">
                AND t.deadweight_once = #{deadweightOnce, jdbcType=DECIMAL}
            </if>
            <if test="loadFactor != null">
                AND t.load_factor = #{loadFactor, jdbcType=DECIMAL}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="sailDatetime != null">
                AND t.sail_datetime = #{sailDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="nextFishingDatetime != null">
                AND t.next_fishing_datetime = #{nextFishingDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="backDatetime != null">
                AND t.back_datetime = #{backDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="actualBackDatetime != null">
                AND t.actual_back_datetime = #{actualBackDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
            <if test="createTimeMin != null">
                AND t.create_time <![CDATA[ >=]]> #{createTimeMin, jdbcType=BIGINT}
            </if>
            <if test="createTimeMax != null">
                AND t.create_time <![CDATA[ <=]]> #{createTimeMax, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishSailRecord">
        insert into yg_fish_sail_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null ">
                user_id,
            </if>
            <if test="userBoatId != null ">
                user_boat_id,
            </if>
            <if test="boatId != null">
                boat_id,
            </if>
            <if test="boatType != null and boatType != '' ">
                boat_type,
            </if>
            <if test="regionType != null and regionType != '' ">
                region_type,
            </if>
            <if test="speed != null ">
                speed,
            </if>
            <if test="enduranceTime != null ">
                endurance_time,
            </if>
            <if test="enduranceMileage != null ">
                endurance_mileage,
            </if>
            <if test="fishingTime != null ">
                fishing_time,
            </if>
            <if test="deadweight != null ">
                deadweight,
            </if>
            <if test="deadweightOnce != null ">
                deadweight_once,
            </if>
            <if test="loadFactor != null ">
                load_factor,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="sailDatetime != null ">
                sail_datetime,
            </if>
            <if test="nextFishingDatetime != null">
                next_fishing_datetime,
            </if>
            <if test="backDatetime != null ">
                back_datetime,
            </if>
            <if test="actualBackDatetime != null">
                actual_back_datetime,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="userBoatId != null">
                #{userBoatId,jdbcType=BIGINT},
            </if>
            <if test="boatId != null">
                #{boatId, jdbcType=BIGINT},
            </if>
            <if test="boatType != null and boatType != '' ">
                #{boatType,jdbcType=VARCHAR},
            </if>
            <if test="regionType != null and regionType != '' ">
                #{regionType,jdbcType=VARCHAR},
            </if>
            <if test="speed != null">
                #{speed,jdbcType=DECIMAL},
            </if>
            <if test="enduranceTime != null">
                #{enduranceTime,jdbcType=INTEGER},
            </if>
            <if test="enduranceMileage != null">
                #{enduranceMileage,jdbcType=DECIMAL},
            </if>
            <if test="fishingTime != null">
                #{fishingTime,jdbcType=INTEGER},
            </if>
            <if test="deadweight != null">
                #{deadweight,jdbcType=DECIMAL},
            </if>
            <if test="deadweightOnce != null">
                #{deadweightOnce,jdbcType=DECIMAL},
            </if>
            <if test="loadFactor != null">
                #{loadFactor,jdbcType=DECIMAL},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="sailDatetime != null">
                #{sailDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="nextFishingDatetime != null">
                #{nextFishingDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="backDatetime != null">
                #{backDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualBackDatetime != null">
                #{actualBackDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from yg_fish_sail_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishSailRecord">
        update yg_fish_sail_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="userBoatId != null">
                user_boat_id = #{userBoatId,jdbcType=BIGINT},
            </if>
            <if test="boatId != null">
                boat_id = #{boatId, jdbcType=BIGINT},
            </if>
            <if test="boatType != null and boatType != '' ">
                boat_type = #{boatType,jdbcType=VARCHAR},
            </if>
            <if test="regionType != null and regionType != '' ">
                region_type = #{regionType,jdbcType=VARCHAR},
            </if>
            <if test="speed != null">
                speed = #{speed,jdbcType=DECIMAL},
            </if>
            <if test="enduranceTime != null">
                endurance_time = #{enduranceTime,jdbcType=INTEGER},
            </if>
            <if test="enduranceMileage != null">
                endurance_mileage = #{enduranceMileage,jdbcType=DECIMAL},
            </if>
            <if test="fishingTime != null">
                fishing_time = #{fishingTime,jdbcType=INTEGER},
            </if>
            <if test="deadweight != null">
                deadweight = #{deadweight,jdbcType=DECIMAL},
            </if>
            <if test="deadweightOnce != null">
                deadweight_once = #{deadweightOnce,jdbcType=DECIMAL},
            </if>
            <if test="loadFactor != null">
                load_factor = #{loadFactor,jdbcType=DECIMAL},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="sailDatetime != null">
                sail_datetime = #{sailDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="nextFishingDatetime != null">
                next_fishing_datetime = #{nextFishingDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="backDatetime != null">
                back_datetime = #{backDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="actualBackDatetime != null">
                actual_back_datetime = #{actualBackDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishSailRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectNextFishing" parameterType="com.std.core.pojo.domain.FishSailRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record t
        where t.status in ('0','1') and t.next_fishing_datetime <![CDATA[ <= ]]> #{date}
    </select>

    <select id="selectFishingEnd" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record t
        where t.status in ('0','1') and t.back_datetime <![CDATA[ <= ]]> #{date}
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_sail_record t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select
        count(1)
        from yg_fish_sail_record t
        <include refid="where_condition"/>
    </select>

    <select id="selectSailJour" parameterType="java.lang.Long"
            resultType="com.std.core.pojo.response.FishSailJourRes">
        SELECT
            t.user_id userId
            ,SUM( td.fishing_quantity ) fishingQuantity
        FROM
            yg_fish_sail_record t
        INNER JOIN yg_fish_sail_record_detail td ON t.id = td.frequency_record_id
        WHERE
            td.variety_id = #{varietyId, jdbcType=BIGINT}
        GROUP BY
            t.user_id
    </select>

</mapper>