<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionPeriodRelationMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionPeriodRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.category
        , t.period_id
        , t.collection_id
        , t.order_no
        , t.price
        , t.total_quantity
        , t.remain_quantity
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="category != null and category != '' ">
                AND t.category = #{category, jdbcType=VARCHAR}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionPeriodRelation">
        insert into nft_collection_period_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="category != null and category != '' ">
                category,
            </if>
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="category != null and category != '' ">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null ">
                #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_period_relation
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByPeriod" parameterType="java.lang.Long">
        delete
        from nft_collection_period_relation
        where period_id = #{periodId,jdbcType=BIGINT}
    </delete>

    <update id="updateRemainQuantity" parameterType="com.std.core.pojo.domain.CollectionPeriodRelation">
        update nft_collection_period_relation
        SET remain_quantity = remain_quantity - #{buyQuantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
          AND remain_quantity <![CDATA[ >=]]> #{buyQuantity,jdbcType=INTEGER}
    </update>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionPeriodRelation">
        update nft_collection_period_relation
        <set>
            <if test="category != null and category != '' ">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateRemainPayOrderCancel">
        update nft_collection_period_relation
        set remain_quantity =remain_quantity + #{remainQuantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_relation t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriodRelation"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_relation t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="BaseCollectionResultMap" type="com.std.core.pojo.response.CollectionPeriodCollectionPageRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="author_id" jdbcType="BIGINT" property="authorId"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>

    </resultMap>

    <resultMap id="BaseCollectionResultRelationMap" type="com.std.core.pojo.response.CollectionPeriodCollectionPageResRelation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <id column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="author_id" jdbcType="BIGINT" property="authorId"/>
        <result column="author" jdbcType="VARCHAR" property="author"/>
        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
    </resultMap>

    <sql id="Base_CollectionColumn_List">
        t
        .
        id
        , tnc.category
        , t.name
        , t.level_type
        , t.file_type
        , t.cover_file_url
        , t.file_url
        , t.content_type
        , tnc.total_quantity
        , tnc.remain_quantity
        , tnc.order_no
        , t.buy_type
    </sql>

    <!-- 查询 -->
    <select id="selectListByPeriodId" parameterType="java.lang.Long"
            resultMap="BaseCollectionResultMap">
        select
        <include refid="Base_CollectionColumn_List"/>,t.author_id,nc.name author
        from nft_collection t
        inner join nft_collection_period_relation tnc on t.id = tnc.collection_id
        inner join nft_company nc on t.author_id = nc.id
        where period_id = #{periodId,jdbcType=BIGINT} order by tnc.order_no desc
        <if test="collectionNumber != null">
            limit #{collectionNumber}
        </if>
    </select>
    <select id="selectRelation" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_relation t
        where t.period_id = #{periodId,jdbcType=BIGINT} AND t.collection_id = #{collectionId, jdbcType=BIGINT} for update
    </select>
    <select id="getPeriodLockTime" resultType="java.lang.Integer">
        SELECT t.lock_time
        FROM nft_collection t
        where t.id = (SELECT collection_id FROM nft_collection_period_relation WHERE period_id = #{id} LIMIT 1)
    </select>
    <select id="getPeriodTransformLimitTime" resultType="java.lang.Integer">
        SELECT t.transform_limit_time
        FROM nft_collection t
        where t.id = (SELECT collection_id FROM nft_collection_period_relation WHERE period_id = #{id} LIMIT 1)
    </select>
    <select id="ListByPeriodId" resultType="com.std.core.pojo.domain.AwardEntity">
        select t.id
        , case when 0 = #{config} then t.total_quantity else t.remain_quantity end as weight
        from nft_collection_period_relation t
        where t.period_id = #{periodId}
        and t.remain_quantity <![CDATA[ >]]> 0
        <if test="idList != null and idList.size() != 0 ">
            AND t.collection_id not in
            <foreach item="item" index="index" collection="idList" open="(" separator=","
                    close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectListByPeriodIds" resultMap="BaseCollectionResultRelationMap">
        select
        <include refid="Base_CollectionColumn_List"/>,t.author_id,nc.name author, tnc.period_id, t.lock_time
        from nft_collection t
        inner join nft_collection_period_relation tnc on t.id = tnc.collection_id
        inner join nft_company nc on t.author_id = nc.id
        where period_id in
        <foreach collection="periodIds" item="periodId" separator="," open="(" close=")" index="index">
            #{periodId,jdbcType=BIGINT}
        </foreach>
    </select>

    <select id="selectSellCollectionCount" parameterType="java.lang.Long" resultType="java.lang.Integer">
        select count(id)
        from nft_collection_period tp
        where tp.status in ('1', '2')
          and tp.channel_id = '1'
          and ((tp.total_quantity <![CDATA[ = ]]> tp.remain_quantity and tp.start_status = 0)
            or (tp.total_quantity <![CDATA[ >= ]]> tp.remain_quantity and tp.start_status = 1)
            or (tp.total_quantity <![CDATA[ > ]]> tp.remain_quantity and tp.start_status = 2)
            )
          and tp.author_ids = #{companyId}
    </select>

    <select id="selectCollectionRelationListByPeriodId" parameterType="com.std.core.pojo.domain.CollectionPeriodRelation"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_relation t
        where t.period_id=#{periodId}
    </select>
    <select id="selectPeriodCollectionStatistics"
            resultType="com.std.core.pojo.domain.PeriodCollectionStatistics">

        SELECT
            t.id,
            t.total_quantity totalQuantity,
            t.total_quantity - t.remain_quantity saleQuantity,
            t.price,
            ta.id collectionId,
            ta.`name` collectionName,
            ta.produced_id producedId,
            ta.category,
            ta.plate_category plateCategory,
            ta.level_type levelType,
            ta.cover_file_url coverFileUrl,
            ta.author_id authorId,
            ta.divide_author_id divideAuthorId,
            ta.plat_divide_rate platDivideRate
        FROM
            nft_collection_period_relation t
                INNER JOIN nft_collection ta ON t.collection_id = ta.id
                INNER JOIN nft_collection_period tb ON t.period_id = tb.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
        <if test="collectionId != null">
            AND t.collection_id = #{collectionId, jdbcType=BIGINT}
        </if>
        <if test="category != null and category != '' ">
            AND tb.category = #{category, jdbcType=VARCHAR}
        </if>
        <if test="authorId != null">
            AND ta.author_id = #{authorId, jdbcType=BIGINT}
        </if>
        <if test="producedId != null">
            AND ta.produced_id = #{producedId, jdbcType=DECIMAL}
        </if>
        </trim>
        order by t.id desc
    </select>

</mapper>