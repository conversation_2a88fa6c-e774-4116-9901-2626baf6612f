<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company_type" jdbcType="VARCHAR" property="companyType"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>

        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="size" jdbcType="INTEGER" property="size"/>
        <result column="max_number" jdbcType="INTEGER" property="maxNumber"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="send_type" jdbcType="VARCHAR" property="sendType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sold_status" jdbcType="VARCHAR" property="soldStatus"/>

        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
        <result column="transform_limit_time" jdbcType="INTEGER" property="transformLimitTime"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="right_type" jdbcType="VARCHAR" property="rightType"/>
        <result column="right_content" jdbcType="VARCHAR" property="rightContent"/>

        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="collectionName" jdbcType="VARCHAR" property="collectionName"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="play_rule" jdbcType="VARCHAR" property="playRule"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.company_type
        , t.company_id
        , t.name
        , t.collection_id
        , t.period_id
        , t.pic
        , t.size
        , t.max_number
        , t.total_quantity
        , t.remain_quantity
        , t.type
        , t.send_type
        , t.status
        , t.sold_status

        , t.lock_time
        , t.transform_limit_time
        , t.content
        , t.right_type
        , t.right_content

        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.channel_id
        , t.play_rule
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="companyType != null and companyType != '' ">
                AND t.company_type = #{companyType, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="size != null">
                AND t.size = #{size, jdbcType=INTEGER}
            </if>
            <if test="maxNumber != null">
                AND t.max_number = #{maxNumber, jdbcType=INTEGER}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="sendType != null and sendType != '' ">
                AND t.send_type = #{sendType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                AND t.sold_status = #{soldStatus, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="lockTime != null">
                AND t.lock_time = #{lockTime, jdbcType=INTEGER}
            </if>
            <if test="transformLimitTime != null">
                AND t.transform_limit_time = #{transformLimitTime, jdbcType=INTEGER}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="rightType != null and rightType != '' ">
                AND t.right_type = #{rightType, jdbcType=VARCHAR}
            </if>
            <if test="rightContent != null and rightContent != '' ">
                AND t.right_content = #{rightContent, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="playRule != null and playRule != '' ">
                AND t.play_rule = #{playRule, jdbcType=VARCHAR}
            </if>
            <if test="channelIdList != null and channelIdList.size() != 0 ">
                AND t.channel_id in
                <foreach item="item" index="index" collection="channelIdList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivity" useGeneratedKeys="true"
            keyProperty="id">
        insert into lxa_invitation_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="companyType != null and companyType != '' ">
                company_type,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="periodId != null">
                period_id,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="size != null ">
                size,
            </if>
            <if test="maxNumber != null ">
                max_number,
            </if>
            <if test="totalQuantity != null">
                total_quantity,
            </if>
            <if test="remainQuantity != null">
                remain_quantity,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="sendType != null and sendType != '' ">
                send_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type,
            </if>
            <if test="rightContent != null and rightContent != '' ">
                right_content,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="playRule != null and playRule != '' ">
                play_rule,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyType != null and companyType != '' ">
                #{companyType, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                #{periodId, jdbcType=BIGINT},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                #{size,jdbcType=INTEGER},
            </if>
            <if test="maxNumber != null">
                #{maxNumber,jdbcType=INTEGER},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity, jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity, jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="sendType != null and sendType != '' ">
                #{sendType, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                #{soldStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="content != null and content != '' ">
                #{content, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                #{rightType, jdbcType=VARCHAR},
            </if>
            <if test="rightContent != null and rightContent != '' ">
                #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelId != null">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="playRule != null and playRule != '' ">
                #{playRule, jdbcType=VARCHAR},
            </if>
        </trim>

    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from lxa_invitation_activity
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivity">
        update lxa_invitation_activity
        <set>
            <if test="companyType != null and companyType != '' ">
                company_type = #{companyType, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="periodId != null">
                period_id = #{periodId, jdbcType=BIGINT},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="size != null">
                size = #{size,jdbcType=INTEGER},
            </if>
            <if test="maxNumber != null">
                max_number = #{maxNumber,jdbcType=INTEGER},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity, jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity, jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="sendType != null and sendType != '' ">
                send_type = #{sendType, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status = #{soldStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time = #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="content != null and content != '' ">
                content = #{content, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type = #{rightType, jdbcType=VARCHAR},
            </if>
            <if test="rightContent != null and rightContent != '' ">
                right_content = #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="playRule != null and playRule != '' ">
                play_rule = #{playRule, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="modifyStatus">
        update lxa_invitation_activity
        <set>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status = #{soldStatus, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity t
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.InvitationActivity"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectChannelMerchant" resultType="java.lang.Long">
        select channel_id from lxa_invitation_activity where company_id=#{companyId} group by channel_id
    </select>
</mapper>