<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.InvitationActivityBlindBoxCollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.InvitationActivityBlindBoxCollection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="collection_pic" jdbcType="VARCHAR" property="collectionPic"/>

        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.activity_id
        , t.collection_id
        , t.collection_name
        , t.collection_pic
        , t.price
        , t.total_quantity
        , t.remain_quantity
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name = #{collectionName, jdbcType=VARCHAR}
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                AND t.collection_pic = #{collectionPic, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.InvitationActivityBlindBoxCollection">
        insert into lxa_invitation_activity_blind_box_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name,
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic,
            </if>
              <if test="price != null ">
                price,
              </if>
              <if test="totalQuantity != null ">
                total_quantity,
              </if>
              <if test="remainQuantity != null ">
                remain_quantity,
              </if>
              <if test="orderNo != null ">
                order_no,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                #{collectionPic, jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">

        <foreach collection="list" index="index" item="item" separator=";">
            insert into lxa_invitation_activity_blind_box_collection
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.activityId != null ">
                    activity_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    collection_name,
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    collection_pic,
                </if>
                <if test="item.price != null ">
                    price,
                </if>
                <if test="item.totalQuantity != null ">
                    total_quantity,
                </if>
                <if test="item.remainQuantity != null ">
                    remain_quantity,
                </if>
                <if test="item.orderNo != null ">
                    order_no,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.activityId != null">
                    #{item.activityId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    #{item.collectionName, jdbcType=VARCHAR},
                </if>
                <if test="item.collectionPic != null and item.collectionPic != '' ">
                    #{item.collectionPic, jdbcType=VARCHAR},
                </if>
                <if test="item.price != null">
                    #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.totalQuantity != null">
                    #{item.totalQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.remainQuantity != null">
                    #{item.remainQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.orderNo != null">
                    #{item.orderNo,jdbcType=INTEGER},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from lxa_invitation_activity_blind_box_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByActivity">
        delete from lxa_invitation_activity_blind_box_collection
        where activity_id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.InvitationActivityBlindBoxCollection">
        update lxa_invitation_activity_blind_box_collection
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName, jdbcType=VARCHAR},
            </if>
            <if test="collectionPic != null and collectionPic != '' ">
                collection_pic = #{collectionPic, jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_blind_box_collection t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.InvitationActivityBlindBoxCollection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_blind_box_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectCollectionByActivityRandom" resultType="com.std.core.pojo.domain.AwardEntity">
        select t.id
        , case when 0 = #{config} then t.total_quantity else t.remain_quantity end as weight
        from lxa_invitation_activity_blind_box_collection t
        where t.activity_id = #{activityId}
        and t.remain_quantity <![CDATA[ >]]> 0
        <if test="idList != null and idList.size() != 0 ">
            AND t.collection_id not in
            <foreach item="item" index="index" collection="idList" open="(" separator=","
                     close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from lxa_invitation_activity_blind_box_collection t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>