<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.PeriodDiscountDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.PeriodDiscountDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate"/>
        <result column="discount_time" jdbcType="INTEGER" property="discountTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.ref_type
        , t.ref_id
        , t.collection_id
        , t.discount_rate
        , t.discount_time
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="discountRate != null">
                AND t.discount_rate = #{discountRate, jdbcType=DECIMAL}
            </if>
            <if test="discountTime != null">
                AND t.discount_time = #{discountTime, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.PeriodDiscountDetail">
        insert into nft_period_discount_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="discountRate != null ">
                discount_rate,
            </if>
            <if test="discountTime != null ">
                discount_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="discountRate != null">
                #{discountRate,jdbcType=DECIMAL},
            </if>
            <if test="discountTime != null">
                #{discountTime,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_period_discount_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByPeriodId" parameterType="java.lang.Long">
        delete
        from nft_period_discount_detail
        where ref_id = #{refId,jdbcType=BIGINT}
          and ref_type = '0'
    </delete>

    <!-- 删除 -->
    <delete id="deleteByBlindBox" parameterType="java.lang.Long">
        delete
        from nft_period_discount_detail
        where ref_id = #{refId,jdbcType=BIGINT}
          and ref_type = '1'
    </delete>


    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.PeriodDiscountDetail">
        update nft_period_discount_detail
        <set>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate,jdbcType=DECIMAL},
            </if>
            <if test="discountTime != null">
                discount_time = #{discountTime,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_discount_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.PeriodDiscountDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_discount_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByUserId" parameterType="com.std.core.pojo.domain.PeriodDiscountDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_period_discount_detail t
        where ref_type=#{refType} and ref_id=#{refId} and collection_id in
        (select collection_id from nft_collection_detail where owner_id=#{userId} and owner_type='0' and status not in ('4','6','11','13'))
        order by discount_rate asc limit 1
    </select>

    <select id="selectCollectionList" resultType="java.lang.Long">
        select collection_id
        from nft_period_discount_detail
        where ref_id in (select id from nft_collection_period where `status` = '1' and start_status = '1')
    </select>
</mapper>