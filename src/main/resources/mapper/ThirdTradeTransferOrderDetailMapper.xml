<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ThirdTradeTransferOrderDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="reduce_cost" jdbcType="DECIMAL" property="reduceCost"/>
        <result column="reback_plat" jdbcType="DECIMAL" property="rebackPlat"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.order_id
        , t.collection_id
        , t.collection_detail_id
        , t.price
        , t.reduce_cost
        , t.reback_plat
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderId != null">
                AND t.order_id = #{orderId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="reduceCost != null">
                AND t.reduce_cost = #{reduceCost, jdbcType=DECIMAL}
            </if>
            <if test="rebackPlat != null">
                AND t.reback_plat = #{rebackPlat, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail">
        insert into cs_third_trade_transfer_order_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null ">
                order_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="reduceCost != null ">
                reduce_cost,
            </if>
            <if test="rebackPlat != null ">
                reback_plat,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">
                #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="reduceCost != null">
                #{reduceCost,jdbcType=DECIMAL},
            </if>
            <if test="rebackPlat != null">
                #{rebackPlat,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <insert id="insertBatchSelective">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into cs_third_trade_transfer_order_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.orderId != null ">
                    order_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.price != null ">
                    price,
                </if>
                <if test="item.reduceCost != null ">
                    reduce_cost,
                </if>
                <if test="item.rebackPlat != null ">
                    reback_plat,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.orderId != null ">
                    #{item.orderId},
                </if>
                <if test="item.collectionId != null ">
                    #{item.collectionId},
                </if>
                <if test="item.collectionDetailId != null ">
                    #{item.collectionDetailId},
                </if>
                <if test="item.price != null ">
                    #{item.price},
                </if>
                <if test="item.reduceCost != null ">
                    #{item.reduceCost},
                </if>
                <if test="item.rebackPlat != null ">
                    #{item.rebackPlat},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from cs_third_trade_transfer_order_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail">
        update cs_third_trade_transfer_order_detail
        <set>
            <if test="orderId != null">
                order_id = #{orderId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="reduceCost != null">
                reduce_cost = #{reduceCost,jdbcType=DECIMAL},
            </if>
            <if test="rebackPlat != null">
                reback_plat = #{rebackPlat,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_third_trade_transfer_order_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_third_trade_transfer_order_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="RelationBaseResultMap" type="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_id" jdbcType="BIGINT" property="orderId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="reduce_cost" jdbcType="DECIMAL" property="reduceCost"/>
        <result column="reback_plat" jdbcType="DECIMAL" property="rebackPlat"/>

        <result column="tran_no" jdbcType="VARCHAR" property="tranNo"/>
        <result column="source_user_id" jdbcType="BIGINT" property="sourceUserId"/>
        <result column="source_wallet_hash" jdbcType="VARCHAR" property="sourceWalletHash"/>
        <result column="source_mobile" jdbcType="VARCHAR" property="sourceMobile"/>
        <result column="target_user_id" jdbcType="BIGINT" property="targetUserId"/>
        <result column="target_wallet_hash" jdbcType="VARCHAR" property="targetWalletHash"/>
        <result column="target_mobile" jdbcType="VARCHAR" property="targetMobile"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="callback_datetime" jdbcType="TIMESTAMP" property="callbackDatetime"/>
    </resultMap>

    <sql id="RelationBase_Column_List">
        td
        .
        id
        , td.order_id
        , td.collection_id
        , td.collection_detail_id
        , td.price
        , t.tran_no
        , t.source_user_id
        , t.source_wallet_hash
        , t.source_mobile
        , t.target_user_id
        , t.target_wallet_hash
        , t.target_mobile
        , t.status
        , t.create_datetime
        , t.callback_datetime
    </sql>

    <sql id="relation_where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND td.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="platType != null and platType != '' ">
                AND t.plat_type = #{platType, jdbcType=VARCHAR}
            </if>
            <if test="tranNo != null and tranNo != '' ">
                AND t.tran_no = #{tranNo, jdbcType=VARCHAR}
            </if>
            <if test="sourceUserId != null">
                AND t.source_user_id = #{sourceUserId, jdbcType=BIGINT}
            </if>
            <if test="sourceWalletHash != null and sourceWalletHash != '' ">
                AND t.source_wallet_hash = #{sourceWalletHash, jdbcType=VARCHAR}
            </if>
            <if test="sourceMobile != null and sourceMobile != '' ">
                AND t.source_mobile = #{sourceMobile, jdbcType=VARCHAR}
            </if>
            <if test="targetUserId != null">
                AND t.target_user_id = #{targetUserId, jdbcType=BIGINT}
            </if>
            <if test="targetWalletHash != null and targetWalletHash != '' ">
                AND t.target_wallet_hash = #{targetWalletHash, jdbcType=VARCHAR}
            </if>
            <if test="targetMobile != null and targetMobile != '' ">
                AND t.target_mobile = #{targetMobile, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="callbackDatetime != null">
                AND t.callback_datetime = #{callbackDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="collectionId != null">
                AND td.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND td.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 组合条件查询 -->
    <select id="selectByConditionRelationOrder" parameterType="com.std.core.pojo.domain.ThirdTradeTransferOrderDetail"
            resultMap="RelationBaseResultMap">
        select
        <include refid="RelationBase_Column_List"/>
        from cs_third_trade_transfer_order_detail td inner join cs_third_trade_transfer_order t on td.order_id = t.id
        <include refid="relation_where_condition"/>
        order by td.id desc
    </select>
</mapper>