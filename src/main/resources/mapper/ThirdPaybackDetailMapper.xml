<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ThirdPaybackDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ThirdPaybackDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo"/>
        <result column="pay_order_no" jdbcType="VARCHAR" property="payOrderNo"/>
        <result column="pay_third_no" jdbcType="VARCHAR" property="payThirdNo"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.order_type
        , t.order_id
        , t.company_id
        , t.merchant_no
        , t.pay_order_no
        , t.pay_third_no
        , t.amount
        , t.create_time
        , t.status
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="orderType != null and orderType != '' ">
                AND t.order_type = #{orderType, jdbcType=VARCHAR}
            </if>
            <if test="orderId != null and orderId != '' ">
                AND t.order_id = #{orderId, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                AND t.merchant_no = #{merchantNo, jdbcType=VARCHAR}
            </if>
            <if test="payOrderNo != null and payOrderNo != '' ">
                AND t.pay_order_no = #{payOrderNo, jdbcType=VARCHAR}
            </if>
            <if test="payThirdNo != null and payThirdNo != '' ">
                AND t.pay_third_no = #{payThirdNo, jdbcType=VARCHAR}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ThirdPaybackDetail">
        insert into tstd_third_payback_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="orderType != null and orderType != '' ">
                order_type,
            </if>
            <if test="orderId != null and orderId != '' ">
                order_id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no,
            </if>
            <if test="payOrderNo != null and payOrderNo != '' ">
                pay_order_no,
            </if>
            <if test="payThirdNo != null and payThirdNo != '' ">
                pay_third_no,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="orderType != null and orderType != '' ">
                #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null and orderId != '' ">
                #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null and payOrderNo != '' ">
                #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payThirdNo != null and payThirdNo != '' ">
                #{payThirdNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_third_payback_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ThirdPaybackDetail">
        update tstd_third_payback_detail
        <set>
            <if test="orderType != null and orderType != '' ">
                order_type = #{orderType,jdbcType=VARCHAR},
            </if>
            <if test="orderId != null and orderId != '' ">
                order_id = #{orderId,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId,jdbcType=BIGINT},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="payOrderNo != null and payOrderNo != '' ">
                pay_order_no = #{payOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="payThirdNo != null and payThirdNo != '' ">
                pay_third_no = #{payThirdNo,jdbcType=VARCHAR},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_third_payback_detail t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_third_payback_detail t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ThirdPaybackDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_third_payback_detail t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>