<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.User">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="kind" jdbcType="VARCHAR" property="kind"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="meta_role_id" jdbcType="BIGINT" property="metaRoleId"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="identify_style" jdbcType="VARCHAR" property="identifyStyle"/>
        <result column="id_kind" jdbcType="VARCHAR" property="idKind"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="login_pwd_strength" jdbcType="VARCHAR" property="loginPwdStrength"/>
        <result column="trade_pwd_strength" jdbcType="VARCHAR" property="tradePwdStrength"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="recovery_status" jdbcType="VARCHAR" property="recoveryStatus"/>
        <result column="identify_status" jdbcType="VARCHAR" property="identifyStatus"/>
        <result column="grouping" jdbcType="VARCHAR" property="grouping"/>
        <result column="register_datetime" jdbcType="TIMESTAMP" property="registerDatetime"/>
        <result column="register_ip" jdbcType="VARCHAR" property="registerIp"/>
        <result column="invite_no" jdbcType="BIGINT" property="inviteNo"/>
        <result column="user_referee" jdbcType="BIGINT" property="userReferee"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="last_login_datetime" jdbcType="TIMESTAMP" property="lastLoginDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="refer_user_count" jdbcType="BIGINT" property="referUserCount"/>
        <result column="author_rate" jdbcType="DECIMAL" property="authorRate"/>
        <result column="black_times" jdbcType="INTEGER" property="blackTimes"/>
        <result column="channel_flag" jdbcType="VARCHAR" property="channelFlag"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="is_channel" jdbcType="VARCHAR" property="isChannel"/>
        <result column="google_secret" jdbcType="VARCHAR" property="googleSecret"/>
        <result column="channel_merchant_id" jdbcType="BIGINT" property="channelMerchantId"/>
        <result column="channel_uid" jdbcType="BIGINT" property="channelUid"/>
        <result column="loginStatus" jdbcType="VARCHAR" property="loginStatus"/>
        <result column="block_address" jdbcType="VARCHAR" property="blockAddress"/>
        <result column="yao_flag" jdbcType="VARCHAR" property="yaoFlag"/>
        <result column="series_no" jdbcType="VARCHAR" property="seriesNo"/>

    </resultMap>

    <resultMap id="UserResultMap" type="com.std.core.pojo.response.UserDetailRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="credit_score" jdbcType="DECIMAL" property="creditScore"/>
        <result column="available_amount" jdbcType="DECIMAL" property="availableAmount"/>
        <result column="frozen_amount" jdbcType="DECIMAL" property="frozenAmount"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="block_address" jdbcType="VARCHAR" property="blockAddress"/>
    </resultMap>

    <resultMap id="UserTeamDetailMap" type="com.std.core.pojo.response.TeamUserOssRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="cuserId" jdbcType="VARCHAR" property="cuserId"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.kind
        , t.type
        , t.meta_role_id
        , t.login_name
        , t.nickname
        , t.mobile
        , t.email
        , t.photo
        , t.identify_style
        , t.id_kind
        , t.id_no
        , t.login_pwd_strength
        , t.trade_pwd_strength
        , t.real_name
        , t.status
        , t.recovery_status
        , t.identify_status
        , t.grouping
        , t.register_datetime
        , t.register_ip
        , t.invite_no
        , t.user_referee
        , t.introduce
        , t.remark
        , t.last_login_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.company_id
        , t.author_rate
        , t.black_times
        , t.channel_flag
        , t.channel_type
        , t.channel_id
        , t.is_channel
        , t.google_secret
        , t.channel_merchant_id
        , t.channel_uid
        , t.block_address
        , t.yao_flag
        , t.series_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="nickname != null and nickname != '' ">
                AND t.nickname like concat('%',#{nickname},'%')
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="metaRoleId != null">
                AND t.meta_role_id = #{metaRoleId, jdbcType=BIGINT}
            </if>
            <if test="loginName != null and loginName != '' ">
                AND t.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                AND t.identify_style = #{identifyStyle, jdbcType=VARCHAR}
            </if>
            <if test="idKind != null and idKind != '' ">
                AND t.id_kind = #{idKind, jdbcType=VARCHAR}
            </if>
            <if test="idNo != null and idNo != '' ">
                AND t.id_no = #{idNo, jdbcType=VARCHAR}
            </if>
            <if test="authFlag != null and authFlag != '' ">
                AND t.id_no is not null
            </if>
            <if test="idNo != null and idNo != '' ">
                AND t.id_no = #{idNo, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND t.real_name like concat('%',#{realName, jdbcType=VARCHAR},'%')
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="mobileForQuery != null and mobileForQuery != '' ">
                AND t.mobile like concat('%',#{mobileForQuery},'%')
            </if>
            <if test="email != null and email != '' ">
                AND t.email = #{email, jdbcType=VARCHAR}
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                AND t.login_pwd = #{loginPwd, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                AND t.recovery_status = #{recoveryStatus, jdbcType=VARCHAR}
            </if>
            <if test="identifyStatus != null and identifyStatus != '' ">
                AND t.identify_status = #{identifyStatus, jdbcType=VARCHAR}
            </if>
            <if test="grouping != null and grouping != '' ">
                AND t.grouping = #{grouping, jdbcType=VARCHAR}
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                AND t.trade_pwd = #{tradePwd, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="inviteNo != null">
                AND t.invite_no = #{inviteNo, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null">
                AND t.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="registerDatetimeStart != null">
                <![CDATA[AND t.register_datetime >= #{registerDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="registerDatetimeEnd != null">
                <![CDATA[AND t.register_datetime <= #{registerDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeStart != null">
                <![CDATA[AND t.last_login_datetime >= #{lastLoginDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeEnd != null">
                <![CDATA[AND t.last_login_datetime <= #{lastLoginDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="authorRate != null">
                AND t.author_rate = #{authorRate, jdbcType=DECIMAL}
            </if>
            <if test="blackTimes != null">
                AND t.black_times = #{blackTimes, jdbcType=INTEGER}
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                AND t.channel_flag = #{channelFlag, jdbcType=VARCHAR}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="isChannel != null and isChannel != '' ">
                AND t.is_channel = #{isChannel, jdbcType=VARCHAR}
            </if>
            <if test="idNotExist != null">
                AND t.id != #{idNotExist, jdbcType=BIGINT}
            </if>
            <if test="googleSecret != null and googleSecret != '' ">
                AND t.google_secret = #{googleSecret, jdbcType=VARCHAR}
            </if>
            <if test="userIdNotExistList != null and userIdNotExistList.size() != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="userIdNotExistList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>

            <if test="exceptFlag != null and exceptFlag != '' ">
                AND t.id not in (select user_id from tstd_statistics_except_user where `type`=#{exceptType})
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (t.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or t.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or t.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or t.id_no like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="channelMerchantId != null ">
                AND t.channel_merchant_id = #{channelMerchantId, jdbcType=BIGINT}
            </if>
            <if test="channelUid != null and channelUid != '' ">
                AND t.channel_uid = #{channelUid, jdbcType=VARCHAR}
            </if>
            <if test="blockAddress != null and blockAddress != '' ">
                AND t.block_address = #{blockAddress, jdbcType=VARCHAR}
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                AND t.yao_flag = #{yaoFlag, jdbcType=VARCHAR}
            </if>
            <if test="seriesNo != null ">
                AND t.series_no = #{seriesNo, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="nickname != null and nickname != '' ">
                AND t.nickname like concat('%',#{nickname},'%')
            </if>
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="metaRoleId != null">
                AND t.meta_role_id = #{metaRoleId, jdbcType=BIGINT}
            </if>
            <if test="loginName != null and loginName != '' ">
                AND t.login_name like concat('%',#{loginName},'%')
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                AND t.identify_style = #{identifyStyle, jdbcType=VARCHAR}
            </if>
            <if test="idKind != null and idKind != '' ">
                AND t.id_kind = #{idKind, jdbcType=VARCHAR}
            </if>
            <if test="idNo != null and idNo != '' ">
                AND t.id_no = #{idNo, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND t.real_name like concat('%',#{realName},'%')
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="mobileForQuery != null and mobileForQuery != '' ">
                AND t.mobile like concat('%',#{mobileForQuery},'%')
            </if>
            <if test="email != null and email != '' ">
                AND t.email = #{email, jdbcType=VARCHAR}
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                AND t.login_pwd = #{loginPwd, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                AND t.recovery_status = #{recoveryStatus, jdbcType=VARCHAR}
            </if>
            <if test="identifyStatus != null and identifyStatus != '' ">
                AND t.identify_status = #{identifyStatus, jdbcType=VARCHAR}
            </if>
            <if test="grouping != null and grouping != '' ">
                AND t.grouping = #{grouping, jdbcType=VARCHAR}
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                AND t.trade_pwd = #{tradePwd, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="inviteNo != null">
                AND t.invite_no = #{inviteNo, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null">
                AND t.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="registerDatetimeStart != null">
                <![CDATA[AND t.register_datetime >= #{registerDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="registerDatetimeEnd != null">
                <![CDATA[AND t.register_datetime <= #{registerDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeStart != null">
                <![CDATA[AND t.last_login_datetime >= #{lastLoginDatetimeStart, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="lastLoginDatetimeEnd != null">
                <![CDATA[AND t.last_login_datetime <= #{lastLoginDatetimeEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="authorRate != null">
                AND t.author_rate = #{authorRate, jdbcType=DECIMAL}
            </if>
            <if test="blackTimes != null">
                AND t.black_times = #{blackTimes, jdbcType=INTEGER}
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                AND t.channel_flag = #{channelFlag, jdbcType=VARCHAR}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null and channelId != '' ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="isChannel != null and isChannel != '' ">
                AND t.is_channel = #{isChannel, jdbcType=VARCHAR}
            </if>
            <if test="idNotExist != null">
                AND t.id != #{idNotExist, jdbcType=BIGINT}
            </if>
            <if test="userIdNotExistList != null and userIdNotExistList.size() != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="userIdNotExistList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="googleSecret != null and googleSecret != '' ">
                AND t.google_secret = #{googleSecret, jdbcType=VARCHAR}
            </if>
            <if test="channelMerchantId != null ">
                AND t.channel_merchant_id = #{channelMerchantId, jdbcType=BIGINT}
            </if>
            <if test="channelUid != null and channelUid != '' ">
                AND t.channel_uid = #{channelUid, jdbcType=VARCHAR}
            </if>
            <if test="blockAddress != null and blockAddress != '' ">
                AND t.block_address = #{blockAddress, jdbcType=VARCHAR}
            </if>
            <if test="seriesNo != null ">
                AND t.series_no = #{seriesNo, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.User">
        insert into tsys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="kind != null and kind != '' ">
                kind,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="metaRoleId != null">
                meta_role_id,
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name,
            </if>
            <if test="nickname != null and nickname != '' ">
                nickname,
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile,
            </if>
            <if test="email != null and email != '' ">
                email,
            </if>
            <if test="photo != null and photo != '' ">
                photo,
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                identify_style,
            </if>
            <if test="idKind != null and idKind != '' ">
                id_kind,
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no,
            </if>
            <if test="realName != null and realName != '' ">
                real_name,
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                login_pwd,
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                login_pwd_strength,
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                trade_pwd,
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                trade_pwd_strength,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                recovery_status,
            </if>
            <if test="identifyStatus != null and identifyStatus != '' ">
                identify_status,
            </if>
            <if test="grouping != null and grouping != '' ">
                grouping,
            </if>
            <if test="registerDatetime != null ">
                register_datetime,
            </if>
            <if test="registerIp != null and registerIp != '' ">
                register_ip,
            </if>
            <if test="inviteNo != null ">
                invite_no,
            </if>
            <if test="userReferee != null ">
                user_referee,
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="lastLoginDatetime != null  ">
                last_login_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null  ">
                update_datetime,
            </if>
            <if test="authorRate != null">
                author_rate,
            </if>
            <if test="blackTimes != null">
                black_times,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                channel_flag,
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type,
            </if>
            <if test="channelId != null ">
                channel_id,
            </if>
            <if test="isChannel != null and isChannel != '' ">
                is_channel,
            </if>
            <if test="channelMerchantId != null ">
                channel_merchant_id,
            </if>
            <if test="channelUid != null and channelUid != '' ">
                channel_uid,
            </if>
            <if test="blockAddress != null and blockAddress != '' ">
                block_address,
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                yao_flag,
            </if>
            <if test="seriesNo != null ">
                series_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="kind != null and kind != '' ">
                #{kind,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="metaRoleId != null">
                #{metaRoleId, jdbcType=BIGINT},
            </if>
            <if test="loginName != null and loginName != '' ">
                #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null and nickname != '' ">
                #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="photo != null and photo != '' ">
                #{photo,jdbcType=VARCHAR},
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                #{identifyStyle, jdbcType=VARCHAR},
            </if>
            <if test="idKind != null and idKind != '' ">
                #{idKind,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                #{realName,jdbcType=VARCHAR},
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                #{loginPwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                #{tradePwd,jdbcType=VARCHAR},
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                #{tradePwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                #{recoveryStatus, jdbcType=VARCHAR},
            </if>
            <if test="identifyStatus != null and identifyStatus != '' ">
                #{identifyStatus, jdbcType=VARCHAR},
            </if>
            <if test="grouping != null and grouping != '' ">
                #{grouping, jdbcType=VARCHAR},
            </if>
            <if test="registerDatetime != null">
                #{registerDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="registerIp != null and registerIp != '' ">
                #{registerIp,jdbcType=VARCHAR},
            </if>
            <if test="inviteNo != null and inviteNo != '' ">
                #{inviteNo,jdbcType=BIGINT},
            </if>
            <if test="userReferee != null and userReferee != '' ">
                #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="introduce != null and introduce != '' ">
                #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDatetime != null">
                #{lastLoginDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="authorRate != null">
                #{authorRate, jdbcType=DECIMAL},
            </if>
            <if test="blackTimes != null">
                #{blackTimes, jdbcType=INTEGER},
            </if>
            <if test="companyId != null ">
                #{companyId,jdbcType=BIGINT},
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                #{channelFlag,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType, jdbcType=VARCHAR},
            </if>
            <if test="channelId != null ">
                #{channelId,jdbcType=BIGINT},
            </if>
            <if test="isChannel != null and isChannel != '' ">
                #{isChannel, jdbcType=VARCHAR},
            </if>
            <if test="channelMerchantId != null ">
                #{channelMerchantId, jdbcType=BIGINT},
            </if>
            <if test="channelUid != null and channelUid != '' ">
                #{channelUid, jdbcType=VARCHAR},
            </if>
            <if test="blockAddress != null and blockAddress != '' ">
                #{blockAddress, jdbcType=VARCHAR},
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                #{yaoFlag, jdbcType=VARCHAR},
            </if>
            <if test="seriesNo != null ">
                #{seriesNo, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into tsys_user
        (id
        ,kind
        ,type
        ,login_name
        ,nickname
        ,mobile
        ,email
        ,photo
        ,identify_style
        ,id_kind
        ,id_no
        ,real_name
        ,login_pwd
        ,login_pwd_strength
        ,status
        ,register_datetime
        ,register_ip
        ,remark
        ,last_login_datetime
        ,company_id
        ,channel_uid
        ,block_address)
        values
        <foreach item="user" index="index" collection="list" separator=",">
            (
            #{user.id,jdbcType=BIGINT},
            #{user.kind,jdbcType=VARCHAR},
            #{user.type,jdbcType=VARCHAR},
            #{user.loginName,jdbcType=VARCHAR},
            #{user.nickname,jdbcType=VARCHAR},
            #{user.mobile,jdbcType=VARCHAR},
            #{user.email,jdbcType=VARCHAR},
            #{user.photo,jdbcType=VARCHAR},
            #{user.identifyStyle,jdbcType=VARCHAR},
            #{user.idKind,jdbcType=VARCHAR},
            #{user.idNo,jdbcType=VARCHAR},
            #{user.realName,jdbcType=VARCHAR},
            #{user.loginPwd,jdbcType=VARCHAR},
            #{user.loginPwdStrength,jdbcType=VARCHAR},
            #{user.status,jdbcType=VARCHAR},
            #{user.registerDatetime,jdbcType=TIMESTAMP},
            #{user.registerIp,jdbcType=VARCHAR},
            #{user.remark,jdbcType=VARCHAR},
            #{user.lastLoginDatetime,jdbcType=TIMESTAMP},
            #{user.companyId,jdbcType=BIGINT},
            #{user.channelUid,jdbcType=VARCHAR},
            #{user.blockAddress,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.User">
        update tsys_user
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="metaRoleId != null">
                meta_role_id = #{metaRoleId, jdbcType=BIGINT},
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name = #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="nickname != null and nickname != '' ">
                nickname = #{nickname,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="email != null and email != '' ">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="photo != null and photo != '' ">
                photo = #{photo,jdbcType=VARCHAR},
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                identify_style = #{identifyStyle,jdbcType=VARCHAR},
            </if>
            <if test="idKind != null and idKind != '' ">
                id_kind = #{idKind,jdbcType=VARCHAR},
            </if>
            <if test="idNo != null and idNo != '' ">
                id_no = #{idNo,jdbcType=VARCHAR},
            </if>
            <if test="realName != null and realName != '' ">
                real_name = #{realName,jdbcType=VARCHAR},
            </if>
            <if test="loginPwd != null and loginPwd != '' ">
                login_pwd = #{loginPwd,jdbcType=VARCHAR},
            </if>
            <if test="loginPwdStrength != null and loginPwdStrength != '' ">
                login_pwd_strength = #{loginPwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="tradePwd != null and tradePwd != '' ">
                trade_pwd = #{tradePwd,jdbcType=VARCHAR},
            </if>
            <if test="tradePwdStrength != null and tradePwdStrength != '' ">
                trade_pwd_strength = #{tradePwdStrength,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                recovery_status = #{recoveryStatus, jdbcType=VARCHAR},
            </if>
            <if test="identifyStatus != null and identifyStatus != '' ">
                identify_status = #{identifyStatus, jdbcType=VARCHAR},
            </if>
            <if test="grouping != null and grouping != '' ">
                grouping = #{grouping,jdbcType=VARCHAR},
            </if>
            <if test="userReferee != null">
                user_referee = #{userReferee,jdbcType=BIGINT},
            </if>
            <if test="introduce != null">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lastLoginDatetime != null">
                last_login_datetime = #{lastLoginDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="authorRate != null">
                author_rate = #{authorRate, jdbcType=DECIMAL},
            </if>
            <if test="blackTimes != null">
                black_times = #{blackTimes,jdbcType=INTEGER},
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                channel_flag = #{channelFlag,jdbcType=VARCHAR},
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType, jdbcType=VARCHAR},
            </if>
            <if test="channelId != null ">
                channel_id = #{channelId,jdbcType=BIGINT},
            </if>
            <if test="isChannel != null and isChannel != '' ">
                is_channel = #{isChannel, jdbcType=VARCHAR},
            </if>
            <if test="googleSecret != null and googleSecret != '' ">
                google_secret = #{googleSecret, jdbcType=VARCHAR},
            </if>
            <if test="channelMerchantId != null ">
                channel_merchant_id = #{channelMerchantId, jdbcType=BIGINT},
            </if>
            <if test="channelUid != null and channelUid != '' ">
                channel_uid = #{channelUid, jdbcType=VARCHAR},
            </if>
            <if test="companyId != null ">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="yaoFlag != null and yaoFlag != '' ">
                yao_flag = #{yaoFlag, jdbcType=VARCHAR},
            </if>
            <if test="blockAddress != null and blockAddress != '' ">
                block_address = #{blockAddress, jdbcType=VARCHAR},
            </if>
            <if test="seriesNo != null ">
                series_no = #{seriesNo, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="batchModifyChannelFlag">
        <!--        <foreach collection="list" item="item" index="index" open="" close="" separator=";">-->
        <!--            update tsys_user-->
        <!--            SET channel_flag =  #{channelFlag,jdbcType=VARCHAR}-->
        <!--            where id = #{item.id}-->
        <!--        </foreach>-->

        <if test="userList != null and userList.size > 0 ">
            update tsys_user
            SET channel_flag = #{channelFlag,jdbcType=VARCHAR},
            channel_id = #{channelId,jdbcType=BIGINT}
            where id in
            <foreach item="item" index="index" collection="userList" open="(" separator=","
                    close=")">
                #{item.id}
            </foreach>
        </if>

    </update>
    <update id="updateLogintime">
        update tsys_user
        <set>
            <if test="lastLoginDatetime != null">
                last_login_datetime = #{lastLoginDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>


    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <resultMap id="SimpleBaseResultMap" type="com.std.core.pojo.domain.User">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="Simple_Base_Column_List">
        t
        .
        id
        , t.login_name
        , t.nickname
        , t.photo
        , t.status
    </sql>

    <!-- 查询 -->
    <select id="selectSimpleByPrimaryKey" parameterType="java.lang.Long"
            resultMap="SimpleBaseResultMap">
        select
        <include refid="Simple_Base_Column_List"/>
        from tsys_user t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        <include refid="where_condition"/>

        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="RefBaseResultMap" type="com.std.core.pojo.domain.UserBak">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
    </resultMap>

    <!-- 组合条件查询 -->
    <select id="selectByConditionRefId" parameterType="com.std.core.pojo.domain.UserBak"
            resultMap="RefBaseResultMap">
        select t.id
             , t.user_id
             , t.quantity
        from tsys_user_bak t
    </select>


    <sql id="UserRef_Column_List">
        t
        .
        id
        , t.kind
        , t.type
        , t.login_name
        , t.nickname
        , t.mobile
        , t.email
        , t.photo
        , t.id_kind
        , t.id_no
        , t.trade_pwd_strength
        , t.real_name
        , t.city
        , t.area
        , t.address
        , t.status
        , t.register_datetime
        , t.register_ip
        , t.invite_no
        , t.user_referee
        , t.introduce
        , t.remark
        , t.last_login_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.company_id
        ,(select count(1) from tsys_user tu where tu.user_referee = t.id) as refer_user_count
    </sql>

    <!-- 组合条件查询数量 -->
    <select id="selectCount" parameterType="com.std.core.pojo.domain.User" resultType="java.lang.Integer">
        SELECT count(1) FROM tsys_user t
        <include refid="where_condition"/>
    </select>

    <select id="selectUserRefereeByCondition" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        <include refid="where_condition"/>

        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="getAllCUser" resultType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="UserRef_Column_List"/>
        from tsys_user t
        left join ttask_cuser tt on t.id = tt.user_id
        where t.kind = #{userKind,jdbcType=VARCHAR} and t.status = #{status,jdbcType=VARCHAR}
        and t.id not in (select user_id as uid from tgyl_seller group by user_id)
    </select>


    <select id="selectSubUserList" parameterType="java.lang.Long" resultType="com.std.core.pojo.response.SubUserRes">
        select t.id,
               t.nickname,
               t.mobile,
               t.photo,
               DATE_FORMAT(t.register_datetime, '%Y-%m-%d %H:%i:%s') as registerDatetime,
               tc.member_flag                                           memberFlag
        from tsys_user t
                 left join tmm_cuser tc on t.id = tc.user_id
        where t.user_referee = #{id,jdbcType=BIGINT}
        /*   order by tc.member_flag desc
          , t.register_datetime desc */
    </select>


    <select id="ossTeamDeatil" resultType="com.std.core.pojo.domain.User" resultMap="UserTeamDetailMap">
        select t.id, tc.id as cuserId, t.nickname, t.real_name, tc.level, t.mobile, t.status
        from tsys_user t
                 left join tmm_cuser tc on t.id = tu.user_id
        where t.user_referee = #{userReferee,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectIdList" resultType="java.lang.Long">
        select t.id
        from tsys_user t
        where t.kind = 'C'
          and t.status = 'normal'
    </select>

    <select id="getCommunityUserList" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from (
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        where t.id = #{channelId,jdbcType=BIGINT} and t.is_channel = '1'
        union
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        where t.channel_id = #{channelId,jdbcType=BIGINT}
        and t.is_channel = #{isChannel,jdbcType=BIGINT}
        ) t
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.User"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user t
        <include refid="where_condition_oss"/>

        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="BackBaseResultMap" type="com.std.core.pojo.domain.UserBack">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <select id="selectUserBackList" resultMap="BackBaseResultMap">
        select t.id
             , t.user_id
             , t.login_name
             , t.amount
             , t.remark
        from tstd_user_back t
    </select>

    <select id="selectWithdrawBackList" resultType="com.std.core.pojo.domain.UserWithdrawBack">
        select t.id
             , t.mobile
             , t.user_id       userId
             , t.login_name    loginName
             , t.amount
             , t.remain_amount remainAmount
             , t.status
             , t.remark
        from tstd_user_back_withdraw t
        where t.status = '0'
    </select>
    <select id="selectByConditionCompany" resultType="com.std.core.pojo.domain.User" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,t.loginStatus
        from (
        select
        <include refid="Base_Column_List"/>
        ,IFNULL(( SELECT ta.config_type FROM tstd_login_error_record ta WHERE t.id =
        ta.user_id and ta.user_kind=t.kind and (ta.unlock_datetime <![CDATA[ >]]>#{date} or ta.unlock_datetime is null ) ORDER BY ta.id desc
        LIMIT 1 ),'0') loginStatus
        from tsys_user t
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="kind != null and kind != '' ">
                AND t.kind = #{kind, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="loginName != null and loginName != '' ">
                AND t.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="email != null and email != '' ">
                AND t.email = #{email, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="mainFlagYes != null and mainFlagYes != '' ">
                AND t.id = t.company_id
            </if>
            <if test="mainFlagNo != null and mainFlagNo != '' ">
                AND t.id <![CDATA[ !=]]> t.company_id
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
        ) t
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="loginStatus != null and loginStatus != '' ">
                AND t.loginStatus = #{loginStatus, jdbcType=VARCHAR}
            </if>
        </trim>

    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="SimpleBaseResultMap">
        select
        <include refid="Simple_Base_Column_List"/>
        from tsys_user t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectVirtualUserList" resultType="java.lang.Long">
        select t.id
        from tsys_user t
        where t.tag = '1'
    </select>

    <update id="updateWithdrawBack">
        update tstd_user_back_withdraw
        <set>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null ">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name = #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="amount != null ">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="remainAmount != null ">
                remain_amount = #{remainAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="resetManualIdentifyStyle">
        update tsys_user set identify_style= '1' where id in
        (<foreach item="item" index="index" collection="list" open="(" separator=","
            close=")">
        #{item,jdbcType=BIGINT}
    </foreach>)
    </update>
    <update id="updateLoginStatus">
        update tsys_user
        set login_status=#{loginStatus}
        where id = #{id}
    </update>
</mapper>