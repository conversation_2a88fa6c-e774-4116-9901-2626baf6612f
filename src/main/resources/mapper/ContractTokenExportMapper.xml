<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ContractTokenExportMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ContractTokenExport">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="from_address" jdbcType="VARCHAR" property="fromAddress"/>
        <result column="to_address" jdbcType="VARCHAR" property="toAddress"/>
        <result column="quantity" jdbcType="BIGINT" property="quantity"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="apply_note" jdbcType="VARCHAR" property="applyNote"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="approve_user" jdbcType="BIGINT" property="approveUser"/>
        <result column="approve_note" jdbcType="VARCHAR" property="approveNote"/>
        <result column="approve_datetime" jdbcType="TIMESTAMP" property="approveDatetime"/>

        <result column="back_user" jdbcType="BIGINT" property="backUser"/>
        <result column="back_note" jdbcType="VARCHAR" property="backNote"/>
        <result column="back_datetime" jdbcType="TIMESTAMP" property="backDatetime"/>

        <result column="broadcast_user" jdbcType="BIGINT" property="broadcastUser"/>
        <result column="broadcast_note" jdbcType="VARCHAR" property="broadcastNote"/>
        <result column="broadcast_datetime" jdbcType="TIMESTAMP" property="broadcastDatetime"/>

        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.quantity
        , t.from_address
        , t.to_address
        , t.quantity
        , t.status
        , t.remark
        , t.apply_note
        , t.apply_datetime
        , t.approve_user
        , t.approve_note
        , t.approve_datetime

        , t.back_user
        , t.back_note
        , t.back_datetime

        , t.broadcast_user
        , t.broadcast_note
        , t.broadcast_datetime
        , t.pay_amount
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.commission_amount
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="fromAddress != null and fromAddress != '' ">
                AND t.from_address = #{fromAddress, jdbcType=VARCHAR}
            </if>
            <if test="toAddress != null and toAddress != '' ">
                AND t.to_address = #{toAddress, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="noStatusList != null and noStatusList.size() != 0 ">
                AND t.status not in
                <foreach item="item" index="index" collection="noStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="applyNote != null and applyNote != '' ">
                AND t.apply_note = #{applyNote, jdbcType=VARCHAR}
            </if>
            <if test="applyDatetime != null">
                AND t.apply_datetime = #{applyDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="approveUser != null">
                AND t.approve_user = #{approveUser, jdbcType=BIGINT}
            </if>
            <if test="approveNote != null and approveNote != '' ">
                AND t.approve_note = #{approveNote, jdbcType=VARCHAR}
            </if>
            <if test="approveDatetime != null">
                AND t.approve_datetime = #{approveDatetime, jdbcType=TIMESTAMP}
            </if>

            <if test="backUser != null">
                AND t.back_user = #{backUser, jdbcType=BIGINT}
            </if>
            <if test="backNote != null and backNote != '' ">
                AND t.back_note = #{backNote, jdbcType=VARCHAR}
            </if>
            <if test="backDatetime != null">
                AND t.back_datetime = #{backDatetime, jdbcType=TIMESTAMP}
            </if>

            <if test="broadcastUser != null">
                AND t.broadcast_user = #{broadcastUser, jdbcType=BIGINT}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payCancelDatetime != null">
                AND t.pay_datetime &lt; #{payCancelDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ContractTokenExport" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_contract_token_export
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="fromAddress != null and fromAddress != '' ">
                from_address,
            </if>
            <if test="toAddress != null and toAddress != '' ">
                to_address,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note,
            </if>
            <if test="applyDatetime != null ">
                apply_datetime,
            </if>
            <if test="approveUser != null ">
                approve_user,
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note,
            </if>
            <if test="approveDatetime != null ">
                approve_datetime,
            </if>
            <if test="backUser != null">
                back_user,
            </if>
            <if test="backNote != null and backNote != '' ">
                back_note,
            </if>
            <if test="backDatetime != null">
                back_datetime,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="payBalanceAmount != null ">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null ">
                pay_cash_amount,
            </if>
            <if test="commissionAmount != null ">
                commission_amount,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="quantity != null ">
                #{quantity,jdbcType=BIGINT},
            </if>
            <if test="fromAddress != null and fromAddress != '' ">
                #{fromAddress,jdbcType=VARCHAR},
            </if>
            <if test="toAddress != null and toAddress != '' ">
                #{toAddress,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUser != null">
                #{approveUser,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null">
                #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="backUser != null">
                #{backUser, jdbcType=BIGINT},
            </if>
            <if test="backNote != null and backNote != '' ">
                #{backNote, jdbcType=VARCHAR},
            </if>
            <if test="backDatetime != null">
                #{backDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_contract_token_export
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ContractTokenExport">
        update nft_contract_token_export
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="fromAddress != null and fromAddress != '' ">
                from_address = #{fromAddress,jdbcType=VARCHAR},
            </if>
            <if test="toAddress != null and toAddress != '' ">
                to_address = #{toAddress,jdbcType=VARCHAR},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note = #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="applyDatetime != null">
                apply_datetime = #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="approveUser != null">
                approve_user = #{approveUser,jdbcType=BIGINT},
            </if>
            <if test="approveNote != null and approveNote != '' ">
                approve_note = #{approveNote,jdbcType=VARCHAR},
            </if>
            <if test="approveDatetime != null">
                approve_datetime = #{approveDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="backUser != null">
                back_user = #{backUser, jdbcType=BIGINT},
            </if>
            <if test="backNote != null and backNote != '' ">
                back_note = #{backNote, jdbcType=VARCHAR},
            </if>
            <if test="backDatetime != null">
                back_datetime = #{backDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="broadcastUser != null">
                broadcast_user = #{broadcastUser,jdbcType=BIGINT},
            </if>
            <if test="broadcastNote != null and broadcastNote != '' ">
                broadcast_note = #{broadcastNote,jdbcType=VARCHAR},
            </if>
            <if test="broadcastDatetime != null">
                broadcast_datetime = #{broadcastDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract_token_export t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract_token_export t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ContractTokenExport"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_contract_token_export t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>