<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishUserLogMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishUserLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="online_duration" jdbcType="INTEGER" property="onlineDuration"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>

    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.user_id
        , t.role_id
        , t.online_duration
        , t.create_datetime
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="roleId != null">
                AND t.role_id = #{roleId, jdbcType=BIGINT}
            </if>
            <if test="onlineDuration != null">
                AND t.online_duration = #{roleId, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishUserLog">
        insert into yg_fish_user_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
            <if test="status != null and status != '' ">
                status,
            </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="roleId != null ">
                role_id,
              </if>
            <if test="onlineDuration != null">
                online_duration,
            </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="onlineDuration != null">
                #{roleId, jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_fish_user_log
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishUserLog">
        update yg_fish_user_log
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="onlineDuration != null">
                online_duration = #{onlineDuration, jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_user_log t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishUserLog"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_user_log t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectTodayFirstTime" parameterType="com.std.core.pojo.domain.UserEntryRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_user_log t
        where t.user_id=#{userId} and DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> DATE_FORMAT(#{date},'%Y-%m-%d')
          and t.type='0' order by t.id asc limit 1

    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_user_log t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectWeekSumTime" resultType="java.lang.Integer">
        select
            COALESCE(sum(t.online_duration),0)
        from yg_fish_user_log t
        where t.user_id = #{userId,jdbcType=BIGINT} and DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> DATE_FORMAT(#{date},'%Y-%m-%d') and t.type='0'
        and t.status in ('1','2');
    </select>
</mapper>