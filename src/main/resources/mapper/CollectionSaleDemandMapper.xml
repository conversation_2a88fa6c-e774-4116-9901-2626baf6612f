<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionSaleDemandMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionSaleDemand">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="chain" jdbcType="VARCHAR" property="chain"/>
        <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="other_object_flag" jdbcType="VARCHAR" property="otherObjectFlag"/>
        <result column="expect_date_start" jdbcType="TIMESTAMP" property="expectDateStart"/>
        <result column="expect_date_end" jdbcType="TIMESTAMP" property="expectDateEnd"/>
        <result column="mandate_url" jdbcType="VARCHAR" property="mandateUrl"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.collection_id
        , t.chain
        , t.buy_type
        , t.price
        , t.quantity
        , t.content
        , t.other_object_flag
        , t.expect_date_start
        , t.expect_date_end
        , t.mandate_url
        , t.creater
        , t.creater_name
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="chain != null and chain != '' ">
                AND t.chain = #{chain, jdbcType=VARCHAR}
            </if>
            <if test="buyType != null and buyType != '' ">
                AND t.buy_type = #{buyType, jdbcType=VARCHAR}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="otherObjectFlag != null and otherObjectFlag != '' ">
                AND t.other_object_flag = #{otherObjectFlag, jdbcType=VARCHAR}
            </if>
            <if test="expectDateStart != null">
                AND t.expect_date_start = #{expectDateStart, jdbcType=TIMESTAMP}
            </if>
            <if test="expectDateEnd != null">
                AND t.expect_date_end = #{expectDateEnd, jdbcType=TIMESTAMP}
            </if>
            <if test="mandateUrl != null and mandateUrl != '' ">
                AND t.mandate_url = #{mandateUrl, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionSaleDemand">
        insert into nft_collection_sale_demand
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
            <if test="chain != null and chain != '' ">
                chain,
            </if>
              <if test="buyType != null and buyType != '' ">
                buy_type,
              </if>
              <if test="price != null ">
                price,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="content != null and content != '' ">
                content,
              </if>
              <if test="otherObjectFlag != null and otherObjectFlag != '' ">
                other_object_flag,
              </if>
              <if test="expectDateStart != null ">
                expect_date_start,
              </if>
              <if test="expectDateEnd != null ">
                expect_date_end,
              </if>
              <if test="mandateUrl != null and mandateUrl != '' ">
                mandate_url,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="chain != null and chain != '' ">
                #{chain, jdbcType=VARCHAR},
            </if>
            <if test="buyType != null and buyType != '' ">
                #{buyType,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="otherObjectFlag != null and otherObjectFlag != '' ">
                #{otherObjectFlag,jdbcType=VARCHAR},
            </if>
            <if test="expectDateStart != null">
                #{expectDateStart,jdbcType=TIMESTAMP},
            </if>
            <if test="expectDateEnd != null">
                #{expectDateEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="mandateUrl != null and mandateUrl != '' ">
                #{mandateUrl,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_collection_sale_demand
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByCollectionId">
        delete from nft_collection_sale_demand
        where collection_id = #{collectionId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionSaleDemand">
        update nft_collection_sale_demand
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="chain != null and chain != '' ">
                chain = #{chain, jdbcType=VARCHAR},
            </if>
            <if test="buyType != null and buyType != '' ">
                buy_type = #{buyType,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="otherObjectFlag != null and otherObjectFlag != '' ">
                other_object_flag = #{otherObjectFlag,jdbcType=VARCHAR},
            </if>
            <if test="expectDateStart != null">
                expect_date_start = #{expectDateStart,jdbcType=TIMESTAMP},
            </if>
            <if test="expectDateEnd != null">
                expect_date_end = #{expectDateEnd,jdbcType=TIMESTAMP},
            </if>
            <if test="mandateUrl != null and mandateUrl != '' ">
                mandate_url = #{mandateUrl,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_sale_demand t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionSaleDemand"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_sale_demand t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>