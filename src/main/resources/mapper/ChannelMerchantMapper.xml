<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelMerchantMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelMerchant">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>

        <result column="sms_prefix" jdbcType="VARCHAR" property="smsPrefix"/>
        <result column="pay_back_url" jdbcType="VARCHAR" property="payBackUrl"/>
        <result column="invite_url" jdbcType="VARCHAR" property="inviteUrl"/>

        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.name
        , t.sms_prefix
        , t.pay_back_url
        , t.invite_url
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="idList != null and idList.size() != 0 ">
                AND t.id in
                <foreach item="item" index="index" collection="idList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="smsPrefix != null and smsPrefix != '' ">
                AND t.sms_prefix = #{smsPrefix, jdbcType=VARCHAR}
            </if>
            <if test="payBackUrl != null and payBackUrl != '' ">
                AND t.pay_back_url = #{payBackUrl, jdbcType=VARCHAR}
            </if>
            <if test="inviteUrl != null and inviteUrl != '' ">
                AND t.invite_url = #{inviteUrl, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelMerchant">
        insert into third_channel_merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="smsPrefix != null and smsPrefix != '' ">
                sms_prefix,
            </if>
            <if test="payBackUrl != null and payBackUrl != '' ">
                pay_back_url,
            </if>
            <if test="inviteUrl != null and inviteUrl != '' ">
                invite_url,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="smsPrefix != null and smsPrefix != '' ">
                #{smsPrefix, jdbcType=VARCHAR},
            </if>
            <if test="payBackUrl != null and payBackUrl != '' ">
                #{payBackUrl, jdbcType=VARCHAR},
            </if>
            <if test="inviteUrl != null and inviteUrl != '' ">
                #{inviteUrl, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from third_channel_merchant
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelMerchant">
        update third_channel_merchant
        <set>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="smsPrefix != null and smsPrefix != '' ">
                sms_prefix = #{smsPrefix, jdbcType=VARCHAR},
            </if>
            <if test="payBackUrl != null and payBackUrl != '' ">
                pay_back_url = #{payBackUrl, jdbcType=VARCHAR},
            </if>
            <if test="inviteUrl != null and inviteUrl != '' ">
                invite_url = #{inviteUrl, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_channel_merchant t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelMerchant"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_channel_merchant t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionDistribution" parameterType="com.std.core.pojo.domain.ChannelMerchant"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from third_channel_merchant t
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="idList != null and idList.size() != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="idList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="smsPrefix != null and smsPrefix != '' ">
                AND t.sms_prefix = #{smsPrefix, jdbcType=VARCHAR}
            </if>
            <if test="payBackUrl != null and payBackUrl != '' ">
                AND t.pay_back_url = #{payBackUrl, jdbcType=VARCHAR}
            </if>
            <if test="inviteUrl != null and inviteUrl != '' ">
                AND t.invite_url = #{inviteUrl, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>