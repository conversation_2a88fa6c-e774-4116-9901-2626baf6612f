<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelConfigMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_name" jdbcType="VARCHAR" property="channelName"/>
        <result column="click_number" jdbcType="INTEGER" property="clickNumber"/>
        <result column="channel_url" jdbcType="VARCHAR" property="channelUrl"/>
        <result column="registerCount" jdbcType="INTEGER" property="registerCount"/>
        <result column="company_id" jdbcType="INTEGER" property="companyId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.channel_name
        , t.click_number
        , t.channel_url
        , t.company_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="channelName != null and channelName != '' ">
                AND t.channel_name like concat('%',#{channelName},'%')
            </if>
            <if test="clickNumber != null">
                AND t.click_number = #{clickNumber, jdbcType=INTEGER}
            </if>
            <if test="channelUrl != null and channelUrl != '' ">
                AND t.channel_url = #{channelUrl, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null ">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelConfig" useGeneratedKeys="true" keyProperty="id">
        insert into tsys_channel_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="companyId != null ">
                company_id,
            </if>
            <if test="channelName != null and channelName != '' ">
                channel_name,
            </if>
            <if test="clickNumber != null ">
                click_number,
            </if>
            <if test="channelUrl != null and channelUrl != '' ">
                channel_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="companyId != null ">
                #{companyId, jdbcType=BIGINT},
            </if>
            <if test="channelName != null and channelName != '' ">
                #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="clickNumber != null">
                #{clickNumber,jdbcType=INTEGER},
            </if>
            <if test="channelUrl != null and channelUrl != '' ">
                #{channelUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tsys_channel_config
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelConfig">
        update tsys_channel_config
        <set>
            <if test="companyId != null ">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="channelName != null and channelName != '' ">
                channel_name = #{channelName,jdbcType=VARCHAR},
            </if>
            <if test="clickNumber != null">
                click_number = #{clickNumber,jdbcType=INTEGER},
            </if>
            <if test="channelUrl != null and channelUrl != '' ">
                channel_url = #{channelUrl,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_channel_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_channel_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        (SELECT count(1) from tsys_user ta where ta.channel_type='1' and ta.channel_id=t.id) as registerCount
        from tsys_channel_config t
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.ChannelConfig"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        (SELECT count(1) from tsys_user ta where ta.channel_type='1' and ta.channel_id=t.id) as registerCount
        from tsys_channel_config t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_channel_config t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>