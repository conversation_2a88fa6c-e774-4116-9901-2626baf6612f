<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LoginErrorRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LoginErrorRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="user_kind" jdbcType="VARCHAR" property="userKind"/>
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="time" jdbcType="INTEGER" property="time"/>
        <result column="config_id" jdbcType="BIGINT" property="configId"/>
        <result column="config_type" jdbcType="VARCHAR" property="configType"/>
        <result column="lock_hour" jdbcType="INTEGER" property="lockHour"/>
        <result column="unlock_datetime" jdbcType="TIMESTAMP" property="unlockDatetime"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.type
        , t.user_id
        , t.user_kind
        , t.login_name
        , t.time
        , t.config_id
        , t.config_type
        , t.lock_hour
        , t.unlock_datetime
        , t.creater
        , t.create_datetime
        , t.create_time
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userKind != null and userKind != '' ">
                AND t.user_kind = #{userKind, jdbcType=VARCHAR}
            </if>
            <if test="loginName != null and loginName != '' ">
                AND t.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="time != null">
                AND t.time = #{time, jdbcType=INTEGER}
            </if>
            <if test="configId != null">
                AND t.config_id = #{configId, jdbcType=BIGINT}
            </if>
            <if test="configType != null and configType != '' ">
                AND t.config_type = #{configType, jdbcType=VARCHAR}
            </if>
            <if test="lockHour != null">
                AND t.lock_hour = #{lockHour, jdbcType=INTEGER}
            </if>
            <if test="unlockDatetime != null">
                AND t.unlock_datetime = #{unlockDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.LoginErrorRecord">
        insert into tstd_login_error_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="userKind != null and userKind != '' ">
                user_kind,
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name,
            </if>
            <if test="time != null ">
                time,
            </if>
            <if test="configId != null ">
                config_id,
            </if>
            <if test="configType != null and configType != '' ">
                config_type,
            </if>
            <if test="lockHour != null ">
                lock_hour,
            </if>
            <if test="unlockDatetime != null ">
                unlock_datetime,
            </if>
            <if test="creater != null">
                creater,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="userId != null ">
                #{userId, jdbcType=BIGINT},
            </if>
            <if test="userKind != null and userKind != '' ">
                #{userKind,jdbcType=VARCHAR},
            </if>
            <if test="loginName != null and loginName != '' ">
                #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                #{time,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                #{configId,jdbcType=BIGINT},
            </if>
            <if test="configType != null and configType != '' ">
                #{configType, jdbcType=VARCHAR},
            </if>
            <if test="lockHour != null">
                #{lockHour,jdbcType=INTEGER},
            </if>
            <if test="unlockDatetime != null">
                #{unlockDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                #{creater, jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                #{createTime, jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_login_error_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LoginErrorRecord">
        update tstd_login_error_record
        <set>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="userId != null ">
                user_id = #{userId, jdbcType=BIGINT},
            </if>
            <if test="userKind != null and userKind != '' ">
                user_kind = #{userKind,jdbcType=VARCHAR},
            </if>
            <if test="loginName != null and loginName != '' ">
                login_name = #{loginName,jdbcType=VARCHAR},
            </if>
            <if test="time != null">
                time = #{time,jdbcType=INTEGER},
            </if>
            <if test="configId != null">
                config_id = #{configId,jdbcType=BIGINT},
            </if>
            <if test="configType != null and configType != '' ">
                config_type = #{configType, jdbcType=VARCHAR},
            </if>
            <if test="lockHour != null">
                lock_hour = #{lockHour,jdbcType=INTEGER},
            </if>
            <if test="unlockDatetime != null">
                unlock_datetime = #{unlockDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                creater = #{creater, jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createTime != null">
                create_time = #{createTime, jdbcType=BIGINT},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_login_error_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LoginErrorRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_login_error_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByLoginName" parameterType="com.std.core.pojo.domain.LoginErrorRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_login_error_record t
        where t.login_name=#{loginName} and t.user_kind=#{kind}
        order by t.id desc
        limit 1
    </select>
    <select id="selectLoginStatus" parameterType="java.lang.Long"
            resultMap="BaseResultMap">

        SELECT
        <include refid="Base_Column_List"/>
        FROM tstd_login_error_record t
        WHERE t.user_id =#{userId}
        and t.user_kind=#{kind}
        and (t.unlock_datetime <![CDATA[ >]]>#{date} or t.unlock_datetime is null ) ORDER BY t.id desc LIMIT 1

    </select>
</mapper>