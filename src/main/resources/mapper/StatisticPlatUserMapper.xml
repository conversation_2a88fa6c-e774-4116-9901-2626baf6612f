<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.StatisticPlatUserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.StatisticPlatUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="increase_count" jdbcType="INTEGER" property="increaseCount"/>
        <result column="login_count" jdbcType="INTEGER" property="loginCount"/>
        <result column="total_count" jdbcType="INTEGER" property="totalCount"/>
        <result column="date" jdbcType="DATE" property="date"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.increase_count
        , t.login_count
        , t.total_count
        , t.date
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="increaseCount != null">
                AND t.increase_count = #{increaseCount, jdbcType=INTEGER}
            </if>
            <if test="loginCount != null">
                AND t.login_count = #{loginCount, jdbcType=INTEGER}
            </if>
            <if test="totalCount != null">
                AND t.total_count = #{totalCount, jdbcType=INTEGER}
            </if>
            <if test="date != null">
                AND t.date = #{date, jdbcType=DATE}
            </if>
            <if test="dateStart != null">
                <![CDATA[AND t.date >= #{dateStart, jdbcType=DATE}]]>
            </if>
            <if test="dateEnd != null">
                <![CDATA[AND t.date <= #{dateEnd, jdbcType=DATE}]]>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.StatisticPlatUser">
        insert into ttask_statistic_plat_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="increaseCount != null ">
                increase_count,
              </if>
              <if test="loginCount != null ">
                login_count,
              </if>
              <if test="totalCount != null ">
                total_count,
              </if>
              <if test="date != null ">
                date,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="increaseCount != null">
                #{increaseCount,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null">
                #{loginCount,jdbcType=INTEGER},
            </if>
            <if test="totalCount != null">
                #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                #{date,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from ttask_statistic_plat_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.StatisticPlatUser">
        update ttask_statistic_plat_user
        <set>
            <if test="id != null">
                id = #{id,jdbcType=BIGINT},
            </if>
            <if test="increaseCount != null">
                increase_count = #{increaseCount,jdbcType=INTEGER},
            </if>
            <if test="loginCount != null">
                login_count = #{loginCount,jdbcType=INTEGER},
            </if>
            <if test="totalCount != null">
                total_count = #{totalCount,jdbcType=INTEGER},
            </if>
            <if test="date != null">
                date = #{date,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ttask_statistic_plat_user t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.StatisticPlatUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ttask_statistic_plat_user t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>