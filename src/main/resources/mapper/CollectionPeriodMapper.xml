<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionPeriodMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionPeriod">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="pit_id" jdbcType="BIGINT" property="pitId"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="plate_category" jdbcType="VARCHAR" property="plateCategory"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="plan_pic" jdbcType="VARCHAR" property="planPic"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>

        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="author_ids" jdbcType="BIGINT" property="authorIds"/>
        <result column="start_sell_date" jdbcType="TIMESTAMP" property="startSellDate"/>
        <result column="end_sell_date" jdbcType="TIMESTAMP" property="endSellDate"/>

        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="buy_max" jdbcType="INTEGER" property="buyMax"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="sold_status" jdbcType="VARCHAR" property="soldStatus"/>
        <result column="start_status" jdbcType="VARCHAR" property="startStatus"/>
        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>

        <result column="advance_mins" jdbcType="INTEGER" property="advanceMins"/>
        <result column="right_content" jdbcType="VARCHAR" property="rightContent"/>
        <result column="right_type" jdbcType="VARCHAR" property="rightType"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>

        <result column="priority_add_quantity_flag" jdbcType="VARCHAR" property="priorityAddQuantityFlag"/>
        <result column="priority_number" jdbcType="INTEGER" property="priorityNumber"/>
        <result column="priority_draw_param" jdbcType="INTEGER" property="priorityDrawParam"/>
        <result column="draw_param" jdbcType="INTEGER" property="drawParam"/>
        <result column="draw_note" jdbcType="VARCHAR" property="drawNote"/>
        <result column="transform_limit_time" jdbcType="INTEGER" property="transformLimitTime"/>

        <result column="word_flag" jdbcType="VARCHAR" property="wordFlag"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="create_type" jdbcType="VARCHAR" property="createType"/>
        <result column="meta_release_flag" jdbcType="VARCHAR" property="metaReleaseFlag"/>
        <result column="collection_number" jdbcType="INTEGER" property="collectionNumber"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.pit_id
        , t.category
        , t.plate_category
        , t.name
        , t.plan_pic
        , t.introduce
        , t.cover_file_url
        , t.file_type
        , t.tags
        , t.content
        , t.author_ids
        , t.start_sell_date
        , t.end_sell_date
        , t.total_quantity
        , t.remain_quantity
        , t.price
        , t.buy_max
        , t.order_no
        , t.status
        , t.sold_status
        , t.start_status
        , t.lock_time
        , t.advance_mins
        , t.right_content
        , t.right_type
        , t.updater
        , t.updater_name
        , t.update_datetime

        , t.priority_add_quantity_flag
        , t.priority_number
        , t.priority_draw_param
        , t.draw_param
        , t.draw_note
        , t.transform_limit_time
        , t.word_flag
        , t.channel_id
        , t.create_type
        , t.meta_release_flag
        , t.collection_number
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="pitId != null ">
                AND t.pit_id = #{pitId, jdbcType=BIGINT}
            </if>
            <if test="category != null and category != '' ">
                AND t.category = #{category, jdbcType=VARCHAR}
            </if>
            <if test="categoryList != null and categoryList.size() != 0 ">
                AND t.category in
                <foreach item="item" index="index" collection="categoryList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                or t.id in (select period_id from nft_period_channel_word where word like concat('%', #{keywords, jdbcType=VARCHAR},'%'))
                or t.author_ids in (select id from nft_company where name like concat('%', #{keywords, jdbcType=VARCHAR},'%'))
                or t.tags like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="fileType != null and fileType != '' ">
                AND t.file_type = #{fileType, jdbcType=VARCHAR}
            </if>
            <if test="startSellDate != null">
                AND t.start_sell_date = #{startSellDate, jdbcType=TIMESTAMP}
            </if>
            <if test="startSellDateStart != null ">
                <![CDATA[AND ((date_format(t.start_sell_date, '%Y-%m-%d') >= date_format(#{startSellDateStart},'%Y-%m-%d') AND t.sold_status ='1') or t.sold_status = '0')]]>
            </if>
            <if test="startSellDateStartPlan != null">
                <![CDATA[AND date_format(t.start_sell_date, '%Y-%m-%d') >= date_format(#{startSellDateStartPlan},'%Y-%m-%d') AND t.sold_status in ('0','1')]]>
            </if>
            <if test="sellDateStart != null ">
                <![CDATA[AND date_format(t.start_sell_date, '%Y-%m-%d') >= date_format(#{sellDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="sellDateEnd != null ">
                <![CDATA[AND date_format(t.start_sell_date, '%Y-%m-%d') <= date_format(#{sellDateEnd},'%Y-%m-%d') ]]>
            </if>
            <if test="endSellDate != null">
                AND t.end_sell_date = #{endSellDate, jdbcType=TIMESTAMP}
            </if>
            <if test="endSellDateEnd != null">
                <![CDATA[AND t.end_sell_date <= #{endSellDateEnd, jdbcType=TIMESTAMP}]]>
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="soldFlag != null and soldFlag != '' ">
                AND ((t.total_quantity <![CDATA[ >= ]]> remain_quantity and t.start_status =1)
                or (t.total_quantity <![CDATA[ > ]]> remain_quantity and t.start_status =2)
                or (t.total_quantity <![CDATA[ = ]]> remain_quantity and t.start_status =0 ))
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="buyMax != null">
                AND t.buy_max = #{buyMax, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                AND t.sold_status = #{soldStatus, jdbcType=VARCHAR}
            </if>
            <if test="startStatus != null and startStatus != '' ">
                AND t.start_status = #{startStatus, jdbcType=VARCHAR}
            </if>
            <if test="startStatusList != null and startStatusList.size() != 0 ">
                AND t.start_status in
                <foreach item="item" index="index" collection="startStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="authorIds != null ">
                AND t.author_ids = #{authorIds, jdbcType=BIGINT}
            </if>
            <if test="lockTime != null">
                AND t.lock_time = #{lockTime, jdbcType=INTEGER}
            </if>
            <if test="rightContent != null and rightContent != '' ">
                AND t.right_content = #{rightContent, jdbcType=VARCHAR}
            </if>
            <if test="rightType != null and rightType != '' ">
                AND t.right_type = #{rightType, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="priorityNumber != null">
                AND t.priority_number = #{priorityNumber, jdbcType=INTEGER}
            </if>
            <if test="priorityDrawParam != null">
                AND t.priority_draw_param = #{priorityDrawParam, jdbcType=INTEGER}
            </if>
            <if test="drawParam != null">
                AND t.draw_param = #{drawParam, jdbcType=INTEGER}
            </if>
            <if test="transformLimitTime != null">
                AND t.transform_limit_time = #{transformLimitTime, jdbcType=INTEGER}
            </if>
            <if test="wordFlag != null and wordFlag != '' ">
                AND t.word_flag = #{wordFlag, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="createType != null and createType != '' ">
                AND t.create_type = #{createType, jdbcType=VARCHAR}
            </if>
            <if test="metaReleaseFlag != null and metaReleaseFlag != '' ">
                AND t.meta_release_flag = #{metaReleaseFlag, jdbcType=VARCHAR}
            </if>
            <if test="collectionNumber != null ">
                AND t.collection_number = #{collectionNumber, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionPeriod" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection_period
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="pitId != null ">
                pit_id,
            </if>
            <if test="category != null and category != '' ">
                category,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="planPic != null and planPic != '' ">
                plan_pic,
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce,
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
            </if>
            <if test="fileType != null and fileType != '' ">
                file_type,
            </if>
            <if test="tags != null and tags != '' ">
                tags,
            </if>
            <if test="authorIds != null ">
                author_ids,
            </if>
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="startSellDate != null ">
                start_sell_date,
            </if>
            <if test="endSellDate != null">
                end_sell_date,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="buyMax != null">
                buy_max,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status,
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="advanceMins != null">
                advance_mins,
            </if>
            <if test="rightContent != null and rightContent != '' ">
                right_content,
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="priorityAddQuantityFlag != null">
                priority_add_quantity_flag,
            </if>
            <if test="priorityNumber != null">
                priority_number,
            </if>
            <if test="priorityDrawParam != null">
                priority_draw_param,
            </if>
            <if test="drawParam != null">
                draw_param,
            </if>
            <if test="drawNote != null">
                draw_note,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="wordFlag != null and wordFlag != '' ">
                word_flag,
            </if>
            <if test="channelId != null ">
                channel_id,
            </if>
            <if test="createType != null and createType != '' ">
                create_type,
            </if>
            <if test="metaReleaseFlag != null and metaReleaseFlag != '' ">
                meta_release_flag,
            </if>
            <if test="collectionNumber != null ">
                collection_number,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="pitId != null ">
                #{pitId, jdbcType=BIGINT},
            </if>
            <if test="category != null and category != '' ">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="planPic != null and planPic != '' ">
                #{planPic,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != '' ">
                #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="tags != null and tags != '' ">
                #{tags,jdbcType=VARCHAR},
            </if>
            <if test="authorIds != null">
                #{authorIds, jdbcType=BIGINT},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="startSellDate != null">
                #{startSellDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endSellDate != null">
                #{endSellDate, jdbcType=TIMESTAMP},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="buyMax != null">
                #{buyMax, jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                #{soldStatus, jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="rightContent != null and rightContent != '' ">
                #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                #{rightType, jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="priorityAddQuantityFlag != null">
                #{priorityAddQuantityFlag, jdbcType=VARCHAR},
            </if>
            <if test="priorityNumber != null">
                #{priorityNumber, jdbcType=INTEGER},
            </if>
            <if test="priorityDrawParam != null">
                #{priorityDrawParam, jdbcType=INTEGER},
            </if>
            <if test="drawParam != null">
                #{drawParam, jdbcType=INTEGER},
            </if>
            <if test="drawNote != null">
                #{drawNote, jdbcType=VARCHAR},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="wordFlag != null and wordFlag != '' ">
                #{wordFlag, jdbcType=VARCHAR},
            </if>
            <if test="channelId != null ">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="createType != null and createType != '' ">
                #{createType, jdbcType=VARCHAR},
            </if>
            <if test="metaReleaseFlag != null and metaReleaseFlag != '' ">
                #{metaReleaseFlag, jdbcType=VARCHAR},
            </if>
            <if test="collectionNumber != null ">
                #{collectionNumber, jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <insert id="creatPeriodSend">
        insert into nft_collection_period_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                send_flag,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="sendFlag != null and sendFlag != '' ">
                #{sendFlag, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_period
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionPeriod">
        update nft_collection_period
        <set>
            <if test="pitId != null ">
                pit_id = #{pitId, jdbcType=BIGINT},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category = #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="planPic != null and planPic != '' ">
                plan_pic = #{planPic,jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != '' ">
                file_type = #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="tags != null and tags != '' ">
                tags = #{tags,jdbcType=VARCHAR},
            </if>
            <if test="authorIds != null">
                author_ids = #{authorIds, jdbcType=BIGINT},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="startSellDate != null">
                start_sell_date = #{startSellDate,jdbcType=TIMESTAMP},
            </if>
            <if test="endSellDate != null">
                end_sell_date = #{endSellDate, jdbcType=TIMESTAMP},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="buyMax != null">
                buy_max = #{buyMax, jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                sold_status = #{soldStatus, jdbcType=VARCHAR},
            </if>
            <if test="startStatus != null and startStatus != '' ">
                start_status = #{startStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="advanceMins != null">
                advance_mins = #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="rightContent != null and rightContent != '' ">
                right_content = #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type = #{rightType, jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="priorityAddQuantityFlag != null and priorityAddQuantityFlag != ''">
                priority_add_quantity_flag = #{priorityAddQuantityFlag, jdbcType=VARCHAR},
            </if>
            <if test="priorityNumber != null">
                priority_number = #{priorityNumber, jdbcType=INTEGER},
            </if>
            <if test="priorityDrawParam != null">
                priority_draw_param = #{priorityDrawParam, jdbcType=INTEGER},
            </if>
            <if test="drawParam != null">
                draw_param = #{drawParam, jdbcType=INTEGER},
            </if>
            <if test="drawNote != null and drawNote != ''">
                draw_note = #{drawNote, jdbcType=VARCHAR},
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time = #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="wordFlag != null and wordFlag != ''">
                word_flag = #{wordFlag, jdbcType=VARCHAR},
            </if>
            <if test="channelId != null ">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="createType != null and createType != '' ">
                create_type = #{createType, jdbcType=VARCHAR},
            </if>
            <if test="metaReleaseFlag != null and metaReleaseFlag != '' ">
                meta_release_flag = #{metaReleaseFlag, jdbcType=VARCHAR},
            </if>
            <if test="collectionNumber != null ">
                collection_number = #{collectionNumber, jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateRemainQuantity" parameterType="com.std.core.pojo.domain.CollectionPeriod">
        update nft_collection_period
        SET remain_quantity = remain_quantity - #{buyQuantity,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
          AND remain_quantity - #{buyQuantity,jdbcType=INTEGER} <![CDATA[ >=]]> 0
    </update>

    <update id="batchUpdate">
        <foreach collection="list" item="item" index="index" separator=";">
            update nft_collection_period
            <set>
                <if test="item.name != null and item.name != '' ">
                    name = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.planPic != null and item.planPic != '' ">
                    plan_pic = #{item.planPic,jdbcType=VARCHAR},
                </if>
                <if test="item.introduce != null and item.introduce != '' ">
                    introduce = #{item.introduce,jdbcType=VARCHAR},
                </if>
                <if test="item.coverFileUrl != null and item.coverFileUrl != '' ">
                    cover_file_url = #{item.coverFileUrl,jdbcType=VARCHAR},
                </if>
                <if test="item.fileType != null and item.fileType != '' ">
                    file_type = #{item.fileType,jdbcType=VARCHAR},
                </if>
                <if test="item.tags != null and item.tags != '' ">
                    tags = #{item.tags,jdbcType=VARCHAR},
                </if>
                <if test="item.authorIds != null">
                    author_ids = #{item.authorIds, jdbcType=BIGINT},
                </if>
                <if test="item.content != null and item.content != '' ">
                    content = #{item.content,jdbcType=VARCHAR},
                </if>
                <if test="item.startSellDate != null">
                    start_sell_date = #{item.startSellDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.endSellDate != null">
                    end_sell_date = #{item.endSellDate, jdbcType=TIMESTAMP},
                </if>
                <if test="item.totalQuantity != null">
                    total_quantity = #{item.totalQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.remainQuantity != null">
                    remain_quantity = #{item.remainQuantity,jdbcType=INTEGER},
                </if>
                <if test="item.price != null">
                    price = #{item.price,jdbcType=DECIMAL},
                </if>
                <if test="item.buyMax != null">
                    buy_max = #{item.buyMax, jdbcType=INTEGER},
                </if>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo, jdbcType=INTEGER},
                </if>
                <if test="item.status != null and item.status != '' ">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.soldStatus != null and item.soldStatus != '' ">
                    sold_status = #{item.soldStatus, jdbcType=VARCHAR},
                </if>
                <if test="item.startStatus != null and item.startStatus != '' ">
                    start_status = #{item.startStatus, jdbcType=VARCHAR},
                </if>
                <if test="item.lockTime != null">
                    lock_time = #{item.lockTime, jdbcType=INTEGER},
                </if>
                <if test="item.advanceMins != null">
                    advance_mins = #{item.advanceMins, jdbcType=INTEGER},
                </if>
                <if test="item.rightContent != null and item.rightContent != '' ">
                    right_content = #{item.rightContent, jdbcType=VARCHAR},
                </if>
                <if test="item.rightType != null and item.rightType != '' ">
                    right_type = #{item.rightType, jdbcType=VARCHAR},
                </if>
                <if test="item.updater != null">
                    updater = #{item.updater,jdbcType=BIGINT},
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    updater_name = #{item.updaterName,jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    update_datetime = #{item.updateDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.priorityNumber != null">
                    priority_number = #{item.priorityNumber, jdbcType=INTEGER},
                </if>
                <if test="item.priorityDrawParam != null">
                    priority_draw_param = #{item.priorityDrawParam, jdbcType=INTEGER},
                </if>
                <if test="item.drawParam != null">
                    draw_param = #{item.drawParam, jdbcType=INTEGER},
                </if>
                <if test="item.drawNote != null">
                    draw_note = #{item.drawNote, jdbcType=VARCHAR},
                </if>
                <if test="item.transformLimitTime != null">
                    transform_limit_time = #{item.transformLimitTime, jdbcType=INTEGER},
                </if>
                <if test="item.wordFlag != null and item.wordFlag != ''">
                    word_flag = #{item.wordFlag, jdbcType=VARCHAR},
                </if>
                <if test="item.channelId != null ">
                    channel_id = #{item.channelId, jdbcType=BIGINT},
                </if>
                <if test="item.orderNo != null">
                    order_no = #{item.orderNo, jdbcType=INTEGER},
                </if>
                <if test="item.createType != null and item.createType != '' ">
                    create_type = #{item.createType, jdbcType=VARCHAR},
                </if>
                <if test="item.collectionNumber != null ">
                    collection_number = #{item.collectionNumber, jdbcType=BIGINT},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectPeriodStart" resultType="java.lang.Long">
        SELECT id
        FROM nft_collection_period
        WHERE start_sell_date <![CDATA[<=]]> #{startSellDate}
          AND `start_status` = #{startStatus}
          AND `status` = '1'
    </select>

    <update id="updatePeriodDown">
        UPDATE nft_collection_period
        set status='2'
        where sold_status = '1';
    </update>

    <update id="updatePeriodSend">
        update nft_collection_period_send
        <set>
            <if test="sendFlag != null and sendFlag != '' ">
                send_flag = #{sendFlag, jdbcType=VARCHAR},
            </if>
            <if test="sendDatetime != null">
                send_datetime = #{sendDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectPeriodEnd" resultType="java.lang.Long">
        SELECT id
        FROM nft_collection_period
        WHERE remain_quantity <![CDATA[<=]]> 0
          AND `start_status` = '1'
          and sold_status = '0'
    </select>

    <update id="updatePeriodStart">
        UPDATE nft_collection_period
        SET `start_status` = '1'
        WHERE id IN
        <foreach item="item" index="index" collection="idList" open="(" separator=","
                close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>
    <update id="updatePeriodEnd">
        UPDATE nft_collection_period
        SET `start_status` = '2',
        sold_status='1'
        WHERE 1=1
        <if test="idList != null and idList.size() != 0 ">
            and id IN
            <foreach item="item" index="index" collection="idList" open="(" separator=","
                    close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>
    </update>
    <update id="updateRemainPayOrderCancel" parameterType="com.std.core.pojo.domain.CollectionPeriod">
        update nft_collection_period
        SET remain_quantity = remain_quantity + #{remainQuantity,jdbcType=INTEGER},
            start_status    = #{startStatus},
            sold_status='0'
        where id = #{id}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <sql id="Simple_Column_List">
        t
        .
        id
        , t.pit_id
        , t.category
        , t.name
        , t.start_sell_date
        , t.total_quantity
        , t.remain_quantity
        , t.price
        , t.buy_max
        , t.author_ids
        , t.status
        , t.sold_status
        , t.start_status
        , t.lock_time
        , t.advance_mins
        , t.priority_number
        , t.priority_draw_param
        , t.draw_param
        , t.channel_id
        , t.meta_release_flag
    </sql>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Simple_Column_List"/>
        from nft_collection_period t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriod"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.CollectionPeriod"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="Base_Front_Column_List">
        t
        .
        id
        , t.category
        , t.name
        , t.introduce
        , t.cover_file_url coverFileUrl
        , t.file_type fileType
        , t.tags
        , t.author_ids authorIds
        , t.content
        , t.start_sell_date startSellDate
        , t.end_sell_date endSellDate
        , t.total_quantity totalQuantity
        , t.remain_quantity remainQuantity
        , t.price
        , t.status
        , t.sold_status soldStatus
        , t.start_status startStatus
        , t.lock_time lockTime
        , t.right_content rightContent
        , t.right_type rightType
        , t.advance_mins advanceMins
        , t.buy_max buyMax
        , t.draw_note playNote
        , t.priority_add_quantity_flag  priorityAddQuantityFlag
        , t.word_flag wordFlag
        , t.collection_number collectionNumber
    </sql>

    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.CollectionPeriodPageFrontRes">
        select
        <include refid="Base_Front_Column_List"/>
        from nft_collection_period t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByConditionFrontLaster" resultType="com.std.core.pojo.response.CollectionPeriodPageFrontRes">
        select t.id,
               t.update_datetime as updateDatetime
        from nft_collection_period t
        where t.status = '1'
          and channel_id = '1'
        order by update_datetime desc limit 1
    </select>

    <select id="selectFrontList" resultType="com.std.core.pojo.response.CollectionPeriodListRes">
        select
        <include refid="Base_Front_Column_List"/>
        from nft_collection_period t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="Simple_Base_Front_Column_List">
        t
        .
        id
        , t.name
        , t.cover_file_url coverFileUrl
        , t.file_type fileType
        , t.start_status startStatus
        , t.start_sell_date startSellDate
    </sql>

    <select id="selectSimpleFrontList" resultType="com.std.core.pojo.response.CollectionPeriodByPlateRes">
        select
        <include refid="Simple_Base_Front_Column_List"/>
        from nft_collection_period t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPrimaryKeyFront" resultType="com.std.core.pojo.response.CollectionPeriodDetailFrontRes">
        select
        <include refid="Base_Front_Column_List"/>
        from nft_collection_period t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="getQuickStartPeriod" resultType="com.std.core.pojo.domain.CollectionPeriodSend">
        select t.category
             , t.name
             , t.start_sell_date startSellDate
             , ta.send_flag      sendFlag
             , ta.id
             , ta.period_id      periodId
        from nft_collection_period t
                 inner JOIN nft_collection_period_send ta on t.id = ta.period_id
        where DATE_ADD(t.start_sell_date, INTERVAL -#{sendHour} HOUR) <![CDATA[ <=]]> #{sendDatetime}
          and ta.send_flag = '1'
          and t.status = '1';
    </select>

    <select id="selectPeriodDrawStrawsEnd" resultType="com.std.core.pojo.domain.CollectionPeriod"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        where t.category='3' and t.status='1' and t.start_status='1' and DATE_FORMAT(t.end_sell_date,'%Y-%m-%d %H')
        <![CDATA[ <=]]>
        DATE_FORMAT(#{date},'%Y-%m-%d %H')
    </select>

    <resultMap id="TreasurePlanMap" type="com.std.core.pojo.response.TreasurePlanHomeListRes">
        <result column="startDate" jdbcType="VARCHAR" property="startDate"/>

        <collection property="resList" ofType="com.std.core.pojo.response.TreasurePlanHomeDetailRes">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="category" jdbcType="VARCHAR" property="category"/>
            <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
            <result column="cover_file_url" jdbcType="VARCHAR" property="pic"/>
            <result column="name" jdbcType="VARCHAR" property="title"/>
            <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
            <result column="unit" jdbcType="VARCHAR" property="totalQuantityUnit"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="start_sell_date" jdbcType="TIMESTAMP" property="startSellDate"/>
        </collection>
    </resultMap>

    <select id="treasurePlanListHome" resultType="com.std.core.pojo.response.TreasurePlanHomeListRes"
            resultMap="TreasurePlanMap">
        SELECT date_format(t.start_sell_date, '%m-%d') startDate,
        t.id,
        t.category,
        t.file_type,
        t.cover_file_url,
        t.`name`,
        t.total_quantity,
        t.price,
        t.start_sell_date,
        CASE
        WHEN t.category = '0' THEN
        "个"
        ELSE "份"
        END unit
        FROM nft_collection_period t
        WHERE t.`status` = '1'
        AND date_format(t.start_sell_date, '%Y-%m-%d') <![CDATA[ >=]]> date_format(#{date}, '%Y-%m-%d')
        <if test="channelId != null">
            AND t.channel_id = #{channelId, jdbcType=BIGINT}
        </if>
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
        </if>
        ORDER BY t.start_sell_date;
    </select>

    <select id="selectAuctionOss" resultType="com.std.core.pojo.domain.CollectionPeriod"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period t
        inner join nft_period_auction ta on ta.period_id =t.id
        <include refid="where_condition_auction"/>
        order by t.id desc
    </select>
    <select id="selectAddedCount" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period t
        where t.status = '1'
          and t.channel_id = #{channelId}
    </select>

    <select id="selectIngCountByAuthorIds" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period t
        where t.status = '1'
          and start_status in ('0', '1')
          and t.author_ids = #{authorIds}
    </select>

    <select id="selectOnlineCountByPlateCategory" resultType="java.lang.Integer">
        select count(1)
        from nft_collection_period t
        where t.status = '1'
          and t.plate_category = #{plateCategory}
    </select>

    <select id="selectChannelMerchant" resultType="java.lang.Long">
        select channel_id
        from nft_collection_period
        where author_ids = #{companyId}
        group by channel_id
    </select>

    <resultMap id="TreasurePlanCompanyMap" type="com.std.core.pojo.response.TreasurePlanCompanyListRes">
        <result column="startDate" jdbcType="VARCHAR" property="startDate"/>

        <collection property="resList" ofType="com.std.core.pojo.response.TreasurePlanCompanyDetailRes">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="category" jdbcType="VARCHAR" property="category"/>
            <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
            <result column="cover_file_url" jdbcType="VARCHAR" property="pic"/>
            <result column="name" jdbcType="VARCHAR" property="title"/>
            <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
            <result column="unit" jdbcType="VARCHAR" property="totalQuantityUnit"/>
            <result column="price" jdbcType="DECIMAL" property="price"/>
            <result column="start_sell_date" jdbcType="TIMESTAMP" property="startSellDate"/>
            <result column="tags" jdbcType="VARCHAR" property="tags"/>
            <result column="status" jdbcType="VARCHAR" property="status"/>
            <result column="author_ids" jdbcType="BIGINT" property="authorIds"/>
        </collection>
    </resultMap>

    <select id="treasurePlanCompanyListHome" resultMap="TreasurePlanCompanyMap">

        SELECT date_format(t.start_sell_date, '%m-%d') startDate,
        t.id,
        t.category,
        t.file_type,
        t.cover_file_url,
        t.`name`,
        t.total_quantity,
        t.price,
        t.start_sell_date,
        t.tags,
        t.author_ids,
        t.status,
        CASE
        WHEN t.category = '0' THEN
        "个"
        ELSE "份"
        END unit
        FROM nft_collection_period t
        WHERE
        date_format(t.start_sell_date, '%Y-%m') = #{planDate}
        AND t.status in ('1','3')
        <if test="channelId != null">
            AND t.channel_id = #{channelId, jdbcType=BIGINT}
        </if>
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
        </if>

    </select>
    <select id="selectApplyForAndToApplyFor" resultType="java.lang.Integer">
        select count(1)
        FROM nft_collection_period t
        where DATE_FORMAT(t.start_sell_date
                  , '%Y-%m-%d') = DATE_FORMAT(#{startSellDate}
                  , '%Y-%m-%d')
          and t.status in ('1', '3')
          and t.plate_category = #{plateCategory};
    </select>
    <select id="selectMaxOrderNo" resultType="java.lang.Integer">
        select COALESCE(max(t.order_no), 0)
        from nft_collection_period t;
    </select>


    <sql id="where_condition_auction">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="category != null and category != '' ">
                AND t.category = #{category, jdbcType=VARCHAR}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="soldStatus != null and soldStatus != '' ">
                AND t.sold_status = #{soldStatus, jdbcType=VARCHAR}
            </if>
            <if test="startStatus != null and startStatus != '' ">
                AND t.start_status = #{startStatus, jdbcType=VARCHAR}
            </if>
            <if test="authorIds != null ">
                AND t.author_ids = #{authorIds, jdbcType=BIGINT}
            </if>
            <if test="auctionStatus != null and auctionStatus != '' ">
                AND ta.status = #{auctionStatus, jdbcType=VARCHAR}
            </if>
            <if test="lastUserId != null and lastUserId != '' ">
                AND ta.last_user_id = #{lastUserId, jdbcType=VARCHAR}
            </if>
            <if test="sellDateStart != null and sellDateStart != '' ">
                <![CDATA[AND date_format(t.start_sell_date, '%Y-%m-%d') >= date_format(#{sellDateStart},'%Y-%m-%d') ]]>
            </if>
            <if test="sellDateEnd != null and sellDateEnd != '' ">
                <![CDATA[AND date_format(t.start_sell_date, '%Y-%m-%d') <= date_format(#{sellDateEnd},'%Y-%m-%d') ]]>
            </if>
            <if test="channelId != null ">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
        </trim>
    </sql>


</mapper>