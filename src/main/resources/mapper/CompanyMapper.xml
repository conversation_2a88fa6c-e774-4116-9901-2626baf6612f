<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CompanyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Company">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="licence_no" jdbcType="VARCHAR" property="licenceNo"/>
        <result column="licence_url" jdbcType="VARCHAR" property="licenceUrl"/>
        <result column="divide_flag" jdbcType="VARCHAR" property="divideFlag"/>

        <result column="legal_licence_type" jdbcType="VARCHAR" property="legalLicenceType"/>
        <result column="legal_licence_no" jdbcType="VARCHAR" property="legalLicenceNo"/>
        <result column="legal_licence_front_url" jdbcType="VARCHAR" property="legalLicenceFrontUrl"/>
        <result column="legal_licence_back_url" jdbcType="VARCHAR" property="legalLicenceBackUrl"/>
        <result column="legal_real_name" jdbcType="VARCHAR" property="legalRealName"/>
        <result column="legal_mobile" jdbcType="VARCHAR" property="legalMobile"/>

        <result column="open_account_licence_no" jdbcType="VARCHAR" property="openAccountLicenceNo"/>
        <result column="open_account_licence_url" jdbcType="VARCHAR" property="openAccountLicenceUrl"/>
        <result column="settle_card_type" jdbcType="VARCHAR" property="settleCardType"/>
        <result column="settle_card_no" jdbcType="VARCHAR" property="settleCardNo"/>
        <result column="settle_bank_code" jdbcType="VARCHAR" property="settleBankCode"/>
        <result column="province" jdbcType="VARCHAR" property="province"/>
        <result column="city" jdbcType="VARCHAR" property="city"/>
        <result column="district" jdbcType="VARCHAR" property="district"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>

        <result column="contact_name" jdbcType="VARCHAR" property="contactName"/>
        <result column="contact_mobile" jdbcType="VARCHAR" property="contactMobile"/>
        <result column="contact_licence_no" jdbcType="VARCHAR" property="contactLicenceNo"/>
        <result column="contact_email" jdbcType="VARCHAR" property="contactEmail"/>
        <result column="service_phone" jdbcType="VARCHAR" property="servicePhone"/>

        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="introduce" jdbcType="VARCHAR" property="introduce"/>
        <result column="introduce_pdf" jdbcType="VARCHAR" property="introducePdf"/>
        <result column="plat_divide_rate" jdbcType="DECIMAL" property="platDivideRate"/>
        <result column="request_no" jdbcType="VARCHAR" property="requestNo"/>
        <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="divide_status" jdbcType="VARCHAR" property="divideStatus"/>

        <result column="location" jdbcType="VARCHAR" property="location"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="bond_price" jdbcType="DECIMAL" property="bondPrice"/>
        <result column="is_look" jdbcType="VARCHAR" property="isLook"/>
        <result column="main_user" jdbcType="BIGINT" property="mainUser"/>
        <result column="plat_flag" jdbcType="BIGINT" property="platFlag"/>
        <result column="independence_plat_flag" jdbcType="BIGINT" property="independencePlatFlag"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.short_name
        , t.type
        , t.licence_no
        , t.licence_url
        , t.divide_flag

        , t.legal_licence_type
        , t.legal_licence_no
        , t.legal_licence_front_url
        , t.legal_licence_back_url
        , t.legal_real_name
        , t.legal_mobile

        , t.open_account_licence_no
        , t.open_account_licence_url
        , t.settle_card_type
        , t.settle_card_no
        , t.settle_bank_code
        , t.province
        , t.city
        , t.district
        , t.address

        , t.contact_name
        , t.contact_mobile
        , t.contact_licence_no
        , t.contact_email
        , t.service_phone

        , t.logo
        , t.cover_file_url
        , t.introduce
        , t.introduce_pdf
        , t.plat_divide_rate
        , t.request_no
        , t.merchant_no

        , t.status
        , t.divide_status
        , t.location
        , t.order_no
        , t.updater
        , t.update_datetime
        , t.remark
        , t.bond_price
        , t.is_look
        , t.main_user
        , t.plat_flag
        , t.independence_plat_flag
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (t.name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                OR t.short_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="nameStr != null and nameStr != '' ">
                AND t.name = #{nameStr, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                AND t.divide_flag = #{divideFlag, jdbcType=VARCHAR}
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                AND t.cover_file_url = #{coverFileUrl, jdbcType=VARCHAR}
            </if>
            <if test="introducePdf != null and introducePdf != '' ">
                AND t.introduce_pdf = #{introducePdf, jdbcType=VARCHAR}
            </if>
            <if test="platDivideRate != null">
                AND t.plat_divide_rate = #{platDivideRate, jdbcType=DECIMAL}
            </if>
            <if test="requestNo != null and requestNo != '' ">
                AND t.request_no = #{requestNo, jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                AND t.merchant_no = #{merchantNo, jdbcType=VARCHAR}
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                AND t.licence_no = #{licenceNo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                AND t.divide_status = #{divideStatus, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="bondPrice != null ">
                AND t.bond_price = #{bondPrice, jdbcType=DECIMAL}
            </if>
            <if test="isLook != null and isLook != '' ">
                AND t.is_look = #{isLook, jdbcType=VARCHAR}
            </if>
            <if test="mainUser != null ">
                AND t.main_user = #{mainUser, jdbcType=BIGINT}
            </if>
            <if test="platFlag != null and platFlag != '' ">
                AND t.plat_flag = #{platFlag, jdbcType=VARCHAR}
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                AND t.independence_plat_flag = #{independencePlatFlag, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">

            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="shortName != null and shortName != '' ">
                AND t.short_name like concat('%',#{shortName, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                AND t.legal_mobile like concat('%',#{legalMobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                AND t.cover_file_url = #{coverFileUrl, jdbcType=VARCHAR}
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                AND t.licence_no = #{licenceNo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                AND t.divide_status = #{divideStatus, jdbcType=VARCHAR}
            </if>
            <if test="location != null and location != '' ">
                AND t.location = #{location, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                t.legal_mobile like concat('%',#{legalMobile, jdbcType=VARCHAR},'%') or
                t.name like concat('%',#{name, jdbcType=VARCHAR},'%') or
                t.legal_real_name like concat('%',#{legalRealName, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="bondPrice != null ">
                AND t.bond_price = #{bondPrice, jdbcType=DECIMAL}
            </if>
            <if test="isLook != null and isLook != '' ">
                AND t.is_look = #{isLook, jdbcType=VARCHAR}
            </if>
            <if test="mainUser != null ">
                AND t.main_user = #{mainUser, jdbcType=BIGINT}
            </if>
            <if test="platFlag != null and platFlag != '' ">
                AND t.plat_flag = #{platFlag, jdbcType=VARCHAR}
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                AND t.independence_plat_flag = #{independencePlatFlag, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Company" useGeneratedKeys="true" keyProperty="id">
        insert into nft_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                licence_no,
            </if>
            <if test="licenceUrl != null and licenceUrl != '' ">
                licence_url,
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                divide_flag,
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                legal_licence_type,
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                legal_licence_no,
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                legal_licence_front_url,
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                legal_licence_back_url,
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                legal_real_name,
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                legal_mobile,
            </if>
            <if test="openAccountLicenceNo != null and openAccountLicenceNo != '' ">
                open_account_licence_no,
            </if>
            <if test="openAccountLicenceUrl != null and openAccountLicenceUrl != '' ">
                open_account_licence_url,
            </if>
            <if test="settleCardType != null and settleCardType != '' ">
                settle_card_type,
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no,
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code,
            </if>
            <if test="province != null and province != '' ">
                province,
            </if>
            <if test="city != null and city != '' ">
                city,
            </if>
            <if test="district != null and district != '' ">
                district,
            </if>
            <if test="address != null and address != '' ">
                address,
            </if>
            <if test="contactName != null and contactName != '' ">
                contact_name,
            </if>
            <if test="contactMobile != null and contactMobile != '' ">
                contact_mobile,
            </if>
            <if test="contactLicenceNo != null and contactLicenceNo != '' ">
                contact_licence_no,
            </if>
            <if test="contactEmail != null and contactEmail != '' ">
                contact_email,
            </if>
            <if test="servicePhone != null and servicePhone != '' ">
                service_phone,
            </if>
            <if test="logo != null and logo != '' ">
                logo,
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce,
            </if>
            <if test="introducePdf != null and introducePdf != '' ">
                introduce_pdf,
            </if>
            <if test="platDivideRate != null ">
                plat_divide_rate,
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no,
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                divide_status,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="bondPrice != null ">
                bond_price,
            </if>
            <if test="isLook != null and isLook != '' ">
                is_look,
            </if>
            <if test="mainUser != null ">
                main_user,
            </if>
            <if test="platFlag != null and platFlag != '' ">
                plat_flag,
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                independence_plat_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null and shortName != '' ">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                #{licenceNo,jdbcType=VARCHAR},
            </if>
            <if test="licenceUrl != null and licenceUrl != '' ">
                #{licenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                #{divideFlag,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                #{legalLicenceType,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                #{legalLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                #{legalLicenceFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                #{legalLicenceBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                #{legalRealName,jdbcType=VARCHAR},
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                #{legalMobile,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceNo != null and openAccountLicenceNo != '' ">
                #{openAccountLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceUrl != null and openAccountLicenceUrl != '' ">
                #{openAccountLicenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="settleCardType != null and settleCardType != '' ">
                #{settleCardType,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="district != null and district != '' ">
                #{district,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null and contactName != '' ">
                #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null and contactMobile != '' ">
                #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactLicenceNo != null and contactLicenceNo != '' ">
                #{contactLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null and contactEmail != '' ">
                #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null and servicePhone != '' ">
                #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl, jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="introducePdf != null and introducePdf != '' ">
                #{introducePdf, jdbcType=VARCHAR},
            </if>
            <if test="platDivideRate != null">
                #{platDivideRate,jdbcType=DECIMAL},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                #{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                #{divideStatus, jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bondPrice != null ">
                #{bondPrice, jdbcType=DECIMAL},
            </if>
            <if test="isLook != null and isLook != '' ">
                #{isLook, jdbcType=VARCHAR},
            </if>
            <if test="mainUser != null ">
                #{mainUser, jdbcType=BIGINT},
            </if>
            <if test="platFlag != null and platFlag != '' ">
                #{platFlag, jdbcType=VARCHAR},
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                #{independencePlatFlag, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_company
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Company">
        update nft_company
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                licence_no = #{licenceNo,jdbcType=VARCHAR},
            </if>
            <if test="licenceUrl != null and licenceUrl != '' ">
                licence_url = #{licenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                divide_flag = #{divideFlag,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                legal_licence_type = #{legalLicenceType,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                legal_licence_no = #{legalLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                legal_licence_front_url = #{legalLicenceFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                legal_licence_back_url = #{legalLicenceBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                legal_real_name = #{legalRealName,jdbcType=VARCHAR},
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                legal_mobile = #{legalMobile,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceNo != null and openAccountLicenceNo != '' ">
                open_account_licence_no = #{openAccountLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceUrl != null and openAccountLicenceUrl != '' ">
                open_account_licence_url = #{openAccountLicenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="settleCardType != null and settleCardType != '' ">
                settle_card_type = #{settleCardType,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no = #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code = #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="district != null and district != '' ">
                district = #{district,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null and contactName != '' ">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null and contactMobile != '' ">
                contact_mobile = #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactLicenceNo != null and contactLicenceNo != '' ">
                contact_licence_no = #{contactLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null and contactEmail != '' ">
                contact_email = #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null and servicePhone != '' ">
                service_phone = #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl, jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="introducePdf != null and introducePdf != '' ">
                introduce_pdf = #{introducePdf, jdbcType=VARCHAR},
            </if>
            <if test="platDivideRate != null">
                plat_divide_rate = #{platDivideRate,jdbcType=DECIMAL},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no =#{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                divide_status = #{divideStatus, jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location, jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo != '' ">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bondPrice != null ">
                bond_price = #{bondPrice, jdbcType=DECIMAL},
            </if>
            <if test="isLook != null and isLook != '' ">
                is_look = #{isLook, jdbcType=VARCHAR},
            </if>
            <if test="mainUser != null ">
                main_user = #{mainUser, jdbcType=BIGINT},
            </if>
            <if test="platFlag != null and platFlag != '' ">
                plat_flag = #{platFlag, jdbcType=VARCHAR},
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                independence_plat_flag = #{independencePlatFlag, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimary">
        update nft_company
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null and shortName != '' ">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="licenceNo != null and licenceNo != '' ">
                licence_no = #{licenceNo,jdbcType=VARCHAR},
            </if>
            <if test="licenceUrl != null and licenceUrl != '' ">
                licence_url = #{licenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="divideFlag != null and divideFlag != '' ">
                divide_flag = #{divideFlag,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceType != null and legalLicenceType != '' ">
                legal_licence_type = #{legalLicenceType,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceNo != null and legalLicenceNo != '' ">
                legal_licence_no = #{legalLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceFrontUrl != null and legalLicenceFrontUrl != '' ">
                legal_licence_front_url = #{legalLicenceFrontUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalLicenceBackUrl != null and legalLicenceBackUrl != '' ">
                legal_licence_back_url = #{legalLicenceBackUrl,jdbcType=VARCHAR},
            </if>
            <if test="legalRealName != null and legalRealName != '' ">
                legal_real_name = #{legalRealName,jdbcType=VARCHAR},
            </if>
            <if test="legalMobile != null and legalMobile != '' ">
                legal_mobile = #{legalMobile,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceNo != null and openAccountLicenceNo != '' ">
                open_account_licence_no = #{openAccountLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="openAccountLicenceUrl != null and openAccountLicenceUrl != '' ">
                open_account_licence_url = #{openAccountLicenceUrl,jdbcType=VARCHAR},
            </if>
            <if test="settleCardType != null and settleCardType != '' ">
                settle_card_type = #{settleCardType,jdbcType=VARCHAR},
            </if>
            <if test="settleCardNo != null and settleCardNo != '' ">
                settle_card_no = #{settleCardNo,jdbcType=VARCHAR},
            </if>
            <if test="settleBankCode != null and settleBankCode != '' ">
                settle_bank_code = #{settleBankCode,jdbcType=VARCHAR},
            </if>
            <if test="province != null and province != '' ">
                province = #{province,jdbcType=VARCHAR},
            </if>
            <if test="city != null and city != '' ">
                city = #{city,jdbcType=VARCHAR},
            </if>
            <if test="district != null and district != '' ">
                district = #{district,jdbcType=VARCHAR},
            </if>
            <if test="address != null and address != '' ">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="contactName != null and contactName != '' ">
                contact_name = #{contactName,jdbcType=VARCHAR},
            </if>
            <if test="contactMobile != null and contactMobile != '' ">
                contact_mobile = #{contactMobile,jdbcType=VARCHAR},
            </if>
            <if test="contactLicenceNo != null and contactLicenceNo != '' ">
                contact_licence_no = #{contactLicenceNo,jdbcType=VARCHAR},
            </if>
            <if test="contactEmail != null and contactEmail != '' ">
                contact_email = #{contactEmail,jdbcType=VARCHAR},
            </if>
            <if test="servicePhone != null and servicePhone != '' ">
                service_phone = #{servicePhone,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl, jdbcType=VARCHAR},
            </if>
            <if test="introduce != null and introduce != '' ">
                introduce = #{introduce,jdbcType=VARCHAR},
            </if>
            <if test="introducePdf != null and introducePdf != '' ">
                introduce_pdf = #{introducePdf, jdbcType=VARCHAR},
            </if>
            <if test="platDivideRate != null">
                plat_divide_rate = #{platDivideRate,jdbcType=DECIMAL},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no =#{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null and merchantNo != '' ">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="divideStatus != null and divideStatus != '' ">
                divide_status = #{divideStatus, jdbcType=VARCHAR},
            </if>
            <if test="location != null and location != '' ">
                location = #{location, jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo != '' ">
                order_no = #{orderNo, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="bondPrice != null ">
                bond_price = #{bondPrice, jdbcType=DECIMAL},
            </if>
            <if test="isLook != null and isLook != '' ">
                is_look = #{isLook, jdbcType=VARCHAR},
            </if>
            <if test="mainUser != null ">
                main_user = #{mainUser, jdbcType=BIGINT},
            </if>
            <if test="platFlag != null and platFlag != '' ">
                plat_flag = #{platFlag, jdbcType=VARCHAR},
            </if>
            <if test="independencePlatFlag != null and independencePlatFlag != '' ">
                independence_plat_flag = #{independencePlatFlag, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateIsLook">
        update nft_company
        <set>
            <if test="isLook != null and isLook != '' ">
                is_look = #{isLook, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Company"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="FrontBaseResultMap" type="com.std.core.pojo.response.CompanyListRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="collection_quantity" jdbcType="INTEGER" property="collectionQuantity"/>
        <result column="follow_quantity" jdbcType="INTEGER" property="followQuantity"/>
        <result column="follow_flag" jdbcType="VARCHAR" property="followFlag"/>
        <result column="sell_quantity" jdbcType="INTEGER" property="sellQuantity"/>
        <result column="selling_quantity" jdbcType="INTEGER" property="sellingQuantity"/>
    </resultMap>

    <!-- 组合条件查询 -->
    <select id="selectByConditionFront" parameterType="com.std.core.pojo.domain.Company"
            resultMap="FrontBaseResultMap">
        select
        t.id
        , t.short_name as name
        , t.type
        , t.logo
        , t.cover_file_url
        , (select count(1) from nft_collection where author_id = t.id and status = '1') as collection_quantity
        , (select count(1) from tstd_user_relation where to_user = t.id and type = '1') as follow_quantity
        , (select count(id)
        from nft_collection_period tp
        where tp.status = '1'
        and tp.start_status in ('0', '1')
        and tp.channel_id ='1'
        and tp.remain_quantity > 0
        and tp.author_ids = t.id) as selling_quantity
        , (select count(id)
        from nft_collection_period tp
        where tp.status in ('1', '2')
        and tp.channel_id ='1'
        and ((tp.total_quantity <![CDATA[ = ]]> tp.remain_quantity and tp.start_status = 0)
        or (tp.total_quantity <![CDATA[ >= ]]> tp.remain_quantity and tp.start_status = 1)
        or (tp.total_quantity <![CDATA[ > ]]> tp.remain_quantity and tp.start_status = 2)
        )
        and tp.author_ids = t.id) as sell_quantity
        from nft_company t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByConditionFrontRelationMy" parameterType="com.std.core.pojo.domain.Company"
            resultMap="FrontBaseResultMap">
        select t.id
             , t.short_name                                                                  as name
             , t.type
             , t.logo
             , t.cover_file_url
             , (select count(1) from nft_collection where author_id = t.id and status = '1') as collection_quantity
             , (select count(1) from tstd_user_relation where to_user = t.id and type = '1') as follow_quantity
             , '1'                                                                              follow_flag
             , (select count(id)
                from nft_collection_period tp
                where tp.status = '1'
                  and tp.start_status in ('0', '1')
                  and tp.channel_id = '1'
                  and tp.remain_quantity > 0
                  and tp.author_ids = t.id)                                                  as selling_quantity
             , (select count(id)
                from nft_collection_period tp
                where tp.status in ('1', '2')
                  and tp.channel_id = '1'
                  and ((tp.total_quantity <![CDATA[ = ]]> tp.remain_quantity and tp.start_status = 0)
                    or (tp.total_quantity <![CDATA[ >= ]]> tp.remain_quantity and tp.start_status = 1)
                    or (tp.total_quantity <![CDATA[ > ]]> tp.remain_quantity and tp.start_status = 2)
                    )
                  and tp.author_ids = t.id)                                                  as sell_quantity
        from nft_company t
                 inner join
             tstd_user_relation tr
             on (t.id = tr.to_user and tr.type = '1')
        where t.status = '2'
          and tr.user_id = #{userId}
    </select>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.Company"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_company t
        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>