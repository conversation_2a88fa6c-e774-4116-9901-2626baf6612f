<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionDetailTransferRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionDetailTransferRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="from_user_id" jdbcType="BIGINT" property="fromUserId"/>
        <result column="to_user_id" jdbcType="BIGINT" property="toUserId"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="collectionOrderNumber" jdbcType="INTEGER" property="collectionOrderNumber"/>
        <result column="contractTokenId" jdbcType="BIGINT" property="contractTokenId"/>
        <result column="tokenId" jdbcType="BIGINT" property="tokenId"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.from_user_id
        , t.to_user_id
        , t.fee
        , t.quantity
        , t.type
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="fromUserId != null">
                AND t.from_user_id = #{fromUserId, jdbcType=BIGINT}
            </if>
            <if test="toUserId != null">
                AND t.to_user_id = #{toUserId, jdbcType=BIGINT}
            </if>
            <if test="fee != null">
                AND t.fee = #{fee, jdbcType=DECIMAL}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="fromUserId != null">
                AND t.from_user_id = #{fromUserId, jdbcType=BIGINT}
            </if>
            <if test="toUserId != null">
                AND t.to_user_id = #{toUserId, jdbcType=BIGINT}
            </if>
            <if test="fee != null">
                AND t.fee = #{fee, jdbcType=DECIMAL}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code like concat('%',#{payOrderCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payBalanceAmount != null">
                AND t.pay_balance_amount = #{payBalanceAmount, jdbcType=DECIMAL}
            </if>
            <if test="payCashAmount != null">
                AND t.pay_cash_amount = #{payCashAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="collectionDetailId != null">
                AND t.id in (select DISTINCT transfer_record_id from nft_collection_detail_transfer_detail where collection_detail_id=#{collectionDetailId})
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionDetailTransferRecord">
        insert into nft_collection_detail_transfer_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="fromUserId != null ">
                from_user_id,
            </if>
            <if test="toUserId != null ">
                to_user_id,
            </if>
            <if test="fee != null ">
                fee,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="payBalanceAmount != null ">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null ">
                pay_cash_amount,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fromUserId != null">
                #{fromUserId,jdbcType=BIGINT},
            </if>
            <if test="toUserId != null">
                #{toUserId,jdbcType=BIGINT},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null ">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_detail_transfer_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionDetailTransferRecord">
        update nft_collection_detail_transfer_record
        <set>
            <if test="fromUserId != null">
                from_user_id = #{fromUserId,jdbcType=BIGINT},
            </if>
            <if test="toUserId != null">
                to_user_id = #{toUserId,jdbcType=BIGINT},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity},
            </if>
            <if test="type != null and type != '' ">
                type = #{type, jdbcType=VARCHAR},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByPrimaryKeyForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_record t
        where t.id = #{id,jdbcType=BIGINT} for update
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionDetailTransferRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.CollectionDetailTransferRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_detail_transfer_record t
        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectTransferNotFinish" resultType="java.lang.Integer">
        SELECT
            count( 1 )
        FROM
            nft_collection_detail_transfer_record t
                INNER JOIN nft_collection_detail_transfer_detail ta ON t.id = ta.transfer_record_id
                INNER JOIN nft_collection_detail tb ON ta.collection_detail_id=tb.id
        WHERE
            t.to_user_id = #{toUserId}
          AND t.pay_status = '0'
          AND tb.collection_id=#{collectionId};
    </select>
</mapper>