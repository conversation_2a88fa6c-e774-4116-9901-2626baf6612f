<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserGroupMapper">

    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserGroup">
        <result column="t_user_id" jdbcType="BIGINT" property="userId"/>
        <result column="t_group_id" jdbcType="BIGINT" property="groupId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.user_id as t_user_id,
        t.group_id as t_group_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="userId != null and userId != '' ">
                AND t.user_id = #{userId,jdbcType=BIGINT}
            </if>
            <if test="groupId != null and groupId != '' ">
                AND t.group_id = #{groupId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserGroup"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_user_group t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <insert id="insert" parameterType="com.std.core.pojo.domain.UserGroup">
        insert into tsys_user_group (user_id
        , group_id
        )
        values (#{userId,jdbcType=BIGINT}
        , #{groupId,jdbcType=BIGINT}
        )
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tsys_user_group (user_id, group_id)
        values
        <foreach item="userGroup" index="index" collection="list" separator=",">
            (#{userGroup.userId,jdbcType=BIGINT}, #{userGroup.groupId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <delete id="deleteByUserId" parameterType="java.lang.Long">
        delete from tsys_user_group
        where user_id = #{userId,jdbcType=BIGINT}
    </delete>

</mapper>
