<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsAdditionalInformationMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsAdditionalInformation">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="record_id" jdbcType="BIGINT" property="recordId"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="goodsName" jdbcType="VARCHAR" property="goodsName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.goods_id
        , t.record_id
        , t.content
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="recordId != null">
                AND t.record_id = #{recordId, jdbcType=BIGINT}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsAdditionalInformation">
        insert into mall_goods_additional_information
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="goodsId != null ">
                goods_id,
              </if>
              <if test="recordId != null ">
                record_id,
              </if>
              <if test="content != null and content != '' ">
                content,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
            <if test="creater != null">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="recordId != null">
                #{recordId,jdbcType=BIGINT},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater, jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into mall_goods_additional_information
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.goodsId != null ">
                    goods_id,
                </if>
                <if test="item.recordId != null ">
                    record_id,
                </if>
                <if test="item.content != null and item.content != '' ">
                    content,
                </if>
                <if test="item.status != null and item.status != '' ">
                    status,
                </if>
                <if test="item.creater != null">
                    creater,
                </if>
                <if test="item.createrName != null and item.createrName != '' ">
                    creater_name,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
                <if test="item.updater != null ">
                    updater,
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    updater_name,
                </if>
                <if test="item.updateDatetime != null ">
                    update_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.goodsId != null">
                    #{item.goodsId,jdbcType=BIGINT},
                </if>
                <if test="item.recordId != null">
                    #{item.recordId,jdbcType=BIGINT},
                </if>
                <if test="item.content != null and item.content != '' ">
                    #{item.content,jdbcType=VARCHAR},
                </if>
                <if test="item.status != null and item.status != '' ">
                    #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.creater != null">
                    #{item.creater, jdbcType=BIGINT},
                </if>
                <if test="item.createrName != null and item.createrName != '' ">
                    #{item.createrName, jdbcType=VARCHAR},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updater != null">
                    #{item.updater,jdbcType=BIGINT},
                </if>
                <if test="item.updaterName != null and item.updaterName != '' ">
                    #{item.updaterName,jdbcType=VARCHAR},
                </if>
                <if test="item.updateDatetime != null">
                    #{item.updateDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from mall_goods_additional_information
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByGoodsId">
        delete from mall_goods_additional_information
        where goods_id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsAdditionalInformation">
        update mall_goods_additional_information
        <set>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="recordId != null">
                record_id = #{recordId,jdbcType=BIGINT},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater, jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName, jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_additional_information t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsAdditionalInformation"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_additional_information t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_additional_information t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.GoodsAdditionalInformation"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name goodsName
        from mall_goods_additional_information t
        inner join mall_goods_integral ta on t.goods_id=ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>