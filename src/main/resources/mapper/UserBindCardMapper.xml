<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserBindCardMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserBindCard">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="channel_bank_id" jdbcType="BIGINT" property="channelBankId"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="card_type" jdbcType="VARCHAR" property="cardType"/>
        <result column="card_real_name" jdbcType="VARCHAR" property="cardRealName"/>
        <result column="card_id_no" jdbcType="VARCHAR" property="cardIdNo"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="pre_sign_code" jdbcType="VARCHAR" property="preSignCode"/>
        <result column="sms_check_code" jdbcType="VARCHAR" property="smsCheckCode"/>
        <result column="security_code" jdbcType="VARCHAR" property="securityCode"/>
        <result column="expiry_date" jdbcType="VARCHAR" property="expiryDate"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="protocol_no" jdbcType="VARCHAR" property="protocolNo"/>
        <result column="pre_bind_datetime" jdbcType="TIMESTAMP" property="preBindDatetime"/>
        <result column="bind_datetime" jdbcType="TIMESTAMP" property="bindDatetime"/>
        <result column="unbind_datetime" jdbcType="TIMESTAMP" property="unbindDatetime"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.channel_bank_id
        , t.bank_code
        , t.bank_name
        , t.card_no
        , t.card_type
        , t.card_real_name
        , t.card_id_no
        , t.mobile
        , t.pre_sign_code
        , t.sms_check_code
        , t.security_code
        , t.expiry_date
        , t.status
        , t.protocol_no
        , t.pre_bind_datetime
        , t.bind_datetime
        , t.unbind_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="channelBankId != null">
                AND t.channel_bank_id = #{channelBankId, jdbcType=BIGINT}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code = #{bankCode, jdbcType=VARCHAR}
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name = #{bankName, jdbcType=VARCHAR}
            </if>
            <if test="cardNo != null and cardNo != '' ">
                AND t.card_no = #{cardNo, jdbcType=VARCHAR}
            </if>
            <if test="cardType != null and cardType != '' ">
                AND t.card_type = #{cardType, jdbcType=VARCHAR}
            </if>
            <if test="cardRealName != null and cardRealName != '' ">
                AND t.card_real_name = #{cardRealName, jdbcType=VARCHAR}
            </if>
            <if test="cardIdNo != null and cardIdNo != '' ">
                AND t.card_id_no = #{cardIdNo, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="preSignCode != null and preSignCode != '' ">
                AND t.pre_sign_code = #{preSignCode, jdbcType=VARCHAR}
            </if>
            <if test="smsCheckCode != null and smsCheckCode != '' ">
                AND t.sms_check_code = #{smsCheckCode, jdbcType=VARCHAR}
            </if>
            <if test="securityCode != null and securityCode != '' ">
                AND t.security_code = #{securityCode, jdbcType=VARCHAR}
            </if>
            <if test="expiryDate != null and expiryDate != '' ">
                AND t.expiry_date = #{expiryDate, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="protocolNo != null and protocolNo != '' ">
                AND t.protocol_no = #{protocolNo, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="preBindDatetime != null">
                AND t.pre_bind_datetime = #{preBindDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="bindDatetime != null">
                AND t.bind_datetime = #{bindDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="unbindDatetime != null">
                AND t.unbind_datetime = #{unbindDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND ta.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="channelBankId != null">
                AND t.channel_bank_id = #{channelBankId, jdbcType=BIGINT}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code like concat('%', #{bankCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name like concat('%', #{bankName, jdbcType=VARCHAR},'%')
            </if>
            <if test="cardNo != null and cardNo != '' ">
                AND t.card_no like concat('%', #{cardNo, jdbcType=VARCHAR},'%')
            </if>
            <if test="cardType != null and cardType != '' ">
                AND t.card_type = #{cardType, jdbcType=VARCHAR}
            </if>
            <if test="cardRealName != null and cardRealName != '' ">
                AND t.card_real_name like concat('%', #{cardRealName, jdbcType=VARCHAR},'%')
            </if>
            <if test="cardIdNo != null and cardIdNo != '' ">
                AND t.card_id_no like concat('%', #{cardIdNo, jdbcType=VARCHAR},'%')
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile like concat('%', #{mobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="preSignCode != null and preSignCode != '' ">
                AND t.pre_sign_code = #{preSignCode, jdbcType=VARCHAR}
            </if>
            <if test="smsCheckCode != null and smsCheckCode != '' ">
                AND t.sms_check_code = #{smsCheckCode, jdbcType=VARCHAR}
            </if>
            <if test="securityCode != null and securityCode != '' ">
                AND t.security_code like concat('%',#{securityCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="expiryDate != null and expiryDate != '' ">
                AND t.expiry_date = #{expiryDate, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="protocolNo != null and protocolNo != '' ">
                AND t.protocol_no = #{protocolNo, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="preBindDatetime != null">
                AND t.pre_bind_datetime = #{preBindDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="bindDatetime != null">
                AND t.bind_datetime = #{bindDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="unbindDatetime != null">
                AND t.unbind_datetime = #{unbindDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserBindCard">
        insert into tstd_user_bind_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="channelBankId != null ">
                channel_bank_id,
            </if>
            <if test="bankCode != null and bankCode != '' ">
                bank_code,
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name,
            </if>
            <if test="cardNo != null and cardNo != '' ">
                card_no,
            </if>
            <if test="cardType != null and cardType != '' ">
                card_type,
            </if>
            <if test="cardRealName != null and cardRealName != '' ">
                card_real_name,
            </if>
            <if test="cardIdNo != null and cardIdNo != '' ">
                card_id_no,
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile,
            </if>
            <if test="preSignCode != null and preSignCode != '' ">
                pre_sign_code,
            </if>
            <if test="smsCheckCode != null and smsCheckCode != '' ">
                sms_check_code,
            </if>
            <if test="securityCode != null and securityCode != '' ">
                security_code,
            </if>
            <if test="expiryDate != null and expiryDate != '' ">
                expiry_date,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="protocolNo != null and protocolNo != '' ">
                protocol_no,
            </if>
            <if test="preBindDatetime != null ">
                pre_bind_datetime,
            </if>
            <if test="bindDatetime != null ">
                bind_datetime,
            </if>
            <if test="unbindDatetime != null ">
                unbind_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelBankId != null">
                #{channelBankId,jdbcType=BIGINT},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null and cardNo != '' ">
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null and cardType != '' ">
                #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardRealName != null and cardRealName != '' ">
                #{cardRealName,jdbcType=VARCHAR},
            </if>
            <if test="cardIdNo != null and cardIdNo != '' ">
                #{cardIdNo,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile, jdbcType=VARCHAR},
            </if>
            <if test="preSignCode != null and preSignCode != '' ">
                #{preSignCode,jdbcType=VARCHAR},
            </if>
            <if test="smsCheckCode != null and smsCheckCode != '' ">
                #{smsCheckCode,jdbcType=VARCHAR},
            </if>
            <if test="securityCode != null and securityCode != '' ">
                #{securityCode, jdbcType=VARCHAR},
            </if>
            <if test="expiryDate != null and expiryDate != '' ">
                #{expiryDate, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="protocolNo != null and protocolNo != '' ">
                #{protocolNo, jdbcType=VARCHAR},
            </if>
            <if test="preBindDatetime != null">
                #{preBindDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindDatetime != null">
                #{bindDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="unbindDatetime != null">
                #{unbindDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_user_bind_card
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserBindCard">
        update tstd_user_bind_card
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelBankId != null">
                channel_bank_id = #{channelBankId,jdbcType=BIGINT},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="cardNo != null and cardNo != '' ">
                card_no = #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null and cardType != '' ">
                card_type = #{cardType,jdbcType=VARCHAR},
            </if>
            <if test="cardRealName != null and cardRealName != '' ">
                card_real_name = #{cardRealName,jdbcType=VARCHAR},
            </if>
            <if test="cardIdNo != null and cardIdNo != '' ">
                card_id_no = #{cardIdNo,jdbcType=VARCHAR},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile, jdbcType=VARCHAR},
            </if>
            <if test="preSignCode != null and preSignCode != '' ">
                pre_sign_code = #{preSignCode,jdbcType=VARCHAR},
            </if>
            <if test="smsCheckCode != null and smsCheckCode != '' ">
                sms_check_code = #{smsCheckCode,jdbcType=VARCHAR},
            </if>
            <if test="securityCode != null and securityCode != '' ">
                security_code = #{securityCode, jdbcType=VARCHAR},
            </if>
            <if test="expiryDate != null and expiryDate != '' ">
                expiry_date = #{expiryDate, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="protocolNo != null and protocolNo != '' ">
                protocol_no = #{protocolNo, jdbcType=VARCHAR},
            </if>
            <if test="preBindDatetime != null">
                pre_bind_datetime = #{preBindDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="bindDatetime != null">
                bind_datetime = #{bindDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="unbindDatetime != null">
                unbind_datetime = #{unbindDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateSmsCodeFailure">
        update tstd_user_bind_card
        set `status`='3',
            unbind_datetime=#{date}
        where id in (select id
                     from (select id
                           from tstd_user_bind_card
                           where `status` = '0'
                             and #{date} <![CDATA[ >=]]> DATE_ADD(pre_bind_datetime, INTERVAL #{time} MINUTE)) a);
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_bind_card t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserBindCard"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_bind_card t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.UserBindCard"
            resultMap="BaseResultMap">
        select
        t
        .
        id
        , t.user_id
        , t.channel_bank_id
        , t.bank_code
        , t.bank_name
        , t.card_no
        , t.card_type
        , t.card_real_name
        , t.card_id_no
        , t.mobile
        , t.security_code
        , t.expiry_date
        , t.status
        , t.pre_bind_datetime
        , t.bind_datetime
        , t.unbind_datetime
        from tstd_user_bind_card t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionFront" parameterType="com.std.core.pojo.domain.UserBindCard"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,tb.logo,ta.channel_type
        from tstd_user_bind_card t
        inner join tstd_bank_channel ta on t.channel_bank_id=ta.id
        inner join tstd_bank_info tb on ta.bank_info_id=tb.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSmsCodeFailure" resultType="java.lang.Long">
        select id
        from tstd_user_bind_card
        where `status` = '0'
          and #{date} <![CDATA[ >=]]> DATE_ADD(pre_bind_datetime, INTERVAL #{time} MINUTE)
    </select>
    <select id="selectListCheck" parameterType="com.std.core.pojo.domain.UserBindCard"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_user_bind_card t
        INNER JOIN tstd_bank_channel ta on t.channel_bank_id=ta.id
        where

        t.card_no = #{cardNo, jdbcType=VARCHAR}

        AND t.status = #{status, jdbcType=VARCHAR}
        AND ta.channel_type=#{channelType}

    </select>

</mapper>