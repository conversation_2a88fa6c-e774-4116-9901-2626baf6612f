<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelSmsRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelSmsRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="ref_id" jdbcType="INTEGER" property="refId"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="request" jdbcType="VARCHAR" property="request"/>
        <result column="respond" jdbcType="VARCHAR" property="respond"/>
        <result column="request_no" jdbcType="VARCHAR" property="requestNo"/>
        <result column="request_type" jdbcType="VARCHAR" property="requestType"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.channel_type
        , t.ref_id
        , t.ref_type
        , t.request
        , t.respond
        , t.request_no
        , t.request_type
        , t.creater
        , t.creater_name
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=INTEGER}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="request != null and request != '' ">
                AND t.request = #{request, jdbcType=VARCHAR}
            </if>
            <if test="respond != null and respond != '' ">
                AND t.respond = #{respond, jdbcType=VARCHAR}
            </if>
            <if test="requestNo != null and requestNo != '' ">
                AND t.request_no = #{requestNo, jdbcType=VARCHAR}
            </if>
            <if test="requestType != null and requestType != '' ">
                AND t.request_type = #{requestType, jdbcType=VARCHAR}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelSmsRecord">
        insert into nft_channel_sms_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="channelType != null and channelType != '' ">
                channel_type,
              </if>
              <if test="refId != null ">
                ref_id,
              </if>
              <if test="refType != null and refType != '' ">
                ref_type,
              </if>
              <if test="request != null and request != '' ">
                request,
              </if>
              <if test="respond != null and respond != '' ">
                respond,
              </if>
              <if test="requestNo != null and requestNo != '' ">
                request_no,
              </if>
              <if test="requestType != null and requestType != '' ">
                request_type,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=INTEGER},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="request != null and request != '' ">
                #{request,jdbcType=VARCHAR},
            </if>
            <if test="respond != null and respond != '' ">
                #{respond,jdbcType=VARCHAR},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                #{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="requestType != null and requestType != '' ">
                #{requestType,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_channel_sms_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelSmsRecord">
        update nft_channel_sms_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=INTEGER},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="request != null and request != '' ">
                request = #{request,jdbcType=VARCHAR},
            </if>
            <if test="respond != null and respond != '' ">
                respond = #{respond,jdbcType=VARCHAR},
            </if>
            <if test="requestNo != null and requestNo != '' ">
                request_no = #{requestNo,jdbcType=VARCHAR},
            </if>
            <if test="requestType != null and requestType != '' ">
                request_type = #{requestType,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_channel_sms_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelSmsRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_channel_sms_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>