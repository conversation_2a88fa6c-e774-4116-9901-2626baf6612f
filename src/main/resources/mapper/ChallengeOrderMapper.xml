<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChallengeOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChallengeOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="challenge_id" jdbcType="BIGINT" property="challengeId"/>
        <result column="challenge_type" jdbcType="VARCHAR" property="challengeType"/>
        <result column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="channel_id" jdbcType="BIGINT" property="channelId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="award_ref_id" jdbcType="BIGINT" property="awardRefId"/>
        <result column="award_name" jdbcType="VARCHAR" property="awardName"/>
        <result column="award_pic" jdbcType="VARCHAR" property="awardPic"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="re_mobile" jdbcType="VARCHAR" property="reMobile"/>
        <result column="re_address" jdbcType="VARCHAR" property="reAddress"/>
        <result column="deliverer" jdbcType="VARCHAR" property="deliverer"/>
        <result column="delivery_datetime" jdbcType="TIMESTAMP" property="deliveryDatetime"/>
        <result column="logistics_company" jdbcType="VARCHAR" property="logisticsCompany"/>
        <result column="logistics_code" jdbcType="VARCHAR" property="logisticsCode"/>
        <result column="logistics_trace" jdbcType="VARCHAR" property="logisticsTrace"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="is_distribution" jdbcType="VARCHAR" property="isDistribution"/>
        <result column="challengeName" jdbcType="VARCHAR" property="challengeName"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.challenge_id
        , t.challenge_type
        , t.company_id
        , t.channel_id
        , t.user_id
        , t.award_ref_id
        , t.award_name
        , t.award_pic
        , t.status
        , t.create_time
        , t.receiver
        , t.re_mobile
        , t.re_address
        , t.deliverer
        , t.delivery_datetime
        , t.logistics_company
        , t.logistics_code
        , t.logistics_trace
        , t.updater
        , t.updater_name
        , t.update_time
        , t.remark
        , t.is_distribution
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="challengeId != null">
                AND t.challenge_id = #{challengeId, jdbcType=BIGINT}
            </if>
            <if test="challengeType != null and challengeType != '' ">
                AND t.challenge_type = #{challengeType, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="awardRefId != null">
                AND t.award_ref_id = #{awardRefId, jdbcType=BIGINT}
            </if>
            <if test="awardName != null and awardName != '' ">
                AND t.award_name like concat('%', #{awardName, jdbcType=VARCHAR},'%')
            </if>
            <if test="awardPic != null and awardPic != '' ">
                AND t.award_pic = #{awardPic, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="receiver != null and receiver != '' ">
                AND t.receiver = #{receiver, jdbcType=VARCHAR}
            </if>
            <if test="reMobile != null and reMobile != '' ">
                AND t.re_mobile = #{reMobile, jdbcType=VARCHAR}
            </if>
            <if test="reAddress != null and reAddress != '' ">
                AND t.re_address = #{reAddress, jdbcType=VARCHAR}
            </if>
            <if test="deliverer != null and deliverer != '' ">
                AND t.deliverer = #{deliverer, jdbcType=VARCHAR}
            </if>
            <if test="deliveryDatetime != null">
                AND t.delivery_datetime = #{deliveryDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                AND t.logistics_company = #{logisticsCompany, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                AND t.logistics_code = #{logisticsCode, jdbcType=VARCHAR}
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                AND t.logistics_trace = #{logisticsTrace, jdbcType=VARCHAR}
            </if>
            <if test="updater != null ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="isDistribution != null and isDistribution != '' ">
                AND t.is_distribution = #{isDistribution, jdbcType=VARCHAR}
            </if>

        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="challengeId != null">
                AND t.challenge_id = #{challengeId, jdbcType=BIGINT}
            </if>
            <if test="challengeName != null">
                AND ta.name like concat('%',#{challengeName},'%')
            </if>
            <if test="challengeType != null and challengeType != '' ">
                AND t.challenge_type = #{challengeType, jdbcType=VARCHAR}
            </if>
            <if test="companyId != null">
                AND t.company_id = #{companyId, jdbcType=BIGINT}
            </if>
            <if test="channelId != null">
                AND t.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="awardRefId != null">
                AND t.award_ref_id = #{awardRefId, jdbcType=BIGINT}
            </if>
            <if test="awardName != null and awardName != '' ">
                AND t.award_name like concat('%', #{awardName, jdbcType=VARCHAR},'%')
            </if>
            <if test="awardPic != null and awardPic != '' ">
                AND t.award_pic = #{awardPic, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%') or
                tu.nickname like concat('%', #{keywords, jdbcType=VARCHAR},'%')

                )
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=TIMESTAMP}
            </if>
            <if test="receiver != null and receiver != '' ">
                AND t.receiver = #{receiver, jdbcType=VARCHAR}
            </if>
            <if test="reMobile != null and reMobile != '' ">
                AND t.re_mobile = #{reMobile, jdbcType=VARCHAR}
            </if>
            <if test="reAddress != null and reAddress != '' ">
                AND t.re_address = #{reAddress, jdbcType=VARCHAR}
            </if>
            <if test="deliverer != null and deliverer != '' ">
                AND t.deliverer = #{deliverer, jdbcType=VARCHAR}
            </if>
            <if test="deliveryDatetime != null">
                AND t.delivery_datetime = #{deliveryDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                AND t.logistics_company = #{logisticsCompany, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                AND t.logistics_code = #{logisticsCode, jdbcType=VARCHAR}
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                AND t.logistics_trace = #{logisticsTrace, jdbcType=VARCHAR}
            </if>
            <if test="updater != null ">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null">
                AND t.update_time = #{updateTime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="isDistribution != null and isDistribution != '' ">
                AND t.is_distribution = #{isDistribution, jdbcType=VARCHAR}
            </if>
            <if test="collectionDetailId != null ">
                AND t.id in (SELECT DISTINCT order_id from nft_challenge_order_detail where collection_detail_id =#{collectionDetailId})
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.id in (SELECT DISTINCT order_id from nft_challenge_order_detail where
                collection_detail_id IN (SELECT id from nft_collection_detail where collection_id in (SELECT id from nft_collection WHERE
                name LIKE concat('%', #{collectionName, jdbcType=VARCHAR},'%'))))
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.std.core.pojo.domain.ChallengeOrder">
        insert into nft_challenge_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="challengeId != null ">
                challenge_id,
            </if>
            <if test="challengeType != null and challengeType != '' ">
                challenge_type,
            </if>
            <if test="companyId != null">
                company_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="awardRefId != null">
                award_ref_id ,
            </if>
            <if test="awardName != null and awardName != '' ">
                award_name,
            </if>
            <if test="awardPic != null and awardPic != '' ">
                award_pic,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="createTime != null ">
                create_time,
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver,
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile,
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address,
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer,
            </if>
            <if test="deliveryDatetime != null ">
                delivery_datetime,
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company,
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code,
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                logistics_trace,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateTime != null ">
                update_time,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="isDistribution != null and isDistribution != '' ">
                is_distribution,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="challengeId != null">
                #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="challengeType != null and challengeType != '' ">
                #{challengeType,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                #{companyId, jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                #{channelId, jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="awardRefId != null">
                #{awardRefId, jdbcType=BIGINT},
            </if>
            <if test="awardName != null and awardName != '' ">
                #{awardName, jdbcType=VARCHAR},
            </if>
            <if test="awardPic != null and awardPic != '' ">
                #{awardPic, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiver != null and receiver != '' ">
                #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                #{logisticsTrace,jdbcType=VARCHAR},
            </if>
            <if test="updater != null ">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDistribution != null and isDistribution != '' ">
                #{isDistribution, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_challenge_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChallengeOrder">
        update nft_challenge_order
        <set>
            <if test="challengeId != null">
                challenge_id = #{challengeId,jdbcType=BIGINT},
            </if>
            <if test="challengeType != null and challengeType != '' ">
                challenge_type = #{challengeType,jdbcType=VARCHAR},
            </if>
            <if test="companyId != null">
                company_id = #{companyId, jdbcType=BIGINT},
            </if>
            <if test="channelId != null">
                channel_id = #{channelId, jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="awardRefId != null">
                award_ref_id = #{awardRefId, jdbcType=BIGINT},
            </if>
            <if test="awardName != null and awardName != '' ">
                award_name = #{awardName, jdbcType=VARCHAR},
            </if>
            <if test="awardPic != null and awardPic != '' ">
                award_pic = #{awardPic, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile = #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address = #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer = #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                delivery_datetime = #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company = #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code = #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                logistics_trace = #{logisticsTrace,jdbcType=VARCHAR},
            </if>
            <if test="updater != null ">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isDistribution != null and isDistribution != '' ">
                is_distribution = #{isDistribution, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChallengeOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name challengeName
        from nft_challenge_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        INNER JOIN nft_challenge ta on t.challenge_id=ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPrimaryKeyOss" parameterType="com.std.core.pojo.domain.ChallengeOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name challengeName
        from nft_challenge_order t
        INNER JOIN nft_challenge ta on t.challenge_id=ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_challenge_order t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>

    <select id="selectByPrimaryKeyFront" resultType="com.std.core.pojo.response.ChallengeOrderDetailRes">
        select t.id,
               t.challenge_id   challengeId,
               t.status,
               t.create_time    createTime,
               t.remark,
               tb.name          companyName,
               tb.logo          companyLogo,
               ta.name          name,
               ta.cover_pic_url coverPicUrl,
               ta.type          type
        from nft_challenge_order t
                 INNER JOIN nft_challenge ta on t.challenge_id = ta.id
                 INNER JOIN nft_company tb on ta.company_id = tb.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.ChallengeOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.name challengeName
        from nft_challenge_order t
        INNER JOIN nft_challenge ta on t.challenge_id=ta.id
        INNER JOIN tsys_user tu on t.user_id = tu.id

        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectCount" resultType="java.lang.Integer">
        select
        count(1)
        from nft_challenge_order t
        <include refid="where_condition"/>
    </select>

</mapper>