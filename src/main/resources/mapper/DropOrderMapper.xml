<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.DropOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.DropOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="ref_collection_id" jdbcType="BIGINT" property="refCollectionId"/>
        <result column="drop_number" jdbcType="INTEGER" property="dropNumber"/>
        <result column="total_drop_number" jdbcType="INTEGER" property="totalDropNumber"/>
        <result column="drop_user_number" jdbcType="INTEGER" property="dropUserNumber"/>
        <result column="success_drop_user_number" jdbcType="INTEGER" property="successDropUserNumber"/>
        <result column="failure_drop_user_number" jdbcType="INTEGER" property="failureDropUserNumber"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_kind" jdbcType="VARCHAR" property="createrKind"/>
        <result column="creatr_name" jdbcType="VARCHAR" property="creatrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.collection_id
        , t.ref_collection_id
        , t.drop_number
        , t.total_drop_number
        , t.drop_user_number
        , t.success_drop_user_number
        , t.failure_drop_user_number
        , t.creater
        , t.creater_kind
        , t.creatr_name
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="refCollectionId != null">
                AND t.ref_collection_id = #{refCollectionId, jdbcType=BIGINT}
            </if>
            <if test="dropNumber != null">
                AND t.drop_number = #{dropNumber, jdbcType=INTEGER}
            </if>
            <if test="totalDropNumber != null">
                AND t.total_drop_number = #{totalDropNumber, jdbcType=INTEGER}
            </if>
            <if test="dropUserNumber != null">
                AND t.drop_user_number = #{dropUserNumber, jdbcType=INTEGER}
            </if>
            <if test="successDropUserNumber != null">
                AND t.success_drop_user_number = #{successDropUserNumber, jdbcType=INTEGER}
            </if>
            <if test="failureDropUserNumber != null">
                AND t.failure_drop_user_number = #{failureDropUserNumber, jdbcType=INTEGER}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrKind != null and createrKind != '' ">
                AND t.creater_kind = #{createrKind, jdbcType=VARCHAR}
            </if>
            <if test="creatrName != null and creatrName != '' ">
                AND t.creatr_name = #{creatrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.DropOrder">
        insert into nft_drop_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="refCollectionId != null ">
                ref_collection_id,
              </if>
              <if test="dropNumber != null ">
                drop_number,
              </if>
              <if test="totalDropNumber != null ">
                total_drop_number,
              </if>
              <if test="dropUserNumber != null ">
                drop_user_number,
              </if>
              <if test="successDropUserNumber != null ">
                success_drop_user_number,
              </if>
              <if test="failureDropUserNumber != null ">
                failure_drop_user_number,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrKind != null and createrKind != '' ">
                creater_kind,
              </if>
              <if test="creatrName != null and creatrName != '' ">
                creatr_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="refCollectionId != null">
                #{refCollectionId,jdbcType=BIGINT},
            </if>
            <if test="dropNumber != null">
                #{dropNumber,jdbcType=INTEGER},
            </if>
            <if test="totalDropNumber != null">
                #{totalDropNumber,jdbcType=INTEGER},
            </if>
            <if test="dropUserNumber != null">
                #{dropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="successDropUserNumber != null">
                #{successDropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="failureDropUserNumber != null">
                #{failureDropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrKind != null and createrKind != '' ">
                #{createrKind,jdbcType=VARCHAR},
            </if>
            <if test="creatrName != null and creatrName != '' ">
                #{creatrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_drop_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.DropOrder">
        update nft_drop_order
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="refCollectionId != null">
                ref_collection_id = #{refCollectionId,jdbcType=BIGINT},
            </if>
            <if test="dropNumber != null">
                drop_number = #{dropNumber,jdbcType=INTEGER},
            </if>
            <if test="totalDropNumber != null">
                total_drop_number = #{totalDropNumber,jdbcType=INTEGER},
            </if>
            <if test="dropUserNumber != null">
                drop_user_number = #{dropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="successDropUserNumber != null">
                success_drop_user_number = #{successDropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="failureDropUserNumber != null">
                failure_drop_user_number = #{failureDropUserNumber,jdbcType=INTEGER},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrKind != null and createrKind != '' ">
                creater_kind = #{createrKind,jdbcType=VARCHAR},
            </if>
            <if test="creatrName != null and creatrName != '' ">
                creatr_name = #{creatrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_drop_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.DropOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_drop_order t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>