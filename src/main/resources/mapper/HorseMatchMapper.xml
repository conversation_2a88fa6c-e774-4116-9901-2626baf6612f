<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.HorseMatchMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.HorseMatch">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="max_number" jdbcType="INTEGER" property="maxNumber"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="start_datetime" jdbcType="TIMESTAMP" property="startDatetime"/>
        <result column="end_datetime" jdbcType="TIMESTAMP" property="endDatetime"/>
        <result column="result" jdbcType="VARCHAR" property="result"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.number
        , t.max_number
        , t.status
        , t.user_id
        , t.create_datetime
        , t.start_datetime
        , t.end_datetime
        , t.result
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="maxNumber != null">
                AND t.max_number = #{maxNumber, jdbcType=INTEGER}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="startDatetime != null">
                AND t.start_datetime = #{startDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="endDatetime != null">
                AND t.end_datetime = #{endDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="result != null and result != '' ">
                AND t.result = #{result, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.HorseMatch" useGeneratedKeys="true"
            keyProperty="id">
        insert into hr_horse_match
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="number != null ">
                number,
            </if>
            <if test="maxNumber != null ">
                max_number,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="startDatetime != null ">
                start_datetime,
            </if>
            <if test="endDatetime != null ">
                end_datetime,
            </if>
            <if test="result != null and result != '' ">
                result,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="maxNumber != null">
                #{maxNumber,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="startDatetime != null">
                #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="result != null and result != '' ">
                #{result, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from hr_horse_match
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.HorseMatch">
        update hr_horse_match
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="maxNumber != null">
                max_number = #{maxNumber,jdbcType=INTEGER},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="startDatetime != null">
                start_datetime = #{startDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="endDatetime != null">
                end_datetime = #{endDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="result != null and result != '' ">
                result = #{result, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_horse_match t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.HorseMatch"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from hr_horse_match t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>