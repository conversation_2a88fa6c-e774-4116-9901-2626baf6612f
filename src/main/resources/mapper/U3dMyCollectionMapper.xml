<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.U3dMyCollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.U3dMyCollection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.collection_id
        , t.collection_detail_id
        , t.user_id
        , t.order_no
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="orderNo != null">
                AND t.order_no = #{orderNo, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.U3dMyCollection">
        insert into nft_u3d_my_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="orderNo != null ">
                order_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null ">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null ">
                #{orderNo,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_u3d_my_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.U3dMyCollection">
        update nft_u3d_my_collection
        <set>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_u3d_my_collection t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.U3dMyCollection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_u3d_my_collection t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="Base_Front_Column_List">
        t
        .
        id
        , t.collection_id
        , t.collection_detail_id
        , tc.name
        , tc.level_type
        ,  tc.file_type
        ,  tc.special_3d_flag
        ,  tc.cover_file_url
        ,  tc.file_url
        ,  tc.android_ab
        ,  tc.ios_ab
        , t.user_id
        , t.order_no
    </sql>

    <resultMap id="BaseFrontResultMap" type="com.std.core.pojo.response.U3dMyCollectionListRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="order_no" jdbcType="INTEGER" property="orderNo"/>
        <result column="special_3d_flag" jdbcType="VARCHAR" property="special3dFlag"/>
    </resultMap>

    <!-- 组合条件查询 -->
    <!--    <select id="selectByConditionFront" parameterType="com.std.core.pojo.domain.U3dMyCollection"-->
    <!--            resultMap="BaseFrontResultMap">-->
    <!--        select-->
    <!--        <include refid="Base_Front_Column_List"/>-->
    <!--        from nft_u3d_my_collection t,nft_collection tc-->
    <!--        where t.collection_id = tc.id-->
    <!--        and t.user_id = #{userId}-->
    <!--        and t.collection_detail_id in (select id from nft_collection_detail-->
    <!--        where owner_id =#{userId} and status not in ('4','6','11'))-->
    <!--        order by t.id asc-->
    <!--    </select>-->

    <!-- 组合条件查询 -->
    <select id="selectByConditionFront" parameterType="com.std.core.pojo.domain.U3dMyCollection"
            resultMap="BaseFrontResultMap">
        select
        <include refid="Base_Front_Column_List"/>
        from nft_u3d_my_collection t, nft_collection tc
        where t.collection_id = tc.id
        and t.user_id = #{userId}
        and t.collection_id in (select collection_id from nft_collection_detail
        where owner_id =#{userId} and status not in ('4','6','11'))
        order by t.id asc
    </select>
</mapper>