<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoExchangeDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoExchangeDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="exchange_id" jdbcType="BIGINT" property="exchangeId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.exchange_id
        , t.collection_id
        , t.collection_detail_id
        , t.token_id
        , t.yin_yao
        , t.yang_yao
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="exchangeId != null">
                AND t.exchange_id = #{exchangeId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="tokenId != null and tokenId != '' ">
                AND t.token_id = #{tokenId, jdbcType=VARCHAR}
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoExchangeDetail">
        insert into es_yao_exchange_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="exchangeId != null ">
                exchange_id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="collectionDetailId != null ">
                collection_detail_id,
              </if>
              <if test="tokenId != null and tokenId != '' ">
                token_id,
              </if>
              <if test="yinYao != null ">
                yin_yao,
              </if>
              <if test="yangYao != null ">
                yang_yao,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="exchangeId != null">
                #{exchangeId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId,jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into es_yao_exchange_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.exchangeId != null ">
                    exchange_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    token_id,
                </if>
                <if test="item.yinYao != null ">
                    yin_yao,
                </if>
                <if test="item.yangYao != null ">
                    yang_yao,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.exchangeId != null">
                    #{item.exchangeId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    #{item.tokenId,jdbcType=VARCHAR},
                </if>
                <if test="item.yinYao != null">
                    #{item.yinYao,jdbcType=DECIMAL},
                </if>
                <if test="item.yangYao != null">
                    #{item.yangYao,jdbcType=DECIMAL},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from es_yao_exchange_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoExchangeDetail">
        update es_yao_exchange_detail
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="exchangeId != null">
                exchange_id = #{exchangeId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id = #{tokenId,jdbcType=VARCHAR},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_exchange_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoExchangeDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_exchange_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectSumByUser" resultType="com.std.core.pojo.response.YaoExchangeDetailSumPageRes">
        select
               t.id,
            t.user_id userId,
            SUM(t.yin_yao) yinYao,
            SUM(t.yang_yao) yangYao,
        t.create_datetime createDatetime
        from es_yao_exchange_detail t
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
        group by t.user_id
    </select>
</mapper>