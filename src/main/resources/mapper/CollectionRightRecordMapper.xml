<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionRightRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionRightRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="right_id" jdbcType="BIGINT" property="rightId"/>
        <result column="right_company_id" jdbcType="BIGINT" property="rightCompanyId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="drop_type" jdbcType="VARCHAR" property="dropType"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="ref_collection_id" jdbcType="BIGINT" property="refCollectionId"/>
        <result column="advance_mins" jdbcType="INTEGER" property="advanceMins"/>
        <result column="discount_rate" jdbcType="DECIMAL" property="discountRate"/>
        <result column="drop_number" jdbcType="INTEGER" property="dropNumber"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.right_id
        , t.right_company_id
        , t.collection_id
        , t.drop_type
        , t.ref_type
        , t.ref_id
        , t.ref_collection_id
        , t.advance_mins
        , t.discount_rate
        , t.drop_number
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="rightId != null">
                AND t.right_id = #{rightId, jdbcType=BIGINT}
            </if>
            <if test="rightCompanyId != null">
                AND t.right_company_id = #{rightCompanyId, jdbcType=BIGINT}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="dropType != null and dropType != '' ">
                AND t.drop_type = #{dropType, jdbcType=VARCHAR}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refCollectionId != null">
                AND t.ref_collection_id = #{refCollectionId, jdbcType=BIGINT}
            </if>
            <if test="advanceMins != null">
                AND t.advance_mins = #{advanceMins, jdbcType=INTEGER}
            </if>
            <if test="discountRate != null">
                AND t.discount_rate = #{discountRate, jdbcType=DECIMAL}
            </if>
            <if test="dropNumber != null">
                AND t.drop_number = #{dropNumber, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionRightRecord">
        insert into nft_collection_right_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="rightId != null ">
                right_id,
            </if>
            <if test="rightCompanyId != null ">
                right_company_id,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="dropType != null and dropType != '' ">
                drop_type,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="refCollectionId != null">
                ref_collection_id,
            </if>
            <if test="advanceMins != null">
                advance_mins,
            </if>
            <if test="discountRate != null">
                discount_rate,
            </if>
            <if test="dropNumber != null">
                drop_number,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="rightId != null">
                #{rightId,jdbcType=BIGINT},
            </if>
            <if test="rightCompanyId != null">
                #{rightCompanyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="dropType != null and dropType != '' ">
                #{dropType, jdbcType=VARCHAR},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="refCollectionId != null">
                #{refCollectionId, jdbcType=BIGINT},
            </if>
            <if test="advanceMins != null">
                #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="discountRate != null">
                #{discountRate, jdbcType=DECIMAL},
            </if>
            <if test="dropNumber != null">
                #{dropNumber, jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_right_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteByrRefId">
        delete
        from nft_collection_right_record
        where ref_id = #{refId,jdbcType=BIGINT}
          and ref_type = #{refType}
    </delete>
    <delete id="deleteDropByrRefId">
        delete
        from nft_collection_right_record
        where ref_id = #{refId,jdbcType=BIGINT}
          and ref_type = #{refType}
          and drop_type = #{dropType}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionRightRecord">
        update nft_collection_right_record
        <set>
            <if test="rightId != null">
                right_id = #{rightId,jdbcType=BIGINT},
            </if>
            <if test="rightCompanyId != null">
                right_company_id = #{rightCompanyId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="dropType != null and dropType != '' ">
                drop_type = #{dropType, jdbcType=VARCHAR},
            </if>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="refCollectionId != null">
                ref_collection_id = #{refCollectionId, jdbcType=BIGINT},
            </if>
            <if test="advanceMins != null">
                advance_mins = #{advanceMins, jdbcType=INTEGER},
            </if>
            <if test="discountRate != null">
                discount_rate = #{discountRate, jdbcType=DECIMAL},
            </if>
            <if test="dropNumber != null">
                drop_number = #{dropNumber, jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionRightRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_right_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>