<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoMilletOrderMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoMilletOrder">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="yin_yao" jdbcType="DECIMAL" property="yinYao"/>
        <result column="yang_yao" jdbcType="DECIMAL" property="yangYao"/>
        <result column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.yin_yao
        , t.yang_yao
        , t.quantity
        , t.status
        , t.create_datetime
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="yinYao != null">
                AND t.yin_yao = #{yinYao, jdbcType=DECIMAL}
            </if>
            <if test="yangYao != null">
                AND t.yang_yao = #{yangYao, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=DECIMAL}
            </if>
            <if test="quantityMin != null">
                AND t.quantity <![CDATA[ >]]> #{quantityMin, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoMilletOrder" useGeneratedKeys="true" keyProperty="id">
        insert into es_yao_millet_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="yinYao != null ">
                yin_yao,
              </if>
              <if test="yangYao != null ">
                yang_yao,
              </if>
              <if test="quantity != null ">
                quantity,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="yinYao != null">
                #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from es_yao_millet_order
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoMilletOrder">
        update es_yao_millet_order
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="yinYao != null">
                yin_yao = #{yinYao,jdbcType=DECIMAL},
            </if>
            <if test="yangYao != null">
                yang_yao = #{yangYao,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_order t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoMilletOrder"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_order t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_order t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>