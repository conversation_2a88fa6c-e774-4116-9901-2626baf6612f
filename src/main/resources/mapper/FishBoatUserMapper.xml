<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.FishBoatUserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.FishBoatUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="boat_id" jdbcType="BIGINT" property="boatId"/>
        <result column="boat_type" jdbcType="VARCHAR" property="boatType"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="get_time" jdbcType="TIMESTAMP" property="getTime"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.name
        , t.user_id
        , t.boat_id
        , t.boat_type
        , t.collection_id
        , t.collection_detail_id
        , t.status
        , t.get_time
        , t.creater
        , t.creater_name
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="boatId != null">
                AND t.boat_id = #{boatId, jdbcType=BIGINT}
            </if>
            <if test="boatType != null and boatType != '' ">
                AND t.boat_type = #{boatType, jdbcType=VARCHAR}
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="getTime != null">
                AND t.get_time = #{getTime, jdbcType=TIMESTAMP}
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.FishBoatUser">
        insert into yg_fish_boat_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="boatId != null ">
                boat_id,
            </if>
            <if test="boatType != null and boatType != '' ">
                boat_type,
            </if>
            <if test="collectionId != null ">
                collection_id,
            </if>
            <if test="collectionDetailId != null ">
                collection_detail_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="getTime != null ">
                get_time,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="boatId != null">
                #{boatId,jdbcType=BIGINT},
            </if>
            <if test="boatType != null and boatType != '' ">
                #{boatType, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="getTime != null">
                #{getTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <insert id="insertBatchSelective">
        insert into yg_fish_boat_user
        (
        name,
        user_id,
        boat_id,
        boat_type,
        collection_id,
        collection_detail_id,
        status,
        get_time,
        creater,
        creater_name,
        create_datetime
        )
        values
        <foreach collection="list" separator="," index="index" item="item">
            (
            #{item.name,jdbcType=VARCHAR},
            #{item.userId,jdbcType=BIGINT},
            #{item.boatId,jdbcType=BIGINT},
            #{item.boatType,jdbcType=VARCHAR},
            #{item.collectionId,jdbcType=BIGINT},
            #{item.collectionDetailId,jdbcType=BIGINT},
            #{item.status, jdbcType=VARCHAR},
            #{item.getTime,jdbcType=TIMESTAMP},
            #{item.creater,jdbcType=BIGINT},
            #{item.createrName,jdbcType=VARCHAR},
            #{item.createDatetime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from yg_fish_boat_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.FishBoatUser">
        update yg_fish_boat_user
        <set>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="boatId != null">
                boat_id = #{boatId,jdbcType=BIGINT},
            </if>
            <if test="boatType != null and boatType != '' ">
                boat_type = #{boatType, jdbcType=VARCHAR},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="getTime != null">
                get_time = #{getTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_boat_user t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.FishBoatUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_boat_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_fish_boat_user t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
</mapper>