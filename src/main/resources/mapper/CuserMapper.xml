<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CuserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Cuser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="member_flag" jdbcType="VARCHAR" property="memberFlag"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="is_specific_account" jdbcType="VARCHAR" property="isSpecificAccount"/>
        <result column="virtual_income" jdbcType="DECIMAL" property="virtualIncome"/>
    </resultMap>

    <resultMap id="RichResultMap" type="com.std.core.pojo.domain.Cuser" extends="BaseResultMap">
        <result column="login_name" jdbcType="VARCHAR" property="loginName"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="nickname" jdbcType="VARCHAR" property="nickname"/>
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="photo" jdbcType="VARCHAR" property="photo"/>
        <result column="node_level" jdbcType="VARCHAR" property="nodeLevel"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="recovery_status" jdbcType="VARCHAR" property="recoveryStatus"/>
        <result column="real_name" jdbcType="VARCHAR" property="realName"/>
        <result column="id_no" jdbcType="VARCHAR" property="idNo"/>
        <result column="front_img" jdbcType="VARCHAR" property="frontImg"/>
        <result column="back_img" jdbcType="VARCHAR" property="backImg"/>
        <result column="recommenderName" jdbcType="VARCHAR" property="recommenderName"/>
        <result column="register_datetime" jdbcType="TIMESTAMP" property="registerDatetime"/>
        <result column="register_ip" jdbcType="VARCHAR" property="registerIp"/>
        <result column="invite_no" jdbcType="BIGINT" property="inviteNo"/>
        <result column="user_referee" jdbcType="BIGINT" property="userReferee"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="last_login_datetime" jdbcType="TIMESTAMP" property="lastLoginDatetime"/>
        <result column="grouping" jdbcType="VARCHAR" property="grouping"/>
        <result column="channel_flag" jdbcType="VARCHAR" property="channelFlag"/>
        <result column="is_channel" jdbcType="VARCHAR" property="isChannel"/>
        <result column="channel_id" jdbcType="VARCHAR" property="channelId"/>
        <result column="identify_style" jdbcType="VARCHAR" property="identifyStyle"/>
        <result column="login_status" jdbcType="VARCHAR" property="loginStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.member_flag
        , t.authentication_status
        , t.rate
        , t.create_datetime
        , t.is_specific_account
        , t.virtual_income
    </sql>

    <sql id="Rich_Column_List">
        <include refid="Base_Column_List"/>
        , tu.login_name
        , tu.nickname
        , tu.mobile
        , tu.email
        , tu.photo
        , tu.status
        , tu.real_name
        , tu.id_no
        , tu.register_datetime
        , tu.register_ip
        , tu.invite_no
        , tu.user_referee
        , tu.updater_name
        , tu.update_datetime
        , tu.remark
        , tu.last_login_datetime
        , tu.channel_flag
        , CONCAT(ifnull(ta.real_name,ta.nickname) ,'(',REPLACE ( ta.mobile, SUBSTR(ta.mobile,4, 4 ), '****'),')') as recommenderName
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                AND t.member_flag = #{memberFlag, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND tu.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="userReferee != null and userReferee != '' ">
                AND tu.user_referee = #{userReferee, jdbcType=VARCHAR}
            </if>
            <if test="userName != null and userName != '' ">
                AND tu.nickname LIKE concat('%',#{userName, jdbcType=VARCHAR},'%')
            </if>
            <if test="isSpecificAccount != null and isSpecificAccount != '' ">
                AND t.is_specific_account =#{isSpecificAccount,jdbcType=VARCHAR}
            </if>
            <if test="virtualIncome != null ">
                AND t.virtual_income =#{virtualIncome,jdbcType=DECIMAL}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Cuser" useGeneratedKeys="true"
            keyProperty="id">
        insert into tmm_cuser
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                member_flag,
            </if>
            <if test="isSpecificAccount != null and isSpecificAccount != '' ">
                is_specific_account,
            </if>
            <if test="virtualIncome != null ">
                virtual_income,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                #{memberFlag,jdbcType=VARCHAR},
            </if>
            <if test="isSpecificAccount != null and isSpecificAccount != '' ">
                #{isSpecificAccount,jdbcType=VARCHAR},
            </if>
            <if test="virtualIncome != null ">
                #{virtualIncome,jdbcType=DECIMAL},
            </if>
        </trim>
    </insert>
    <insert id="insertResetRecord">
        insert into tstd_user_reset_redord
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="content != null and content != '' ">
                content,
            </if>
            <if test="creater != null ">
                creater,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="creater != null ">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null ">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tmm_cuser
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Cuser">
        update tmm_cuser
        <set>
            <if test="memberFlag != null and memberFlag != '' ">
                member_flag = #{memberFlag,jdbcType=VARCHAR},
            </if>
            <if test="rate != null">
                rate = #{rate,jdbcType=DECIMAL},
            </if>
            <if test="isSpecificAccount != null and isSpecificAccount != '' ">
                is_specific_account =#{isSpecificAccount,jdbcType=VARCHAR},
            </if>
            <if test="virtualIncome != null ">
                virtual_income =#{virtualIncome,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="resetRealName" parameterType="com.std.core.pojo.domain.User">
        update tsys_user
        set identify_style  = #{identifyStyle},
            identify_status = #{identifyStatus},
            id_kind         = null,
            id_no           = null,
            real_name       = null

        where id = #{id}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="RichResultMap">
        select
        <include refid="Rich_Column_List"/>
        , tu.grouping
        , (CASE
        WHEN ( CASE WHEN tunl.way = '0' THEN tunl.node_level_auto ELSE tunl.node_level_manual END ) IS NULL THEN
        0 ELSE ( CASE WHEN tunl.way = '0' THEN tunl.node_level_auto ELSE tunl.node_level_manual END )
        END
        ) AS node_level
        from tmm_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        left join tsys_user ta on tu.user_referee=ta.id
        left join tstd_user_node_level tunl on (t.user_id = tunl.user_id and tunl.type='0')
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询 -->
    <select id="selectByUserId" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select *
        from tmm_cuser t
        where t.user_id = #{userId,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_cuser t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectRichByCondition" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="RichResultMap">
        select
        <include refid="Rich_Column_List"/>
        , tu.grouping
        from tmm_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectINfo" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="RichResultMap">
        select
        t.id
        , tu.id as user_id
        , tu.login_name
        , tu.nickname
        , tu.mobile
        , tu.email
        , tu.photo
        , tu.status
        , tu.register_datetime
        , tu.register_ip
        , tu.invite_no
        , tu.user_referee
        , tu.remark
        , tu.last_login_datetime
        , tu.real_name
        , tu.grouping
        ,tu.channel_flag
        ,tu.channel_id
        , tu.is_channel
        , (CASE
        WHEN ( CASE WHEN tunl.way = '0' THEN tunl.node_level_auto ELSE tunl.node_level_manual END ) IS NULL THEN
        0 ELSE ( CASE WHEN tunl.way = '0' THEN tunl.node_level_auto ELSE tunl.node_level_manual END )
        END
        ) AS node_level
        , t.authentication_status
        , t.member_flag
        , t.rate
        , t.virtual_income
        from tmm_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        <if test="keywords2 != null and keywords2 != '' ">
            INNER JOIN tsys_user tu2 ON tu.user_referee = tu2.id
        </if>
        left join tstd_user_node_level tunl on t.user_id = tunl.user_id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="loginName != null and loginName != '' ">
                AND tu.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                AND tu.channel_flag like concat('%',#{channelFlag, jdbcType=VARCHAR},'%')
            </if>
            <if test="isChannel != null and isChannel != '' ">
                AND tu.is_channel = #{isChannel, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND tu.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="realName != null and realName != '' ">
                AND tu.real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND tu.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND tu.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="nodeLevel != null">
                <choose>
                    <when test="nodeLevel == 0 ">
                        and (tunl.node_level_auto is null or tunl.node_level_manual is null)
                    </when>
                    <otherwise>
                        AND ((tunl.node_level_auto = #{nodeLevel, jdbcType=INTEGER} AND tunl.way = '0')
                        OR (tunl.node_level_manual = #{nodeLevel, jdbcType=INTEGER} AND tunl.way = '1'))
                    </otherwise>
                </choose>
            </if>
            <if test="isFreeUser != null and isFreeUser != '' ">
                AND tu.user_referee is null
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null ">
                AND tu.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.id_no like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="keywords2 != null and keywords2 != '' ">
                AND (tu2.nickname like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                or tu2.mobile like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                or tu2.real_name like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                AND t.member_flag = #{memberFlag, jdbcType=VARCHAR}
            </if>
            <if test="nodeLevelType != null and nodeLevelType != '' ">
                AND tunl.type = #{nodeLevelType, jdbcType=VARCHAR}
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <sql id="Base_Oss_list">
        id
        , user_id
        , login_name
        , nickname
        , mobile
        , email
        , photo
        , status
        , recovery_status
        , register_datetime
        , register_ip
        , invite_no
        , user_referee
        , remark
        , last_login_datetime
        , real_name
        , grouping
        , channel_flag
        , channel_id
        , is_channel
        , identify_style
        , node_level
        , authentication_status
        , member_flag
        , rate
        , virtual_income
        , loginStatus
    </sql>

    <select id="select" parameterType="com.std.core.pojo.domain.Cuser"
            resultMap="RichResultMap">
        select
        t.id
        , tu.id as user_id
        , tu.login_name
        , tu.nickname
        , tu.mobile
        , tu.email
        , tu.photo
        , tu.status
        , tu.recovery_status
        , tu.register_datetime
        , tu.register_ip
        , tu.invite_no
        , tu.user_referee
        , tu.remark
        , tu.last_login_datetime
        , tu.real_name
        , tu.grouping
        , tu.channel_flag
        , tu.channel_id
        , tu.is_channel
        , tu.identify_style
        , t.authentication_status
        , t.member_flag
        , t.rate
        , t.virtual_income
        , tu.login_status
        from tmm_cuser t
        INNER JOIN tsys_user tu ON t.user_id = tu.id
        <if test="keywords2 != null and keywords2 != '' ">
            INNER JOIN tsys_user tu2 ON tu.user_referee = tu2.id
        </if>
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="loginName != null and loginName != '' ">
                AND tu.login_name = #{loginName, jdbcType=VARCHAR}
            </if>
            <if test="channelFlag != null and channelFlag != '' ">
                AND tu.channel_flag like concat('%',#{channelFlag, jdbcType=VARCHAR},'%')
            </if>
            <if test="isChannel != null and isChannel != '' ">
                AND tu.is_channel = #{isChannel, jdbcType=VARCHAR}
            </if>
            <if test="channelId != null ">
                AND tu.channel_id = #{channelId, jdbcType=BIGINT}
            </if>
            <if test="realName != null and realName != '' ">
                AND tu.real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="mobile != null and mobile != '' ">
                AND tu.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND tu.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="identifyStyle != null and identifyStyle != '' ">
                AND tu.identify_style = #{identifyStyle, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND tu.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="isFreeUser != null and isFreeUser != '' ">
                AND tu.user_referee is null
            </if>
            <if test="userId != null ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userReferee != null ">
                AND tu.user_referee = #{userReferee, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (tu.nickname like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.mobile like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.real_name like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                or tu.id_no like concat('%',#{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="keywords2 != null and keywords2 != '' ">
                AND (tu2.nickname like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                or tu2.mobile like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                or tu2.real_name like concat('%',#{keywords2, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="memberFlag != null and memberFlag != '' ">
                AND t.member_flag = #{memberFlag, jdbcType=VARCHAR}
            </if>
            <if test="isRealName != null and isRealName != '' ">
                AND (tu.id_no is not null and tu.real_name is not null)
            </if>
            <if test="noRealName != null and noRealName != '' ">
                AND (tu.id_no is null or tu.real_name is null)
            </if>
            <if test="grouping != null and grouping != '' ">
                AND tu.grouping like concat('%',#{grouping, jdbcType=VARCHAR},'%')
            </if>
            <if test="recoveryStatus != null and recoveryStatus != '' ">
                AND tu.recovery_status = #{recoveryStatus, jdbcType=VARCHAR}
            </if>
            <if test="loginStatus != null and loginStatus != '' ">
                AND tu.login_status = #{loginStatus, jdbcType=VARCHAR}
            </if>
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>

    </select>

    <select id="selectVague" resultMap="RichResultMap" parameterType="com.std.core.pojo.domain.Cuser">
        select tu.id as user_id,CONCAT(tu.nickname,'-',tu.mobile) as userName from tmm_cuser t
        left join tsys_user tu on t.user_id=tu.id
        <include refid="where_condition"/>

    </select>

    <select id="getSpecificUser" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_cuser t where t.is_specific_account='1'
    </select>
</mapper>