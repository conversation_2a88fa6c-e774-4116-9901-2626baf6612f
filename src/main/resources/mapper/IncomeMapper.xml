<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IncomeMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Income">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="ref_user_id" jdbcType="BIGINT" property="refUserId"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="team_type" jdbcType="VARCHAR" property="teamType"/>
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"/>
        <result column="amount_type" jdbcType="VARCHAR" property="amountType"/>
        <result column="income_time" jdbcType="TIMESTAMP" property="incomeTime"/>
        <result column="base_amount" jdbcType="DECIMAL" property="baseAmount"/>
        <result column="rate" jdbcType="DECIMAL" property="rate"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="real_amount" jdbcType="DECIMAL" property="realAmount"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="settle_time" jdbcType="TIMESTAMP" property="settleTime"/>
        <result column="settle_amount" jdbcType="DECIMAL" property="settleAmount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.ref_id
        , t.ref_user_id
        , t.type
        , t.team_type
        , t.node_type
        , t.amount_type
        , t.income_time
        , t.base_amount
        , t.rate
        , t.amount
        , t.fee
        , t.real_amount
        , t.status
        , t.settle_time
        , t.settle_amount
        , t.create_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="userList != null and userList.size() != 0 ">
                AND t.user_id in
                <foreach item="item" index="index" collection="userList" open="(" separator=","
                        close=")">
                    #{item.id,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="refUserId != null">
                AND t.ref_user_id = #{refUserId, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="teamType != null and teamType != '' ">
                AND t.team_type = #{teamType, jdbcType=VARCHAR}
            </if>
            <if test="nodeType != null and nodeType != '' ">
                AND t.node_type = #{nodeType, jdbcType=VARCHAR}
            </if>
            <if test="amountType != null and amountType != '' ">
                AND t.amount_type = #{amountType, jdbcType=VARCHAR}
            </if>
            <if test="incomeTime != null">
                AND t.income_time = #{incomeTime, jdbcType=TIMESTAMP}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="fee != null">
                AND t.fee = #{fee, jdbcType=DECIMAL}
            </if>
            <if test="realAmount != null">
                AND t.real_amount = #{realAmount, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="settleTime != null">
                AND t.settle_time = #{settleTime, jdbcType=TIMESTAMP}
            </if>
            <if test="settleAmount != null">
                AND t.settle_amount = #{settleAmount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeStart != null">
                <![CDATA[AND t.create_datetime >= #{createDatetimeStart}]]>
            </if>

            <if test="createDatetimeEnd != null">
                <![CDATA[AND t.create_datetime <= #{createDatetimeEnd}]]>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="keywords2 != null and keywords2 != '' ">
                AND (
                tu2.mobile like concat('%', #{keywords2, jdbcType=VARCHAR},'%') OR
                tu2.id_no like concat('%', #{keywords2, jdbcType=VARCHAR},'%') OR
                tu2.real_name like concat('%', #{keywords2, jdbcType=VARCHAR},'%')
                )
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Income">
        insert into tstd_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="refUserId != null ">
                ref_user_id,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="teamType != null and teamType != '' ">
                team_type,
            </if>
            <if test="nodeType != null and nodeType != '' ">
                node_type,
            </if>
            <if test="amountType != null and amountType != '' ">
                amount_type,
            </if>
            <if test="incomeTime != null ">
                income_time,
            </if>
            <if test="baseAmount != null ">
                base_amount,
            </if>
            <if test="rate != null ">
                rate,
            </if>
            <if test="amount != null ">
                amount,
            </if>
            <if test="fee != null ">
                fee,
            </if>
            <if test="realAmount != null ">
                real_amount,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="settleTime != null ">
                settle_time,
            </if>
            <if test="settleAmount != null ">
                settle_amount,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="refUserId != null">
                #{refUserId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="teamType != null and teamType != '' ">
                #{teamType,jdbcType=VARCHAR},
            </if>
            <if test="nodeType != null and nodeType != '' ">
                #{nodeType,jdbcType=VARCHAR},
            </if>
            <if test="amountType != null and amountType != '' ">
                #{amountType,jdbcType=VARCHAR},
            </if>
            <if test="incomeTime != null">
                #{incomeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="baseAmount != null">
                #{baseAmount,jdbcType=DECIMAL},
            </if>
            <if test="rate != null">
                #{rate,jdbcType=DECIMAL},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="settleTime != null">
                #{settleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleAmount != null">
                #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_income
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Income">
        update tstd_income
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="refUserId != null">
                ref_user_id = #{refUserId,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="teamType != null and teamType != '' ">
                team_type = #{teamType,jdbcType=VARCHAR},
            </if>
            <if test="nodeType != null and nodeType != '' ">
                node_type = #{nodeType,jdbcType=VARCHAR},
            </if>
            <if test="amountType != null and amountType != '' ">
                amount_type = #{amountType,jdbcType=VARCHAR},
            </if>
            <if test="incomeTime != null">
                income_time = #{incomeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="realAmount != null">
                real_amount = #{realAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="settleTime != null">
                settle_time = #{settleTime,jdbcType=TIMESTAMP},
            </if>
            <if test="settleAmount != null">
                settle_amount = #{settleAmount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_income t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Income"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_income t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <if test="keywords2 != null and keywords2 != ''   ">
            INNER JOIN tsys_user tu2 ON t.`ref_user_id`=tu2.`id`
        </if>
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询数量 -->
    <select id="selectTotalRealAmount" parameterType="com.std.core.pojo.domain.Income" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(real_amount), 0) FROM tstd_income t
        <include refid="where_condition"/>
    </select>

    <select id="statisticsPageByDay" resultType="com.std.core.pojo.response.IncomeStatisticsByDayRes">
          SELECT  DATE_FORMAT(create_datetime,'%Y-%m-%d') AS dayDate,type,amount_type AS amountType,sum(real_amount)  AS amount
           FROM tstd_income GROUP BY type,DATE_FORMAT(create_datetime,'%Y-%m-%d'),amount_type
    </select>

    <select id="selectTotalAmount" resultType="java.math.BigDecimal">
        SELECT coalesce(sum(real_amount),0)
        FROM tstd_income
        where status = '1'
        and type != '4'
    </select>

</mapper>