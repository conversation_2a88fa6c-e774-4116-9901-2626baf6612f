<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.SubscriptionSendMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.SubscriptionSend">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="ref_type" jdbcType="VARCHAR" property="refType"/>
        <result column="ref_id" jdbcType="BIGINT" property="refId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="registration_id" jdbcType="VARCHAR" property="registrationId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="send_contnet" jdbcType="VARCHAR" property="sendContnet"/>
        <result column="send_datetime" jdbcType="TIMESTAMP" property="sendDatetime"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.ref_type
        , t.ref_id
        , t.user_id
        , t.registration_id
        , t.status
        , t.send_contnet
        , t.send_datetime
        , t.create_datetime
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="refType != null and refType != '' ">
                AND t.ref_type = #{refType, jdbcType=VARCHAR}
            </if>
            <if test="refId != null">
                AND t.ref_id = #{refId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="registrationId != null and registrationId != '' ">
                AND t.registration_id = #{registrationId, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="sendContnet != null and sendContnet != '' ">
                AND t.send_contnet = #{sendContnet, jdbcType=VARCHAR}
            </if>
            <if test="sendDatetime != null">
                AND t.send_datetime = #{sendDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.SubscriptionSend">
        insert into tstd_subscription_send
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="refType != null and refType != '' ">
                ref_type,
            </if>
            <if test="refId != null ">
                ref_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="registrationId != null and registrationId != '' ">
                registration_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="sendContnet != null and sendContnet != '' ">
                send_contnet,
            </if>
            <if test="sendDatetime != null ">
                send_datetime,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="updateDatetime != null">
                update_datetime,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="refType != null and refType != '' ">
                #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                #{refId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="registrationId != null and registrationId != '' ">
                #{registrationId,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="sendContnet != null and sendContnet != '' ">
                #{sendContnet,jdbcType=VARCHAR},
            </if>
            <if test="sendDatetime != null">
                #{sendDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_subscription_send
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.SubscriptionSend">
        update tstd_subscription_send
        <set>
            <if test="refType != null and refType != '' ">
                ref_type = #{refType,jdbcType=VARCHAR},
            </if>
            <if test="refId != null">
                ref_id = #{refId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="registrationId != null and registrationId != '' ">
                registration_id = #{registrationId,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="sendContnet != null and sendContnet != '' ">
                send_contnet = #{sendContnet,jdbcType=VARCHAR},
            </if>
            <if test="sendDatetime != null">
                send_datetime = #{sendDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime, jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_subscription_send t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.SubscriptionSend"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_subscription_send t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_subscription_send t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectSend" parameterType="com.std.core.pojo.domain.SubscriptionSend"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_subscription_send t
        where t.status='0'
        and t.send_datetime <![CDATA[<=]]> #{date}

    </select>
</mapper>