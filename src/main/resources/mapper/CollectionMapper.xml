<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Collection">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="three_channel_type" jdbcType="VARCHAR" property="threeChannelType"/>
        <result column="collection_hash" jdbcType="VARCHAR" property="collectionHash"/>
        <result column="three_channel_id" jdbcType="VARCHAR" property="threeChannelId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="serial_name" jdbcType="VARCHAR" property="serialName"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="plate_category" jdbcType="VARCHAR" property="plateCategory"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="buy_type" jdbcType="VARCHAR" property="buyType"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="content_type" jdbcType="VARCHAR" property="contentType"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="file_url" jdbcType="VARCHAR" property="fileUrl"/>
        <result column="produced_id" jdbcType="BIGINT" property="producedId"/>
        <result column="author_id" jdbcType="BIGINT" property="authorId"/>
        <result column="divide_author_id" jdbcType="BIGINT" property="divideAuthorId"/>
        <result column="tags" jdbcType="VARCHAR" property="tags"/>
        <result column="right_content" jdbcType="VARCHAR" property="rightContent"/>
        <result column="right_type" jdbcType="VARCHAR" property="rightType"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
        <result column="market_quantity" jdbcType="INTEGER" property="marketQuantity"/>
        <result column="remain_quantity" jdbcType="INTEGER" property="remainQuantity"/>
        <result column="plat_remain_quantity" jdbcType="INTEGER" property="platRemainQuantity"/>
        <result column="chain_type" jdbcType="VARCHAR" property="chainType"/>
        <result column="contract_id" jdbcType="BIGINT" property="contractId"/>
        <result column="pay_amount" jdbcType="DECIMAL" property="payAmount"/>
        <result column="pay_type" jdbcType="VARCHAR" property="payType"/>
        <result column="pay_order_code" jdbcType="VARCHAR" property="payOrderCode"/>
        <result column="pay_status" jdbcType="VARCHAR" property="payStatus"/>
        <result column="pay_datetime" jdbcType="TIMESTAMP" property="payDatetime"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="token_import_id" jdbcType="BIGINT" property="tokenImportId"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="channel_transfer_status" jdbcType="VARCHAR" property="channelTransferStatus"/>
        <result column="availableSellQuantity" jdbcType="BIGINT" property="availableSellQuantity"/>
        <result column="pay_balance_amount" jdbcType="DECIMAL" property="payBalanceAmount"/>
        <result column="pay_cash_amount" jdbcType="DECIMAL" property="payCashAmount"/>
        <result column="storage_fee" jdbcType="DECIMAL" property="storageFee"/>
        <result column="file_size" jdbcType="DECIMAL" property="fileSize"/>
        <result column="commission_amount" jdbcType="DECIMAL" property="commissionAmount"/>
        <result column="down_status" jdbcType="DECIMAL" property="downStatus"/>
        <result column="module_ids" jdbcType="VARCHAR" property="moduleIds"/>
        <result column="lock_time" jdbcType="INTEGER" property="lockTime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="max_exchange_time" jdbcType="INTEGER" property="maxExchangeTime"/>
        <result column="transform_limit_time" jdbcType="INTEGER" property="transformLimitTime"/>

        <result column="android_ab" jdbcType="VARCHAR" property="androidAb"/>
        <result column="ios_ab" jdbcType="VARCHAR" property="iosAb"/>
        <result column="u3d_flag" jdbcType="VARCHAR" property="u3dFlag"/>
        <result column="ticket_type" jdbcType="VARCHAR" property="ticketType"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="single_max_quantity" jdbcType="INTEGER" property="singleMaxQuantity"/>
        <result column="plat_divide_rate" jdbcType="DECIMAL" property="platDivideRate"/>
        <result column="account_integral" jdbcType="DECIMAL" property="accountIntegral"/>
        <result column="use_flag" jdbcType="VARCHAR" property="useFlag"/>
        <result column="use_way" jdbcType="VARCHAR" property="useWay"/>
        <result column="class_id" jdbcType="VARCHAR" property="classId"/>
        <result column="publish_xmeta" jdbcType="VARCHAR" property="publishXmeta"/>
        <result column="publish_xmeta_style" jdbcType="VARCHAR" property="publishXmetaStyle"/>
        <result column="exchange_xmeta" jdbcType="VARCHAR" property="exchangeXmeta"/>
        <result column="red_packet_flag" jdbcType="VARCHAR" property="redPacketFlag"/>
        <result column="meta_biz_type" jdbcType="VARCHAR" property="metaBizType"/>
        <result column="meta_biz_id" jdbcType="BIGINT" property="metaBizId"/>
        <result column="meta_biz_type" jdbcType="VARCHAR" property="metaBizType"/>
        <result column="special_3d_Flag" jdbcType="VARCHAR" property="special3dFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.three_channel_type
        , t.three_channel_id
        , t.collection_hash
        , t.user_id
        , t.serial_name
        , t.category
        , t.plate_category
        , t.name
        , t.type
        , t.buy_type
        , t.level_type
        , t.file_type
        , t.cover_file_url
        , t.content_type
        , t.content
        , t.file_url
        , t.produced_id
        , t.author_id
        , t.divide_author_id
        , t.tags

        , t.right_content
        , t.right_type

        , t.total_quantity
        , t.market_quantity
        , t.remain_quantity
        , t.plat_remain_quantity
        , t.chain_type
        , t.contract_id
        , t.pay_amount
        , t.pay_type
        , t.pay_order_code
        , t.pay_status
        , t.pay_datetime
        , t.create_datetime
        , t.token_import_id
        , t.status
        , t.channel_transfer_status
        , t.pay_balance_amount
        , t.pay_cash_amount
        , t.storage_fee
        , t.file_size
        , t.commission_amount
        , t.down_status
        , t.lock_time
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.max_exchange_time
        , t.transform_limit_time

        , t.android_ab
        , t.ios_ab
        , t.u3d_flag
        , t.ticket_type
        , t.integral_price
        , t.single_max_quantity
        , t.plat_divide_rate
        , t.account_integral
        , t.use_flag
        , t.use_way
        , t.class_id
        , t.publish_xmeta
        , t.publish_xmeta_style
        , t.exchange_xmeta
        , t.red_packet_flag
        , t.meta_biz_type
        , t.meta_biz_id
        , t.special_3d_flag
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                AND t.three_channel_type = #{threeChannelType, jdbcType=VARCHAR}
            </if>
            <if test="threeChannelId != null">
                AND t.three_channel_id = #{threeChannelId, jdbcType=BIGINT}
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                AND t.collection_hash = #{collectionHash, jdbcType=VARCHAR}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="producedId != null">
                AND t.produced_id = #{producedId, jdbcType=BIGINT}
            </if>
            <if test="authorId != null">
                AND t.author_id = #{authorId, jdbcType=BIGINT}
            </if>
            <if test="divideAuthorId != null">
                AND t.divide_author_id = #{divideAuthorId, jdbcType=BIGINT}
            </if>
            <if test="serialName != null and serialName != '' ">
                AND t.serial_name = #{serialName, jdbcType=VARCHAR}
            </if>
            <if test="category != null and category != '' ">
                AND t.category = #{category, jdbcType=VARCHAR}
            </if>
            <if test="categoryList != null and categoryList.size() != 0 ">
                AND t.category in
                <foreach item="item" index="index" collection="categoryList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="nameStr != null and nameStr != '' ">
                AND t.name = #{nameStr, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="noType != null and noType != '' ">
                AND t.type != #{noType, jdbcType=VARCHAR}
            </if>
            <if test="typeList != null and typeList.size() != 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="buyType != null and buyType != '' ">
                AND t.buy_type = #{buyType, jdbcType=VARCHAR}
            </if>
            <if test="noBuyType != null and noBuyType != '' ">
                AND t.buy_type <![CDATA[ != ]]> #{noBuyType, jdbcType=VARCHAR}
            </if>
            <if test="buyTypeList != null and buyTypeList.size() != 0 ">
                AND t.buy_type in
                <foreach item="item" index="index" collection="buyTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="levelType != null and levelType != '' ">
                AND t.level_type = #{levelType, jdbcType=VARCHAR}
            </if>
            <if test="fileType != null and fileType != '' ">
                AND t.file_type = #{fileType, jdbcType=VARCHAR}
            </if>
            <if test="contentType != null and contentType != '' ">
                AND t.content_type = #{contentType, jdbcType=VARCHAR}
            </if>
            <if test="content != null and content != '' ">
                AND t.content = #{content, jdbcType=VARCHAR}
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                AND t.cover_file_url = #{coverFileUrl, jdbcType=VARCHAR}
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                AND t.file_url = #{fileUrl, jdbcType=VARCHAR}
            </if>
            <if test="tags != null and tags != '' ">
                AND t.tags = #{tags, jdbcType=VARCHAR}
            </if>

            <if test="rightContent != null and rightContent != '' ">
                AND t.right_content = #{rightContent, jdbcType=VARCHAR}
            </if>
            <if test="rightType != null and rightType != '' ">
                AND t.right_type = #{rightType, jdbcType=VARCHAR}
            </if>

            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=INTEGER}
            </if>
            <if test="marketQuantity != null">
                AND t.market_quantity = #{marketQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=INTEGER}
            </if>
            <if test="remainQuantityFlag != null and remainQuantityFlag > 0 ">
                AND t.remain_quantity <![CDATA[ >]]> '0'
            </if>
            <if test="platRemainQuantity != null">
                AND t.plat_remain_quantity = #{platRemainQuantity, jdbcType=INTEGER}
            </if>
            <if test="chainType != null and chainType != '' ">
                AND t.chain_type = #{chainType, jdbcType=VARCHAR}
            </if>
            <if test="contractId != null">
                AND t.contract_id = #{contractId, jdbcType=BIGINT}
            </if>
            <if test="payAmount != null">
                AND t.pay_amount = #{payAmount, jdbcType=DECIMAL}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payDatetime != null">
                AND t.pay_datetime = #{payDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payCancelDatetime != null">
                AND t.pay_datetime &lt; #{payCancelDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetimeStart != null ">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="tokenImportId != null">
                AND t.token_import_id = #{tokenImportId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                AND t.channel_transfer_status = #{channelTransferStatus, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="noStatusList != null and noStatusList.size() != 0 ">
                AND t.status not in
                <foreach item="item" index="index" collection="noStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="collectionIdList != null and collectionIdList.size() != 0 ">
                AND t.id in
                <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="rejectCollectionIdList != null and rejectCollectionIdList.size() != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="rejectCollectionIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userIdNotExistList != null and userIdNotExistList.size() != 0 ">
                AND t.user_id not in
                <foreach item="item" index="index" collection="userIdNotExistList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="deduction != null and deduction != '' ">
                AND t.pay_balance_amount <![CDATA[ >]]> 0
            </if>
            <if test="noDeduction != null and noDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ <=]]> 0
            </if>
            <if test="commissionAmount != null">
                AND t.commission_amount = #{commissionAmount, jdbcType=DECIMAL}
            </if>
            <if test="downStatus != null and downStatus != '' ">
                AND t.down_status = #{downStatus, jdbcType=VARCHAR}
            </if>
            <if test="moduleIds != null and moduleIds != '' ">
                AND t.module_ids = #{moduleIds, jdbcType=VARCHAR}
            </if>
            <if test="moduleIdsFlag != null and moduleIdsFlag != '' ">
                AND t.module_ids is null
            </if>

            <if test="lockTime != null">
                AND t.lock_time = #{lockTime, jdbcType=INTEGER}
            </if>
            <if test="noLockTime != null">
                AND t.lock_time <![CDATA[ != ]]> '-1'
            </if>
            <if test="lockTimeMin != null">
                AND t.lock_time <![CDATA[ >=]]> #{lockTimeMin, jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="transformLimitTime != null">
                AND t.transform_limit_time = #{transformLimitTime, jdbcType=INTEGER}
            </if>
            <if test="u3dFlag != null and u3dFlag != '' ">
                AND t.u3d_flag = #{u3dFlag, jdbcType=VARCHAR}
            </if>
            <if test="ticketType != null and ticketType != '' ">
                AND t.ticket_type = #{ticketType, jdbcType=VARCHAR}
            </if>
            <if test="integralPrice != null">
                AND t.integral_price = #{integralPrice, jdbcType=DECIMAL}
            </if>
            <if test="singleMaxQuantity != null">
                AND t.single_max_quantity = #{singleMaxQuantity, jdbcType=INTEGER}
            </if>
            <if test="platDivideRate != null">
                AND t.plat_divide_rate = #{platDivideRate, jdbcType=DECIMAL}
            </if>
            <if test="accountIntegral != null">
                AND t.account_integral = #{accountIntegral, jdbcType=DECIMAL}
            </if>
            <if test="useFlag != null and useFlag != '' ">
                AND t.use_flag = #{useFlag, jdbcType=VARCHAR}
            </if>
            <if test="useWay != null and useWay != '' ">
                AND t.use_way = #{useWay, jdbcType=VARCHAR}
            </if>
            <if test="chain != null and chain != '' ">
                AND t.contract_id in (select id from nft_contract where chain=#{chain, jdbcType=VARCHAR})
            </if>
            <if test="challengeFlag != null">
                AND t.id not in ( SELECT DISTINCT t.collection_id from nft_collection_detail t
                INNER JOIN nft_collection ta on t.collection_id=ta.id
                where t.lock_time=-1 AND t.transform_limit_time=-1 and ta.type='2')
            </if>
            <if test="classId != null and classId != '' ">
                AND t.class_id = #{classId, jdbcType=VARCHAR}
            </if>
            <if test="publishXmeta != null and publishXmeta != '' ">
                AND t.publish_xmeta = #{publishXmeta, jdbcType=VARCHAR}
            </if>
            <if test="publishXmetaStyle != null and publishXmetaStyle != '' ">
                AND t.publish_xmeta_style = #{publishXmetaStyle, jdbcType=VARCHAR}
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                AND t.exchange_xmeta = #{exchangeXmeta, jdbcType=VARCHAR}
            </if>
            <if test="redPacketFlag != null and redPacketFlag != '' ">
                AND t.red_packet_flag = #{redPacketFlag, jdbcType=VARCHAR}
            </if>
            <if test="metaBizType != null and metaBizType != '' ">
                AND t.meta_biz_type = #{metaBizType, jdbcType=VARCHAR}
            </if>
            <if test="metaBizId != null ">
                AND t.meta_biz_id = #{metaBizId, jdbcType=BIGINT}
            </if>
            <if test="special3dFlag != null ">
                AND t.special_3d_flag = #{special3dFlag, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Collection" useGeneratedKeys="true"
            keyProperty="id">
        insert into nft_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type,
            </if>
            <if test="threeChannelId != null">
                three_channel_id,
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                collection_hash,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="serialName != null and serialName != '' ">
                serial_name,
            </if>
            <if test="category != null and category != '' ">
                category,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="buyType != null and buyType != '' ">
                buy_type,
            </if>
            <if test="levelType != null and levelType != '' ">
                level_type,
            </if>
            <if test="fileType != null and fileType != '' ">
                file_type,
            </if>
            <if test="contentType != null ">
                content_type,
            </if>
            <if test="content != null ">
                content,
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                file_url,
            </if>
            <if test="producedId != null">
                produced_id,
            </if>
            <if test="authorId != null and authorId != '' ">
                author_id,
            </if>
            <if test="divideAuthorId != null">
                divide_author_id,
            </if>
            <if test="tags != null and tags != '' ">
                tags,
            </if>
            <if test="rightContent != null and rightContent != '' ">
                right_content,
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type,
            </if>

            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="marketQuantity != null">
                market_quantity ,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="platRemainQuantity != null">
                plat_remain_quantity,
            </if>
            <if test="chainType != null and chainType != '' ">
                chain_type,
            </if>
            <if test="contractId != null and contractId != '' ">
                contract_id,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="tokenImportId != null ">
                token_import_id,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                channel_transfer_status,
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount,
            </if>
            <if test="storageFee != null">
                storage_fee,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="commissionAmount != null">
                commission_amount,
            </if>
            <if test="downStatus != null and downStatus != '' ">
                down_status,
            </if>
            <if test="moduleIds != null and moduleIds != '' ">
                module_ids,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="maxExchangeTime != null ">
                max_exchange_time,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="androidAb != null and androidAb != '' ">
                android_ab,
            </if>
            <if test="iosAb != null and iosAb != '' ">
                ios_ab,
            </if>
            <if test="u3dFlag != null and u3dFlag != '' ">
                u3d_flag,
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type,
            </if>
            <if test="integralPrice != null">
                integral_price,
            </if>
            <if test="singleMaxQuantity != null">
                single_max_quantity,
            </if>
            <if test="platDivideRate != null">
                plat_divide_rate,
            </if>
            <if test="accountIntegral != null">
                account_integral,
            </if>
            <if test="useFlag != null and useFlag != '' ">
                use_flag,
            </if>
            <if test="useWay != null and useWay != '' ">
                use_way,
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                exchange_xmeta,
            </if>
            <if test="metaBizType != null and metaBizType != '' ">
                meta_biz_type,
            </if>
            <if test="metaBizId != null ">
                meta_biz_id,
            </if>
            <if test="special3dFlag != null and special3dFlag != '' ">
                special_3d_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                #{collectionHash, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="serialName != null and serialName != '' ">
                #{serialName,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != '' ">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="buyType != null and buyType != '' ">
                #{buyType, jdbcType=VARCHAR},
            </if>
            <if test="levelType != null and levelType != '' ">
                #{levelType,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != '' ">
                #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="producedId != null">
                #{producedId, jdbcType=BIGINT},
            </if>
            <if test="authorId != null and authorId != '' ">
                #{authorId,jdbcType=BIGINT},
            </if>
            <if test="divideAuthorId != null">
                #{divideAuthorId, jdbcType=BIGINT},
            </if>
            <if test="tags != null and tags != '' ">
                #{tags, jdbcType=VARCHAR},
            </if>
            <if test="rightContent != null and rightContent != '' ">
                #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                #{rightType, jdbcType=VARCHAR},
            </if>

            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="marketQuantity != null">
                #{marketQuantity, jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="platRemainQuantity != null">
                #{platRemainQuantity, jdbcType=INTEGER},
            </if>
            <if test="chainType != null and chainType != '' ">
                #{chainType,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null and contractId != '' ">
                #{contractId,jdbcType=BIGINT},
            </if>

            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="tokenImportId != null">
                #{tokenImportId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                #{channelTransferStatus, jdbcType=VARCHAR},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="storageFee != null">
                #{storageFee,jdbcType=DECIMAL},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount, jdbcType=DECIMAL},
            </if>
            <if test="downStatus != null and downStatus != '' ">
                #{downStatus, jdbcType=VARCHAR},
            </if>
            <if test="moduleIds != null and moduleIds != '' ">
                #{moduleIds, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="maxExchangeTime != null ">
                #{maxExchangeTime, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="androidAb != null and androidAb != '' ">
                #{androidAb,jdbcType=VARCHAR},
            </if>
            <if test="iosAb != null and iosAb != '' ">
                #{iosAb,jdbcType=VARCHAR},
            </if>
            <if test="u3dFlag != null and u3dFlag != '' ">
                #{u3dFlag,jdbcType=VARCHAR},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                #{ticketType, jdbcType=VARCHAR},
            </if>
            <if test="integralPrice != null">
                #{integralPrice, jdbcType=DECIMAL},
            </if>
            <if test="singleMaxQuantity != null">
                #{singleMaxQuantity, jdbcType=INTEGER},
            </if>
            <if test="platDivideRate != null">
                #{platDivideRate, jdbcType=DECIMAL},
            </if>
            <if test="accountIntegral != null">
                #{accountIntegral, jdbcType=DECIMAL},
            </if>
            <if test="useFlag != null and useFlag != '' ">
                #{useFlag, jdbcType=VARCHAR},
            </if>
            <if test="useWay != null and useWay != '' ">
                #{useWay, jdbcType=VARCHAR},
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                #{exchangeXmeta, jdbcType=VARCHAR},
            </if>
            <if test="metaBizType != null and metaBizType != '' ">
                #{metaBizType, jdbcType=VARCHAR},
            </if>
            <if test="metaBizId != null ">
                #{metaBizId, jdbcType=BIGINT},
            </if>
            <if test="special3dFlag != null and special3dFlag != '' ">
                #{special3dFlag, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="channelInsert" useGeneratedKeys="true" keyProperty="id">
        insert into nft_collection
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type,
            </if>
            <if test="threeChannelId != null">
                three_channel_id,
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                collection_hash,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="serialName != null and serialName != '' ">
                serial_name,
            </if>
            <if test="category != null and category != '' ">
                category,
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category,
            </if>
            <if test="name != null and name != '' ">
                name,
            </if>
            <if test="type != null and type != '' ">
                type,
            </if>
            <if test="buyType != null and buyType != '' ">
                buy_type,
            </if>
            <if test="levelType != null and levelType != '' ">
                level_type,
            </if>
            <if test="fileType != null and fileType != '' ">
                file_type,
            </if>
            <if test="contentType != null ">
                content_type,
            </if>
            <if test="content != null ">
                content,
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                file_url,
            </if>
            <if test="authorId != null and authorId != '' ">
                author_id,
            </if>
            <if test="tags != null and tags != '' ">
                tags,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="marketQuantity != null">
                market_quantity ,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="platRemainQuantity != null">
                plat_remain_quantity,
            </if>
            <if test="chainType != null and chainType != '' ">
                chain_type,
            </if>
            <if test="contractId != null and contractId != '' ">
                contract_id,
            </if>
            <if test="payAmount != null ">
                pay_amount,
            </if>
            <if test="payType != null and payType != '' ">
                pay_type,
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code,
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status,
            </if>
            <if test="payDatetime != null ">
                pay_datetime,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                channel_transfer_status,
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount,
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount,
            </if>
            <if test="storageFee != null">
                storage_fee,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="downStatus != null and downStatus != '' ">
                down_status,
            </if>
            <if test="lockTime != null">
                lock_time,
            </if>
            <if test="updater != null ">
                updater,
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="maxExchangeTime != null ">
                max_exchange_time,
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time,
            </if>
            <if test="useFlag != null and useFlag != '' ">
                use_flag,
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type,
            </if>
            <if test="useWay != null and useWay != '' ">
                use_way,
            </if>
            <if test="publishXmeta != null and publishXmeta != '' ">
                publish_xmeta,
            </if>
            <if test="publishXmetaStyle != null and publishXmetaStyle != '' ">
                publish_xmeta_style,
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                exchange_xmeta,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="threeChannelType != null and threeChannelType != '' ">
                #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                #{collectionHash, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="serialName != null and serialName != '' ">
                #{serialName,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != '' ">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="buyType != null and buyType != '' ">
                #{buyType, jdbcType=VARCHAR},
            </if>
            <if test="levelType != null and levelType != '' ">
                #{levelType,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != '' ">
                #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                #{content,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="authorId != null and authorId != '' ">
                #{authorId,jdbcType=BIGINT},
            </if>
            <if test="tags != null and tags != '' ">
                #{tags, jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="marketQuantity != null">
                #{marketQuantity, jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="platRemainQuantity != null">
                #{platRemainQuantity, jdbcType=INTEGER},
            </if>
            <if test="chainType != null and chainType != '' ">
                #{chainType,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null and contractId != '' ">
                #{contractId,jdbcType=BIGINT},
            </if>

            <if test="payAmount != null">
                #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                #{channelTransferStatus, jdbcType=VARCHAR},
            </if>
            <if test="payBalanceAmount != null">
                #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="storageFee != null">
                #{storageFee,jdbcType=DECIMAL},
            </if>
            <if test="fileSize != null">
                #{fileSize,jdbcType=DECIMAL},
            </if>
            <if test="downStatus != null and downStatus != '' ">
                #{downStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="maxExchangeTime != null ">
                #{maxExchangeTime, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="useFlag != null and useFlag != '' ">
                #{useFlag, jdbcType=VARCHAR},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                #{ticketType, jdbcType=VARCHAR},
            </if>
            <if test="useWay != null and useWay != '' ">
                #{useWay, jdbcType=VARCHAR},
            </if>
            <if test="publishXmeta != null and publishXmeta != '' ">
                #{publishXmeta, jdbcType=VARCHAR},
            </if>
            <if test="publishXmetaStyle != null and publishXmetaStyle != '' ">
                #{publishXmetaStyle, jdbcType=VARCHAR},
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                #{exchangeXmeta, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Collection">
        update nft_collection
        <set>
            <if test="threeChannelType != null and threeChannelType != '' ">
                three_channel_type = #{threeChannelType, jdbcType=VARCHAR},
            </if>
            <if test="threeChannelId != null">
                three_channel_id = #{threeChannelId, jdbcType=BIGINT},
            </if>
            <if test="collectionHash != null and collectionHash != '' ">
                collection_hash = #{collectionHash, jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="serialName != null and serialName != '' ">
                serial_name = #{serialName,jdbcType=VARCHAR},
            </if>
            <if test="category != null and category != '' ">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                plate_category = #{plateCategory, jdbcType=VARCHAR},
            </if>
            <if test="name != null and name != '' ">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="buyType != null and buyType != '' ">
                buy_type = #{buyType, jdbcType=VARCHAR},
            </if>
            <if test="levelType != null and levelType != '' ">
                level_type = #{levelType,jdbcType=VARCHAR},
            </if>
            <if test="fileType != null and fileType != '' ">
                file_type = #{fileType,jdbcType=VARCHAR},
            </if>
            <if test="contentType != null and contentType != '' ">
                content_type = #{contentType,jdbcType=VARCHAR},
            </if>
            <if test="content != null and content != '' ">
                content = #{content,jdbcType=VARCHAR},
            </if>
            <if test="contractId != null and contractId != '' ">
                contract_id = #{contractId,jdbcType=BIGINT},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null and fileUrl != '' ">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="producedId != null">
                produced_id = #{producedId, jdbcType=BIGINT},
            </if>
            <if test="authorId != null and authorId != '' ">
                author_id = #{authorId,jdbcType=BIGINT},
            </if>
            <if test="divideAuthorId != null">
                divide_author_id = #{divideAuthorId, jdbcType=BIGINT},
            </if>
            <if test="tags != null and tags != '' ">
                tags = #{tags, jdbcType=VARCHAR},
            </if>
            <if test="rightContent != null">
                right_content = #{rightContent, jdbcType=VARCHAR},
            </if>
            <if test="rightType != null and rightType != '' ">
                right_type = #{rightType, jdbcType=VARCHAR},
            </if>

            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=INTEGER},
            </if>
            <if test="marketQuantity != null">
                market_quantity = #{marketQuantity, jdbcType=INTEGER},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=INTEGER},
            </if>
            <if test="platRemainQuantity != null">
                plat_remain_quantity = #{platRemainQuantity, jdbcType=INTEGER},
            </if>
            <if test="chainType != null and chainType != '' ">
                chain_type = #{chainType,jdbcType=VARCHAR},
            </if>

            <if test="payAmount != null">
                pay_amount = #{payAmount,jdbcType=DECIMAL},
            </if>
            <if test="payType != null and payType != '' ">
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                pay_order_code = #{payOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="payStatus != null and payStatus != '' ">
                pay_status = #{payStatus,jdbcType=VARCHAR},
            </if>
            <if test="payDatetime != null">
                pay_datetime = #{payDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="tokenImportId != null">
                token_import_id = #{tokenImportId,jdbcType=BIGINT},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                channel_transfer_status = #{channelTransferStatus, jdbcType=VARCHAR},
            </if>
            <if test="payBalanceAmount != null">
                pay_balance_amount = #{payBalanceAmount,jdbcType=DECIMAL},
            </if>
            <if test="payCashAmount != null">
                pay_cash_amount = #{payCashAmount,jdbcType=DECIMAL},
            </if>
            <if test="storageFee != null">
                storage_fee = #{storageFee,jdbcType=DECIMAL},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                commission_amount = #{commissionAmount, jdbcType=DECIMAL},
            </if>
            <if test="downStatus != null and downStatus != '' ">
                down_status = #{downStatus, jdbcType=VARCHAR},
            </if>
            <if test="lockTime != null">
                lock_time = #{lockTime, jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="maxExchangeTime != null and maxExchangeTime != '' ">
                max_exchange_time = #{maxExchangeTime, jdbcType=INTEGER},
            </if>
            <if test="transformLimitTime != null">
                transform_limit_time = #{transformLimitTime, jdbcType=INTEGER},
            </if>
            <if test="u3dFlag != null and u3dFlag != '' ">
                u3d_flag = #{u3dFlag, jdbcType=VARCHAR},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type = #{ticketType, jdbcType=VARCHAR},
            </if>
            <if test="integralPrice != null">
                integral_price = #{integralPrice, jdbcType=DECIMAL},
            </if>
            <if test="singleMaxQuantity != null">
                single_max_quantity = #{singleMaxQuantity, jdbcType=INTEGER},
            </if>
            <if test="platDivideRate != null">
                plat_divide_rate = #{platDivideRate, jdbcType=DECIMAL},
            </if>
            <if test="accountIntegral != null">
                account_integral = #{accountIntegral, jdbcType=DECIMAL},
            </if>
            <if test="useFlag != null and useFlag != '' ">
                use_flag = #{useFlag, jdbcType=VARCHAR},
            </if>
            <if test="useWay != null and useWay != '' ">
                use_way = #{useWay, jdbcType=VARCHAR},
            </if>
            <if test="classId != null and classId != '' ">
                class_id = #{classId, jdbcType=VARCHAR},
            </if>
            <if test="publishXmeta != null and publishXmeta != '' ">
                publish_xmeta = #{publishXmeta, jdbcType=VARCHAR},
            </if>
            <if test="publishXmetaStyle != null and publishXmetaStyle != '' ">
                publish_xmeta_style = #{publishXmetaStyle, jdbcType=VARCHAR},
            </if>
            <if test="exchangeXmeta != null and exchangeXmeta != '' ">
                exchange_xmeta = #{exchangeXmeta, jdbcType=VARCHAR},
            </if>
            <if test="redPacketFlag != null and redPacketFlag != '' ">
                red_packet_flag = #{redPacketFlag, jdbcType=VARCHAR},
            </if>
            <if test="metaBizType != null and metaBizType != '' ">
                meta_biz_type = #{metaBizType, jdbcType=VARCHAR},
            </if>
            <if test="metaBizId != null ">
                meta_biz_id = #{metaBizId, jdbcType=BIGINT},
            </if>
            <if test="special3dFlag != null and special3dFlag != '' ">
                special_3d_flag = #{special3dFlag, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="changeCollectionQuantity">
        update nft_collection
        set remain_quantity =remain_quantity - 1
        where id = #{id}
          and `status` = '1'
          and remain_quantity > 1;
    </update>

    <update id="refreshMarketQuantity">
        UPDATE nft_collection t
        SET t.market_quantity = t.total_quantity -(
        SELECT
        count( 1 )
        FROM
        nft_collection_detail ta
        WHERE
        ta.owner_type = '1'
        AND ta.status ='6'
        AND ta.collection_id = t.id
        )
        where 1=1
        <if test="collectionIdList != null and collectionIdList.size() != 0 ">
            AND t.id in
            <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                    close=")">
                #{item,jdbcType=BIGINT}
            </foreach>
        </if>

    </update>
    <update id="quantitySubtractByDetail">
        update nft_collection
        set total_quantity=total_quantity - 1,
            market_quantity=market_quantity - 1
        where id = (select collection_id from nft_collection_detail where id = #{orderDetailId})
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <!-- 组合条件查询 -->
    <select id="selectextendByCondition" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        ,tu.availableSellQuantity
        from nft_collection t
        INNER JOIN
        (SELECT collection_id , count(1) as availableSellQuantity FROM nft_collection_detail
        WHERE 1=1
        <if test="ownerId != null">
            AND owner_id = #{ownerId, jdbcType=BIGINT}
        </if>
        <if test="sellStatus != null">
            AND `status` = #{sellStatus, jdbcType=VARCHAR}
        </if>
        <if test="sellStatusList != null and sellStatusList.size() != 0 ">
            AND `status` in
            <foreach item="item" index="index" collection="sellStatusList" open="(" separator=","
                    close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        GROUP BY collection_id) tu
        on t.id = tu.collection_id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectCountByCondition" parameterType="com.std.core.pojo.domain.Collection"
            resultType="java.lang.Long">
        select
        count(1)
        from nft_collection t
        <include refid="where_condition"/>

    </select>

    <select id="selectTradeTotalAmount" parameterType="com.std.core.pojo.domain.Collection"
            resultType="java.math.BigDecimal">
        select
        COALESCE(sum(t.pay_amount), 0)
        from nft_collection t
        <include refid="where_condition"/>

    </select>

    <select id="selectMonthAmount" parameterType="com.std.core.pojo.domain.MonthCondition"
            resultType="java.math.BigDecimal">
        <![CDATA[select coalesce(sum(commission_amount), 0) from nft_collection t where t.pay_status = '1' and t.pay_datetime >= #{monthStart} and t.pay_datetime <= #{monthEnd}]]>
        <if test="teamList != null and teamList.size() != 0 ">
            AND t.user_id in
            <foreach item="item" index="index" collection="teamList" open="(" separator="," close=")">
                #{item.id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <resultMap id="ChallengeBaseResultMap" type="com.std.core.pojo.response.CollectionChallengeRes">
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="lock_flag" jdbcType="VARCHAR" property="lockFlag"/>
    </resultMap>

    <select id="selectChallengeByCondition" parameterType="com.std.core.pojo.domain.Collection" resultMap="ChallengeBaseResultMap">
        select *
        from ((select concat(t.id, '_', '0') id
        , t.name
        , '0' lock_flag
        from nft_collection t
        where t.lock_time != '-1' and t.status = '1'
        and t.id not in ( SELECT DISTINCT t.collection_id from nft_collection_detail t
        INNER JOIN nft_collection ta on t.collection_id=ta.id
        where t.lock_time=-1 AND t.transform_limit_time=-1 and ta.type='2')
        and t.three_channel_type='0'
        <if test="keywords != null and keywords != '' ">
            AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
        </if>
        <if test="authorId != null">
            AND t.author_id = #{authorId, jdbcType=BIGINT}
        </if>)
        union
        (select concat(t.id, '_', '1') id
        , t.name
        , '1' lockFlag
        from nft_collection t
        where t.status = '1'
        and t.id not in ( SELECT DISTINCT t.collection_id from nft_collection_detail t
        INNER JOIN nft_collection ta on t.collection_id=ta.id
        where t.lock_time=-1 AND t.transform_limit_time=-1 and ta.type='2')
        and t.three_channel_type='0'
        <if test="keywords != null and keywords != '' ">
            AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
        </if>
        <if test="authorId != null">
            AND t.author_id = #{authorId, jdbcType=BIGINT}
        </if>)) t
        order by id asc
    </select>

    <resultMap id="My_BaseResultMap" type="com.std.core.pojo.response.CollectionMyOwnerPageRes">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="three_channel_type" jdbcType="VARCHAR" property="threeChannelType"/>
        <result column="three_channel_id" jdbcType="VARCHAR" property="threeChannelId"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="plate_category" jdbcType="VARCHAR" property="plateCategory"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="level_type" jdbcType="VARCHAR" property="levelType"/>
        <result column="file_type" jdbcType="VARCHAR" property="fileType"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="total_quantity" jdbcType="INTEGER" property="totalQuantity"/>
    </resultMap>

    <select id="selectMyOwnerPage" parameterType="com.std.core.pojo.domain.Collection" resultMap="BaseResultMap">
        select
        t
        .
        id
        , t.three_channel_type
        , t.three_channel_id
        , t.category
        , t.plate_category
        , t.name
        , t.level_type
        , t.file_type
        , t.cover_file_url
        , count(1) total_quantity
        from nft_collection t
        INNER JOIN nft_collection_detail ta ON t.id = ta.collection_id
        where ta.`status` NOT IN ( '4', '6', '11' ) and ta.owner_id = #{userId}
        <if test="category != null and category != '' ">
            AND t.category = #{category, jdbcType=VARCHAR}
        </if>
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
        </if>
        <if test="keywords != null and keywords != '' ">
            AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
        </if>
        GROUP BY ta.collection_id
        ORDER BY ta.update_time DESC
    </select>

    <select id="sumTotalQuantity" resultType="java.lang.Integer">
        select count(1)
        from nft_collection t
        where t.author_id = #{authorId}
        <if test="status != null and status != '' ">
            AND t.status = #{status, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectForUpdate" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="challengePage" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        where t.author_id=#{authorId}
        and t.status='1'
        and t.type in ('1','3')
        AND t.remain_quantity <![CDATA[ >]]> '0'
        <if test="name != null and name != '' ">
            AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
        </if>
        <if test="threeChannelType != null and threeChannelType != '' ">
            AND t.three_channel_type = #{threeChannelType, jdbcType=VARCHAR}
        </if>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="getDropUserListReq" resultType="com.std.core.pojo.domain.CollectionDropUserList">
        select login_name     loginName
             , collection_id  collectionId
             , total_quantity totalQuantity
        from nft_user_collection_snapshot
    </select>
    <select id="selectPageCollectionIntegral" resultType="com.std.core.pojo.response.CollectionIntegralPageRes">
        select t
            .
            id
             , t.name
             , t.level_type     levelType
             , t.cover_file_url coverFileUrl
             , t.integral_price integralPrice
        from nft_collection t
        where t.integral_price <![CDATA[ >]]> 0
        order by t.id desc
    </select>
    <select id="selectPagePit" resultType="com.std.core.pojo.response.CollectionPitPageRes">
        SELECT
        ta.id,
        ta.`name`,
        ta.level_type levelType,
        ta.cover_file_url coverFileUrl,
        ta.three_channel_type threeChannelType
        FROM
        ( SELECT DISTINCT collection_id FROM nft_collection_detail WHERE owner_type = '0' AND `status` IN ( '0', '2',
        '14', '15' ) ) t
        LEFT JOIN nft_collection ta ON t.collection_id = ta.id
        WHERE
        ta.type in ('1','3')
        AND ta.`status` = '1'
        <if test="threeChannelType != null and threeChannelType != '' ">
            AND ta.three_channel_type = #{threeChannelType, jdbcType=VARCHAR}
        </if>
        <if test="name != null and name != '' ">
            AND ta.name like concat('%',#{name, jdbcType=VARCHAR},'%')
        </if>
        order by ta.id desc
    </select>
    <select id="selectChannelTransfer" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        inner join nft_contract ta on t.contract_id=ta.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="threeChannelType != null and threeChannelType != '' ">
                AND t.three_channel_type = #{threeChannelType, jdbcType=VARCHAR}
            </if>
            <if test="threeChannelId != null">
                AND t.three_channel_id = #{threeChannelId, jdbcType=BIGINT}
            </if>
            <if test="producedId != null">
                AND t.produced_id = #{producedId, jdbcType=BIGINT}
            </if>
            <if test="authorId != null">
                AND t.author_id = #{authorId, jdbcType=BIGINT}
            </if>
            <if test="divideAuthorId != null">
                AND t.divide_author_id = #{divideAuthorId, jdbcType=BIGINT}
            </if>
            <if test="category != null and category != '' ">
                AND t.category = #{category, jdbcType=VARCHAR}
            </if>
            <if test="plateCategory != null and plateCategory != '' ">
                AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
            </if>
            <if test="name != null and name != '' ">
                AND t.name like concat('%',#{name, jdbcType=VARCHAR},'%')
            </if>
            <if test="nameStr != null and nameStr != '' ">
                AND t.name = #{nameStr, jdbcType=VARCHAR}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="noType != null and noType != '' ">
                AND t.type != #{noType, jdbcType=VARCHAR}
            </if>
            <if test="typeList != null and typeList.size() != 0 ">
                AND t.type in
                <foreach item="item" index="index" collection="typeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="buyType != null and buyType != '' ">
                AND t.buy_type = #{buyType, jdbcType=VARCHAR}
            </if>
            <if test="buyTypeList != null and buyTypeList.size() != 0 ">
                AND t.buy_type in
                <foreach item="item" index="index" collection="buyTypeList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="levelType != null and levelType != '' ">
                AND t.level_type = #{levelType, jdbcType=VARCHAR}
            </if>
            <if test="fileType != null and fileType != '' ">
                AND t.file_type = #{fileType, jdbcType=VARCHAR}
            </if>
            <if test="rightType != null and rightType != '' ">
                AND t.right_type = #{rightType, jdbcType=VARCHAR}
            </if>
            <if test="remainQuantityFlag != null and remainQuantityFlag > 0 ">
                AND t.remain_quantity <![CDATA[ >]]> '0'
            </if>
            <if test="chainType != null and chainType != '' ">
                AND t.chain_type = #{chainType, jdbcType=VARCHAR}
            </if>
            <if test="contractId != null">
                AND t.contract_id = #{contractId, jdbcType=BIGINT}
            </if>
            <if test="payType != null and payType != '' ">
                AND t.pay_type = #{payType, jdbcType=VARCHAR}
            </if>
            <if test="payOrderCode != null and payOrderCode != '' ">
                AND t.pay_order_code = #{payOrderCode, jdbcType=VARCHAR}
            </if>
            <if test="payStatus != null and payStatus != '' ">
                AND t.pay_status = #{payStatus, jdbcType=VARCHAR}
            </if>
            <if test="payCancelDatetime != null">
                AND t.pay_datetime &lt; #{payCancelDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="payDatetimeStart != null ">
                <![CDATA[AND t.pay_datetime >= #{payDatetimeStart}]]>
            </if>
            <if test="payDatetimeEnd != null">
                <![CDATA[AND t.pay_datetime <= #{payDatetimeEnd}]]>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="tokenImportId != null">
                AND t.token_import_id = #{tokenImportId, jdbcType=BIGINT}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="channelTransferStatus != null and channelTransferStatus != '' ">
                AND t.channel_transfer_status = #{channelTransferStatus, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="noStatusList != null and noStatusList.size() != 0 ">
                AND t.status not in
                <foreach item="item" index="index" collection="noStatusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="collectionIdList != null and collectionIdList.size() != 0 ">
                AND t.id in
                <foreach item="item" index="index" collection="collectionIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="rejectCollectionIdList != null and rejectCollectionIdList.size() != 0 ">
                AND t.id not in
                <foreach item="item" index="index" collection="rejectCollectionIdList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
            <if test="userIdNotExistList != null and userIdNotExistList.size() != 0 ">
                AND t.user_id not in
                <foreach item="item" index="index" collection="userIdNotExistList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="deduction != null and deduction != '' ">
                AND t.pay_balance_amount <![CDATA[ >]]> 0
            </if>
            <if test="noDeduction != null and noDeduction != '' ">
                AND t.pay_balance_amount <![CDATA[ <=]]> 0
            </if>
            <if test="downStatus != null and downStatus != '' ">
                AND t.down_status = #{downStatus, jdbcType=VARCHAR}
            </if>
            <if test="u3dFlag != null and u3dFlag != '' ">
                AND t.u3d_flag = #{u3dFlag, jdbcType=VARCHAR}
            </if>
            <if test="useFlag != null and useFlag != '' ">
                AND t.use_flag = #{useFlag, jdbcType=VARCHAR}
            </if>
            <if test="useWay != null and useWay != '' ">
                AND t.use_way = #{useWay, jdbcType=VARCHAR}
            </if>
            AND ta.chain = 'BSN'
        </trim>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectMyChannelTransferOwnerPage"
            resultType="com.std.core.pojo.response.CollectionMyOwnerPageRes">
        select
        t
        .
        id
        , t.three_channel_type threeChannelType
        , t.three_channel_id threeChannelId
        , t.category
        , t.plate_category plateCategory
        , t.name
        , t.level_type levelType
        , t.file_type fileType
        , t.cover_file_url coverFileUrl
        , count(1) totalQuantity
        from nft_collection t
        INNER JOIN nft_collection_detail ta ON t.id = ta.collection_id
        INNER JOIN nft_collection_transfer_channel_record tb on t.id=tb.collection_id
        where ta.`status` ='0' and ta.owner_id = #{userId}
        and ta.lock_time='-1' and tb.status='1'
        <if test="category != null and category != '' ">
            AND t.category = #{category, jdbcType=VARCHAR}
        </if>
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory, jdbcType=VARCHAR}
        </if>
        <if test="keywords != null and keywords != '' ">
            AND t.name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
        </if>
        GROUP BY ta.collection_id
        ORDER BY ta.update_time DESC

    </select>
    <select id="selectCompanyNotSellCollectionPage" parameterType="com.std.core.pojo.domain.Collection"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection t
        where t.author_id =#{companyId} and t.status='1' and three_channel_type <![CDATA[ !=]]> '1' and t.buy_type ='2'
        and t.id in (
        select DISTINCT collection_id FROM lxa_invitation_activity where send_type='0' AND `status` !='0'
        UNION
        select DISTINCT t.collection_id FROM lxa_invitation_activity_blind_box_collection t INNER JOIN lxa_invitation_activity ta on
        t.activity_id = ta.id where ta.`status` !='0'
        UNION
        select DISTINCT t.collection_id FROM lxa_invitation_activity_registered_collection t INNER JOIN lxa_invitation_activity ta on
        t.activity_id = ta.id where ta.`status` !='0'
        UNION
        SELECT DISTINCT t.collection_id FROM tstd_marketing_activity_collection t INNER JOIN nft_chip_activity ta ON t.ref_id =ta.id where
        t.ref_type ='0' AND ta.`status` !='0' AND ta.company_id =#{companyId}
        UNION
        SELECT DISTINCT t.collection_id FROM nft_drop_record t INNER JOIN nft_collection ta ON t.collection_id=ta.id where
        ta.author_id=#{companyId} AND t.`status`='1'
        UNION
        SELECT DISTINCT t.collection_id FROM nft_exchange_code t where t.company_id=#{companyId} AND t.`status`='1'
        )
        order by t.id desc
    </select>
    <select id="selectCollectionCreatePeriod" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM nft_collection t WHERE t.STATUS = '1'
        and t.type in ('1','3')

        AND
        (
        (
        t.author_id = #{companyId}
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory}
        </if>
        )
        OR id IN (
        SELECT
        b.collection_id
        FROM
        nft_collection_right_company a
        INNER JOIN nft_collection_rights_detail b ON a.ref_id = b.id
        WHERE
        a.ref_type = #{refType}
        AND a.company_id = '0'
        <if test="plateCategory != null and plateCategory != '' ">
            AND t.plate_category = #{plateCategory}
        </if>
        UNION
        SELECT
        b.collection_id
        FROM
        nft_collection_right_company a
        INNER JOIN nft_collection_rights_detail b ON a.ref_id = b.id
        WHERE
        a.ref_type = #{refType}
        AND a.plate_category = '0'
        AND a.company_id = #{companyId}
        )
        )

        <if test="collectionName != null and collectionName != '' ">
            AND t.name like concat ('%',#{collectionName, jdbcType=VARCHAR},'%')
        </if>
    </select>

    <select id="selectMyHavePublishXmetaCollectionCount" resultType="com.xmeta.opensdk.model.vo.GoodsCountVO">
        select t.id              archiveId,
               case publish_xmeta_style
                   when '0' then
                       (select count(1)
                        from nft_collection_detail
                        where collection_id = t.id
                          and status = '0'
                          and lock_time = '-1'
                          and owner_type = '0'
                          and owner_id = #{userId})
                   when '1' then
                       (select count(1)
                        from nft_collection_detail nd,
                             nft_collection_detail_exchange_card nc
                        where nd.collection_id = t.id
                          and nd.id = nc.publish_collection_detail_id
                          and nd.status = '0'
                          and nd.lock_time = '-1'
                          and nd.owner_type = '0'
                          and nc.status = '0'
                          and nd.owner_id = #{userId})
                   else 0 end as `count`
        from nft_collection t
        where t.status = '1'
          and t.type in ('1', '2', '3')
          and t.publish_xmeta = '1'
        order by t.id desc
    </select>
</mapper>