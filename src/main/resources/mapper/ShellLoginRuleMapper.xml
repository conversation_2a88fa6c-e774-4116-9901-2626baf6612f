<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ShellLoginRuleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ShellLoginRule">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="day" jdbcType="INTEGER" property="day"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="extra_amount" jdbcType="DECIMAL" property="extraAmount"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="creater" jdbcType="BIGINT" property="creater"/>
        <result column="creater_name" jdbcType="VARCHAR" property="createrName"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.day
        , t.amount
        , t.extra_amount
        , t.status
        , t.creater
        , t.creater_name
        , t.create_datetime
        , t.updater
        , t.updater_name
        , t.update_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="day != null">
                AND t.day = #{day, jdbcType=INTEGER}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="extraAmount != null">
                AND t.extra_amount = #{extraAmount, jdbcType=DECIMAL}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="creater != null">
                AND t.creater = #{creater, jdbcType=BIGINT}
            </if>
            <if test="createrName != null and createrName != '' ">
                AND t.creater_name = #{createrName, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != '' ">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ShellLoginRule">
        insert into yg_shell_login_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="day != null ">
                day,
              </if>
              <if test="amount != null ">
                amount,
              </if>
              <if test="extraAmount != null ">
                extra_amount,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
              <if test="creater != null ">
                creater,
              </if>
              <if test="createrName != null and createrName != '' ">
                creater_name,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="updater != null ">
                updater,
              </if>
              <if test="updaterName != null and updaterName != '' ">
                updater_name,
              </if>
              <if test="updateDatetime != null ">
                update_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="day != null">
                #{day,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="extraAmount != null">
                #{extraAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from yg_shell_login_rule
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ShellLoginRule">
        update yg_shell_login_rule
        <set>
            <if test="day != null">
                day = #{day,jdbcType=INTEGER},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="extraAmount != null">
                extra_amount = #{extraAmount,jdbcType=DECIMAL},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="creater != null">
                creater = #{creater,jdbcType=BIGINT},
            </if>
            <if test="createrName != null and createrName != '' ">
                creater_name = #{createrName,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                updater = #{updater,jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != '' ">
                updater_name = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_login_rule t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ShellLoginRule"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_login_rule t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByDay" parameterType="com.std.core.pojo.domain.YaoLoginRule"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from yg_shell_login_rule t
        where t.day <![CDATA[ >=]]> #{day} and t.status='1' order by t.day limit 1
    </select>
</mapper>