<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.BankChannelMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.BankChannel">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bank_info_id" jdbcType="BIGINT" property="bankInfoId"/>
        <result column="channel_type" jdbcType="VARCHAR" property="channelType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="order_on" jdbcType="INTEGER" property="orderOn"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.bank_info_id
        , t.channel_type
        , t.status
        , t.remark
        , t.order_on
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bankInfoId != null">
                AND t.bank_info_id = #{bankInfoId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                ta.bank_code like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                ta.bank_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="type != null and type != '' ">
                AND ta.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="channelType != null and channelType != '' ">
                AND t.channel_type = #{channelType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="orderOn != null">
                AND t.order_on = #{orderOn, jdbcType=INTEGER}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.BankChannel">
        insert into tstd_bank_channel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="bankInfoId != null ">
                bank_info_id,
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="orderOn != null ">
                order_on,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bankInfoId != null">
                #{bankInfoId,jdbcType=BIGINT},
            </if>
            <if test="channelType != null and channelType != '' ">
                #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderOn != null">
                #{orderOn,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_bank_channel
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.BankChannel">
        update tstd_bank_channel
        <set>
            <if test="bankInfoId != null">
                bank_info_id = #{bankInfoId,jdbcType=BIGINT},
            </if>
            <if test="channelType != null and channelType != '' ">
                channel_type = #{channelType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderOn != null">
                order_on = #{orderOn,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bank_channel t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.BankChannel"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bank_channel t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="detailByBankId" parameterType="com.std.core.pojo.domain.BankChannel"
            resultMap="BaseResultMap">

        select
        <include refid="Base_Column_List"/>,ta.bank_name
        from tstd_bank_channel t
        inner join tstd_bank_info ta on t.bank_info_id=ta.id
        where ta.bank_code=#{bankCode} and t.status=#{status} and t.channel_type=#{channelType}
    </select>
    <select id="selectByConditionFront" resultType="com.std.core.pojo.response.BankChannelListRes">
        select
        t.id
        , ta.type
        , ta.bank_code bankCode
        , ta.bank_name bankName
        , t.remark
        , ta.logo
        , t.channel_type channelType
        , tb.bank_name channelName
        from tstd_bank_channel t
        inner join tstd_bank_info ta on t.bank_info_id=ta.id
        inner join tstd_channel_bank tb on tb.channel_bank=t.channel_type
        where ta.type=#{type} and t.status=#{status} and t.channel_type=#{channelType}
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByPrimaryKeyOss" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.bank_code,ta.bank_name,ta.type
        from tstd_bank_channel t
        inner join tstd_bank_info ta on t.bank_info_id=ta.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.BankChannel"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,ta.bank_code,ta.bank_name,ta.type
        from tstd_bank_channel t
        inner join tstd_bank_info ta on t.bank_info_id=ta.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>