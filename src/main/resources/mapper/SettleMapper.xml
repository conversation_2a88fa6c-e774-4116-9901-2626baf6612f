<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.SettleMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.Settle">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="from_account_number" jdbcType="VARCHAR" property="fromAccountNumber"/>
        <result column="to_account_number" jdbcType="VARCHAR" property="toAccountNumber"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="apply_amount" jdbcType="DECIMAL" property="applyAmount"/>
        <result column="apply_fee" jdbcType="DECIMAL" property="applyFee"/>
        <result column="actual_amount" jdbcType="DECIMAL" property="actualAmount"/>
        <result column="apply_datetime" jdbcType="TIMESTAMP" property="applyDatetime"/>
        <result column="apply_user_id" jdbcType="BIGINT" property="applyUserId"/>
        <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName"/>
        <result column="apply_note" jdbcType="VARCHAR" property="applyNote"/>
        <result column="repay_datetime" jdbcType="TIMESTAMP" property="repayDatetime"/>
        <result column="repay_user_id" jdbcType="BIGINT" property="repayUserId"/>
        <result column="repay_user_name" jdbcType="VARCHAR" property="repayUserName"/>
        <result column="repay_note" jdbcType="VARCHAR" property="repayNote"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.from_account_number
        , t.to_account_number
        , t.currency
        , t.apply_amount
        , t.apply_fee
        , t.actual_amount
        , t.apply_datetime
        , t.apply_user_id
        , t.apply_user_name
        , t.apply_note
        , t.repay_datetime
        , t.repay_user_id
        , t.repay_user_name
        , t.repay_note
        , t.status
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="fromAccountNumber != null and fromAccountNumber != '' ">
                AND t.from_account_number = #{fromAccountNumber, jdbcType=VARCHAR}
            </if>
            <if test="toAccountNumber != null and toAccountNumber != '' ">
                AND t.to_account_number = #{toAccountNumber, jdbcType=VARCHAR}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="applyUserId != null">
                AND t.apply_user_id = #{applyUserId, jdbcType=BIGINT}
            </if>
            <if test="applyUserName != null and applyUserName != '' ">
                AND t.apply_user_name = #{applyUserName, jdbcType=VARCHAR}
            </if>
            <if test="repayUserId != null">
                AND t.repay_user_id = #{repayUserId, jdbcType=BIGINT}
            </if>
            <if test="repayUserName != null and repayUserName != '' ">
                AND t.repay_user_name = #{repayUserName, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>

          <if test="statusList != null and statusList.size() != 0 ">
            AND t.status in
            <foreach item="item" index="index" collection="statusList" open="(" separator=","
              close=")">
              #{item,jdbcType=VARCHAR}
            </foreach>
          </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.Settle">
        insert into tstd_settle
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="fromAccountNumber != null and fromAccountNumber != '' ">
                from_account_number,
              </if>
              <if test="toAccountNumber != null and toAccountNumber != '' ">
                to_account_number,
              </if>
              <if test="currency != null and currency != '' ">
                currency,
              </if>
              <if test="applyAmount != null">
                apply_amount,
              </if>
              <if test="applyFee != null ">
                apply_fee,
              </if>
              <if test="actualAmount != null  ">
                actual_amount,
              </if>
              <if test="applyDatetime != null  ">
                apply_datetime,
              </if>
              <if test="applyUserId != null ">
                apply_user_id,
              </if>
              <if test="applyUserName != null and applyUserName != '' ">
                apply_user_name,
              </if>
              <if test="applyNote != null and applyNote != '' ">
                apply_note,
              </if>
              <if test="repayDatetime != null  ">
                repay_datetime,
              </if>
              <if test="repayUserId != null ">
                repay_user_id,
              </if>
              <if test="repayUserName != null and repayUserName != '' ">
                repay_user_name,
              </if>
              <if test="repayNote != null and repayNote != '' ">
                repay_note,
              </if>
              <if test="status != null and status != '' ">
                status,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="fromAccountNumber != null and fromAccountNumber != '' ">
                #{fromAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="toAccountNumber != null and toAccountNumber != '' ">
                #{toAccountNumber,jdbcType=VARCHAR},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="applyAmount != null">
                #{applyAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyFee != null">
                #{applyFee,jdbcType=DECIMAL},
            </if>
            <if test="actualAmount != null">
                #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyDatetime != null">
                #{applyDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="applyUserId != null">
                #{applyUserId,jdbcType=BIGINT},
            </if>
            <if test="applyUserName != null and applyUserName != '' ">
                #{applyUserName,jdbcType=VARCHAR},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="repayDatetime != null">
                #{repayDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="repayUserId != null">
                #{repayUserId,jdbcType=BIGINT},
            </if>
            <if test="repayUserName != null and repayUserName != '' ">
                #{repayUserName,jdbcType=VARCHAR},
            </if>
            <if test="repayNote != null and repayNote != '' ">
                #{repayNote,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_settle
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.Settle">
        update tstd_settle
        <set>
            <if test="applyFee != null">
                apply_fee = #{applyFee,jdbcType=DECIMAL},
            </if>
            <if test="actualAmount != null">
                actual_amount = #{actualAmount,jdbcType=DECIMAL},
            </if>
            <if test="applyNote != null and applyNote != '' ">
                apply_note = #{applyNote,jdbcType=VARCHAR},
            </if>
            <if test="repayDatetime != null">
                repay_datetime = #{repayDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="repayUserId != null">
                repay_user_id = #{repayUserId,jdbcType=BIGINT},
            </if>
            <if test="repayUserName != null and repayUserName != '' ">
                repay_user_name = #{repayUserName,jdbcType=VARCHAR},
            </if>
            <if test="repayNote != null and repayNote != '' ">
                repay_note = #{repayNote,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

  <select id="selectSumByCondition" parameterType="com.std.core.pojo.domain.Settle"
    resultType="java.math.BigDecimal">
    select ifnull(sum(t.apply_amount), 0) from tstd_settle t
    <include refid="where_condition"/>
  </select>

  <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_settle t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.Settle"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_settle t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>