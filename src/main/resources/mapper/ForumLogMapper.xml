<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ForumLogMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ForumLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="type_note" jdbcType="VARCHAR" property="typeNote"/>
        <result column="rel_id" jdbcType="VARCHAR" property="relId"/>
        <result column="rel_user_id" jdbcType="VARCHAR" property="relUserId"/>
        <result column="pre_id" jdbcType="VARCHAR" property="preId"/>
        <result column="post_id" jdbcType="BIGINT" property="postId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="status" jdbcType="CHAR" property="status"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.type
        , t.type_note
        , t.rel_id
        , t.rel_user_id
        , t.pre_id
        , t.post_id
        , t.user_id
        , t.status
        , t.create_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="typeNote != null and typeNote != '' ">
                AND t.type_note = #{typeNote, jdbcType=VARCHAR}
            </if>
            <if test="relId != null and relId != '' ">
                AND t.rel_id = #{relId, jdbcType=VARCHAR}
            </if>
            <if test="relUserId != null and relUserId != '' ">
                AND t.rel_user_id = #{relUserId, jdbcType=VARCHAR}
            </if>
            <if test="preId != null and preId != '' ">
                AND t.pre_id = #{preId, jdbcType=VARCHAR}
            </if>
            <if test="postId != null">
                AND t.post_id = #{postId, jdbcType=BIGINT}
            </if>
            <if test="userId != null and userId != '' ">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="status != null">
                AND t.status = #{status, jdbcType=CHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ForumLog">
        insert into tstd_forum_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="type != null and type != '' ">
                type,
              </if>
              <if test="typeNote != null and typeNote != '' ">
                type_note,
              </if>
              <if test="relId != null and relId != '' ">
                rel_id,
              </if>
              <if test="relUserId != null and relUserId != '' ">
                rel_user_id,
              </if>
              <if test="preId != null and preId != '' ">
                pre_id,
              </if>
              <if test="postId != null ">
                post_id,
              </if>
              <if test="userId != null and userId != '' ">
                user_id,
              </if>
              <if test="status != null ">
                status,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
              <if test="remark != null and remark != '' ">
                remark,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="type != null and type != '' ">
                #{type,jdbcType=VARCHAR},
            </if>
            <if test="typeNote != null and typeNote != '' ">
                #{typeNote,jdbcType=VARCHAR},
            </if>
            <if test="relId != null and relId != '' ">
                #{relId,jdbcType=VARCHAR},
            </if>
            <if test="relUserId != null and relUserId != '' ">
                #{relUserId,jdbcType=VARCHAR},
            </if>
            <if test="preId != null and preId != '' ">
                #{preId,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                #{postId,jdbcType=BIGINT},
            </if>
            <if test="userId != null and userId != '' ">
                #{userId,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=CHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tstd_forum_log
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ForumLog">
        update tstd_forum_log
        <set>
            <if test="type != null and type != '' ">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="typeNote != null and typeNote != '' ">
                type_note = #{typeNote,jdbcType=VARCHAR},
            </if>
            <if test="relId != null and relId != '' ">
                rel_id = #{relId,jdbcType=VARCHAR},
            </if>
            <if test="relUserId != null and relUserId != '' ">
                rel_user_id = #{relUserId,jdbcType=VARCHAR},
            </if>
            <if test="preId != null and preId != '' ">
                pre_id = #{preId,jdbcType=VARCHAR},
            </if>
            <if test="postId != null">
                post_id = #{postId,jdbcType=BIGINT},
            </if>
            <if test="userId != null and userId != '' ">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=CHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_log t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ForumLog"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_forum_log t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>