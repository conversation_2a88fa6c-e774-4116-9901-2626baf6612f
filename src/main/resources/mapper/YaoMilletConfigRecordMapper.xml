<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.YaoMilletConfigRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.YaoMilletConfigRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="currency" jdbcType="VARCHAR" property="currency"/>
        <result column="total_quantity" jdbcType="DECIMAL" property="totalQuantity"/>
        <result column="remain_quantity" jdbcType="DECIMAL" property="remainQuantity"/>
        <result column="date_number" jdbcType="INTEGER" property="dateNumber"/>
        <result column="date_time" jdbcType="DATE" property="dateTime"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.currency
        , t.total_quantity
        , t.remain_quantity
        , t.date_number
        , t.date_time
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="currency != null and currency != '' ">
                AND t.currency = #{currency, jdbcType=VARCHAR}
            </if>
            <if test="totalQuantity != null">
                AND t.total_quantity = #{totalQuantity, jdbcType=DECIMAL}
            </if>
            <if test="remainQuantity != null">
                AND t.remain_quantity = #{remainQuantity, jdbcType=DECIMAL}
            </if>
            <if test="dateNumber != null">
                AND t.date_number = #{dateNumber, jdbcType=INTEGER}
            </if>
            <if test="dateTime != null">
                AND t.date_time = #{dateTime, jdbcType=DATE}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.YaoMilletConfigRecord">
        insert into es_yao_millet_config_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="currency != null and currency != '' ">
                currency,
            </if>
            <if test="totalQuantity != null ">
                total_quantity,
            </if>
            <if test="remainQuantity != null ">
                remain_quantity,
            </if>
            <if test="dateNumber != null ">
                date_number,
            </if>
            <if test="dateTime != null ">
                date_time,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="currency != null and currency != '' ">
                #{currency,jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                #{totalQuantity,jdbcType=DECIMAL},
            </if>
            <if test="remainQuantity != null">
                #{remainQuantity,jdbcType=DECIMAL},
            </if>
            <if test="dateNumber != null">
                #{dateNumber,jdbcType=INTEGER},
            </if>
            <if test="dateTime != null">
                #{dateTime,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from es_yao_millet_config_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.YaoMilletConfigRecord">
        update es_yao_millet_config_record
        <set>
            <if test="currency != null and currency != '' ">
                currency = #{currency,jdbcType=VARCHAR},
            </if>
            <if test="totalQuantity != null">
                total_quantity = #{totalQuantity,jdbcType=DECIMAL},
            </if>
            <if test="remainQuantity != null">
                remain_quantity = #{remainQuantity,jdbcType=DECIMAL},
            </if>
            <if test="dateNumber != null">
                date_number = #{dateNumber,jdbcType=INTEGER},
            </if>
            <if test="dateTime != null">
                date_time = #{dateTime,jdbcType=DATE},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateSubtractQuantity">
        update es_yao_millet_config_record
        set remain_quantity = remain_quantity - #{quantity,jdbcType=DECIMAL}
            where id = #{id,jdbcType=BIGINT} and remain_quantity <![CDATA[ >=]]> #{quantity,jdbcType=DECIMAL}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.YaoMilletConfigRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config_record t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByDateNumberForUpdate"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config_record t
        where t.currency = #{currency} and t.date_number = #{dateNumber}
        for update
    </select>
    <select id="selectByDateNumber" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from es_yao_millet_config_record t
        where t.currency = #{currency} and t.date_number = #{dateNumber}
    </select>
</mapper>