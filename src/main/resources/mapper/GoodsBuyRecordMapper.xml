<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.GoodsBuyRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GoodsBuyRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="supplier_id" jdbcType="BIGINT" property="supplierId"/>
        <result column="address_id" jdbcType="BIGINT" property="addressId"/>
        <result column="goods_id" jdbcType="BIGINT" property="goodsId"/>
        <result column="goods_name" jdbcType="VARCHAR" property="goodsName"/>
        <result column="pic" jdbcType="VARCHAR" property="pic"/>
        <result column="original_price" jdbcType="DECIMAL" property="originalPrice"/>
        <result column="price" jdbcType="DECIMAL" property="price"/>
        <result column="integral_price" jdbcType="DECIMAL" property="integralPrice"/>
        <result column="pay_price" jdbcType="DECIMAL" property="payPrice"/>
        <result column="quantity" jdbcType="INTEGER" property="quantity"/>
        <result column="goods_type" jdbcType="VARCHAR" property="goodsType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="information_id" jdbcType="BIGINT" property="informationId"/>
        <result column="receiver" jdbcType="VARCHAR" property="receiver"/>
        <result column="re_mobile" jdbcType="VARCHAR" property="reMobile"/>
        <result column="re_address" jdbcType="VARCHAR" property="reAddress"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
        <result column="deliverer" jdbcType="VARCHAR" property="deliverer"/>
        <result column="delivery_datetime" jdbcType="TIMESTAMP" property="deliveryDatetime"/>
        <result column="logistics_company" jdbcType="VARCHAR" property="logisticsCompany"/>
        <result column="logistics_code" jdbcType="VARCHAR" property="logisticsCode"/>
        <result column="logistics_trace" jdbcType="VARCHAR" property="logisticsTrace"/>
        <result column="updater" jdbcType="BIGINT" property="updater"/>
        <result column="updater_name" jdbcType="VARCHAR" property="updaterName"/>
        <result column="update_datetime" jdbcType="TIMESTAMP" property="updateDatetime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.user_id
        , t.supplier_id
        , t.address_id
        , t.goods_id
        , t.goods_name
        , t.pic
        , t.original_price
        , t.price
        , t.integral_price
        , t.pay_price
        , t.quantity
        , t.goods_type
        , t.status
        , t.information_id
        , t.receiver
        , t.re_mobile
        , t.re_address
        , t.create_datetime
        , t.deliverer
        , t.delivery_datetime
        , t.logistics_company
        , t.logistics_code
        , t.logistics_trace
        , t.updater
        , t.updater_name
        , t.update_datetime
        , t.remark
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="supplierId != null">
                AND t.supplier_id = #{supplierId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="addressId != null">
                AND t.address_id = #{addressId, jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="goodsName != null and goodsName != '' ">
                AND t.goods_name like concat('%',#{goodsName, jdbcType=VARCHAR},'%')
            </if>
            <if test="pic != null and pic != '' ">
                AND t.pic = #{pic, jdbcType=VARCHAR}
            </if>
            <if test="originalPrice != null">
                AND t.original_price = #{originalPrice, jdbcType=DECIMAL}
            </if>
            <if test="price != null">
                AND t.price = #{price, jdbcType=DECIMAL}
            </if>
            <if test="integralPrice != null">
                AND t.integral_price = #{integralPrice, jdbcType=DECIMAL}
            </if>
            <if test="payPrice != null">
                AND t.pay_price = #{payPrice, jdbcType=DECIMAL}
            </if>
            <if test="quantity != null">
                AND t.quantity = #{quantity, jdbcType=INTEGER}
            </if>
            <if test="goodsType != null and goodsType != '' ">
                AND t.goods_type = #{goodsType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="informationId != null">
                AND t.information_id = #{informationId, jdbcType=BIGINT}
            </if>
            <if test="receiver != null and receiver != '' ">
                AND t.receiver like concat('%',#{receiver, jdbcType=VARCHAR},'%')
            </if>
            <if test="reMobile != null and reMobile != '' ">
                AND t.re_mobile like concat('%',#{reMobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="reAddress != null and reAddress != '' ">
                AND t.re_address = #{reAddress, jdbcType=VARCHAR}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="deliverer != null and deliverer != '' ">
                AND t.deliverer = #{deliverer, jdbcType=VARCHAR}
            </if>
            <if test="deliveryDatetime != null">
                AND t.delivery_datetime = #{deliveryDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                AND t.logistics_company = #{logisticsCompany, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                AND t.logistics_code = #{logisticsCode, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCodeStr != null and logisticsCodeStr != '' ">
                AND t.logistics_code like concat('%',#{logisticsCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                AND t.logistics_trace = #{logisticsTrace, jdbcType=VARCHAR}
            </if>
            <if test="updater != null">
                AND t.updater = #{updater, jdbcType=BIGINT}
            </if>
            <if test="updaterName != null and updaterName != ''">
                AND t.updater_name = #{updaterName, jdbcType=VARCHAR}
            </if>
            <if test="updateDatetime != null">
                AND t.update_datetime = #{updateDatetime, jdbcType=TIMESTAMP}
            </if>
            <if test="remark != null and remark != ''">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="createDatetimeStart != null">
                AND t.create_datetime <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
            </if>
            <if test="createDatetimeEnd != null">
                AND t.create_datetime <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.GoodsBuyRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into mall_goods_buy_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="supplierId != null">
                supplier_id,
            </if>
            <if test="addressId != null ">
                address_id,
            </if>
            <if test="goodsId != null ">
                goods_id,
            </if>
            <if test="goodsName != null and goodsName != '' ">
                goods_name,
            </if>
            <if test="pic != null and pic != '' ">
                pic,
            </if>
            <if test="originalPrice != null">
                original_price,
            </if>
            <if test="price != null ">
                price,
            </if>
            <if test="integralPrice != null ">
                integral_price,
            </if>
            <if test="payPrice != null ">
                pay_price,
            </if>
            <if test="quantity != null ">
                quantity,
            </if>
            <if test="goodsType != null and goodsType != '' ">
                goods_type,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="informationId != null ">
                information_id,
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver,
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile,
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer,
            </if>
            <if test="deliveryDatetime != null ">
                delivery_datetime,
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company,
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code,
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                logistics_trace,
            </if>
            <if test="updater != null">
                updater,
            </if>
            <if test="updaterName != null and updaterName != ''">
                updater_name,
            </if>
            <if test="updateDatetime != null ">
                update_datetime,
            </if>
            <if test="remark != null and remark != ''">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="supplierId != null">
                #{supplierId, jdbcType=BIGINT},
            </if>
            <if test="addressId != null">
                #{addressId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="goodsName != null and goodsName != '' ">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                #{pic,jdbcType=VARCHAR},
            </if>
            <if test="originalPrice != null">
                #{originalPrice, jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="integralPrice != null">
                #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="payPrice != null">
                #{payPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                #{quantity,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null and goodsType != '' ">
                #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="informationId != null">
                #{informationId,jdbcType=BIGINT},
            </if>
            <if test="receiver != null and receiver != '' ">
                #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                #{logisticsTrace,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != ''">
                #{remark, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mall_goods_buy_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.GoodsBuyRecord">
        update mall_goods_buy_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="supplierId != null">
                supplier_id = #{supplierId, jdbcType=BIGINT},
            </if>
            <if test="addressId != null">
                address_id = #{addressId,jdbcType=BIGINT},
            </if>
            <if test="goodsId != null">
                goods_id = #{goodsId,jdbcType=BIGINT},
            </if>
            <if test="goodsName != null and goodsName != '' ">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="pic != null and pic != '' ">
                pic = #{pic,jdbcType=VARCHAR},
            </if>
            <if test="originalPrice != null">
                original_price = #{originalPrice, jdbcType=DECIMAL},
            </if>
            <if test="price != null">
                price = #{price,jdbcType=DECIMAL},
            </if>
            <if test="integralPrice != null">
                integral_price = #{integralPrice,jdbcType=DECIMAL},
            </if>
            <if test="payPrice != null">
                pay_price = #{payPrice,jdbcType=DECIMAL},
            </if>
            <if test="quantity != null">
                quantity = #{quantity,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null and goodsType != '' ">
                goods_type = #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="informationId != null">
                information_id = #{informationId,jdbcType=BIGINT},
            </if>
            <if test="receiver != null and receiver != '' ">
                receiver = #{receiver,jdbcType=VARCHAR},
            </if>
            <if test="reMobile != null and reMobile != '' ">
                re_mobile = #{reMobile,jdbcType=VARCHAR},
            </if>
            <if test="reAddress != null and reAddress != '' ">
                re_address = #{reAddress,jdbcType=VARCHAR},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="deliverer != null and deliverer != '' ">
                deliverer = #{deliverer,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDatetime != null">
                delivery_datetime = #{deliveryDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                logistics_company = #{logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                logistics_code = #{logisticsCode,jdbcType=VARCHAR},
            </if>
            <if test="logisticsTrace != null and logisticsTrace != '' ">
                logistics_trace = #{logisticsTrace,jdbcType=VARCHAR},
            </if>
            <if test="updater != null">
                updater = #{updater, jdbcType=BIGINT},
            </if>
            <if test="updaterName != null and updaterName != ''">
                updater_name = #{updaterName, jdbcType=VARCHAR},
            </if>
            <if test="updateDatetime != null">
                update_datetime = #{updateDatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null and remark != ''">
                remark = #{remark, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_buy_record t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GoodsBuyRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_buy_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectForUpdate" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mall_goods_buy_record t
        where t.id = #{id,jdbcType=BIGINT}
        for update
    </select>
    <select id="selectSum" resultType="com.std.core.pojo.response.GoodsBuyRecordSumRes">
        select
        count(1) totalBuyCount,COALESCE(sum(integral_price),0) totalIntegralPrice,COALESCE(sum(original_price),0)
        totalOriginalPrice
        from mall_goods_buy_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="supplierId != null">
                AND t.supplier_id = #{supplierId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="addressId != null">
                AND t.address_id = #{addressId, jdbcType=BIGINT}
            </if>
            <if test="goodsId != null">
                AND t.goods_id = #{goodsId, jdbcType=BIGINT}
            </if>
            <if test="goodsName != null and goodsName != '' ">
                AND t.goods_name like concat('%',#{goodsName, jdbcType=VARCHAR},'%')
            </if>
            <if test="goodsType != null and goodsType != '' ">
                AND t.goods_type = #{goodsType, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                         close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="informationId != null">
                AND t.information_id = #{informationId, jdbcType=BIGINT}
            </if>
            <if test="receiver != null and receiver != '' ">
                AND t.receiver like concat('%',#{receiver, jdbcType=VARCHAR},'%')
            </if>
            <if test="reMobile != null and reMobile != '' ">
                AND t.re_mobile like concat('%',#{reMobile, jdbcType=VARCHAR},'%')
            </if>
            <if test="deliverer != null and deliverer != '' ">
                AND t.deliverer = #{deliverer, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCompany != null and logisticsCompany != '' ">
                AND t.logistics_company = #{logisticsCompany, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCode != null and logisticsCode != '' ">
                AND t.logistics_code = #{logisticsCode, jdbcType=VARCHAR}
            </if>
            <if test="logisticsCodeStr != null and logisticsCodeStr != '' ">
                AND t.logistics_code like concat('%',#{logisticsCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="createDatetimeStart != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> #{createDatetimeStart}
            </if>
            <if test="createDatetimeEnd != null">
                AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ <=]]> #{createDatetimeEnd}
            </if>
        </trim>
    </select>
    <select id="selectSumUser" resultType="java.lang.Integer">
        SELECT
        count( 1 )
        FROM
        ( SELECT t.user_id, t.create_datetime FROM mall_goods_buy_record t INNER JOIN tsys_user tu ON t.user_id = tu.id
        GROUP BY t.user_id ) t
        WHERE 1=1
        <if test="createDatetimeStart != null">
            AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ >=]]> #{createDatetimeStart, jdbcType=TIMESTAMP}
        </if>
        <if test="createDatetimeEnd != null">
            AND DATE_FORMAT(t.create_datetime,'%Y-%m-%d') <![CDATA[ <=]]> #{createDatetimeEnd, jdbcType=TIMESTAMP}
        </if>
    </select>
</mapper>