<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.CollectionPeriodWhiteJoinMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.CollectionPeriodWhiteJoin">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="period_id" jdbcType="BIGINT" property="periodId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="number" jdbcType="INTEGER" property="number"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.period_id
        , t.user_id
        , t.mobile
        , t.number
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="periodId != null">
                AND t.period_id = #{periodId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="mobile != null and mobile != '' ">
                AND t.mobile = #{mobile, jdbcType=VARCHAR}
            </if>
            <if test="number != null">
                AND t.number = #{number, jdbcType=INTEGER}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.CollectionPeriodWhiteJoin" keyProperty="id"
            useGeneratedKeys="true">
        insert into nft_collection_period_white_join
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="periodId != null ">
                period_id,
            </if>
            <if test="userId != null ">
                user_id,
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile,
            </if>
            <if test="number != null ">
                number,
            </if>
            <if test="createDatetime != null ">
                create_datetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="periodId != null">
                #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null and mobile != '' ">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                #{number,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <insert id="insertBatch">
        <foreach collection="list" index="index" item="item" separator=";">
            insert into nft_collection_period_white_join
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.periodId != null ">
                    period_id,
                </if>
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.mobile != null and item.mobile != ''">
                    mobile,
                </if>
                <if test="item.number != null ">
                    number,
                </if>
                <if test="item.createDatetime != null ">
                    create_datetime,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.periodId != null">
                    #{item.periodId,jdbcType=BIGINT},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.mobile != null and item.mobile != ''">
                    #{item.mobile,jdbcType=BIGINT},
                </if>
                <if test="item.number != null">
                    #{item.number,jdbcType=INTEGER},
                </if>
                <if test="item.createDatetime != null">
                    #{item.createDatetime,jdbcType=TIMESTAMP},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from nft_collection_period_white_join
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除 -->
    <delete id="deleteByPeriodId" parameterType="java.lang.Long">
        delete
        from nft_collection_period_white_join
        where period_id = #{periodId,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.CollectionPeriodWhiteJoin">
        update nft_collection_period_white_join
        <set>
            <if test="periodId != null">
                period_id = #{periodId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null and mobile != '' ">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="number != null">
                number = #{number,jdbcType=INTEGER},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_white_join t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.CollectionPeriodWhiteJoin"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_collection_period_white_join t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectTotalNumber" parameterType="com.std.core.pojo.domain.CollectionPeriodWhiteJoin"
            resultType="java.lang.Integer">
        select ifnull(sum(t.number),0)
        from nft_collection_period_white_join t
        <include refid="where_condition"/>
    </select>
</mapper>