<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.BankInfoMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.BankInfo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="bank_code" jdbcType="VARCHAR" property="bankCode"/>
        <result column="bank_name" jdbcType="VARCHAR" property="bankName"/>
        <result column="type" jdbcType="VARCHAR" property="type"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="order_on" jdbcType="INTEGER" property="orderOn"/>

    </resultMap>

    <sql id="Base_Column_List">
        t
        .
        id
        , t.bank_code
        , t.bank_name
        , t.type
        , t.logo
        , t.status
        , t.remark
        , t.order_on
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code = #{bankCode, jdbcType=VARCHAR}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                t.bank_code like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.bank_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name like concat('%', #{bankName, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="logo != null and logo != '' ">
                AND t.logo = #{logo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="orderOn != null">
                AND t.order_on = #{orderOn, jdbcType=INTEGER}
            </if>

        </trim>
    </sql>
    <sql id="where_condition_oss">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="bankCode != null and bankCode != '' ">
                AND t.bank_code like concat('%', #{bankCode, jdbcType=VARCHAR},'%')
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                t.bank_code like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                t.bank_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="bankName != null and bankName != '' ">
                AND t.bank_name like concat('%', #{bankName, jdbcType=VARCHAR},'%')
            </if>
            <if test="type != null and type != '' ">
                AND t.type = #{type, jdbcType=VARCHAR}
            </if>
            <if test="logo != null and logo != '' ">
                AND t.logo = #{logo, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != '' ">
                AND t.status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="statusList != null and statusList.size() != 0 ">
                AND t.status in
                <foreach item="item" index="index" collection="statusList" open="(" separator=","
                        close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="remark != null and remark != '' ">
                AND t.remark = #{remark, jdbcType=VARCHAR}
            </if>
            <if test="orderOn != null">
                AND t.order_on = #{orderOn, jdbcType=INTEGER}
            </if>

        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.BankInfo">
        insert into tstd_bank_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null ">
                id,
            </if>
            <if test="bankCode != null and bankCode != '' ">
                bank_code,
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name,
            </if>
            <if test="type != null and type != '' ">
                `type`,
            </if>
            <if test="logo != null and logo != '' ">
                logo,
            </if>
            <if test="status != null and status != '' ">
                status,
            </if>
            <if test="remark != null and remark != '' ">
                remark,
            </if>
            <if test="orderOn != null">
                order_on,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="bankCode != null and bankCode != '' ">
                #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                #{type, jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                #{logo,jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderOn != null">
                #{orderOn, jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from tstd_bank_info
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.BankInfo">
        update tstd_bank_info
        <set>
            <if test="bankCode != null and bankCode != '' ">
                bank_code = #{bankCode,jdbcType=VARCHAR},
            </if>
            <if test="bankName != null and bankName != '' ">
                bank_name = #{bankName,jdbcType=VARCHAR},
            </if>
            <if test="logo != null and logo != '' ">
                logo = #{logo,jdbcType=VARCHAR},
            </if>
            <if test="type != null and type != '' ">
                `type` = #{type, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != '' ">
                status = #{status,jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != '' ">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderOn != null">
                order_on = #{orderOn, jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bank_info t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.BankInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bank_info t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectByConditionOss" parameterType="com.std.core.pojo.domain.BankInfo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tstd_bank_info t
        <include refid="where_condition_oss"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>
</mapper>