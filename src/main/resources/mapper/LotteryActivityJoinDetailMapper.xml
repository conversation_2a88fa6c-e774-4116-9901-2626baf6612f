<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.LotteryActivityJoinDetailMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.LotteryActivityJoinDetail">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="activity_id" jdbcType="BIGINT" property="activityId"/>
        <result column="activity_name" jdbcType="VARCHAR" property="activityName"/>
        <result column="join_id" jdbcType="BIGINT" property="joinId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="collection_id" jdbcType="BIGINT" property="collectionId"/>
        <result column="collection_name" jdbcType="VARCHAR" property="collectionName"/>
        <result column="cover_file_url" jdbcType="VARCHAR" property="coverFileUrl"/>
        <result column="collection_detail_id" jdbcType="BIGINT" property="collectionDetailId"/>
        <result column="token_id" jdbcType="VARCHAR" property="tokenId"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.activity_id
        , t.activity_name
        , t.join_id
        , t.user_id
        , t.collection_id
        , t.collection_name
        , t.cover_file_url
        , t.collection_detail_id
        , t.token_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="activityId != null">
                AND t.activity_id = #{activityId, jdbcType=BIGINT}
            </if>
            <if test="activityName != null and activityName != '' ">
                AND t.activity_name like concat('%',#{activityName, jdbcType=VARCHAR},'%')
            </if>
            <if test="joinId != null">
                AND t.join_id = #{joinId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="collectionId != null">
                AND t.collection_id = #{collectionId, jdbcType=BIGINT}
            </if>
            <if test="collectionName != null and collectionName != '' ">
                AND t.collection_name like concat('%',#{collectionName, jdbcType=VARCHAR},'%')
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                AND t.cover_file_url = #{coverFileUrl, jdbcType=VARCHAR}
            </if>
            <if test="collectionDetailId != null">
                AND t.collection_detail_id = #{collectionDetailId, jdbcType=BIGINT}
            </if>
            <if test="tokenId != null and tokenId != '' ">
                AND t.token_id = #{tokenId, jdbcType=VARCHAR}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.LotteryActivityJoinDetail">
        insert into nft_lottery_activity_join_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="activityId != null ">
                activity_id,
              </if>
              <if test="activityName != null and activityName != '' ">
                activity_name,
              </if>
              <if test="joinId != null ">
                join_id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="collectionId != null ">
                collection_id,
              </if>
              <if test="collectionName != null and collectionName != '' ">
                collection_name,
              </if>
              <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url,
              </if>
              <if test="collectionDetailId != null ">
                collection_detail_id,
              </if>
              <if test="tokenId != null and tokenId != '' ">
                token_id,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="activityId != null">
                #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="joinId != null">
                #{joinId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="collectionDetailId != null">
                #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                #{tokenId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="insertBatch">
        <foreach collection="list" item="item" index="index" separator=";">
            insert into nft_lottery_activity_join_detail
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.activityId != null ">
                    activity_id,
                </if>
                <if test="item.activityName != null and item.activityName != '' ">
                    activity_name,
                </if>
                <if test="item.joinId != null ">
                    join_id,
                </if>
                <if test="item.userId != null ">
                    user_id,
                </if>
                <if test="item.collectionId != null ">
                    collection_id,
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    collection_name,
                </if>
                <if test="item.coverFileUrl != null and item.coverFileUrl != '' ">
                    cover_file_url,
                </if>
                <if test="item.collectionDetailId != null ">
                    collection_detail_id,
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    token_id,
                </if>
            </trim>
            <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="item.activityId != null">
                    #{item.activityId,jdbcType=BIGINT},
                </if>
                <if test="item.activityName != null and item.activityName != '' ">
                    #{item.activityName,jdbcType=VARCHAR},
                </if>
                <if test="item.joinId != null">
                    #{item.joinId,jdbcType=BIGINT},
                </if>
                <if test="item.userId != null">
                    #{item.userId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionId != null">
                    #{item.collectionId,jdbcType=BIGINT},
                </if>
                <if test="item.collectionName != null and item.collectionName != '' ">
                    #{item.collectionName,jdbcType=VARCHAR},
                </if>
                <if test="item.coverFileUrl != null and item.coverFileUrl != '' ">
                    #{item.coverFileUrl,jdbcType=VARCHAR},
                </if>
                <if test="item.collectionDetailId != null">
                    #{item.collectionDetailId,jdbcType=BIGINT},
                </if>
                <if test="item.tokenId != null and item.tokenId != '' ">
                    #{item.tokenId,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from nft_lottery_activity_join_detail
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.LotteryActivityJoinDetail">
        update nft_lottery_activity_join_detail
        <set>
            <if test="activityId != null">
                activity_id = #{activityId,jdbcType=BIGINT},
            </if>
            <if test="activityName != null and activityName != '' ">
                activity_name = #{activityName,jdbcType=VARCHAR},
            </if>
            <if test="joinId != null">
                join_id = #{joinId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="collectionId != null">
                collection_id = #{collectionId,jdbcType=BIGINT},
            </if>
            <if test="collectionName != null and collectionName != '' ">
                collection_name = #{collectionName,jdbcType=VARCHAR},
            </if>
            <if test="coverFileUrl != null and coverFileUrl != '' ">
                cover_file_url = #{coverFileUrl,jdbcType=VARCHAR},
            </if>
            <if test="collectionDetailId != null">
                collection_detail_id = #{collectionDetailId,jdbcType=BIGINT},
            </if>
            <if test="tokenId != null and tokenId != '' ">
                token_id = #{tokenId,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.LotteryActivityJoinDetail"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from nft_lottery_activity_join_detail t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
</mapper>