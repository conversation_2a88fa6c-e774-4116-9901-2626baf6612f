<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IncomeDailyMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.IncomeDaily">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="income_date" jdbcType="TIMESTAMP" property="incomeDate"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.income_date
        , t.amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="incomeDate != null">
                AND t.income_date = #{incomeDate, jdbcType=TIMESTAMP}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.IncomeDaily">
        insert into tmm_income_daily
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="incomeDate != null ">
                income_date,
              </if>
              <if test="amount != null ">
                amount,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="incomeDate != null">
                #{incomeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tmm_income_daily
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.IncomeDaily">
        update tmm_income_daily
        <set>
            <if test="incomeDate != null">
                income_date = #{incomeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_income_daily t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.IncomeDaily"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_income_daily t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <select id="total" resultType="com.std.core.pojo.response.IncomeDailyTotalRes">
        select coalesce(sum(amount),0) as amount  from tmm_income_daily
    </select>

    <insert id="userStatisticTrigger" parameterType="java.util.Date">
        <![CDATA[
        insert into tmm_income_daily (`income_date`, `amount`, `create_datetime`)
        select
        #{yesterdayStart} as `income_date`,
        coalesce(sum(settle_amount),0) as amount,
        now() as create_datetime
        from tstd_income
        where DATE_FORMAT(create_datetime,'%Y-%m-%d') = DATE_FORMAT(#{yesterdayStart},'%Y-%m-%d')
        ]]>
    </insert>

</mapper>