<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.IncomeDailyUserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.IncomeDailyUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="node_type" jdbcType="VARCHAR" property="nodeType"/>
        <result column="income_date" jdbcType="TIMESTAMP" property="incomeDate"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.node_type
        , t.income_date
        , t.amount
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="nodeType != null and nodeType != '' ">
                AND t.node_type = #{nodeType, jdbcType=VARCHAR}
            </if>
            <if test="incomeDate != null">
                AND t.income_date = #{incomeDate, jdbcType=TIMESTAMP}
            </if>
            <if test="amount != null">
                AND t.amount = #{amount, jdbcType=DECIMAL}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.IncomeDailyUser">
        insert into tmm_income_daily_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="nodeType != null and nodeType != '' ">
                node_type,
              </if>
              <if test="incomeDate != null ">
                income_date,
              </if>
              <if test="amount != null ">
                amount,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="nodeType != null and nodeType != '' ">
                #{nodeType,jdbcType=VARCHAR},
            </if>
            <if test="incomeDate != null">
                #{incomeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from tmm_income_daily_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.IncomeDailyUser">
        update tmm_income_daily_user
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="nodeType != null and nodeType != '' ">
                node_type = #{nodeType,jdbcType=VARCHAR},
            </if>
            <if test="incomeDate != null">
                income_date = #{incomeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_income_daily_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.IncomeDailyUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tmm_income_daily_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>

    <insert id="userStatisticTrigger" parameterType="java.util.Date">
        <![CDATA[
        insert into tmm_income_daily_user (`user_id`, `node_type`, `income_date`, `amount`, `create_datetime`)
        select
        user_id,
        max(node_type) as node_type,
        #{yesterdayStart} as `income_date`,
        coalesce(sum(settle_amount),0) as amount,
        now() as create_datetime
        from tstd_income
        where DATE_FORMAT(create_datetime,'%Y-%m-%d') = DATE_FORMAT(#{yesterdayStart},'%Y-%m-%d')
        AND type != '4'
        group by user_id
        ]]>
    </insert>
</mapper>