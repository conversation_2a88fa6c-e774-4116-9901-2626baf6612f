<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.ChannelUserMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.ChannelUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="channel_code" jdbcType="VARCHAR" property="channelCode"/>
        <result column="channel_user_id" jdbcType="BIGINT" property="channelUserId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.channel_code
        , t.channel_user_id
        , t.user_id
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="channelCode != null">
                AND t.channel_code = #{channelCode, jdbcType=VARCHAR}
            </if>
            <if test="channelUserId != null">
                AND t.channel_user_id = #{channelUserId, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.ChannelUser">
        insert into cs_channel_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="channelCode != null ">
                channel_code,
              </if>
              <if test="channelUserId != null ">
                channel_user_id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="channelCode != null">
                #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelUserId != null">
                #{channelUserId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from cs_channel_user
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.ChannelUser">
        update cs_channel_user
        <set>
            <if test="channelCode != null">
                channel_code = #{channelCode,jdbcType=VARCHAR},
            </if>
            <if test="channelUserId != null">
                channel_user_id = #{channelUserId,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.ChannelUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectCulturalchainByIdNo" parameterType="com.std.core.pojo.domain.ChannelUser"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from cs_channel_user t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where tu.id_no=#{idNo} and t.channel_code=#{channelCode}
    </select>
</mapper>