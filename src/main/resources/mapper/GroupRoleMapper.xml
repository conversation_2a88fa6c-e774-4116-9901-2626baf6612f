<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.std.core.mapper.GroupRoleMapper">

    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.GroupRole">
        <result column="t_group_id" property="groupId" jdbcType="BIGINT"/>
        <result column="t_role_id" property="roleId" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.group_id as t_group_id,
    t.role_id as t_role_id
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="groupId != null and groupId != '' ">
                AND t.group_id = #{groupId,jdbcType=BIGINT}
            </if>
            <if test="roleId != null and roleId != '' ">
                AND t.role_id = #{roleId,jdbcType=BIGINT}
            </if>
        </trim>
    </sql>

    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.GroupRole"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tsys_group_role t
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
                ${orderBy}
            </if>
        </trim>
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from tsys_group_role t
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="selectCountByCondition" parameterType="com.std.core.pojo.domain.GroupRole"
            resultType="java.lang.Long">
        select count(1) from tsys_group_role t
        <include refid="where_condition"/>
    </select>

    <delete id="deleteByGroupId" parameterType="java.lang.Long">
        delete
        from tsys_group_role
        where group_id = #{groupId,jdbcType=BIGINT}
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into tsys_group_role (group_id, role_id)
        values
        <foreach item="groupRole" index="index" collection="list" separator=",">
            (#{groupRole.groupId,jdbcType=BIGINT}, #{groupRole.roleId,jdbcType=BIGINT})
        </foreach>
    </insert>

</mapper>
