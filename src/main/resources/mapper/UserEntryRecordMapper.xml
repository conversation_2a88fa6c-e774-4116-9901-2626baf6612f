<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.std.core.mapper.UserEntryRecordMapper">
    <resultMap id="BaseResultMap" type="com.std.core.pojo.domain.UserEntryRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="ticket_type" jdbcType="VARCHAR" property="ticketType"/>
        <result column="ticket_id" jdbcType="BIGINT" property="ticketId"/>
        <result column="create_time" jdbcType="BIGINT" property="createTime"/>
        <result column="create_datetime" jdbcType="TIMESTAMP" property="createDatetime"/>
    </resultMap>

    <sql id="Base_Column_List">
        t.id
        , t.user_id
        , t.ticket_type
        , t.ticket_id
        , t.create_time
        , t.create_datetime
    </sql>

    <sql id="where_condition">
        <trim prefix="WHERE" prefixOverrides="AND | OR">
            <if test="id != null">
                AND t.id = #{id, jdbcType=BIGINT}
            </if>
            <if test="userId != null">
                AND t.user_id = #{userId, jdbcType=BIGINT}
            </if>
            <if test="keywords != null and keywords != '' ">
                AND (
                tu.mobile like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.id_no like concat('%', #{keywords, jdbcType=VARCHAR},'%') OR
                tu.real_name like concat('%', #{keywords, jdbcType=VARCHAR},'%')
                )
            </if>
            <if test="ticketType != null and ticketType != '' ">
                AND t.ticket_type = #{ticketType, jdbcType=VARCHAR}
            </if>
            <if test="ticketId != null">
                AND t.ticket_id = #{ticketId, jdbcType=BIGINT}
            </if>
            <if test="createTime != null">
                AND t.create_time = #{createTime, jdbcType=BIGINT}
            </if>
            <if test="createDatetime != null">
                AND t.create_datetime = #{createDatetime, jdbcType=TIMESTAMP}
            </if>
        </trim>
    </sql>

    <!-- 选择添加 -->
    <insert id="insertSelective" parameterType="com.std.core.pojo.domain.UserEntryRecord">
        insert into meta_user_entry_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
              <if test="id != null ">
                id,
              </if>
              <if test="userId != null ">
                user_id,
              </if>
              <if test="ticketType != null and ticketType != '' ">
                ticket_type,
              </if>
              <if test="ticketId != null ">
                ticket_id,
              </if>
              <if test="createTime != null ">
                create_time,
              </if>
              <if test="createDatetime != null ">
                create_datetime,
              </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                #{ticketType,jdbcType=VARCHAR},
            </if>
            <if test="ticketId != null">
                #{ticketId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>

    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from meta_user_entry_record
        where id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 选择修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="com.std.core.pojo.domain.UserEntryRecord">
        update meta_user_entry_record
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="ticketType != null and ticketType != '' ">
                ticket_type = #{ticketType,jdbcType=VARCHAR},
            </if>
            <if test="ticketId != null">
                ticket_id = #{ticketId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=BIGINT},
            </if>
            <if test="createDatetime != null">
                create_datetime = #{createDatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 查询 -->
    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from meta_user_entry_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        where t.id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 组合条件查询 -->
    <select id="selectByCondition" parameterType="com.std.core.pojo.domain.UserEntryRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from meta_user_entry_record t
        INNER JOIN tsys_user tu on t.user_id = tu.id
        <include refid="where_condition"/>
        <trim prefix="ORDER BY ">
            <if test="orderBy != null and orderBy != '' ">
              ${orderBy}
            </if>
        </trim>
    </select>
    <select id="selectTodayFirstTime" parameterType="com.std.core.pojo.domain.UserEntryRecord"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from meta_user_entry_record t
        where t.user_id=#{userId} and DATE_FORMAT(t.create_datetime,'%Y-%m-%d') = DATE_FORMAT(#{date},'%Y-%m-%d') order by t.create_time asc limit 1
    </select>
</mapper>