####### server #######
server.port=6903
###### system.code #######
system.code=CL-META
####### dev datasource #######
spring.datasource.url=*******************************************************************************************************************************************************************
#spring.datasource.url=********************************************************************************************************************************************************************************
#spring.datasource.url=*******************************************************************************************************************************************************************
##spring.datasource.username=root
##spring.datasource.password=cdxt123456Q
spring.datasource.username=meta_stage
spring.datasource.password=meta_stage2022@
#spring.datasource.url=********************************************************************************************************************************************************************
#spring.datasource.username=meta_online_22
#spring.datasource.password=meta_online_2201
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
####### eureka #######
eureka.client.serviceUrl.defaultZone=***********************************/eureka/
eureka.client.register-with-eureka=true
eureka.client.fetchRegistry=true
####### sms #######
sms_url=http://**************:7902/std-sms/api
cloud_wallet_url=http://*************:2803/forward-service/api
###### logger #######
logging.level.root=INFO
logging.level.org.springframework.web=INFO
logging.level.com.std.core.mapper=INFO
####### swagger #######
swagger.host:api.dev.metahz.com
swagger.enable=true
##### AliOSS  #######
oss.endpoint=sts.cn-hangzhou.aliyuncs.com
oss.accessKeyId=LTAI5tKpVvey4X9o7NLy23mf
oss.accessKeySecret=******************************
oss.roleArn=acs:ram::1807472798409592:role/ygzcrole
oss.bucket=fish-meta
oss.bucket.endpoint=https://oss-accelerate.aliyuncs.com
oss.bucket.ossEndpoint=oss-accelerate
oss.bucket.filePath=https://fish-meta.oss-accelerate.aliyuncs.com
##### AliOCR #######
ocr.accessKeyId=LTAI5t5cyb64jvKj4L3EKU9W
ocr.accessKeySecret=******************************
ocr.endpoint=ocr-api.cn-hangzhou.aliyuncs.com
###### dingxiang #######
dingxiang.appId=78d86f92f4b64f25bf65800f83e711f3
dingxiang.appSecret=c8cb69aa3119be06014f4c05efd46d7d
dingxiang.apiServer=https://dxvip.dingxiang-inc.com
###### bsn #######
bsn.plat.code=M1000
bsn.key=M100011111111111111111111
bsn.base.url=https://ddc.wanlinsl.com/api
bsn.host.address=iaa1dprgyz3zp402cmr889ejz2wj5uf8n76w23c50y
###### alipay #######
alipay.providerid=2088341304406111
alipay.appid=2021003107627080
alipay.privatekey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC8ytCre3u4aK5dEJ+j8EUEytf3Eocd6eEReP+UDnm1pT+axOHhlG9fc6mucm8l9P+dBEHox3ymItgv6vEZbJeM9qQTbq0ophR7wosccTavMNka8tZN9PNT4R6q2bGSeDKWtNFwaHR6D3ma2MIe5Q3npWWlU42jmmKPE8PGs4eM+p+e3HvdvrHY00Vw9SSboW5IYrWQthbex0lZrS2n/WtqfQe9+IBWtk/TBqR+hLElQ1+3sJjSXpRQFSFSpyRR6oq2vqJvUuQ6jT7vw/Mn68aQANlJ6x1L4KhaaGYXrE71kbgmWBEvALW8JTI1lVntXMC3duW81CxROgnzOzFQBFlHAgMBAAECggEAcr5yBUeP8oztLPMlk+Y8jKUcjbZdGPlFKfF4pOwHXTeKaO6wuwIUjtclkjlLBK7bjPtBc9GLGQ6khrssvl8Z3TsqjgT+iW4nlPCwzCCjM+dkjNR7G/Z8zcZNdVnKa1NSq/ZKNR/Ha0hsiIils1FUGi6UkN8vfvgeXweMPgBvqma7506UHF2yWV1wZjAY04qRoRUwRiYF5tkoMlbVW4YTHrs8unZw2IAj3q3J92jxYWg0U/r6X/VjUw2V+wUHBoDct2UF071+tMZ64oBHBQtaunV56ZAzp+4l1/Ln3GZl9ZIwi2aV09Q6T8fQgUMAKsWBPsU5qD5bvXa9hXS8ZWDkwQKBgQD3w9x/WE4jRJQSfN1Ze/C3OTKlq9yEhmZES8uCVSD4yuGvTE9cr7eKd5yqYKC4ARILEiHrB+jj8aZ3vmQZWYwtRsPE37whYHXDKZ7NDrhn+GqqbYs5RONX0vTVv/zDOQPOfHVBITKM4uPceoLf2wZsD2Ka7bk4YbCPpq4qDOb8iwKBgQDDES0ouFRw0Lagf2AkW8YSJRk5hPhYAhvsP9AJF1nO/K4kOFzraPsx9IUe2IL/ZiYwHU89/mQvuSzyOdpzHb+suBr3hWMfscy6FXWoCzNEpCF9/wPs6+G38FXLlAIAegVqX4tLfPQQqEsnS5RyefSQKzMjmEu4k75dlPtAk83BtQKBgQCXzb9ZnucxIKGAulR8sT2JZVgsvaH86MPb8HyePD5ZcSTZ3O17IoZYJnUsxstyMeW2NsPlVxFQKA0QrOxzw7PpQcRM6bBA2vMMQoHzraM6wK/KABgfXJ/WfYBZPqpFirdUWlkVMJviSlnGCqIJOg8zQjHuLt0fOGuPeFaZmRIlhQKBgEOtp+9i1Nk3cMWKq/6xYK1W9cOBIPDX+nwjP7iHw38PKBQlAggjKlQQtEcZc4nNFR7IbH/jzTrJkpCQBfy+q4vvbFvIibWraI5/d4zQJf+AuqYOXnxK8stwtTOmrHMh4tMfRyLvvXYyDbEaheyZ54JFmr6UzmDB0eCCGhIztcWpAoGASVKj5CJw3hkDgoqp6xuX9s8E1wOBOt0lDxOqlE1oswznyynqo3iy7ENfdtcSaFK2re1CTIlp7WFwa/yoLwFheZShnwbzxFLDbZ+Mcu9UCqjiwneK4AcaNsqAS7N9KyH7zym/FNjeIDJT5yJhPCKBiKrEOWrp9vggB0q/mlcUm9I=
alipay.publickey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAlb0/vF2jsLfCQzrs/Fuds+U4ubUzlgN3mqMTU97NSkiw5yo+sajLkW5vEGa4YRRfnGO7QDMh9H+9vF5DcEqHogQZ1w0sH5t180URqJSngcxPQFK5/nzwWqGtk0uYxs4Xq+NAX4einMvLgubfRj1QHytMDWzqqo5iplbjBZTnBsjTN4cJ4c5FISprFC9uTTDFVepgaCdhA4V25RA+ffA35kPdl0V9O1tqumYo2vmJKTYAXEE3BlGyHsW3LfoTUgkFODZZZ8ZHFMpdsykJYIUsEw/WMvYDyOuKRLk5M2i+RNh438/SDu+YgSXIm4B37QmLfXXq6cvY9rAiaO86Z4teQQIDAQAB
alipay.notifyurl=http://api.metahz.com/callback/public/alipay
alipay.returnurl=http://front.metahz.com/pay/paySuccess
alipay.signtype=RSA2
alipay.gateway=https://openapi.alipay.com/gateway.do
alipay.format=json
alipay.charset=UTF-8
###### wechat #######
wechat.h5.appid=wx1c9837d9ba02c433
wechat.h5.secret=a72c9eeae2e0a5c4443f5e6ad377af01
wechat.appid=wxe251665319847d00
wechat.backurl=http://api.dev.metahz.com/callback/public/wechat
wechat.merchant.id=1400666002
wechat.merchant.privatekey=cd3c5e63fa3b2599216e1da5d3c9e66d
wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
#wechat.appid=wx1ef7949d8a04a200
#wechat.backurl=http://api.metahz.com/callback/public/wechat
#wechat.merchant.id=1617970563
#wechat.merchant.privatekey=615340e9a36b4fb93c44ff6c0edf6a8c
#wechat.refundurl=https://api.mch.weixin.qq.com/secapi/pay/refund
###### adapay #######
adapay.appid=app_511dc9b5-63d1-4e5d-9668-0582d29ef447
adapay.backurl=http://**************:6903/callback/public/adapay
adapay.merchant.key=1400666002
adapay.api.key=api_live_4a8d8286-e01d-4854-8e89-dcf847ffec17
adapay.mock.api.key=api_test_bc402229-801c-49d2-b995-028833c7cae3
adapay.rsa.private.key=MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBANV9tuQ8CZmHbgjludGO/3WG42FFllyKlA1gCiYt6Vp9Vg5GiNw6Oey/lbC/82MtGq3dMvZYZTVWt4eocqeZnb0ddXJVLZtCYoy4YNwWXi2JWZc87+dmNxoIjKR4ANCFr719p1W+UerbUsi9B9C7woAHoqhFtIQbB8kxVIZEOC7zAgMBAAECgYBe/MMuA0RmKeqcTNIDWjNxMCXk9pgy7nl3Bf8eA5lq6I8sZNep3MI/AvLwJEd/Hedb6iotjyDgvYeE9T6mMWQLm3CJtvd7XddjLwdo2jrSrbPqa6AAPdghsMsGfI1lhbmqcwCUhkl9uUqJA9TpG9/79jreEuqeWp1+eaIkRWvx6QJBAPJCYU9LymoxYrLEpTQP6dqWoPIT9vr36TNjNpp/tNjw7gpIBdYurD65OoX29Gd7zrM2aVHSSSlS14v38AZALFcCQQDhmZ4u7t4vua8z2Ku03/pMpV/O0cGSuYlWxn23QxbjkAXr2jqFbh/ZthhHC4yzk4Xp2/8yvQ5BNMEe7ODEEHDFAkAH1yAhGdnWL/z6viR+l9lAqslQrFa87pMMh7R3sZRfxQRfLs+Ji/8lFIeRWFm2k6ov4J3t+PlHLhgtvnt1KFSHAkBxBMR6PrCQuDVNg/6BXrPGMhMNSYfwOYLUNhxE8xdEaaKNxYYL0l+icdc3wFF8pSsxPUVb5dp+UC9vjrwLfTEBAkEApOGgdRb8XO3bYKb/5B4qySMLoriaHkJcNlu82r+79tew1nPUpVupg+PXTjDDua5uqUIiLHmbbpKy/+WHeSYjXg==
###### babyPay #######
babyPay.backurl=http://**************:6903/callback/public/adapay
babyPay.merchant.no=************
babyPay.pwd=123456
babyPay.url=https://test-api.huishouqian.com/api/acquiring
babyPay.pfxpath=/************_pri.pfx
babyPay.publicpath=/MANDAO_************_pub.cer
###### babyPayBank #######
babyPayBank.merchant.id=*********
babyPayBank.terminal.id=*********
babyPayBank.aes.key=4f66405c4f66405c
babyPayBank.baopay.url=https://vgw.baofoo.com/cutpayment/protocol/backTransRequest
babyPayBank.card.info.url=https://vgw.baofoo.com/biztransfer/product/bankcard/v1/bin/info
babyPayBank.pri.key.pass=*********_286941
babyPayBank.pfxpath=/bfkey_*********@@*********.pfx
babyPayBank.cerpath=/bfkey_*********@@*********.cer
babyPayBank.backurl=http://**************:6903/callback/public/baopay_bank
###### channleAction.System #######
channleAction.System.url=http://*************:1818/api/core/v1/auction_system
channleAction.System.user.apply.authorization=/public/authorization
channleAction.System.user.collection.info=/public/user_collection_info
channleAction.System.collection.pass.on=/public/pass_on
channleAction.System.roll.out.callback=/public/roll_out_callback
channleAction.System.get.token=/public/get_token
###### yeepay.config #######
yeepay.bank.parentMerchantNo=***********
yeepay.bank.merchantNo=***********
yeepay.bank.notifyUrl=http://**************:6903/callback/public/yeepay_bank
yeepay.bank.fundProcessType=DELAY_SETTLE
yeepay.bank.appKey=app_***********
yeepay.bank.url=https://cash.yeepay.com/cashier/std
yeepay.bank.certType=RSA2048
yeepay.bank.priKey=MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDnnsSLuctvsiMji8sIJb1AhnWnBu7L+bJQIbwYlA4FRt8RS8fHMHHIOPwL4tBZExAif9/vtC/edHa52B7qbnLo7y8UgcKDWv/xjkafJYFxW7Pqde4QDQSydfTMvZOQC1+Ic18mcMA6U4LxE4lBR6ZEz7IAxGxbBvVIpepqBooh7aKNaEIWfHUj8RAN9Ev6KWUlCXD6zUcg1yo9zAmbUlnSvh28Hh/+Q3ZIg9aaIMsJ2enw2O7fYtpAp8UkRR2YSNgDN0JxrCd9crCRZObb0G+a65atnN+YW9BPJtekPLcBeJ5nRK1YRtBqyabiVdz2gR3k7qzsKyjDYMYR5S3Ar0urAgMBAAECggEABcdSGB2omS0S2lsfNz2LIZYK+fOJfqSBUd5dl9MqPGI+p+r6spoC3ZjGzeqqj+12NzN2v79kCh988iPrCt9Bjsb04ATSv6+nlENHDhygKA/MsZbY2AhzHUXQcIodKWo6x8RVGsY+zYBjxRfboJ1BZTFSAr1yBmR4jHlk/xJLB530JLkw8ybI4JsbMa1BXC8N7cKsn90nKQmEKW7tOy28vTcIokzC/0j7PLFC5qI1tSULsmkzio9gZTXVk9Lokdqpz6CN9RKN3Y4CVQK7VKIv9B9WN04TfbpL0X6LpMMgn2gp8TqJF7LEpXEqdXR1Kacqzl6g8hPj5kPAaPdNlfIw7QKBgQD/8u0GlgDK3KTF7CBd0f/hEnGdiPMMzGYSAWlQeRw+6WkPmcsqXKDJcPf79P0997QfHdQFxW7wI7zNNhBEtQWB+l7y1boxeEWPzCva2jEpqMb2nGxXYC/lK4GJw2oK+JFySYG4pQ2vLnoSqn+JLGfEMSMod4+BXlqxUU2s97fr5wKBgQDnqplhN18w6SQv2FOL0IpCH/sk1mnnHAOn+YQ89RtacVTEBkDoUM4uPuHq/zf5NHmZZglc5VdD0FHBPtwAflfxTl2SwfR50T/mOA6AkUErgbclw5tqAY/jwtZidCfu+LtCweS5f2B/Ir/1k6k1SNe4JsnFmYGAa1mQ8pCmPpOJnQKBgCKcGM0RL54+49EXsvC82tr2gYKJ6EPMx+ibZEBLgDRJn7lKVJyv1dYnfoEFidD9U8/5WphFMFEso/Uj4GNKDyqaToiLuhfCabWeKDf51TgK6IykbkoKaa+ViHi2gfplzlUj93SebRFJOZyYjZJg/M8czeJE+JTHy3+6vMQs4yP1AoGAfCuVkGT0cavUyIxDo4IASRcJLqJ6GvSKBSOZPG8H3T83duX/LkX5bRIpVyK6pqj4sD7fr9oBHwjueQyvMNiHXIBraTjqR8zfWg8bvs+cRax+NRCWIYh6zlJlRFoL+Wt6O3rflIlweFRw3TjrDfNgJIUtyurPXbysQiOxYlnSDIUCgYEA8CZo+wjszR3oIZWX2fqDrWVvL3qAshBYZqzoe3xkKznm8sbjc7BQiG+pBaQ80a046+9Q3+ObDpn0KrSHC84Q0Kv7SdJ2k03RFwT0urVu2rJzHXeaEQRGaNnfpiTx85KgayDQfq7Va45F/Y2cEAf5ioivdCIJhBElrVupdcQNYpY=
yeepay.bank.redirectUrl=http://channel.front.dev.metahz.com/pay/return
yeepay.bank.micro.noticeUrl=http://api.dev.metahz.com/callback/public/yeepay_micro_notice
#yeepay.bank.micro.filePath=/Users/<USER>/Desktop/file
yeepay.bank.micro.filePath=/mnt/www/chain_play/file
#yeepay.bank.micro.filePath=/Users/<USER>/Desktop/data
yeepay.bank.merchant.notify.url=http://**************:6903/callback/public/yeepay_user_micro_notice
###### xmly #######
xmly.app.key=62bf6d9bc639fc4dfc8bfa2c653aa861
xmly.app.secret=a0bcb65ca8e5fb105bc8f940ae3e2411
xmly.http.url=https://api.ximalaya.com
xmly.grant.type=js_client_credentials
xmly.device.id=app_jssdk_device
xmly.redirect.uri=http://channel.front.dev.metahz.com/home
xmly.h5.domain.uri=http://channel.front.dev.metahz.com
###### RongCloud #######
rongcloud.appKey=bmdehs6pba19s
rongcloud.appSecret=dWf7mCkOgnd
##### huobi  #######
huobi.websocketHost=wss://api.huobi.de.com
huobi.restHost=https://api.huobi.de.com
huobi.order.prefix=AARJLhwW98
##### REDIS  #######
# Redis\u6570\u636E\u5E93\u7D22\u5F15\uFF08\u9ED8\u8BA4\u4E3A0\uFF09
spring.redis.database=3
#spring.redis.host=*************
spring.redis.host=localhost
spring.redis.port=6379
spring.redis.password=v2Vp5xP0Ieq73uBx
spring.redis.max-active=600
# \u8FDE\u63A5\u6C60\u6700\u5927\u963B\u585E\u7B49\u5F85\u65F6\u95F4\uFF08\u4F7F\u7528\u8D1F\u503C\u8868\u793A\u6CA1\u6709\u9650\u5236\uFF09
spring.redis.max-wait=60000
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5927\u7A7A\u95F2\u8FDE\u63A5
spring.redis.max-idle=300
# \u8FDE\u63A5\u6C60\u4E2D\u7684\u6700\u5C0F\u7A7A\u95F2\u8FDE\u63A5
spring.redis.min-idle=0
spring.redis.timeout=6000
spring.devtools.add-properties=false
### xxl-job admin address list, such as "http://address" or "http://address01,http://address02"
xxl.job.admin.addresses=http://**************:6904/xxl-job-admin
### xxl-job, access token
xxl.job.accessToken=
### xxl-job executor appname
xxl.job.executor.appname=meta-job-executor
### xxl-job executor registry-address: default use address to registry , otherwise use ip:port if address is null
xxl.job.executor.address=
### xxl-job executor server-info
xxl.job.executor.ip=127.0.0.1
xxl.job.executor.port=6309
### xxl-job executor log-path
xxl.job.executor.logpath=/mnt/www/chain_play/xxl-job/jobhandler
### xxl-job executor log-retention-days
xxl.job.executor.logretentiondays=30
### \u706B\u5E01\u4EA4\u6613\u5206\u5E03\u5F0F\u9501\u8D85\u65F6\u65F6\u95F4\uFF08\u5355\u4F4D\uFF1A\u6BEB\u79D2\uFF09
huobi.entrust.timeout=30000
meta.lock.timeout=30000
#-------------------------------------------------------------------------------
# Nacos\u914D\u7F6E
#-------------------------------------------------------------------------------
#nacos:
#config:
#server-addr: ***************:8848
#type: yaml
#namespace: a3029897-dd3d-4205-aae5-28e6b0e425dc
#bootstrap:
#enable: true
#log-enable: true
#context-path: nacos
#data-id: goldMqProducer
#auto-refresh: true
#####mq###
#spring.rabbitmq.host=***************
#spring.rabbitmq.port=5672
#spring.rabbitmq.username=admin
#spring.rabbitmq.password=ASD123
jpush.customer.masterSecret=201b90b905330849e1fb99c0
jpush.customer.appKey=c82bcd8733af76df67eddd05
jpush.apns.production=0
##-----------?????-------------
schedule.doPeriodStartTrigger=1
schedule.doFirstMarketOrderDesignCollecction=1
schedule.doPeriodDrawStrawsEndTrigger=1
schedule.doDealDrawStrawsJoinRecordTrigger=1
schedule.doPayCancelBuyoutProducts=1
schedule.doBuyProductsTimeoutDownTrigger=1
schedule.doSynPayRecordTrigger=1
schedule.doAuctionStartTrigger=1
schedule.doPayCancelAuctionProducts=1
schedule.doSynAuctionBondRefundTrigger=1
schedule.doChallengeFinalDeal=1
schedule.doChallengeFinal=1
schedule.doChangeStatusChangeTrigger=1
schedule.doIncomeSettleTrigger=1
schedule.doStatisticDailyTrigger=1
schedule.doBlindSellFinal=1
schedule.doBlindSellFinalDeal=1
schedule.doStatUserBizData=1
schedule.doSendUpgradeInfoTrigger=1
schedule.doJpushSend=0
schedule.doUnFreezeTrigger=1
schedule.doPeriodJoinRecordMigration=1
schedule.doBaopaySmsCodeFailure=1
schedule.doFirstMarketUnFreezeTrigger=1
schedule.doPeriodAuctionCancelFreezeTrigger=1
schedule.doPeriodAuctionEndFreezeTrigger=1
schedule.doPeriodAuctionBondReturnFreezeTrigger=1
schedule.doPeriodAuctionDefaultFreezeTrigger=1
schedule.doPayBackTrigger=1
schedule.doQueryPayBackTrigger=1
schedule.doDivideTrigger=1
schedule.doUserDivideTrigger=1
schedule.doCheckDivideResultTrigger=1
schedule.doPeriodAuctionDividesTrigger=1
schedule.doInvitationActivityTrigger=1
schedule.doCollectionStatisticsTrigger=1
schedule.doSynchronousYeepaySettleRecordTrigger=1
schedule.doIntegralGoodsDistributionTrigger=1
schedule.doIntegralGoodsEndTrigger=1
schedule.doIntegralGoodsActivityCloseTrigger=1
schedule.doMetaPitSellerEndTrigger=1
schedule.doDegressionAuctionTrigger=1
schedule.doLotteryActivityEndTrigger=1
schedule.doPublishNft=0
schedule.doSyncNftTrade=0
#
#schedule.doPeriodStartTrigger=0
#schedule.doFirstMarketOrderDesignCollecction=0
#schedule.doPeriodDrawStrawsEndTrigger=0
#schedule.doDealDrawStrawsJoinRecordTrigger=0
#schedule.doPayCancelBuyoutProducts=0
#schedule.doBuyProductsTimeoutDownTrigger=0
#schedule.doSynPayRecordTrigger=0
#schedule.doAuctionStartTrigger=0
#schedule.doPayCancelAuctionProducts=0
#schedule.doSynAuctionBondRefundTrigger=0
#schedule.doChallengeFinalDeal=0
#schedule.doChallengeFinal=0
#schedule.doChangeStatusChangeTrigger=0
#schedule.doIncomeSettleTrigger=0
#schedule.doStatisticDailyTrigger=0
#schedule.doBlindSellFinal=0
#schedule.doBlindSellFinalDeal=0
#schedule.doStatUserBizData=0
#schedule.doSendUpgradeInfoTrigger=0
#schedule.doJpushSend=0
#schedule.doFirstMarketUnFreezeTrigger=0
#schedule.doUnFreezeTrigger=0
#schedule.doPeriodJoinRecordMigration=0
#schedule.doBaopaySmsCodeFailure=0
#
#schedule.doPeriodAuctionCancelFreezeTrigger=0
#schedule.doPeriodAuctionEndFreezeTrigger=0
#schedule.doPeriodAuctionBondReturnFreezeTrigger=0
#schedule.doPeriodAuctionDefaultFreezeTrigger=0
#schedule.doPayBackTrigger=0
#schedule.doQueryPayBackTrigger=0
#schedule.doDivideTrigger=0
#schedule.doUserDivideTrigger=0
#schedule.doCheckDivideResultTrigger=0
#schedule.doPeriodAuctionDividesTrigger=0
#schedule.doInvitationActivityTrigger=0
#schedule.doCollectionStatisticsTrigger=0
#schedule.doSynchronousYeepaySettleRecordTrigger=0
#schedule.doIntegralGoodsDistributionTrigger=0
#schedule.doIntegralGoodsEndTrigger=0
#schedule.doIntegralGoodsActivityCloseTrigger=0
#schedule.doMetaPitSellerEndTrigger=0
#schedule.doDegressionAuctionTrigger=0
#schedule.doLotteryActivityEndTrigger=0
#schedule.doPublishNft=0
#schedule.doSyncNftTrade=0