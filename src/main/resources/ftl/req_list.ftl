package ${requestUrl};

import ${commonUrl}.base.BaseListReq;
import java.util.Date;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
 * 列表查询${entityComment}
 *
 * <AUTHOR> ${author}
 * @since : ${createTime}
 */
@Data
public class ${entityName}ListReq extends BaseListReq {

<#list cis as ci>
    /**
     * ${ci.comment}
     */
    @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", position = ${(ci_index+1)*10})
    private ${ci.javaType} ${ci.property};

</#list>

}
	