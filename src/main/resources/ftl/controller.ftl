package ${controllerUrl};

import io.swagger.annotations.ApiOperationSupport;
import java.util.List;

import com.github.pagehelper.PageHelper;

import ${commonUrl}.base.Result;
import ${commonUrl}.page.PageUtil;
import ${commonUrl}.page.PageInfo;
import ${commonUrl}.utils.SqlUtil;
import ${commonUrl}.version.ApiVersion;

import com.std.core.controller.base.BaseController;

import ${entityUrl}.${entityName};
import ${entityUrl}.User;
import ${requestUrl}.${entityName}CreateReq;
import ${requestUrl}.${entityName}ListReq;
import ${requestUrl}.${entityName}ModifyReq;
import ${requestUrl}.${entityName}PageReq;
import ${serviceUrl}.I${entityName}Service;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.Api;
import javax.validation.Valid;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* ${entityComment}Controller
*
* <AUTHOR> ${author}
* @since : ${createTime}
*/
@ApiVersion(1)
@RestController
@Api(value = "${entityComment}管理", tags = "${entityComment}管理")
@RequestMapping("{version}/${requestMappingUrl}")
public class ${entityName}Controller extends BaseController {

@Resource
private I${entityName}Service ${objectName}Service;

@ApiOperation(value = "新增${entityComment}")
@ApiOperationSupport(order = 10)
@PostMapping(value = "/create")
public Result create(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ${entityName}CreateReq request) {
User operator = getUserByToken(token);
${objectName}Service.create(request, operator);

return new Result();
}

@ApiOperation(value = "删除${entityComment}")
@ApiOperationSupport(order = 20)
@PostMapping("/remove/{id}")
public Result remove(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid ${idType} id) {
User operator = getUserByToken(token);
${objectName}Service.remove(id);

return new Result();
}

@ApiOperation(value = "修改${entityComment}")
@ApiOperationSupport(order = 30)
@PostMapping(value = "/modify")
public Result modify(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ${entityName}ModifyReq request) {
User operator = getUserByToken(token);
${objectName}Service.modify(request, operator);

return new Result();
}

@ApiOperation(value = "查询${entityComment}")
@ApiOperationSupport(order = 40)
@PostMapping("/detail/{id}")
public Result<${entityName}> detail(@RequestHeader(value = "Authorization") String token, @PathVariable("id") @Valid ${idType} id) {
User operator = getUserByToken(token);

return new Result<>(${objectName}Service.detail(id));
}

@ApiOperation(value = "分页条件查询${entityComment}")
@ApiOperationSupport(order = 50)
@PostMapping(value = "/page")
public Result<PageInfo<${entityName}>> page(@RequestHeader(value = "Authorization") String token,
@RequestBody @Valid ${entityName}PageReq request) {
User operator = getUserByToken(token);
PageHelper.startPage(request.getPageNum(), request.getPageSize(),
SqlUtil.parseSort(request.getSort(), ${entityName}.class));

return PageUtil.pageResult(${objectName}Service.page(request));
}

@ApiOperation(value = "列表条件查询${entityComment}")
@ApiOperationSupport(order = 60)
@PostMapping(value = "/list")
public Result
<List<${entityName}>> list(@RequestHeader(value = "Authorization") String token, @RequestBody @Valid ${entityName}ListReq request) {
User operator = getUserByToken(token);

return new Result<>(${objectName}Service.list(request));
}

}