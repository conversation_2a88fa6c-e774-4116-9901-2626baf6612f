package ${serviceImplUrl};

import ${commonUrl}.enums.ECommonErrorCode;
import ${commonUrl}.exception.BizException;

import ${commonUrl}.utils.EntityUtils;
import ${commonUrl}.utils.SqlUtil;

import ${mapperUrl}.${entityName}Mapper;

import ${entityUrl}.${entityName};
import ${entityUrl}.User;

import ${requestUrl}.${entityName}CreateReq;
import ${requestUrl}.${entityName}ListReq;
import ${requestUrl}.${entityName}ModifyReq;
import ${requestUrl}.${entityName}PageReq;

import ${serviceUrl}.I${entityName}Service;

<#--import com.std.core.util.IdGeneratorUtil;-->
<#--import com.std.core.enums.EErrorCode;-->
import ${enumUrl}.EErrorCode;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
* ${entityComment}ServiceImpl
*
* <AUTHOR> ${author}
* @since : ${createTime}
*/
@Service
public class ${entityName}ServiceImpl implements I${entityName}Service {

@Resource
private ${entityName}Mapper ${objectName}Mapper;

/**
* 新增${entityComment}
*
* @param req 新增${entityComment}入参
* @param operator 操作人
*/
@Override
public void create(${entityName}CreateReq req, User operator) {
${entityName} ${objectName} = EntityUtils.copyData(req, ${entityName}.class);
${objectName}Mapper.insertSelective(${objectName});
}

/**
* 删除${entityComment}
*
* @param id 主键ID
*/
@Override
public void remove(${idType} id) {
${objectName}Mapper.deleteByPrimaryKey(id);
}

/**
* 修改${entityComment}
*
* @param req 修改${entityComment}入参
* @param operator 操作人
*/
@Override
public void modify(${entityName}ModifyReq req, User operator) {
${entityName} ${objectName} = EntityUtils.copyData(req, ${entityName}.class);
${objectName}Mapper.updateByPrimaryKeySelective(${objectName});
}

/**
* 详情查询${entityComment}
*
* @param id 主键ID
* @return ${entityComment}对象
*/
@Override
public ${entityName} detail(${idType} id) {
${entityName} ${objectName} = ${objectName}Mapper.selectByPrimaryKey(id);
if (null == ${objectName}) {
throw new BizException(EErrorCode.E500003, this.getClass().getSimpleName(), id);
}

return ${objectName};
}

/**
* 分页查询${entityComment}
*
* @param req 分页查询${entityComment}入参
* @return 分页${entityComment}对象
*/
@Override
public List<${entityName}> page(${entityName}PageReq req) {
${entityName} condition = EntityUtils.copyData(req, ${entityName}.class);

return ${objectName}Mapper.selectByCondition(condition);
}

/**
* 列表查询${entityComment}
*
* @param req 列表查询${entityComment}入参
* @return 列表${entityComment}对象
*/
@Override
public List<${entityName}> list(${entityName}ListReq req) {
${entityName} condition = EntityUtils.copyData(req, ${entityName}.class);
condition.setOrderBy(SqlUtil.parseSort(req.getSort(), ${entityName}.class));

return ${objectName}Mapper.selectByCondition(condition);
}

}