package ${requestUrl};

import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;

/**
* 新增${entityComment}
*
* <AUTHOR> ${author}
* @since : ${createTime}
*/
@Data
public class ${entityName}CreateReq {

<#list cis as ci>
    <#if ci.property != "id" && ci.property != "creator" && ci.property != "creatorName"
    && ci.property != "createDateTime"  && ci.property != "updater"  && ci.property != "updaterName"
    && ci.property != "updateDateTime" && ci.property != "remark">
      /**
      * ${ci.comment}
      */
    <#if ci.notNull=="NO">
    <#if ci.javaType=="String">
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", required = true, position = ${(ci_index+1)*10})
      @NotBlank(message = "${ci.comment}不能为空")
      private ${ci.javaType} ${ci.property};
    <#elseif ci.javaType=="Date">
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", required = true, position = ${(ci_index+1)*10})
      @NotBlank(message = "${ci.comment}不能为空")
      private String ${ci.property};
    <#else>
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", required = true, position = ${(ci_index+1)*10})
      @NotNull(message = "${ci.comment}不能为空")
      private ${ci.javaType} ${ci.property};
    </#if>
    <#else>
    <#if ci.javaType=="Date">
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", required = true, position = ${(ci_index+1)*10})
      private String ${ci.property};
    <#else>
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", position = ${(ci_index+1)*10})
      private ${ci.javaType} ${ci.property};
    </#if>
    </#if>

    </#if>
</#list>

}
	