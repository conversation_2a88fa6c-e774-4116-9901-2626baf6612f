package ${requestUrl};

import javax.validation.constraints.NotNull;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

/**
* 修改${entityComment}
*
* <AUTHOR> ${author}
* @since : ${createTime}
*/
@Data
public class ${entityName}ModifyReq {

<#list cis as ci>
    <#if ci.property != "creator" && ci.property != "creatorName" && ci.property != "createDateTime"
    && ci.property != "updater"  && ci.property != "updaterName" && ci.property != "updateDateTime"
    && ci.property != "remark">
      /**
      * ${ci.comment}
      */
    <#if ci.property=="id">
      @NotNull(message = "id不能为空")
    </#if>
    <#if ci.javaType=="Date">
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", position = ${(ci_index+1)*10})
      private String ${ci.property};
    <#else>
      @ApiModelProperty(name = "${ci.property}", value = "${ci.comment}", position = ${(ci_index+1)*10})
      private ${ci.javaType} ${ci.property};
    </#if>

    </#if>
</#list>

}
	