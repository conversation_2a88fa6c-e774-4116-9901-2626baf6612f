package ${mapperUrl};

import ${entityUrl}.${entityName};
import java.util.List;

/**
 * ${entityComment}Mapper
 *
 * <AUTHOR> ${author}
 * @since : ${createTime}
 */
public interface ${entityName}Mapper {

    /**
     * 选择性插入
     *
     * @param record ${entityComment}
     * @return 影响行数
     */
    int insertSelective(${entityName} record);

    /**
     * 根据主键删除
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(${idType} id);

    /**
     * 选择性更新
     *
     * @param record ${entityComment}
     * @return 影响行数
     */
    int updateByPrimaryKeySelective(${entityName} record);

    /**
     * 根据主键查询
     *
     * @param id 主键ID
     * @return ${entityComment}
     */
    ${entityName} selectByPrimaryKey(${idType} id);

    /**
     * 根据条件查询
     *
     * @param condition 查询条件
     * @return ${entityComment}列表
     */
    List<${entityName}> selectByCondition(${entityName} condition);

}
	